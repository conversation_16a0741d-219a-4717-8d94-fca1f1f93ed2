using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.API.Helpers;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/country")]
  [ApiController]
  [Authorize]
  public class CountryController : ControllerBase
  {
    public ICountryService _countryService;

    public CountryController(ICountryService countryService) { 
      _countryService = countryService;
    }

    [HttpGet]
    [Route("/api/countries")]
    [ResponseCache(Duration = 600)]

    public async Task<IActionResult> Search([FromQuery] int? unique, [FromQuery] string? search, CancellationToken cancellationToken)
    {
      var searchDTO = new CountrySearchDTO() { };

      if (! String.IsNullOrEmpty(search))
      {
        searchDTO = JsonSerializer.Deserialize<CountrySearchDTO>(search);
      }

      var response = await _countryService.Search(searchDTO, cancellationToken);

      return Ok(response);
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create(CountryDTO dto, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        var response = await _countryService.Create(dto, cancellationToken);

        return Ok(response);
      }

      return Forbid();
    }

    [HttpDelete]
    [Route("{countryId}")]
    public async Task<IActionResult> Delete(uint countryId, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        var response = await _countryService.Delete(countryId, cancellationToken);

        return Ok(response);
      }

      return Forbid();
    }

    [HttpGet]
    [Route("{countryId}")]
    public async Task<IActionResult> GetCountry(uint countryId, [FromQuery] string search, CancellationToken cancellationToken)
    {
      var searchDTO = new CountrySearchDTO() { };

      if (!String.IsNullOrEmpty(search))
      {
        searchDTO = JsonSerializer.Deserialize<CountrySearchDTO>(search);
      }

      var response = await _countryService.Get(countryId, searchDTO, cancellationToken);

      if (response != null)
      {
        return Ok(response);
      }

      return NotFound();
    }

    [HttpPatch]
    [Route("{countryId}")]
    public async Task<IActionResult> Patch(uint countryId, [FromBody] JsonPatchDocument<Country> patch, CancellationToken cancellationToken)
    {
      // Only edit addresses that are ours (or if we're admin)
      if (User.IsAdmin())
      {
        try
        {
          return Ok(await _countryService.Patch(countryId, patch, cancellationToken));
        }
        catch (Exception ex)
        {
          return ex.ParseError();
        }
      }

      return Forbid();
    }
  }
}
