﻿using Microsoft.AspNetCore.JsonPatch;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;

namespace Trading.Services.InspectCollect.Interfaces
{
  public interface ICValidationInterface
  {
    Task<ICValidationDTO> Create(ICValidationCreateDTO dto);

    Task<ValidatedResultDTO<ICValidationDTO>> Get(Guid id, ICValidationSearchDTO search, CancellationToken cancellationToken);
    Task<bool> Delete(Guid id);

    Task<ICValidationDTO> Patch(Guid id, JsonPatchDocument<ICValidation> patch);

    Task<SearchResultDTO<ICValidationDTO>> Search(ICValidationSearchDTO search, CancellationToken cancellationToken);
  }
}
