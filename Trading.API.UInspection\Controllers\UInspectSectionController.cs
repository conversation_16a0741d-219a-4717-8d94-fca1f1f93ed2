﻿using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.UInspections;
using Trading.API.Data.Models.UInspections;
using Trading.Services.UInspections.Interfaces;

namespace Trading.API.UInspection.Controllers
{
  [Route("api/uinspect/section")]
  [ApiController]
  public class UInspectSectionController : ControllerBase
  {
    private readonly IUInspectSectionService _uInspectSectionService;

    public UInspectSectionController(IUInspectSectionService uInspectSectionService)
    {
      this._uInspectSectionService = uInspectSectionService;
    }

    [HttpGet]
    [Route("/api/uinspect/format/{uInspectFormatId}/sections")]
    [ResponseCache(Duration = 5)]
    public async Task<IActionResult> GetAppraisalSections(uint uInspectFormatId, [FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = new UInspectSectionSearchDTO() { };

        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<UInspectSectionSearchDTO>(query);
        }

        dto.Filters.UInspectFormatId = uInspectFormatId;

        var result = await _uInspectSectionService.Search(dto, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<IActionResult> Get(uint id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = new UInspectSectionSearchDTO();
        
        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<UInspectSectionSearchDTO>(query);
        }

        var result = await _uInspectSectionService.Get(id, cancellationToken, dto);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/uinspect/sections")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = new UInspectSectionSearchDTO();
        
        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<UInspectSectionSearchDTO>(query);
        }

        var result = await _uInspectSectionService.Search(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create([FromBody] UInspectSectionDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        var result = await _uInspectSectionService.Create(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(uint id, JsonPatchDocument<UInspectSection> patch, CancellationToken cancellationToken)
    {
      try
      {
        var result = await _uInspectSectionService.Patch(id, patch, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
