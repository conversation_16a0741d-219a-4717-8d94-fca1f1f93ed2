﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.DTO.LeadCRM;
using Trading.API.Data.Enums.Valuation;

namespace Trading.API.Data.DTO.Valuation
{
  public class ValuationQuoteDataDTO : BaseModelEntityDTO
  {
    public Guid? ValuationQuoteId { get; set; }
    public ValuationQuoteDTO ValuationQuote { get; set; }
    public string ResultData { get; set; }
  }
}
