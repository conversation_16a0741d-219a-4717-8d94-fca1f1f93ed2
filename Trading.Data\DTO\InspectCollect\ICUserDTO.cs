﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.DTO.InspectCollect.Location;

namespace Trading.API.Data.DTO.InspectCollect;

public class ICUserDTO : BaseModelEntityDTO
{
  public string UserName { get; set; }
  public string Email { get; set; }

  public Guid ICContainerGroupId { get; set; }
  public ICContainerGroupDTO ICContainerGroup { get; set; }

  public List<ICUserRoleDTO> ICUserRoles { get; set; }

  public Guid? DefaultLocationId { get; set; }
  public ICLocationDTO DefaultLocation { get; set; }
}

public class ICUserSearchDTO : BaseSearchDTO
{
  public ICUserSearchFilters Filters { get; set; } = new ICUserSearchFilters();
}

public class ICUserSearchFilters : BaseFilter
{
  public bool IncludeDeleted { get; set; } = false;
  public Guid? ICContainerGroupId { get; set; }
}

public class CreateICUserDTO
{
  public Guid ICContainerGroupId { get; set; }
  public string UserName { get; set; }
  public string Email { get; set; }
  public string TempPassword { get; set; }

  public List<string> Roles { get; set; }
}
