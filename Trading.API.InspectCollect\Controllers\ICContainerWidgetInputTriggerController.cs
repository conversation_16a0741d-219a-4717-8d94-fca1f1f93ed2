using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/container-widget-input-trigger")]
  [ApiController]
  [AllowAnonymous]
  public class ICContainerWidgetInputTriggerController : ControllerBase
  {
    private readonly ICContainerWidgetInputTriggerInterface _icContainerWidgetInputTriggerService;

    public ICContainerWidgetInputTriggerController(ICContainerWidgetInputTriggerInterface serviceInterface)
    {
      _icContainerWidgetInputTriggerService = serviceInterface;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(Guid id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      ICContainerWidgetInputTriggerSearchDTO dto = new ICContainerWidgetInputTriggerSearchDTO();

      if (!String.IsNullOrEmpty(query))
      {
        dto = JsonConvert.DeserializeObject<ICContainerWidgetInputTriggerSearchDTO>(query);
      }

      var res = await _icContainerWidgetInputTriggerService.Get(id, dto, cancellationToken);
      return Ok(res);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icContainerWidgetInputTriggerService.Delete(id);
      return Ok(res);
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Post([FromBody]ICContainerWidgetInputTriggerCreateDTO dto, CancellationToken cancellationToken)
    {
      var res = await _icContainerWidgetInputTriggerService.Create(dto);
      return Ok(res);
    }

    [HttpGet]
    [Route("/api/inspect-collect/container-widget-input-triggers")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICContainerWidgetInputTriggerSearchDTO>(query);
      var res = await _icContainerWidgetInputTriggerService.Search(dto, cancellationToken);
      return Ok(res);
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<ICContainerWidgetInputTrigger> dto)
    {
      var response = await _icContainerWidgetInputTriggerService.Patch(id, dto);
      return Ok(response);
    }
  }
}