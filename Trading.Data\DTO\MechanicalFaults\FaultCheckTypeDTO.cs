﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Models.MechanicalFaults;
using Trading.API.Data.Models;
using System.ComponentModel.DataAnnotations;

namespace Trading.API.Data.DTO.MechanicalFaults
{
  public class FaultCheckTypeDTO : BaseModelEntityInt
  {
    [MaxLength(64)]
    public string Name { get; set; }

    [MaxLength(1024)]
    public string Description { get; set; }
    public ICollection<FaultCheckTypeCategory> FaultCheckTypeCategories { get; set; }

    public bool? IsDefault { get; set; }

  }
}
