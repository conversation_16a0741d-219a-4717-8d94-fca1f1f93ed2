﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.Models.DTO;
using Trading.Services.SiteScan.Interfaces;

namespace Trading.API.Controllers
{
  [ApiController]
  [Route("api/sqs")]
  public class SQSController : ControllerBase
  {
    private readonly ISQSQueueService _sqsQueueService;

    public SQSController(ISQSQueueService sqsQueueService)
    {
      _sqsQueueService = sqsQueueService;
    }

    [HttpPost("enqueue-scan")]
    public async Task<IActionResult> EnqueueScanRecord([FromBody] ScanQueueDTO scanQueueDTO, [FromQuery] bool priority = false)
    {
      if (!ModelState.IsValid)
      {
        return BadRequest(ModelState);
      }

      string blob = JsonConvert.SerializeObject(scanQueueDTO);

      bool success = await _sqsQueueService.CreateSQS(blob, priority);

      if (success)
      {
        return Ok(new { message = "Scan record enqueued successfully" });
      }
      else
      {
        return StatusCode(500, new { message = "Failed to enqueue scan record" });
      }
    }
  }
}