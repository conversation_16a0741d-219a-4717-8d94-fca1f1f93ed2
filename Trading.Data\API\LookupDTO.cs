﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO
{
  public class LookupDTO
  {
    public Guid LookupId { get; set; }
    public string TableName { get; set; }
    public string LookupValue { get; set; }

    // filter alt table by vehicle type
    public VehicleTypeEnum? VehicleType { get; set; }

    // for chained lookups, i.e. Make->Model->Derivative
    public uint? ParentId { get; set; }
  }
}
