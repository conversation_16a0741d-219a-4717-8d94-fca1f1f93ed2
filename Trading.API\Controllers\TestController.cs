using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Google.Apis.Auth.OAuth2.Responses;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.Services.ExternalDTO.Accounts;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/test")]
  [ApiController]
  public class TestController : ControllerBase
  {
    private readonly IUtilityService _utilityService;
    private static IHttpContextAccessor _httpAccessor;
    private readonly IXeroService _xeroService;
    private readonly IStripeService _stripeService;
    private readonly IYTService _ytService;
    private readonly ITestService _testService;

    public TestController(IUtilityService utilityService, IYTService ytService
      , ITestService testService
      , IHttpContextAccessor httpAccessor, IXeroService xeroService, IStripeService stripeService)
    {
      _utilityService = utilityService;
      _httpAccessor = httpAccessor;
      _xeroService = xeroService;
      _stripeService = stripeService;
      _ytService = ytService;
      _testService = testService;
    }

    [HttpGet]
    [Route("generateAlts")]
    public async Task<IActionResult> GenerateAlts(CancellationToken cancellationToken)
    {
      try
      {
        var x = await this._utilityService.GenerateAlts(cancellationToken);
        return Ok(x);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);

      }

    }

    [HttpGet]
    [Route("uploadVideo")]
    public async Task<IActionResult> UploadVideo(CancellationToken cancellationToken)
    {
      try
      {
        var x = await this._ytService.TestUpload();
        return Ok(x);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);

      }
    }

    [HttpGet]
    [Route("test2")]
    public async Task<IActionResult> Test2(CancellationToken cancellationToken)
    {
      try
      {
        await this._testService.Test2(cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);

      }
    }

    //[HttpGet]
    //[Route("spoofInvoice")]
    //public async Task<IActionResult> SpoofInvoice()
    //{

    //  // Fetch Payment Intent
    //  //var invoiceParams = await _stripeService.CreateInvoiceParams("4242424242424242");

    //  // Get ready parameters to create the invoice
    //  CreateInvoiceDTO invoiceParams = new CreateInvoiceDTO()
    //  {
    //    billing_business = "tradebids",
    //    billing_address_postcode = "LE10 3JD",
    //    billing_name = "Trade Bids",
    //    net_amount = 200,
    //    tax_amount = 10
    //  }; //await _stripeService.CreateInvoiceParams(payment_intent_id);

    //  // Create the invoice
    //  var invoice = await _xeroService.CreateInvoice(invoiceParams, null, null);

    //  if (invoice.invoice != null)
    //  {
    //    // Set it as paid
    //    //await _xeroService.SetInvoicePaid(invoice.invoice, null, null);
    //    return Ok(invoice);
    //  }

    //  return BadRequest("No invoice found");
    //}

  }
}
