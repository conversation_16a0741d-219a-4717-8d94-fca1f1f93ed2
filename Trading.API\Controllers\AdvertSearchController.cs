﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Enums;
using Trading.Services;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/listingSearch")] // not using [Controller] in route since AdBlocker will block it (contains /ad)
  [ApiController]
  [Authorize]
  public class AdvertSearchController : ControllerBase
  {
    private readonly IAdvertSearchService _advertSearchService;
    private readonly IMapper _mapper;
    private readonly TradingContext _tradingContext;

    public AdvertSearchController(
      IAdvertSearchService advertSearchService,
      TradingContext tradingContext,
      IMapper mapper)
    {
      _advertSearchService = advertSearchService;
      _tradingContext = tradingContext;
      _mapper = mapper;
    }

    [HttpGet("listingSearchCounts")]
    [ResponseCache(Duration = 30)]
    public async Task<ActionResult<IEnumerable<AdvertSearchCountDTO>>> GetAdvertSearchCounts([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = JsonConvert.DeserializeObject<AdvertSearchDTO>(query);
        var response = await _advertSearchService.AdvertSearchCount(dto, cancellationToken);

        return Ok(response);
      }
      catch (Exception ex) { return BadRequest(ex); }
    }

    [HttpGet("options")]
    //    [ResponseCache(Duration = 30)]
    public async Task<ActionResult<IEnumerable<AdvertSearchCountDTO>>> LoadLookups([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var response = await _advertSearchService.LoadLookups((uint)VehicleTypeEnum.Car, cancellationToken);

        return Ok(response);
      }
      catch (Exception ex) { return BadRequest(ex); }
    }

    [HttpGet("searchResultsCount")]
    public async Task<ActionResult<IEnumerable<AdvertSearchCountDTO>>> GetSearchResultsCount([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = JsonConvert.DeserializeObject<AdvertSearchDTO>(query);
        var response = await _advertSearchService.GetSearchResultsCount(dto, cancellationToken);

        return Ok(response);
      }
      catch (Exception ex) { return BadRequest(ex); }
    }

    [HttpGet("testEmailAlert")]
    [AllowAnonymous]
    public async Task<ActionResult> TestEmailAlert(CancellationToken cancellationToken)
    {
      try
      {
        string recipient = "<EMAIL>";
        var sphAdverts = new List<Guid>();

//  Single Photo
//  sphAdverts.Add(Guid.Parse("08dcb1a1-7d58-484e-8afd-************"));
//  3 Photos
         sphAdverts.Add(Guid.Parse("08dcbc3c-0da1-4484-891c-0ba8b939da08"));
         sphAdverts.Add(Guid.Parse("08dcc1d4-**************-479b7e921d3b"));

        var ok = await _advertSearchService.ProcessEmailNotificationMatches(sphAdverts, recipient, new Guid(), cancellationToken);
        return Ok(ok);
      }
      catch (Exception ex) { return BadRequest(ex); }
    }


    [HttpGet("listing-search-results")]
    public async Task<ActionResult<IEnumerable<SphAdvertDTO_Public>>> GetAdvertSearchResults([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = JsonConvert.DeserializeObject<AdvertSearchDTO>(query);
        var suggestedVehicle = (dto.Component == "SuggestedVehicle");

        SearchResultsDTO response;

        if (suggestedVehicle)
        {
          if (!User.IsAdmin())
          {
            return Forbid();
          }

          response = await _advertSearchService.GetSuggestedSearchResults(dto, cancellationToken);
        }
        else
        {
          dto.CurrentContactId = User.ContactId();
          response = await _advertSearchService.GetSearchResults(dto, cancellationToken);
        }

        return Ok(response);
      }
      catch (Exception ex) { return BadRequest(ex); }
    }

    [HttpGet("priceRanges")]
    public async Task<IActionResult> GetPriceRanges(CancellationToken cancellationToken)
    {
      try
      {
        var response = await _advertSearchService.GetPriceRanges(cancellationToken);
        return Ok(response);
      }
      catch (Exception ex) { return BadRequest(ex); }
    }

    [HttpGet("mileageRanges")]
    public async Task<IActionResult> GetMileageRanges(CancellationToken cancellationToken)
    {
      try
      {
        var response = await _advertSearchService.GetMileageRanges(cancellationToken);
        return Ok(response);
      }
      catch (Exception ex) { return BadRequest(ex); }
    }

    [HttpGet("capacityRanges")]
    public async Task<IActionResult> GetCapacityRanges(CancellationToken cancellationToken)
    {
      try
      {
        var response = await _advertSearchService.GetCapacityRanges(cancellationToken);
        return Ok(response);
      }
      catch (Exception ex) { return BadRequest(ex); }
    }

  }
}
