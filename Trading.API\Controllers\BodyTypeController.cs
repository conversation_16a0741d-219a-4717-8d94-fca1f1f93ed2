using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/[controller]")]
  [ApiController]
  [Authorize]
  public class BodyTypeController : ControllerBase
  {
    private readonly IBodyTypeService _bodyTypeService;

    public BodyTypeController(IBodyTypeService BodyTypeService)
    {
      this._bodyTypeService = BodyTypeService;
    }

    [HttpGet]
    [Route("/api/VehicleType/{vehicleTypeId}/BodyTypes")]
    public async Task<IActionResult> GetAll(uint vehicleTypeId, [FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var searchDTO = new BodyTypeSearchDTO() { };

        if (!String.IsNullOrEmpty(query))
        {
          searchDTO = JsonSerializer.Deserialize<BodyTypeSearchDTO>(query);
        }

        searchDTO.Filters.VehicleTypeId = vehicleTypeId;

        var bodyTypes = await _bodyTypeService.Search(searchDTO, cancellationToken);
        return Ok(bodyTypes);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
