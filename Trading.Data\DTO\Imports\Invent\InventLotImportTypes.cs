﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;


namespace Trading.API.Data.DTO.Imports.Invent;

public class AuthResponse
{
  public bool Status { get; set; }
  public string UserToken { get; set; }
}

public class AuctionsResponse
{
  public bool Status { get; set; }
  public List<Auction> Auctions { get; set; }
}

public class AuctionResponse
{
  public bool Status { get; set; }
  public Auction Auction { get; set; }
}

public class LotsResponse
{
  [JsonPropertyName("status")]
  public bool Status { get; set; }

  [JsonPropertyName("lots")]
  public List<AuctionLot> Lots { get; set; }

  [JsonPropertyName("pagination")]
  public PaginationInfo Pagination { get; set; }
}

public class PaginationInfo
{
  [JsonPropertyName("page")]
  public int Page { get; set; }

  [JsonPropertyName("pagesize")] 
  public int PageSize { get; set; }

  [JsonPropertyName("total")]
  public int Total { get; set; }
}

public class AuctionLot
{
  [JsonPropertyName("lot_id")]
  public string LotId { get; set; }

  [JsonPropertyName("vehicle_id")]
  public string VehicleId { get; set; }

  [JsonPropertyName("vrm")]
  public string Vrm { get; set; }

  [JsonPropertyName("vin")]
  public string Vin { get; set; }

  [JsonPropertyName("classification_id")]
  public string ClassificationId { get; set; }

  [JsonPropertyName("classification_title")]
  public string ClassificationTitle { get; set; }

  [JsonPropertyName("body_type")]
  public string BodyType { get; set; }

  [JsonPropertyName("colour")]
  public string Colour { get; set; }

  [JsonPropertyName("variant")]
  public string Variant { get; set; }

  [JsonPropertyName("door_count")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? DoorCount { get; set; }

  [JsonPropertyName("fuel_type")]
  public string FuelType { get; set; }

  [JsonPropertyName("manufacturer")]
  public string Manufacturer { get; set; }

  [JsonPropertyName("year")]
  [JsonConverter(typeof(StringOrNumberConverter))]
  public string Year { get; set; }

  [JsonPropertyName("engine_size")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? EngineSize { get; set; }

  [JsonPropertyName("model")]
  public string Model { get; set; }

  [JsonPropertyName("mileage")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? Mileage { get; set; }

  [JsonPropertyName("vat_status")]
  public string VatStatus { get; set; }

  [JsonPropertyName("nama_grade")]
  public string NamaGrade { get; set; }

  [JsonPropertyName("images")]
  [JsonConverter(typeof(FlexibleStringListConverter))]
  public List<string> Images { get; set; }

  [JsonPropertyName("interiorimages")]
  [JsonConverter(typeof(FlexibleStringConverter))]  
  public string InteriorImages { get; set; }

  [JsonPropertyName("autotrader_retail")]
  public string AutotraderRetail { get; set; }

  [JsonPropertyName("autotrader_trade")]
  public string AutotraderTrade { get; set; }

  [JsonPropertyName("cap_id")]
  public string CapId { get; set; }

  [JsonPropertyName("cap_retail")]
  [JsonConverter(typeof(StringOrNumberConverter))]
  public string CapRetail { get; set; }

  [JsonPropertyName("cap_clean")]
  [JsonConverter(typeof(StringOrNumberConverter))]
  public string CapClean { get; set; }

  [JsonPropertyName("cap_average")]
  [JsonConverter(typeof(StringOrNumberConverter))]
  public string CapAverage { get; set; }

  [JsonPropertyName("cap_below")]
  [JsonConverter(typeof(StringOrNumberConverter))]
  public string CapBelow { get; set; }

  [JsonPropertyName("first_reg_date")]
  public string FirstRegDate { get; set; }

  [JsonPropertyName("former_keeper")]
  public string FormerKeeper { get; set; }

  [JsonPropertyName("non_runner")]
  public string NonRunner { get; set; }

  [JsonPropertyName("has_v5")]
  public bool HasV5 { get; set; }

  [JsonPropertyName("v5_order_status")]
  public string V5OrderStatus { get; set; }

  [JsonPropertyName("vendor_name")]
  public string VendorName { get; set; }

  [JsonPropertyName("auction_id")]
  public string AuctionId { get; set; }

  [JsonPropertyName("auction_datetime")]
  public string AuctionDateTime { get; set; }

  [JsonPropertyName("auctiontype_title")]
  public string AuctionTypeTitle { get; set; }

  [JsonPropertyName("auctionlocation_id")]
  public string AuctionLocationId { get; set; }

  [JsonPropertyName("auctionlocation_title")]
  public string AuctionLocationTitle { get; set; }

  [JsonPropertyName("logisticslocation_id")]
  public string LogisticsLocationId { get; set; }

  [JsonPropertyName("servicehistory")]
  public bool ServiceHistory { get; set; }

  [JsonPropertyName("transmission")]
  public string Transmission { get; set; }

  [JsonPropertyName("inspection_report")]
  public string InspectionReport { get; set; }

  [JsonPropertyName("full")]
  public AuctionLotFullDetails Full { get; set; }

  // Legacy properties for backward compatibility
  [JsonIgnore]
  public string Make => Manufacturer;

  [JsonIgnore]
  public string Co2 => Full?.Co2;

  // Properties that might exist in other endpoints but not in this response
  [JsonPropertyName("auctionlot_id")]
  public string AuctionLotId { get; set; }

  [JsonPropertyName("auctionlot_status")]
  public string AuctionLotStatus { get; set; }

  [JsonPropertyName("reserve_price")]
  [JsonConverter(typeof(FlexibleNumberConverter<decimal>))]
  public decimal? ReservePrice { get; set; }

  [JsonPropertyName("current_bid")]
  [JsonConverter(typeof(FlexibleNumberConverter<decimal>))]
  public decimal? CurrentBid { get; set; }
}

public class AuctionLotFullDetails
{
  [JsonPropertyName("motorvehicle_manufacturer")]
  public string Manufacturer { get; set; }

  [JsonPropertyName("motorvehicle_model")]
  public string Model { get; set; }

  [JsonPropertyName("motorvehicle_variant")]
  public string Variant { get; set; }

  [JsonPropertyName("motorvehicle_registration")]
  public string Registration { get; set; }

  [JsonPropertyName("motorvehicle_prevregistration")]
  public string PrevRegistration { get; set; }

  [JsonPropertyName("motorvehicle_vin")]
  public string Vin { get; set; }

  [JsonPropertyName("motorvehicle_firstregistered")]
  public string FirstRegistered { get; set; }

  [JsonPropertyName("motorvehicle_yearofmanufacture")]
  public string YearOfManufacture { get; set; }

  [JsonPropertyName("motorvehicleclassification_id")]
  public string ClassificationId { get; set; }

  [JsonPropertyName("motorvehicle_colour")]
  public string Colour { get; set; }

  [JsonPropertyName("motorvehicle_motexpires")]
  public string MotExpires { get; set; }

  [JsonPropertyName("motorvehicle_taxdue")]
  public string TaxDue { get; set; }

  [JsonPropertyName("motorvehicle_mileage")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? Mileage { get; set; }

  [JsonPropertyName("motorvehicle_mileagedenominator")]
  public string MileageDenominator { get; set; }

  [JsonPropertyName("motorvehicle_mileagewarranted")]
  public string MileageWarranted { get; set; }

  [JsonPropertyName("motorvehicle_numkeys")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? NumKeys { get; set; }

  [JsonPropertyName("motorvehicle_extaxi")]
  public string ExTaxi { get; set; }

  [JsonPropertyName("vendor_id")]
  public string VendorId { get; set; }

  [JsonPropertyName("vat")]
  public string Vat { get; set; }

  [JsonPropertyName("motorvehicle_hasv5")]
  public string HasV5 { get; set; }

  [JsonPropertyName("motorvehicle_v5orderstatus")]
  public string V5OrderStatus { get; set; }

  [JsonPropertyName("motorvehicle_v5dateordered")]
  public string V5DateOrdered { get; set; }

  [JsonPropertyName("motorvehicle_capretail")]
  [JsonConverter(typeof(StringOrNumberConverter))]
  public string CapRetail { get; set; }

  [JsonPropertyName("motorvehicle_capclean")]
  [JsonConverter(typeof(StringOrNumberConverter))]
  public string CapClean { get; set; }

  [JsonPropertyName("motorvehicle_capaverage")]
  [JsonConverter(typeof(StringOrNumberConverter))]
  public string CapAverage { get; set; }

  [JsonPropertyName("motorvehicle_capbelow")]
  [JsonConverter(typeof(StringOrNumberConverter))]
  public string CapBelow { get; set; }

  [JsonPropertyName("motorvehicle_images")]
  [JsonConverter(typeof(FlexibleStringListConverter))]
  public List<string> Images { get; set; }

  [JsonPropertyName("motorvehicle_formerkeepers")]
  [JsonConverter(typeof(StringOrNumberConverter))]
  public string FormerKeepers { get; set; }

  [JsonPropertyName("motorvehicle_fueltype")]
  public string FuelType { get; set; }

  [JsonPropertyName("motorvehicle_bhp")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? Bhp { get; set; }

  [JsonPropertyName("motorvehicle_standardeuroemissions")]
  public string StandardEuroEmissions { get; set; }

  [JsonPropertyName("motorvehicle_co2")]
  [JsonConverter(typeof(StringOrNumberConverter))]
  public string Co2 { get; set; }

  [JsonPropertyName("motorvehicle_nox")]
  public string Nox { get; set; }

  [JsonPropertyName("motorvehicle_co")]
  [JsonConverter(typeof(StringOrNumberConverter))]
  public string Co { get; set; }

  [JsonPropertyName("motorvehicle_hc")]
  public string Hc { get; set; }

  [JsonPropertyName("motorvehicle_particles")]
  public string Particles { get; set; }

  [JsonPropertyName("motorvehicle_hcnox")]
  public string HcNox { get; set; }

  [JsonPropertyName("motorvehicle_mpgurban")]
  [JsonConverter(typeof(FlexibleNumberConverter<double>))]
  public double? MpgUrban { get; set; }

  [JsonPropertyName("motorvehicle_mpgextraurban")]
  [JsonConverter(typeof(FlexibleNumberConverter<double>))]
  public double? MpgExtraUrban { get; set; }

  [JsonPropertyName("motorvehicle_mpgcombined")]
  [JsonConverter(typeof(FlexibleNumberConverter<double>))]
  public double? MpgCombined { get; set; }

  [JsonPropertyName("motorvehicle_ps")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? Ps { get; set; }

  [JsonPropertyName("motorvehicle_kw")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? Kw { get; set; }

  [JsonPropertyName("motorvehicle_gearboxtype")]
  public string GearboxType { get; set; }

  [JsonPropertyName("motorvehicle_gears")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? Gears { get; set; }

  [JsonPropertyName("motorvehicle_fueldelivery")]
  public string FuelDelivery { get; set; }

  [JsonPropertyName("motorvehicle_axles")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? Axles { get; set; }

  [JsonPropertyName("motorvehicle_fueltankcapacityl")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? FuelTankCapacityL { get; set; }

  [JsonPropertyName("motorvehicle_bodystyle")]
  public string BodyStyle { get; set; }

  [JsonPropertyName("motorvehicle_doorcount")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? DoorCount { get; set; }

  [JsonPropertyName("motorvehicle_seatingcapacity")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? SeatingCapacity { get; set; }

  [JsonPropertyName("motorvehicle_vedband")]
  public string VedBand { get; set; }

  [JsonPropertyName("motorvehicle_ved6month")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? Ved6Month { get; set; }

  [JsonPropertyName("motorvehicle_ved12month")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? Ved12Month { get; set; }

  [JsonPropertyName("motorvehicle_enginesize")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? EngineSize { get; set; }

  [JsonPropertyName("motorvehicle_exactcc")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? ExactCc { get; set; }

  [JsonPropertyName("motorvehicle_dvlatypeapproval")]
  public string DvlaTypeApproval { get; set; }

  [JsonPropertyName("motorvehicle_length")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? Length { get; set; }

  [JsonPropertyName("motorvehicle_width")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? Width { get; set; }

  [JsonPropertyName("motorvehicle_height")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? Height { get; set; }

  [JsonPropertyName("motorvehicle_grossvehicleweight")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? GrossVehicleWeight { get; set; }

  [JsonPropertyName("motorvehicle_luggagecapacityseatsup")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? LuggageCapacitySeatsUp { get; set; }

  [JsonPropertyName("motorvehicle_luggagecapacityseatsdown")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? LuggageCapacitySeatsDown { get; set; }

  [JsonPropertyName("motorvehicle_ncapoverallrating")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? NcapOverallRating { get; set; }

  [JsonPropertyName("motorvehicle_ncapadultoccupantpercentage")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? NcapAdultOccupantPercentage { get; set; }

  [JsonPropertyName("motorvehicle_ncapchildoccupantpercentage")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? NcapChildOccupantPercentage { get; set; }

  [JsonPropertyName("motorvehicle_ncappedestrianpercentage")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? NcapPedestrianPercentage { get; set; }

  [JsonPropertyName("motorvehicle_ncapsafetyassistercentage")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? NcapSafetyAssistPercentage { get; set; }

  [JsonPropertyName("motorvehicle_title")]
  public string Title { get; set; }

  [JsonPropertyName("motorvehicle_flags")]
  public VehicleFlags Flags { get; set; }

  [JsonPropertyName("motorvehicle_cataloguenotes")]
  public string CatalogueNotes { get; set; }

  [JsonPropertyName("auction_id")]
  public string AuctionId { get; set; }

  [JsonPropertyName("auctionlocation_id")]
  public string AuctionLocationId { get; set; }

  [JsonPropertyName("auction_datetime")]
  public string AuctionDateTime { get; set; }

  [JsonPropertyName("auctiontype_title")]
  public string AuctionTypeTitle { get; set; }

  [JsonPropertyName("auctionlocation_title")]
  public string AuctionLocationTitle { get; set; }

  [JsonPropertyName("auctionlot_id")]
  public string AuctionLotId { get; set; }

  [JsonPropertyName("auctionlot_lotnumber")]
  public string AuctionLotNumber { get; set; }

  [JsonPropertyName("motorvehicle_id")]
  public string MotorVehicleId { get; set; }

  [JsonPropertyName("logisticslocation_name")]
  public string LogisticsLocationName { get; set; }

  [JsonPropertyName("motorvehicleclassification_name")]
  public string MotorVehicleClassificationName { get; set; }

  [JsonPropertyName("vendor_name")]
  public string VendorName { get; set; }
}

public class VehicleFlags
{
  [JsonPropertyName("scrapped")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? Scrapped { get; set; }

  [JsonPropertyName("imported")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? Imported { get; set; }

  [JsonPropertyName("exported")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? Exported { get; set; }

  [JsonPropertyName("finance")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? Finance { get; set; }

  [JsonPropertyName("stolen")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? Stolen { get; set; }

  [JsonPropertyName("vcar_theft")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? VcarTheft { get; set; }

  [JsonPropertyName("vcar_damaged")]
  [JsonConverter(typeof(FlexibleNumberConverter<int>))]
  public int? VcarDamaged { get; set; }
}

// Custom JsonConverter to handle fields that might be either strings or numbers
// and convert them to strings

public class FlexibleStringListConverter : JsonConverter<List<string>>
{
  public override List<string> Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
  {
    switch (reader.TokenType)
    {
      case JsonTokenType.StartArray:
        var list = new List<string>();
        while (reader.Read())
        {
          if (reader.TokenType == JsonTokenType.EndArray)
            break;
          if (reader.TokenType == JsonTokenType.String)
            list.Add(reader.GetString());
        }
        return list;

      case JsonTokenType.String:
        var stringValue = reader.GetString();
        return string.IsNullOrEmpty(stringValue) ? new List<string>() : new List<string> { stringValue };

      case JsonTokenType.Null:
        return new List<string>();

      default:
        return new List<string>();
    }
  }

  public override void Write(Utf8JsonWriter writer, List<string> value, JsonSerializerOptions options)
  {
    if (value == null || value.Count == 0)
    {
      writer.WriteStartArray();
      writer.WriteEndArray();
      return;
    }

    writer.WriteStartArray();
    foreach (var item in value)
    {
      writer.WriteStringValue(item);
    }
    writer.WriteEndArray();
  }
}

public class StringOrNumberConverter : JsonConverter<string>
{
  public override string Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
  {
    if (reader.TokenType == JsonTokenType.Number)
    {
      // Handle number values
      if (reader.TryGetInt32(out int intValue))
        return intValue.ToString();
      if (reader.TryGetInt64(out long longValue))
        return longValue.ToString();
      if (reader.TryGetDouble(out double doubleValue))
        return doubleValue.ToString();
    }

    // Handle string values
    if (reader.TokenType == JsonTokenType.String)
    {
      return reader.GetString();
    }

    // Handle null values
    if (reader.TokenType == JsonTokenType.Null)
    {
      return null;
    }

    throw new JsonException($"Unexpected token type: {reader.TokenType}");
  }

  public override void Write(Utf8JsonWriter writer, string value, JsonSerializerOptions options)
  {
    if (value == null)
    {
      writer.WriteNullValue();
      return;
    }

    writer.WriteStringValue(value);
  }
}

// Generic converter for flexible handling of numeric fields
public class FlexibleNumberConverter<T> : JsonConverter<T?> where T : struct
{
  public override T? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
  {
    // Handle null values
    if (reader.TokenType == JsonTokenType.Null)
      return null;

    // Handle number values directly
    if (reader.TokenType == JsonTokenType.Number)
    {
      if (typeof(T) == typeof(int) && reader.TryGetInt32(out int intValue))
        return (T)(object)intValue;
      if (typeof(T) == typeof(long) && reader.TryGetInt64(out long longValue))
        return (T)(object)longValue;
      if (typeof(T) == typeof(decimal) && reader.TryGetDecimal(out decimal decimalValue))
        return (T)(object)decimalValue;
      if (typeof(T) == typeof(double) && reader.TryGetDouble(out double doubleValue))
        return (T)(object)doubleValue;
    }

    // Handle string values
    if (reader.TokenType == JsonTokenType.String)
    {
      string stringValue = reader.GetString();
      if (string.IsNullOrEmpty(stringValue))
        return null;

      if (typeof(T) == typeof(int) && int.TryParse(stringValue, out int intResult))
        return (T)(object)intResult;
      if (typeof(T) == typeof(long) && long.TryParse(stringValue, out long longResult))
        return (T)(object)longResult;
      if (typeof(T) == typeof(decimal) && decimal.TryParse(stringValue, out decimal decimalResult))
        return (T)(object)decimalResult;
      if (typeof(T) == typeof(double) && double.TryParse(stringValue, out double doubleResult))
        return (T)(object)doubleResult;
    }

    return null;
  }

  public override void Write(Utf8JsonWriter writer, T? value, JsonSerializerOptions options)
  {
    if (!value.HasValue)
    {
      writer.WriteNullValue();
      return;
    }

    if (typeof(T) == typeof(int))
      writer.WriteNumberValue((int)(object)value.Value);
    else if (typeof(T) == typeof(long))
      writer.WriteNumberValue((long)(object)value.Value);
    else if (typeof(T) == typeof(decimal))
      writer.WriteNumberValue((decimal)(object)value.Value);
    else if (typeof(T) == typeof(double))
      writer.WriteNumberValue((double)(object)value.Value);
    else
      writer.WriteStringValue(value.Value.ToString());
  }
}

public class Auction
{
  [JsonPropertyName("id")]
  public string Id { get; set; }

  [JsonPropertyName("title")]
  [JsonConverter(typeof(FlexibleStringConverter))]
  public string Title { get; set; }

  [JsonPropertyName("datetime")]
  public string DateTime { get; set; }

  [JsonPropertyName("activitystatus")]
  public string ActivityStatus { get; set; }

  [JsonPropertyName("date")]
  [JsonConverter(typeof(FlexibleStringConverter))]
  public string Date { get; set; }

  [JsonPropertyName("time")]
  [JsonConverter(typeof(FlexibleStringConverter))]
  public string Time { get; set; }

  [JsonPropertyName("allowstandon")]
  [JsonConverter(typeof(FlexibleStringConverter))]
  public string AllowStandOn { get; set; }

  [JsonPropertyName("currentlot")]
  public string CurrentLot { get; set; }

  [JsonPropertyName("information")]
  [JsonConverter(typeof(FlexibleStringConverter))]
  public string Information { get; set; }

  [JsonPropertyName("auctionlocation_id")]
  public string AuctionLocationId { get; set; }

  [JsonPropertyName("auctionlocation_title")]
  public string AuctionLocationTitle { get; set; }

  [JsonPropertyName("auctiontype_title")]
  public string AuctionTypeTitle { get; set; }

  // Properties that were in previous data 
  // keeping them for compatibility but they won't be populated from this current data (?)
  [JsonPropertyName("auction_id")]
  public string AuctionId { get; set; }

  [JsonPropertyName("start_date")]
  [JsonConverter(typeof(FlexibleDateTimeConverter))]
  public DateTime? StartDate { get; set; }

  [JsonPropertyName("end_date")]
  [JsonConverter(typeof(FlexibleDateTimeConverter))]
  public DateTime? EndDate { get; set; }

  [JsonPropertyName("status")]
  public string Status { get; set; }

  [JsonPropertyName("enddatetime")]
  public string EndDateTime { get; set; }
}

public class ImportVehicleRequest
{
  public Guid InventUserId { get; set; }
  public List<Guid> VehicleIds { get; set; }
  public List<Guid> InventLotIds { get; set; }
}

public class RestoreVehicleRequest
{
  public Guid InventUserId { get; set; }
  public Guid InventLotId { get; set; }
}


public class FlexibleStringConverter : JsonConverter<string>
{
  public override string Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
  {
    switch (reader.TokenType)
    {
      case JsonTokenType.String:
        return reader.GetString();
      case JsonTokenType.Null:
        return null;
      case JsonTokenType.False:
      case JsonTokenType.True:
        return null; // or return reader.GetBoolean().ToString() if you want the boolean as string
      case JsonTokenType.Number:
        return reader.GetDecimal().ToString();
      case JsonTokenType.StartArray:
        // Handle array by reading all elements and joining them
        var arrayValues = new List<string>();
        while (reader.Read() && reader.TokenType != JsonTokenType.EndArray)
        {
          if (reader.TokenType == JsonTokenType.String)
          {
            arrayValues.Add(reader.GetString());
          }
          else if (reader.TokenType == JsonTokenType.Number)
          {
            arrayValues.Add(reader.GetDecimal().ToString());
          }
          // Add other types as needed
        }
        return arrayValues.Count > 0 ? string.Join(",", arrayValues) : string.Empty;
      default:
        // Skip any unexpected tokens to avoid reader position issues
        reader.Skip();
        return null;
    }
  }

  public override void Write(Utf8JsonWriter writer, string value, JsonSerializerOptions options)
  {
    writer.WriteStringValue(value);
  }
}

public class FlexibleDateTimeConverter : JsonConverter<DateTime?>
{
  public override DateTime? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
  {
    switch (reader.TokenType)
    {
      case JsonTokenType.String:
        var stringValue = reader.GetString();
        if (string.IsNullOrEmpty(stringValue))
          return null;
        if (DateTime.TryParse(stringValue, out DateTime result))
          return result;
        return null;
      case JsonTokenType.Null:
      case JsonTokenType.False:
      case JsonTokenType.True:
        return null;
      default:
        return null;
    }
  }

  public override void Write(Utf8JsonWriter writer, DateTime? value, JsonSerializerOptions options)
  {
    if (value.HasValue)
      writer.WriteStringValue(value.Value);
    else
      writer.WriteNullValue();
  }
}