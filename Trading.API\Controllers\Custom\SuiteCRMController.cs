﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System.Threading;
using Trading.Services.Interfaces.Custom;
using System;

namespace Trading.API.Remarq.Controllers.Custom
{
  [Route("api/suite-crm")]
  [ApiController]
  [Authorize]
  public class SuiteCRMController : ControllerBase
  {
    private readonly ISuiteCRMService _service;
    private readonly BackgroundTaskService _backgroundTaskService;

    public SuiteCRMController(ISuiteCRMService service, BackgroundTaskService backgroundTaskService)
    {
      _service = service;
      _backgroundTaskService = backgroundTaskService;
    }

    [HttpGet]
    [Route("upload")]
    [AllowAnonymous]
    public async Task<IActionResult> UploadCSV(CancellationToken cancellationToken)
    {
      await _service.UploadCRMLeads();
      return Ok("Leads file uploaded");
    }

    [HttpGet]
    [Route("update-lead-names")]
    [AllowAnonymous]
    public IActionResult UpdateLeadNames()
    {
      var taskId = _backgroundTaskService.StartTask(async (cancellationToken) =>
      {
        await _service.UpdateLeadNames();
      });

      return Ok(new { message = "Lead name update process started", taskId });
    }

    [HttpGet]
    [Route("update-lead-names-status/{taskId}")]
    [AllowAnonymous]
    public IActionResult GetUpdateLeadNamesStatus(Guid taskId)
    {
      if (_backgroundTaskService.TryGetTaskStatus(taskId, out var status))
      {
        return Ok(new { status = status.ToString() });
      }
      return NotFound("Task not found");
    }
  }
}
