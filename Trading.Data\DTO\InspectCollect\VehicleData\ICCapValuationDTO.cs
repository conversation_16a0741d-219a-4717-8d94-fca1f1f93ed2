﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.InspectCollect.VehicleData;

public class ICCapValuationDTO
{
  public Guid? ICVehicleId { get; set; }

  public string VRM { get; set; }

  public string VIN { get; set; }
  public int Odometer { get; set; }

  // valuation details
  public int? PriceClean { get; set; }
  public int? PriceAvg { get; set; }
  public int? PriceBelow { get; set; }
  public int? PriceRetail { get; set; }
  public int? CostWhenNew { get; set; }
}
