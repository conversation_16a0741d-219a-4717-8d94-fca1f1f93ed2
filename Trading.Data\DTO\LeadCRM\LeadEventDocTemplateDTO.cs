﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.DTO.Documents;
using Trading.API.Data.Models;
using Trading.API.Data.Models.LeadCRM;

namespace Trading.API.Data.DTO.LeadCRM
{
  public class LeadEventDocTemplateDTO: BaseModelEntityIntDTO
  {
    public DocumentTemplate DocumentTemplate { get; set; }
    public uint? DocumentTemplateId { get; set; }
    public LeadStatusDTO LeadStatus { get; set; }
    public uint? LeadStatusId { get; set; }
    public bool? Inbound { get; set; } 
  }

  public class LeadEventDocTemplateSearchDTO: BaseSearchDTO 
  {
    public LeadEventDocTemplateFilters Filters { get; set; } = new LeadEventDocTemplateFilters();
  }

  public class LeadEventDocTemplateFilters : BaseFilterInt
  {
  }
}
