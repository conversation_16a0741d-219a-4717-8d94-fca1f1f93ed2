﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums.Valuation;

namespace Trading.API.Data.DTO.Valuation
{
  public class ValuationNodeSearchDTO: BaseSearchDTO
  {
    public ValuationNodeSearchFilters Filters { get; set; } = new ValuationNodeSearchFilters() { };
  }

  public class ValuationNodeSearchFilters : BaseFilterGuid
  {
    public Guid? ValuationProfileId { get; set; }
    public uint? Pillar1 { get; set; }
    public uint? Pillar2 { get; set; }
    public uint? Pillar3 { get; set; }
    public uint? Pillar4 { get; set; }
    public uint? Pillar5 { get; set; }
    public ValuationLookupTypeEnum? LookupType { get; set; }
    public string LookupId { get; set; }
  }
}
