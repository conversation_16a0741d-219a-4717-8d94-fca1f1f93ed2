﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO
{
  public class RostrumMessageSearchDTO : BaseSearchDTO
  {
    public RostrumMessageFilters Filters { get; set; } = new RostrumMessageFilters() { };
  }
  public class RostrumMessageFilters : BaseFilterInt
  {
    public Guid? SaleId { get; set; }
    public Guid? AdvertId { get; set; }
    public Guid? ContactId { get; set; }
  }
}
