﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO
{
  public class ScanQueueSearchDTO : BaseSearchDTO
  {
    public ScanQueueFilters Filters { get; set; } = new ScanQueueFilters() { };
  }

  public class ScanQueueFilters : BaseFilterInt
  {
    public int? ScanBatchId { get; set; }
    public int? ScanCustomerId { get; set; }
    public int? ScanStageId { get; set; }
    public int? StatusId { get; set; }
    public int? ScanQueueTypeId { get; set; }
    public int? ScanVehicleId { get; set; }
    public int? ScanQueuePriorityId { get; set; }
  }
}