﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO.Vehicle;
using Trading.Services.Interfaces;
using Trading.Services.Interfaces.LambdaFunctions;

namespace Trading.API.Remarq.Controllers.LambdaFunctions
{
  [Route("api/lambda-functions")]
  [ApiController]
  [Authorize]
  public class LambdaFunctionsController : ControllerBase
  {
    private readonly ILambdaFunctionsService _lambdaFunctionsService;
    private readonly IVehicleMediaService _vehicleMediaService;

    public LambdaFunctionsController(ILambdaFunctionsService lambdaFunctionsService, IVehicleMediaService vehicleMediaService)
    {
      _lambdaFunctionsService = lambdaFunctionsService;
      _vehicleMediaService = vehicleMediaService;
    }

    [HttpGet("enhance-image")]
    public async Task<IActionResult> GetEnhancedImage([FromQuery] string url)
    {
      var res = await _lambdaFunctionsService.GetEnhancedImage(url);
      return Ok(res);
    }

    [HttpPost("swap-to-enhanced")]
    public async Task<IActionResult> SwapToEnhancedImage([FromBody] EnhancedImageDataDTO enhancedImageData, CancellationToken cancellationToken)
    {
      var ok = await _vehicleMediaService.SwapOriginalForEnhancedImage(enhancedImageData);
      if (ok)
      {
        return Ok("Image swapped");
      }

      return Ok("Image swap failed");
    }
  }
}
