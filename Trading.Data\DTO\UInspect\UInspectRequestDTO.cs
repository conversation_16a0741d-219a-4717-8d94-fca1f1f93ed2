﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO.UInspections
{
  public class UInspectRequestDTO
  {
    public Guid? LeadVehicleId { get; set; }
    public uint? UInspectFormatId { get; set; }
    public string CustomerName { get; set; }
    public string Email { get; set; }
    public string MobileNumber { get; set; }
    public string VRM { get; set; }
    public string VehicleDesc { get; set; }
    public uint? StatusId { get; set; }

    public string ExternalRef { get; set; }

    // this field will determine which UInspect format to use (if UInspectFormatId is null)
    public int PlatformId { get; set; }
  }
}
