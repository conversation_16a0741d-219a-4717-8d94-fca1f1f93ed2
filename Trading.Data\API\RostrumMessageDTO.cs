﻿using System;
using System.Collections.Generic;
using System.Linq;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;

namespace Trading.API.Data.DTO
{
  public class RostrumMessageDTO : BaseModelEntityDTO
  {
    public Guid? SaleId { get; set; }
    
    public SaleDTO Sale { get; set; }
    
    public Guid? ReAdvertId { get; set; }
    
    public AdvertDTO ReAdvert { get; set; }
    public Guid? ContactId { get; set; }
    
    public ContactDTO Contact { get; set; }
    
    public string ContactName { get; set; }
    public string CustomerName { get; set; }
    public string ContactNumber { get; set; }
    
    public string MessageText { get; set; }
    
    public DateTime TimeSent { get; set; }
    
    public bool? Replied { get; set; }
    
    public DateTime TimeReplied { get; set; }

    public bool toRostrum { get; set; } = true;
  }
}