﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>

    <IsPackable>false</IsPackable>

    <Configurations>RemarqDevelopment;RemarqProd;RemarqLocal-DevDB;RemarqLocal-ProdDB;ICLocal-Dev;ICLocal-Prod;IC-Dev;IC-Prod</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="HttpContextMoq" Version="1.6.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.5" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.10.0" />
    <PackageReference Include="Moq" Version="4.20.70" />
    <PackageReference Include="xunit" Version="2.8.0" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.8.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="6.0.2">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Trading.API.Tests.Common\Trading.API.Tests.Common.csproj" />
    <ProjectReference Include="..\Trading.Data\Trading.API.Data.csproj" />
    <ProjectReference Include="..\Trading.Services\Trading.Services.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="DataFiles\BigAuctionExport.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
