using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.InspectCollect.Classes;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/asset")]
  [ApiController]
  [AllowAnonymous]
  public class ICAssetController : ControllerBase
  {
    private readonly ICAssetInterface _icAssetService;

    public ICAssetController(ICAssetInterface serviceInterface)
    {
      _icAssetService = serviceInterface;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icAssetService.Get(id, null, cancellationToken);
      return Ok(res);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icAssetService.Delete(id);
      return Ok(res);
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Post([FromBody] ICAssetCreateDTO dto, CancellationToken cancellationToken)
    {
      var res = await _icAssetService.Create(dto);
      return Ok(res);
    }

    [HttpGet]
    [Route("{icAssetId}/approve")]
    public async Task<ActionResult> Approve(Guid icAssetId)
    {
      var res = await _icAssetService.Approve(icAssetId);
      return Ok(res);
    }

    [HttpPost]
    [Route("{icAssetId}/upload/{temporary}")]
    public async Task<ActionResult> Upload(Guid icAssetId, IFormFile file, bool temporary, CancellationToken cancellationToken)
    {
      var res = await _icAssetService.Upload(icAssetId, file, temporary);

      return Ok(res);
    }

    [HttpGet]
    [Route("/api/inspect-collect/assets")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICAssetSearchDTO>(query);
      var res = await _icAssetService.Search(dto, cancellationToken);
      return Ok(res);
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<ICAsset> dto)
    {
      var response = await _icAssetService.Patch(id, dto);
      return Ok(response);
    }
  }
}