using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/container-widget-input-style")]
  [ApiController]
  [AllowAnonymous]
  public class ICContainerWidgetInputStyleController : ControllerBase
  {
    private readonly ICContainerWidgetInputStyleInterface _icContainerWidgetInputStyleService;

    public ICContainerWidgetInputStyleController(ICContainerWidgetInputStyleInterface serviceInterface)
    {
      _icContainerWidgetInputStyleService = serviceInterface;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(Guid id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      ICContainerWidgetInputStyleSearchDTO dto = new ICContainerWidgetInputStyleSearchDTO();

      if (!String.IsNullOrEmpty(query))
      {
        dto = JsonConvert.DeserializeObject<ICContainerWidgetInputStyleSearchDTO>(query);
      }

      var res = await _icContainerWidgetInputStyleService.Get(id, dto, cancellationToken);
      return Ok(res);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icContainerWidgetInputStyleService.Delete(id);
      return Ok(res);
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Post([FromBody]ICContainerWidgetInputStyleCreateDTO dto, CancellationToken cancellationToken)
    {
      var res = await _icContainerWidgetInputStyleService.Create(dto);
      return Ok(res);
    }

    [HttpGet]
    [Route("/api/inspect-collect/container-widget-input-styles")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICContainerWidgetInputStyleSearchDTO>(query);
      var res = await _icContainerWidgetInputStyleService.Search(dto, cancellationToken);
      return Ok(res);
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<ICContainerWidgetInputStyle> dto)
    {
      var response = await _icContainerWidgetInputStyleService.Patch(id, dto);
      return Ok(response);
    }
  }
}