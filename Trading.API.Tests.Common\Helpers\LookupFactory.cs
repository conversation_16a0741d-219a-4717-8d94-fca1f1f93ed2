﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;

namespace Trading.API.Tests.Common.Helpers
{
    public class LookupFactory
  {
    private CommonServices _common;

    public LookupFactory(CommonServices common)
    {
      _common = common;
    }

    public async Task<T> GetLookup<T>(LookupDTO dto) where T : BaseModelEntityInt
    {
      var id = await _common.LookupService.GetLookupId(dto, CancellationToken.None);
      var entity = await _common.Context.Set<T>().FirstOrDefaultAsync(x => x.Id == id);

      return entity;
    }
  }
}
