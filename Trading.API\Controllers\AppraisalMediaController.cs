using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.Services.Extensions;
using Trading.Services.ExternalDTO;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/[controller]")]
  [ApiController]
  [Authorize]
  public class AppraisalMediaController : ControllerBase
  {
    private readonly IAppraisalService _appraisalService;

    public AppraisalMediaController(IAppraisalService appraisalService)
    {
      _appraisalService = appraisalService;
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> UploadAppraisalMedia(IFormCollection data, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<AppraisalMediaUploadDTO>(data["appraisalMediaUploadDTO"]);

      dto.CustomerId = (Guid)User.CustomerId();

      try
      {
        var medias = await _appraisalService.UploadAppraisalMedia(dto, data.Files, cancellationToken);
        return Ok(medias);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpDelete]
    [Route("{appraisalMediaId}")]
    public async Task<IActionResult> DeleteAppraisalMedia(Guid appraisalMediaId, CancellationToken cancellationToken)
    {

      try
      {
        // It we aren't admin, we have to validate its our own media
        if (User.IsAdmin())
        {
          AppraisalMediaSearchDTO searchDTO = new AppraisalMediaSearchDTO();
          searchDTO.Component = "delete-appraisal-media";

          var appraisalMedia = await _appraisalService.GetAppraisalMedia(appraisalMediaId, searchDTO, cancellationToken);

          if (appraisalMedia.IsValid == false)
          {
            return NotFound();
          }

          if (appraisalMedia.IsValid == false || appraisalMedia.DTO.AppraisalItem.Appraisal.Vehicle.CustomerId != User.CustomerId()) { 
            return Forbid();
          }
        }

        var medias = await _appraisalService.DeleteAppraisalMedia(appraisalMediaId, cancellationToken);
        return Ok(medias);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

  }
}
