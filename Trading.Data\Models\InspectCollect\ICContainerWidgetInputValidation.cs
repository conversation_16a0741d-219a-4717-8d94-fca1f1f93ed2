﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.Models.InspectCollect
{
  [Table("ICContainerWidgetInputValidation")]

  public class ICContainerWidgetInputValidation : BaseModelEntity
  {
    [ForeignKey("ICContainerWidgetInput")]
    public Guid ICContainerWidgetInputId { get; set; }
    public ICContainerWidgetInput ICContainerWidgetInput { get; set; }
    public uint? Position { get; set; }

    [ForeignKey("ICValidation")]
    public Guid? ICValidationId { get; set; }
    public ICValidation ICValidation { get; set; }
  }
}
