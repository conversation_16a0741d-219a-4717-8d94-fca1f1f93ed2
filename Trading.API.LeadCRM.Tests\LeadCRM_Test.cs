using Trading.API.Tests.Common;
using Trading.Services;
using Trading.Services.LeadCRM.Interfaces;

namespace Trading.API.LeadCRM.Tests
{
  public class LeadCRM_Test : TestBase
  {
    private void GetService()
    {
      //IContactActionService contactActionService = new ContactActionService(_context, base._mapper, _common.UserService, null);
      //IWatchlistService watchlistService = new WatchlistService(_context, _mapper, contactActionService);

      //var stripeDTO = new Mock<IOptionsSnapshot<StripeDTO>>();
      //stripeDTO.SetupAllProperties();
      //stripeDTO.SetupGet(p => p.Value).Returns(new StripeDTO { SecretKey = "Fake Key" });

      //var logger = new Mock<ILogger<StripeService>>().Object;
      //IStripeService stripeService = new StripeService(logger, stripeDTO.Object, _context, _common.XeroService, contactActionService);

      //ICustomerOrderService customerOrderService = new CustomerOrderService(_context, _common.XeroService, _mapper, _common.FileStorageService, null, stripeService);
      //IDealService dealService = new DealService(_context, _mapper, null, customerOrderService, contactActionService);

      //INegotiationService negotiationService = new NegotiationService(_context, _mapper, _common.UserService);
      //IEmailService emailService = new Mock<IEmailService>().Object;

      //IBidService bidService = new BidService(dealService, null, _context, _mapper, _common.MessageService, _common.InMailService, negotiationService, emailService);

      //IRoleService roleService = new RoleService(_context, _mapper);
      //IContactRoleService contactRoleService = new ContactRoleService(roleService, _context, _mapper);

      //ICustomerService customerService = new CustomerService(_context, _mapper, emailService, null, contactActionService, _common.UserService);
      //IContactService contactService = new ContactService(null, _context, contactRoleService, roleService, _common.Configuration, customerService, _mapper, contactActionService);

      //IVRMLookupService vrmLookupService = new UKVehicleDataService(_context, new Mock<IOptionsSnapshot<UKVehicleDTO>>().Object, _mapper, _common.LookupService);

      //ILookupService lookupService = new LookupService(_context, _common.DVLAService, _mapper);

      //var service = new LeadCRMService(_context, _mapper, )

      //return new AdvertService(_context, bidService, _mapper, contactService, customerService, lookupService, _common.UserService, _common.MessageService, null, contactActionService);

      //return new LeadCRMService(_context, _mapper, _common.UserService, );
    }

    [Fact]
    public void Test1()
    {

    }
  }
}