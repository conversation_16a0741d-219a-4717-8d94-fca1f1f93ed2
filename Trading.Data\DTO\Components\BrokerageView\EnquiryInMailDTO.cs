﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO.Components.BrokerageView
{
  public class EnquiryInMailDTO : BaseModelEntityDTO
  {
    public Guid? AdvertId { get; set; }
    public Guid ToContactId { get; set; }
    public EnquiryMailContactDTO ToContact { get; set; }
    public Guid FromContactId { get; set; }
    public EnquiryMailContactDTO FromContact { get; set; }
    public string Subject { get; set; }
    public string Body { get; set; }
  }

  public class EnquiryMailContactDTO
  {
    public string ContactName { get; set; }
    public string Email { get; set; }
    public string Phone1 { get; set; }
    public string Phone2 { get; set; }
  }
}
