bin/
obj/
Vutu.API/Vutu.API.csproj.user

.vs/Vutu.API/v16/.suo
<<<<<<< Updated upstream
appsettings.Development.json
=======

Vutu.API/appsettings.development.json

.vs/
>>>>>>> Stashed changes
/Trading.API/.vshistory
/Trading.Services/.vshistory
/Trading.Data/API/.vshistory/DerivSearchDTO.cs
/Trading.Data/DTO/.vshistory/AdvertDTO.cs
/Trading.Data/Enums/.vshistory/MessageTypeEnum.cs
/Trading.Services/HubConfig/.vshistory/MessageHub.cs
/Trading.API/Controllers/.vshistory/VehicleLookupController.cs
/Trading.API/Attributes/.vshistory/SwaggerCustomerAttribute.cs
/Trading.API.Common/.vshistory/SwaggerCustomerAttribute.cs
/Trading.API.Valuations/Controllers/.vshistory/ValuationQuoteController.cs
/Trading.Data/DTO/Valuation/.vshistory/ValuationRequestDTO.cs
/Trading.Data/.vshistory/Trading.API.Data.csproj
/Trading.API/Controllers/.vshistory/CustomerMediaController.cs
/Trading.API/Controllers/Documents/.vshistory/DocumentController.cs
/Trading.API/ServiceConfiguration/.vshistory/ServiceConfigurationExtensions.cs
/Trading.Data/.vshistory/TradingContext.cs
/Trading.Data/DTO/.vshistory/CustomerMediaDTO.cs
/Trading.Data/Enums/.vshistory/CustomerMediaCategoryEnum.cs
/Trading.Data/Models/.vshistory/CustomerMedia.cs
/Trading.Services/DTOMappingProfiles/Core/.vshistory/MappingProfile.cs
/Trading.Services/ExternalDTO/.vshistory/CustomerMediaUploadDTO.cs
/Trading.Services/Interfaces/.vshistory
/Trading.Services/Helpers/.vshistory/URLHelper.cs
/Trading.Data/Migrations/.vshistory/20231014180245_MechanicalFaults.cs
/Trading.Data/Models/.vshistory
/Trading.API/Controllers/.vshistory/VehicleMediumController.cs
/.vshistory/Trading.API.sln
/Trading.API.LeadCRM/Controllers/.vshistory
/Trading.API.LeadCRM/.vshistory/Trading.API.LeadCRM.csproj
/Trading.Data/DTO/LeadCRM/.vshistory/LeadCallbackRequestDTO.cs
/Trading.Data/DTO/Search/LeadCRM/.vshistory/LeadVehicleSearchDTO.cs
/Trading.Data/DTO/Valuation/.vshistory
/Trading.Services.LeadCRM/.vshistory/LeadCRMService.cs
/Trading.Services.LeadCRM/Interfaces/.vshistory/ILeadCRMService.cs
/Trading.Services.Valuations/.vshistory/ValuationQuoteService.cs
/Trading.Data/DTO/MechanicalFaults/.vshistory
/Trading.Data/Models/MechanicalFaults/.vshistory
/Trading.Data/Migrations/.vshistory
/Trading.Data/Models/Vehicle/.vshistory/Vehicle.cs
/Trading.Services/MechanicalFaults/.vshistory/VehicleFaultService.cs
/Trading.Data/DTO/Vehicle/.vshistory/VehicleDTO.cs
/Trading.API/Controllers/MechanicalFaults/.vshistory/VehicleFaultController.cs
/Trading.Data/DTO/Search/MechanicalFaults/.vshistory/VehicleFaultCheckSearchDTO.cs
/Trading.Services/Interfaces/MechanicalFaults/.vshistory/IVehicleFaultService.cs
/Trading.Data/DTO/Vehicle/.vshistory/VehicleDTO_Public.cs
/Trading.Data/DTO/Search/MechanicalFaults/.vshistory
/Trading.Data/DTO/Search/.vshistory/BaseSearchDTO.cs
/Trading.API/Controllers/.vshistory/ContactController.cs
/Trading.API/Controllers/.vshistory/CustomerController.cs
/Trading.Data/DTO/.vshistory/CustomerInternalInfoDTO.cs
/Trading.API/Controllers/.vshistory/CustomerInternalInfoController.cs
/Trading.API.Tests/.vshistory/ContactService_Test.cs
/Trading.API.Valuations.Tests/.vshistory/ValuationService_Test.cs
/Trading.API.Tests.Common/Helpers/.vshistory/CommonServices.cs
/Trading.Data/DTO/.vshistory/CustomerDTO.cs
/Trading.Data/DTO/WhosWhoNS/.vshistory/WhosWhoDTO.cs
/Trading.Data/Models/WhosWhoNS/.vshistory/WhosWho.cs
/Trading.Services/WhosWhoNS/.vshistory/WhosWhoService.cs
/Trading.API/Controllers/WhosWhoNS/.vshistory/WhosWhoController.cs
/Trading.Services/Interfaces/WhosWhoNS/.vshistory/IWhosWhoService.cs
/Trading.Data/DTO/.vshistory/CustomerAssignedToDTO.cs
/Trading.Data/DTO/Admin/.vshistory
/Trading.API.Common/.vshistory/IgnoreNullValuesFilterAttribute.cs
/Trading.Data/Models/Sphinx/.vshistory
/Trading.Data/MigrationData/.vshistory/Sphinx_Definitions.sql
/Trading.API/Controllers/.vshistory/ContactActionController.cs
/Trading.Data/DTO/.vshistory/ContactActionDTO.cs
/Trading.Data/DTO/Search/.vshistory/ContactActionSearchDTO.cs
/Trading.Data/DTO/.vshistory/CustomerAdminDTO.cs
/Trading.Data/API/.vshistory/LoginFailedDTO.cs
/Trading.Data/DTO/Comms/.vshistory/CommsTemplateDTO.cs
/Trading.Data/Models/Comms/.vshistory/CommsTemplate.cs
/Trading.Services/Classes/.vshistory
/Trading.Data/DTO/Search/.vshistory/SavedSearchDTO.cs
/Trading.API.Tests/.vshistory/AdvertService_Test.cs
/Trading.Data/DTO/.vshistory
/Trading.Data/DTO/Sale/.vshistory/SaleDTO.cs
/Trading.API.Tests/.vshistory/BidService_Test.cs
/Trading.API.Tests.Common/Helpers/.vshistory/AdvertHelper.cs
/Trading.Data/DTO/Totals/.vshistory/CustomerAdviewTotalsDTO.cs
/Trading.Data/Enums/Prospects/.vshistory/BrokerageStateEnum.cs
/Trading.API/Controllers/Prospects/.vshistory/AdvertNoteController.cs
/Trading.Data/DTO/Prospects/.vshistory/AdvertNoteDTO.cs
/Trading.Data/DTO/Search/Prospects/.vshistory/BrokerageSearchDTO.cs
/Trading.Data/Models/Prospects/.vshistory/Brokerage.cs
/Trading.API/Controllers/Prospects/.vshistory/BrokerageController.cs
/Trading.API/Controllers/.vshistory/BidController.cs
/Trading.API/Controllers/Prospects/.vshistory/ProspectController.cs
/Trading.Data/DTO/Prospects/.vshistory
/Trading.Data/DTO/Search/Prospects/.vshistory/ProspectSearchDTO.cs
/Trading.Services/DTOMappingProfiles/Core/.vshistory/BrokerageMappingProfile.cs
/Trading.Data/DTO/Totals/.vshistory/CustomerAdvertisingTotalsDTO.cs
/Trading.Data/DTO/Totals/.vshistory/CustomerLoginTotalsDTO.cs
/Trading.Data/DTO/Search/.vshistory
/Trading.API/Controllers/.vshistory/VOIPController.cs
/Trading.API.Tests.Common/Helpers/.vshistory/CustomerContactHelper.cs
/Trading.Services.LeadCRM/.vshistory/Trading.Services.LeadCRM.csproj
/Trading.API.Valuations.Tests/.vshistory/Trading.API.Valuations.Tests.csproj
/Trading.API.Tests/.vshistory/Trading.API.Tests.csproj
/Trading.API.LeadCRM.Tests/.vshistory/Trading.API.LeadCRM.Tests.csproj
/Trading.API.Tests.Common/.vshistory/Trading.API.Tests.Common.csproj
/Trading.API.SiteScan/.vshistory/Trading.API.SiteScan.csproj
/Trading.API.External/.vshistory/Trading.API.External.csproj
/Trading.API.Common/.vshistory/Trading.API.Common.csproj
/Trading.API.UInspection/.vshistory/Trading.API.UInspection.csproj
/Trading.API.Valuations/.vshistory/Trading.API.Valuations.csproj
/Trading.Services.External/.vshistory/Trading.Services.External.csproj
/Trading.Services.SiteScan/.vshistory/Trading.Services.SiteScan.csproj
/Trading.Services.UInspection/.vshistory/Trading.Services.UInspection.csproj
/Trading.Services.Valuations/.vshistory/Trading.Services.Valuations.csproj
/Trading.Services.Valuations/.vshistory/Trading.Services.Valuations.csproj/2023-06-18_12_09_57_162.csproj
/Trading.API/Controllers/.vshistory/BladeRunnerController.cs
/Trading.Data/DTO/Twitter/.vshistory/DTOs.cs
/Trading.Services/Classes/SocialMedia/.vshistory/InstagramService.cs
/Trading.Data/Enums/.vshistory/CommsTypeEnum.cs
/Trading.Data/Models/Comms/.vshistory/CommsHistory.cs
/Trading.API/Controllers/Extensions/.vshistory/ContextExtensions.cs
/Trading.Services.SiteScan/Interfaces/.vshistory/IScanService.cs
/Trading.API/Controllers/.vshistory/AppraisalMediaController.cs
/Trading.Services/ExternalDTO/Configs/.vshistory/S3DTO.cs
/Trading.API.Valuations/Controllers/.vshistory/ValuationController.cs
/Trading.Services/DTOMappingProfiles/Core/Components/MainDashboard/.vshistory/MainDashboardMappingProfile.cs
/Trading.API/Controllers/.vshistory/AdvertController.cs
/Trading.Data/DTO/Components/AdvertView/.vshistory/AdvertViewDataDTO.cs
/Trading.API/Controllers/.vshistory/AppraisalController.cs
/Trading.API/Controllers/.vshistory/OfferController.cs
/Trading.Data/Enums/.vshistory/BidStatusEnum.cs
/Trading.Data/Enums/.vshistory/StatusEnum.cs
/Trading.API/Controllers/.vshistory
/Trading.Data/DTO/Components/WhosWhoNS/.vshistory/WhosWhoEnquiryInMailDTO.cs
/Trading.Data/Enums/.vshistory
/Trading.Data/DTO/Movements/.vshistory/MovementDTO.cs
/Trading.Services/Interfaces/Movements/.vshistory/IMovementService.cs
/Trading.Services/Movements/.vshistory/MovementService.cs
/Trading.Services/ExternalDTO/Auth/.vshistory
/Trading.API.SiteScan/Controllers/.vshistory/ScanController.cs
/Trading.Services.SiteScan/.vshistory
/Trading.Services.SiteScan/Mapping/.vshistory/SiteScanMappingProfile.cs
/Trading.Data/DTO/Scanner/.vshistory/ScanStyleDTO.cs
/Trading.Data/Models/Scanner/.vshistory/ScanStyle.cs
/Trading.Data/DTO/Components/AdvertSearch/.vshistory/AdvertSearchSummaryAdvertDTO.cs
/Trading.Data/DTO/Scanner/.vshistory/ScanQueueSearchDTO.cs
/Trading.Data/DTO/Scanner/.vshistory/ScanStatsDTO.cs
/Trading.Data/Models/Scanner/.vshistory/ScanQueue.cs
/Trading.Data/API/.vshistory
/Trading.Services/DTOMappingProfiles/Core/.vshistory/AdvertMappingProfile.cs
/Trading.Services/ExternalDTO/Configs/.vshistory
/Trading.Data/DTO/Components/MainDashboard/.vshistory/RecentAdvertDTO.cs
/Trading.Data/DTO/Scanner/.vshistory/CreateScanVehicleDTO.cs
/Trading.Services.SiteScan/Interfaces/.vshistory/IScanningService.cs
/Trading.API.SiteScan/Controllers/.vshistory/SQSController.cs
/Trading.Services.SiteScan/Interfaces/.vshistory
/Trading.Services/DTOMappingProfiles/Core/Components/AdvertSearch/.vshistory/AdvertSearchMappingProfile.cs
/Trading.Data/DTO/Scanner/.vshistory
/Trading.Data/Models/Scanner/.vshistory/ScanSample.cs
/Trading.Data/Models/Scanner/.vshistory/ScanVehicle.cs
/Trading.API.SiteScan/Controllers/.vshistory/ScanVehicleController .cs
/Trading.Data/Enums/SiteScan/.vshistory/ScanContentTypeEnum.cs
/Trading.Data/Models/Scanner/.vshistory/ScanCustomer.cs
/Trading.Data/Models/Scanner/.vshistory/ScanConfig.cs
/Trading.Data/Models/Scanner/.vshistory/ScanStage.cs
/Trading.Data/Models/Scanner/.vshistory/ScanPrice.cs
/Trading.Data/Models/InspectCollect/.vshistory/ICLayout.cs
/Trading.Services.InspectCollect/.vshistory/Trading.Services.InspectCollect.csproj
/Trading.API.InspectCollect/Controllers/.vshistory/ICTestController.cs
/Trading.Data/Models/InspectCollect/.vshistory
/Trading.Services.InspectCollect/Classes/.vshistory/ICTestService.cs
/Trading.Services.InspectCollect/Interfaces/.vshistory/ICITestInterface.cs
/Trading.Data/DTO/InspectCollect/.vshistory
/Trading.API.InspectCollect/Controllers/.vshistory
/Trading.Services.InspectCollect/Classes/.vshistory
/Trading.Services.InspectCollect/Interfaces/.vshistory
/Trading.Data/Enums/InspectCollect/.vshistory/ContainerAlignmentEnum.cs
/Trading.Services.InspectCollect/Mapping/.vshistory/ICMapProfiles.cs
/Trading.Data/Enums/InspectCollect/.vshistory
/Trading.Services/Azure/.vshistory/AzureFileStorageService.cs
/Trading.API.Tests.Common/Fakes/.vshistory/FakeFileStorageService.cs
/Trading.API/Controllers/ExtLead/.vshistory
/Trading.Data/DTO/ExtLeads/.vshistory
/Trading.Data/Models/ExtLeads/.vshistory
/Trading.Services/Classes/ExtLeads/.vshistory
/Trading.Services/Interfaces/ExtLeads/.vshistory
/Trading.Services/Custom/.vshistory/BIGImportService.cs
/Trading.API/log
/Trading.Data/DTO/Components/AdvertView/.vshistory/AdvertViewBidBoxDTO.cs
/Trading.API/Controllers/Custom/.vshistory/SuiteCRMController.cs
/Trading.API/Controllers/ZohoCampaign/.vshistory
/Trading.Services/Classes/BackgroundTasks/.vshistory/BackgroundTaskService.cs
/Trading.Services/Classes/ZohoCampaign/.vshistory/ZohoCampaignService.cs
/Trading.Services/Interfaces/ZohoCampaign/.vshistory/IZohoCampaignService.cs
/Trading.Data/Enums/InspectCollect/.vshistory
/Trading.Services/Azure/.vshistory/AzureFileStorageService.cs
/Trading.API.Tests.Common/Fakes/.vshistory/FakeFileStorageService.cs
/Trading.API/Controllers/ExtLead/.vshistory
/Trading.Data/DTO/ExtLeads/.vshistory
/Trading.Data/Models/ExtLeads/.vshistory
/Trading.Services/Classes/ExtLeads/.vshistory
/Trading.Services/Interfaces/ExtLeads/.vshistory
/Trading.API/log
/Trading.Data/DTO/Components/AdvertView/.vshistory/AdvertViewBidBoxDTO.cs
/Trading.Services/Custom/.vshistory/BIGImportService.cs
/Trading.Services/Classes/SocialMedia/.vshistory/SocialMediaFeedService.cs
/Trading.API/Controllers/ZohoCampaign/.vshistory
/Trading.Services/Classes/ZohoCampaign/.vshistory/ZohoCampaignService.cs
/Trading.Services/Interfaces/ZohoCampaign/.vshistory/IZohoCampaignService.cs
/Trading.Services/Classes/BackgroundTasks/.vshistory/BackgroundTaskService.cs
/Trading.API/Controllers/Custom/.vshistory/SuiteCRMController.cs
/Trading.API/QuartzJobs/.vshistory
/Trading.API.InspectCollect/.vshistory/Trading.API.InspectCollect.csproj
/Trading.Services/Extensions/.vshistory/UserExtensions.cs

# Elastic Beanstalk Files
.elasticbeanstalk/*
!.elasticbeanstalk/*.cfg.yml
!.elasticbeanstalk/*.global.yml
/Trading.Data/Models/InspectCollect/ResponseConditionItems/.vshistory/ICResponseConditionItem.cs
/Trading.Services.InspectCollect/Classes/VehicleLookup/.vshistory/ICVehicleService.cs
/Trading.Data/DTO/InspectCollect/VehicleData/.vshistory/ICVehicleDTO.cs
/Trading.Services.InspectCollect/Classes/VehicleLookup/.vshistory/ICCapHPIVehicleLookupService.cs
/Trading.Data/DTO/InspectCollect/VehicleLookup/.vshistory/ICVehicleEnquiryDTO.cs
/Trading.Services.InspectCollect/Classes/VehicleLookup/.vshistory/ICVehicleDataService.cs
/Trading.Data/DTO/InspectCollect/VehicleLookup/.vshistory/ICVehicleCheckResponseDTO.cs
/Trading.Data/DTO/AutoTrader/.vshistory/AutoTraderFeatureDTO.cs
/Trading.Services/Classes/AutoTrader/.vshistory/AutoTraderService.cs
/Trading.Data/Models/AutoTrader/.vshistory/AutoTraderFeature.cs
/Trading.Services/Interfaces/AutoTrader/.vshistory/IAutoTraderService.cs
/Trading.Data/Models/InspectCollect/VehicleData/.vshistory/ICAutoTraderData.cs
/Trading.Data/DTO/InspectCollect/VehicleData/.vshistory/ICAutoTraderDataDTO.cs
/Trading.Data/DTO/InspectCollect/VehicleData/.vshistory/ICOutcomeDTO.cs
/Trading.Data/Models/InspectCollect/VehicleData/.vshistory
/Trading.Data/DTO/AutoTrader/.vshistory/ATResponseModels.cs
/Trading.Services.InspectCollect/Classes/VehicleLookup/.vshistory/ICVehicleDataServiceResults.cs
/Trading.API/Controllers/AuctionImports/.vshistory/AuctionImportController.cs
/Trading.Services/Classes/InventImports/.vshistory
/Trading.Services/Interfaces/InventImports/.vshistory/IAuctionApiClient.cs
/Trading.Data/DTO/Vehicle/.vshistory/VehicleTyreInfoDTO.cs
/Trading.Data/Models/Vehicle/.vshistory/VehicleTyreInfo.cs
/Trading.Data/DTO/Vehicle/.vshistory/VehicleTyrePatchDTO.cs
/Trading.Data/DTO/Components/AdvertView/.vshistory/AdvertViewVehicleTyreInfoDTO.cs
/Trading.Services/ExternalDTO/.vshistory/CreateVehicleDTO.cs
/Trading.Services.Valuations/.vshistory/ValuationService.cs
