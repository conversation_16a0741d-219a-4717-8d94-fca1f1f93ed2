using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/container-widget-input-validations")]
  [ApiController]
  [AllowAnonymous]
  public class ICContainerWidgetInputValidationController : ControllerBase
  {
    private readonly ICContainerWidgetInputValidationInterface _icContainerWidgetInputValidationService;

    public ICContainerWidgetInputValidationController(ICContainerWidgetInputValidationInterface serviceInterface)
    {
      _icContainerWidgetInputValidationService = serviceInterface;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(Guid id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      ICContainerWidgetInputValidationSearchDTO dto = new ICContainerWidgetInputValidationSearchDTO();

      if (!String.IsNullOrEmpty(query))
      {
        dto = JsonConvert.DeserializeObject<ICContainerWidgetInputValidationSearchDTO>(query);
      }

      var res = await _icContainerWidgetInputValidationService.Get(id, dto, cancellationToken);
      return Ok(res);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icContainerWidgetInputValidationService.Delete(id);
      return Ok(res);
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Post([FromBody] ICContainerWidgetInputValidationCreateDTO dto, CancellationToken cancellationToken)
    {
      var res = await _icContainerWidgetInputValidationService.Create(dto);
      return Ok(res);
    }

    [HttpGet]
    [Route("/api/inspect-collect/container-widget-input-validations")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICContainerWidgetInputValidationSearchDTO>(query);
      var res = await _icContainerWidgetInputValidationService.Search(dto, cancellationToken);
      return Ok(res);
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<ICContainerWidgetInputValidation> dto)
    {
      var response = await _icContainerWidgetInputValidationService.Patch(id, dto);
      return Ok(response);
    }
  }
}