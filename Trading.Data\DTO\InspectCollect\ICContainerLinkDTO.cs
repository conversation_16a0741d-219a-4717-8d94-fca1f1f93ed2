﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums.InspectCollect;
using Trading.API.Data.Models.InspectCollect;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICContainerWidgetLinkDTO
  {
    public Guid? Id { get; set; }
    public Guid? ICContainerWidgetId { get; set; }
    public ICContainerWidgetDTO ICContainerWidget { get; set; }
    public Guid? ICLinkedContainerId { get; set; }
    public ICContainerDTO ICLinkedContainer { get; set; }
    public uint? Position { get; set; }
  }

  public class ICContainerWidgetLinkCreateDTO
  {
    public Guid ICContainerWidgetId { get; set; }
    public Guid? ICLinkedContainerId { get; set; }
    public uint? Position { get; set; }
  }
  public class ICContainerWidgetLinkSearchDTO : BaseSearchDTO
  {
    public ICContainerWidgetLinkFilterDTO Filters { get; set; } = new ICContainerWidgetLinkFilterDTO();
  }

  public class ICContainerWidgetLinkFilterDTO : BaseFilter
  {
    public Guid? ICContainerWidgetId { get; set; }
    public Guid? ICLinkedContainerId { get; set; }
  }
}