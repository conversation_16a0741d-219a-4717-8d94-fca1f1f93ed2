﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.API.Data.Models.DTO;

namespace Trading.API.Data.DTO
{
  public class VehicleMediaFilters : BaseFilterGuid
  {
    public Guid? VehicleId { get; set; }
    public MediaTypeEnum MediaType { get; set; }
  }

  public class VehicleMediaSearchDTO: BaseSearchDTO
  {
    public VehicleMediaFilters Filters { get; set; } = new VehicleMediaFilters() { };
  }

  public class VehicleMediaSearchResultDTO
  {
    public VehicleMediaDTO VehicleMediaDTO { get; set; }
    public IEnumerable<VehicleMediaDTO> VehicleMediaDTOs { get; set; }
    public VehicleMedia VehicleMedia { get; set; }
    public IEnumerable<VehicleMedia> VehicleMedias { get; set; }
  }
}
