﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;

namespace Trading.API.Data.DTO
{
  public class CustomerNoteSearchResultDTO
  {
    public IEnumerable<CustomerNoteDTO> CustomerNoteDTOs { get; set; }
    public IEnumerable<CustomerNote> CustomerNotes { get; set; }
    public CustomerNote CustomerNote { get; set; }
    public CustomerNoteDTO CustomerNoteDTO { get; set; }
    public int? Count { get; set; }
  }
}