﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO.Components.WhosWhoNS;

public class AdvertWithMessagesDTO
{
  public Guid AdvertId { get; set; }
  public Guid VendorContactId { get; set; }
  public WhosWhoEnquiryContactDTO VendorInfo { get; set; }
  public string PrimaryImageURL { get; set; }
  public string VRM { get; set; }
  public string VehicleColour { get; set; }
  public string VehicleMake { get; set; }
  public string VehicleModel { get; set; }
  public string VehiclePlate { get; set; }
  public DateTime? AdvertEndDateTime { get; set; }
  public uint SaleTypeId { get; set; }
  public string SaleType { get; set; }

  public List<EnquirerThreadDTO> EnquirerThreads { get; set; }
}

public class EnquirerThreadDTO
{
  public WhosWhoEnquiryContactDTO Enquirer { get; set; }
  public List<ThreadMessageDTO> Messages { get; set; }
}

public class ThreadMessageDTO
{
  public Guid InMailId { get; set; }
  public Guid? InReplyToMailId { get; set; }
  public DateTime Date { get; set; }
  public uint StatusId { get; set; }
  public bool IsActioned { get; set; }
  public bool IsRead { get; set; }
  public string Message { get; set; }
  public string Subject { get; set; }
  public WhosWhoEnquiryContactDTO Sender { get; set; }
}

public class WhosWhoEnquiryContactDTO
{
  public Guid Id { get; set; }
  public string Name { get; set; }
  public string Email { get; set; }
  public string Phone { get; set; }
  public string CustomerName { get; set; }
  public Guid? AssignedTo { get; set; }
}

public class AdvertProjectionDTO
{
  public Guid? AdvertId { get; set; }
  public Guid? ContactId { get; set; }
  public DateTime? EndDateTime { get; set; }
  public string SaleName { get; set; }
  public uint SaleTypeId { get; set; }
  public string VRM { get; set; }
  public string VehicleColour { get; set; }
  public string VehicleMake { get; set; }
  public string VehicleModel { get; set; }
  public string VehiclePlate { get; set; }
  public string PrimaryImageURL { get; set; }
  public Guid VehicleId { get; set; }
  public Guid CustomerId { get; set; }
  public Guid? PrimaryImageId { get; set; }
}
