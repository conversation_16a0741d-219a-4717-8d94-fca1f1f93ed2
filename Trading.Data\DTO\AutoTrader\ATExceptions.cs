﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.AutoTrader;

// Exception classes
public class AutoTraderApiException : Exception
{
  public int StatusCode { get; }
  public string? CfRayId { get; }
  public List<AutoTraderWarning> Warnings { get; }

  public AutoTraderApiException(int statusCode, string message, string? cfRayId = null, List<AutoTraderWarning>? warnings = null)
      : base(message)
  {
    StatusCode = statusCode;
    CfRayId = cfRayId;
    Warnings = warnings ?? new List<AutoTraderWarning>();
  }
}

public class AutoTraderRateLimitException : AutoTraderApiException
{
  public AutoTraderRateLimitException(string? cfRayId = null)
      : base(429, "Too many requests", cfRayId) { }
}

public class AutoTraderServiceUnavailableException : AutoTraderApiException
{
  public AutoTraderServiceUnavailableException(string? cfRayId = null)
      : base(503, "Service unavailable", cfRayId) { }
}
