using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO.Documents
{
    public class DocumentTemplateDTO : BaseModelEntityIntDTO
    {
        public DocumentServiceEnum? DocumentService { get; set; }
        public string ExternalTemplateId { get; set; }
        public bool? FormCompletionRequired { get; set; }
        public uint? DaysToComplete { get; set; }
    }

    public class DocumentTemplateSearchDTO : BaseSearchDTO
    {
        public DocumentTemplateFilters Filters { get; set; } = new DocumentTemplateFilters();
    }

    public class DocumentTemplateFilters : BaseFilterInt
    {
    }
}