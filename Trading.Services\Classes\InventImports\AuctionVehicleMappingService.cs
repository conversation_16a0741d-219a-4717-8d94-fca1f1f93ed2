﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Imports.Invent;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.API.Data.Models.InventData;
using Trading.Services.Interfaces;
using Trading.Services.Interfaces.InventImports;

namespace Trading.Services.Classes.InventImports;


/// <summary>
/// Service to map auction data to vehicle entities
/// </summary>
public class AuctionVehicleMappingService : IAuctionVehicleMappingService
{
  private readonly ILookupService _lookupService;

  public AuctionVehicleMappingService(ILookupService lookupService)
  {
    _lookupService = lookupService ?? throw new ArgumentNullException(nameof(lookupService));
  }

  /// <summary>
  /// Maps an auction lot from the Auction API to a Vehicle entity
  /// </summary>
  /// <param name="lotData">JSON data for a lot from the Auction API</param>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>A Vehicle object populated with data from the API</returns>
  public async Task<Vehicle> MapFromAuctionLotAsync(InventAuctionLot lotData, CancellationToken cancellationToken = default)
  {
    if (lotData == null)
      throw new ArgumentNullException(nameof(lotData));

    // Use lookup service to get IDs for related entities
    uint? makeId = null;
    uint? modelId = null;
    uint? fuelTypeId = null;
    uint? transmissionId = null;
    uint? bodyTypeId = null;
    uint? colorId = null;

    // Get Make ID
    if (!string.IsNullOrEmpty(lotData.Manufacturer))
    {
      makeId = await _lookupService.GetLookupId(cancellationToken, "make", lotData.Manufacturer);

      // Get Model ID if Make exists
      if (makeId.HasValue && !string.IsNullOrEmpty(lotData.Model))
      {
        modelId = await _lookupService.GetLookupId(cancellationToken, "model", lotData.Model, makeId);
      }
    }

    // Get FuelType ID
    if (!string.IsNullOrEmpty(lotData.FuelType))
    {
      fuelTypeId = await _lookupService.GetLookupId(cancellationToken, "fuel_type", lotData.FuelType);
    }

    // Get Transmission ID
    if (!string.IsNullOrEmpty(lotData.Transmission))
    {
      transmissionId = await _lookupService.GetLookupId(cancellationToken, "transmission_type", lotData.Transmission);
    }

    // Get BodyType ID if available
    if (!string.IsNullOrEmpty(lotData.BodyType))
    {
      bodyTypeId = await _lookupService.GetLookupId(cancellationToken, "body_type", lotData.BodyType);
    }

    // Get Color ID
    if (!string.IsNullOrEmpty(lotData.Colour))
    {
      colorId = await _lookupService.GetLookupId(cancellationToken, "colour", lotData.Colour);
    }

    // Parse numeric values safely
    uint mileage = (uint?)lotData.Mileage ?? 0;
    ushort engineCc = (ushort?)lotData.EngineSize ?? 0;

    // Create the vehicle object
    var vehicle = new Vehicle
    {
      AddressId = DataContextExtensions.CAGAddressId,
      ContactId = DataContextExtensions.CAGContactId,
      CustomerId = DataContextExtensions.CAGCustomerId,

      // Basic vehicle information
      Vrm = lotData.Vrm,
      Vin = lotData.Vin,
      Colour = lotData.Colour,

      // Properties with direct mapping
      Doors = (ushort?)lotData.DoorCount,
      Odometer = mileage,
      EngineCc = engineCc,
      YearOfManufacture = (uint?)lotData.Year,

      // Date fields
      DateOfReg = lotData.FirstRegistered,
      MotExpires = null, // Not provided in API

      // Boolean fields
      Runner = !ParseBool(lotData.NonRunner),
      LogBook = ParseBool(lotData.HasV5),

      // Grade - using NAMA grade from API
      Grade = ParseGrade(lotData.NamaGrade),

      // Additional fields
      Owners = 0, // Default value
      NoOfKeys = (byte)(lotData.NumKeys ?? 0),

      // Values that require calculation
      Kerbweight = 0, // Not provided in API
      BHP = 0, // Not provided in API
      Co2 = ParseUint(lotData.Co2) ?? 0,

      // Add customer reference 
      CustomerRef = $"AuctionLot-{lotData.Id}",

      // OdometerUnit (assuming miles as default based on API data)
      OdometerUnit = (int)OdometerUnitEnum.Miles,

      // Service history - no direct mapping from API
      ServiceHistory = false,
      ServiceHistoryType = ServiceHistoryTypeEnum.None,

      // IDs from lookup service
      MakeId = makeId,
      ModelId = modelId,
      FuelTypeId = fuelTypeId,
      TransmissionTypeId = transmissionId,
      BodyTypeId = bodyTypeId,
      VehicleColourId = colorId,

      // Set added/updated dates
      Added = DateTime.Now,
      Updated = DateTime.Now
    };

    // Set mileage range ID
    if (mileage > 0)
    {
      vehicle.MileageRangeId = await _lookupService.GetMileageRangeId(mileage, cancellationToken);
    }

    // Set engine capacity range ID
    if (engineCc > 0)
    {
      vehicle.CapacityRangeId = await _lookupService.GetCapacityRangeId(engineCc, cancellationToken);
    }

    return vehicle;
  }

  #region Helper Methods

  private uint? ParseUint(string value)
  {
    if (string.IsNullOrEmpty(value))
      return null;

    if (uint.TryParse(value, out uint result))
      return result;

    return null;
  }

  private bool ParseBool(dynamic value)
  {
    if (value == null) return false;

    string strValue = value.ToString();

    if (bool.TryParse(strValue, out bool result))
      return result;

    // Handle numeric values (0/1) as booleans
    if (int.TryParse(strValue, out int numericResult))
      return numericResult != 0;

    return false;
  }

  private short ParseGrade(dynamic value)
  {
    if (value == null) return 0;

    if (short.TryParse(value.ToString(), out short result))
      return result;

    return 0;
  }

  #endregion
}

