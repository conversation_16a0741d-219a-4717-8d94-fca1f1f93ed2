using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Trading.API.Common;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.API.Remarq.Controllers.Extensions;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace Trading.API.Controllers
{
  [Route("api/deal")]
  [ApiController]
  [Authorize]
  public class DealController : ControllerBase
  {
    private readonly IDealService _dealService;

    public DealController(IDealService dealService)
    {
      this._dealService = dealService;
    }

    [HttpGet]
    [Route("/api/deals")]
    [AllowAnonymous] // the service filter will prevent execution if no API key or user is not authenticated
    //[Authorize(AuthenticationSchemes = "ApiKeyAuthenticationScheme")]
    public async Task<IActionResult> GetPurchases(string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = new DealSearchDTO();

        if (!string.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<DealSearchDTO>(query);
        }

        var isApiKey = HttpContext.IsAPIKeyRequest();

        // Currently only used in Admin->Deals, so can be restrictive
        if (User.IsAdmin() || isApiKey)
        {
          var deals = await _dealService.Search(dto, cancellationToken);

          return Ok(deals);
        } 
        else
        {
          return Forbid();
        }
      } 
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/customer/{customerId}/purchases")]
    [AllowAnonymous]
    //[Authorize(Policy = "ValidUserOrApiKey")]
    //[Authorize(AuthenticationSchemes = "ApiKeyAuthenticationScheme")]
    //[Authorize]
    public async Task<IActionResult> GetPurchases(Guid customerId, [FromQuery] string? query, CancellationToken cancellationToken)
    {
      var isApiKey = HttpContext.IsAPIKeyRequest();

      if (isApiKey || User.IsAdmin() || customerId == User.CustomerId().Value)
      {
        DealSearchDTO dto = new DealSearchDTO();

        if (!string.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<DealSearchDTO>(query);
        }

        if (!dto.Filters.BuyerCustomerId.HasValue)
        {
          dto.Filters.BuyerCustomerId = customerId;
        }

        var deals = await _dealService.Search(dto, cancellationToken);

        return Ok(deals);
      }
      else
      {
        return Forbid();
      }
    }

    [HttpGet]
    [Route("/api/customer/{customerId}/sales")]
    [AllowAnonymous]
    public async Task<IActionResult> GetSales(Guid customerId, [FromQuery] string? query, CancellationToken cancellationToken)
    {
      var isApiKey = HttpContext.IsAPIKeyRequest();

      if (isApiKey || User.IsAdmin() || customerId == User.CustomerId().Value)
      {
        DealSearchDTO dto = new DealSearchDTO();

        if (!string.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<DealSearchDTO>(query);
        }

        if (!dto.Filters.SellerCustomerId.HasValue)
        {
          dto.Filters.SellerCustomerId = customerId;
        }

        var deals = await _dealService.Search(dto, cancellationToken);

        return Ok(deals);
      }
      else
      {
        return Forbid();
      }
    }

  }
}
