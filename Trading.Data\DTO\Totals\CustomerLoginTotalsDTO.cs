﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.Totals
{
  public class CustomerLoginTotalsDTO
  {
    public Guid? AssignedTo { get; set; }
    public Guid CustomerId { get; set; }
    public string CustomerName { get; set; }
    public string CustomerEmail { get; set; }
    public string ContactName { get; set; }
    public string ContactEmail { get; set; }
    public string ContactPhone1 { get; set; }
    public int LoginCount { get; set; }
    public DateTime? LatestLoginDate { get; set; }
  }
}
