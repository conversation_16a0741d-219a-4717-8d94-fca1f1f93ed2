using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.API.Helpers;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/[controller]")]
  [ApiController]
  [Authorize]
  public class CustomerNoteController : ControllerBase
  {
    private readonly ICustomerNoteService _customerNoteService;

    public CustomerNoteController(
      ICustomerNoteService customerNoteService)
    {
      _customerNoteService = customerNoteService;
    }

    [HttpPost]
    [Route("/api/customer/{customerId}/note")]
    public async Task<IActionResult> AddNote(Guid customerId, [FromBody] CustomerNoteDTO dto, CancellationToken cancellationToken)
    {
      dto.CustomerId = customerId;
      dto.NoteByContactId = User.ContactId();

      var customerNote = await _customerNoteService.Create( dto, cancellationToken);
      return Ok(customerNote);
    }

    [HttpGet]
    [Route("/api/customer/{customerId}/notes")]
    public async Task<IActionResult> Notes(Guid customerId, [FromQuery] string query, CancellationToken cancellationToken)
    {
      CustomerNoteSearchDTO dto = new CustomerNoteSearchDTO();

      if (query != null)
      {
        dto = JsonConvert.DeserializeObject<CustomerNoteSearchDTO>(query);
      }

      dto.CurrentCustomerId = User.CustomerId();
      dto.Filters.CustomerId = customerId;

      var searchResult = await _customerNoteService.Search(dto, cancellationToken);
      var notes = searchResult.CustomerNoteDTOs;

      return Ok(notes);
    }
  }
}
