﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.API.Helpers;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/appraisalItem")]
  [ApiController]
  [Authorize]
  public class AppraisalItemController : ControllerBase
  {
    private readonly IAppraisalService _appraisalService;
    private readonly IMapper _mapper;

    public AppraisalItemController(IAppraisalService appraisalService, IMapper mapper)
    {
      _appraisalService = appraisalService;
      _mapper = mapper;
    }


    [HttpPost]
    [Route("")]
    public async Task<IActionResult> CreateAppraisalItem([FromBody] AppraisalItemDTO appraisalItemDTO, CancellationToken cancellationToken)
    {
      try
      {
        appraisalItemDTO.ItemDesc = appraisalItemDTO.ItemDesc.SanitiseString();

        var entity = await _appraisalService.CreateAppraisalItem(appraisalItemDTO, cancellationToken);
        return Ok(entity);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> PatchAppraisalItem(Guid id, [FromBody] JsonPatchDocument<AppraisalItem> patch, CancellationToken cancellationToken)
    {      
      try
      {
        var item = await _appraisalService.PatchAppraisalItem(id, patch, cancellationToken);
        return Ok(item);
      }
      catch (Exception ex)
      {
        return ex.ParseError();
      }
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<IActionResult> DeleteAppraisalItem(Guid id, CancellationToken cancellationToken)
    {
      try
      {
        await _appraisalService.DeleteAppraisalItem(id, (Guid)User.CustomerId(), cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
