﻿using System;
using Trading.API.Data.Enums.UInspections;

namespace Trading.API.Data.DTO.Search.LeadCRM
{
  public class LeadVehicleSearchDTO : BaseSearchDTO
  {
    public LeadVehicleFilters Filters { get; set; } = new LeadVehicleFilters() { };
  }

  public class LeadVehicleFilters : BaseFilterGuid
  {
    public Guid? LeadVehicleId { get; set; }
    public Guid? Id { get; set; }
    public string ExternalAppraisalCode { get; set; }
    public UInspectSourceEnum? ExternalAppraisalSource { get; set; }
    public string VRM { get; set; }
  }
}
