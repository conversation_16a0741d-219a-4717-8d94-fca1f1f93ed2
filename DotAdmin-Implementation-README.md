# dotAdmin API Integration

This document describes the implementation of the dotAdmin API integration for the Trading.API project.

## Overview

The dotAdmin API integration provides functionality to push vehicles from the ICVehicle table to dotAdmin auction platform. The implementation follows the existing patterns in the Trading.API project and includes:

- HTTP client for dotAdmin API communication
- Service layer for business logic
- API controllers for external access
- Comprehensive unit tests
- Configuration management

## Architecture

### Components

1. **Configuration**: `DotAdminDTO` - Configuration settings for the dotAdmin API
2. **Data Models**: DTOs for authentication, vehicle creation, and API responses
3. **Client Layer**: `IDotAdminClient` / `DotAdminClient` - HTTP communication with dotAdmin API
4. **Service Layer**: `IDotAdminService` / `DotAdminService` - Business logic and ICVehicle integration
5. **API Layer**: `DotAdminController` - REST API endpoints
6. **Tests**: Comprehensive unit tests for all components

### Key Features

- **Authentication Management**: Automatic token refresh and session management
- **ICVehicle Integration**: Direct integration with existing ICVehicle data
- **Vehicle Mapping**: Automatic mapping from ICVehicle fields to dotAdmin format
- **Error Handling**: Comprehensive error handling and logging
- **Flexible Configuration**: Support for different environments and settings

## Configuration

Add the following to your `appsettings.json`:

```json
{
  "DotAdmin": {
    "BaseUrl": "https://dev-stack-admin.dotadmin.net",
    "Username": "<EMAIL>",
    "Password": "your-password",
    "TimeoutSeconds": 30,
    "TokenRefreshBufferSeconds": 300,
    "DefaultCustomerId": 3787,
    "DefaultLocationId": 12,
    "UseCookieAuth": true
  }
}
```

## Usage

### Service Layer

The main service method for pushing ICVehicles to auction:

```csharp
// Inject IDotAdminService
public async Task PushVehicleToAuction(Guid icResponseId, Guid icVehicleId, int locationId)
{
    var vehicle = await _dotAdminService.CreateVehicleFromICVehicleAsync(
        icResponseId, 
        icVehicleId, 
        locationId);
    
    // Vehicle is now created in dotAdmin auction
    Console.WriteLine($"Created vehicle with ID: {vehicle.Id}");
}
```

### API Endpoints

#### Create Vehicle from ICVehicle
```http
POST /api/dotadmin/vehicles/from-ic-vehicle
Content-Type: application/json

{
  "icResponseId": "guid",
  "icVehicleId": "guid", 
  "locationId": 12,
  "customerId": 3787
}
```

#### Create Vehicle with Basic Info
```http
POST /api/dotadmin/vehicles
Content-Type: application/json

{
  "registration": "AB12 CDE",
  "vin": "1234567890",
  "customerId": 3787,
  "locationId": 12,
  "useLookup": true
}
```

#### Authentication Status
```http
GET /api/dotadmin/auth-status
```

## Data Mapping

The service automatically maps ICVehicle fields to dotAdmin format:

| ICVehicle Field | dotAdmin Field | Notes |
|----------------|----------------|-------|
| VRM | motorvehicle_registration | Required |
| VIN | motorvehicle_vin | Optional |
| MakeName | motorvehicle_manufacturer | |
| ModelName | motorvehicle_model | |
| DerivName | motorvehicle_variant | |
| Colour | motorvehicle_colour | |
| BodyTypeName | motorvehicle_bodystyle | |
| FuelTypeName | motorvehicle_fueltype | |
| TransmissionTypeName | motorvehicle_gearboxtype | |
| YearOfManufacture | motorvehicle_yearofmanufacture | |
| Doors | motorvehicle_doorcount | Parsed to int |
| EngineCC | motorvehicle_exactcc | Parsed to int |
| BHP | motorvehicle_bhp | Parsed to int |
| CO2 | motorvehicle_co2 | Parsed to int |
| PreviousKeepers | motorvehicle_formerkeepers | Parsed to int |
| Odometer | motorvehicle_mileage | Parsed to int |

## Vehicle Type and Classification Mapping

The service includes intelligent mapping for vehicle types and classifications:

- **Vehicle Types**: Car, Motorcycle, Plant, Classic
- **Classifications**: Car, LCV, Commercial, Plant, Motorcycle, WAV, Trailer, Other

Mapping is based on the `VehicleTypeName` and `BodyTypeName` fields from ICVehicle.

## Error Handling

The implementation includes comprehensive error handling:

- **Authentication Errors**: Automatic retry with configured credentials
- **Validation Errors**: Clear error messages for missing required fields
- **HTTP Errors**: Proper status code handling and retry logic
- **Business Logic Errors**: Validation of ICVehicle data before submission

## Testing

### Running Tests

```bash
# Run all dotAdmin tests
dotnet test Trading.Services.DotAdmin.Tests
dotnet test Trading.API.DotAdmin.Tests

# Run specific test class
dotnet test --filter "DotAdminServiceTests"
```

### Test Coverage

- **Client Tests**: HTTP communication, authentication, error handling
- **Service Tests**: Business logic, ICVehicle integration, data mapping
- **Controller Tests**: API endpoints, request/response handling
- **Integration Tests**: End-to-end scenarios (can be added)

## Security Considerations

- **Credentials**: Store credentials securely using Azure Key Vault or similar
- **Token Management**: Tokens are automatically refreshed before expiry
- **HTTPS**: All communication uses HTTPS
- **Logging**: Sensitive data is not logged

## Monitoring and Logging

The implementation includes comprehensive logging:

- Authentication events
- Vehicle creation attempts
- Error conditions
- Performance metrics

Use the configured logging framework to monitor dotAdmin integration health.

## Deployment

1. **Configuration**: Update appsettings for target environment
2. **Dependencies**: Ensure all NuGet packages are restored
3. **Database**: No database changes required
4. **Testing**: Run integration tests against sandbox environment

## Future Enhancements

Potential improvements for future versions:

1. **Batch Operations**: Support for creating multiple vehicles at once
2. **Status Tracking**: Track vehicle status changes in dotAdmin
3. **Webhook Support**: Handle callbacks from dotAdmin
4. **Advanced Mapping**: More sophisticated field mapping rules
5. **Caching**: Cache customer and location data
6. **Retry Policies**: More sophisticated retry logic with exponential backoff

## Support

For issues or questions regarding the dotAdmin integration:

1. Check the logs for error details
2. Verify configuration settings
3. Test authentication separately
4. Review the dotAdmin API documentation
5. Contact the development team

## API Documentation Reference

The implementation is based on the dotAdmin API documentation provided. Key endpoints used:

- `POST /login` - Authentication
- `POST /loginselectcustomer` - Customer/location selection  
- `POST /admin/auction/motorvehicles/createmotorvehicle` - Vehicle creation

For the latest API documentation, refer to the dotAdmin API specification.
