using System;
using System.Collections.Generic;
using System.Linq;
using Trading.API.Data.DTO;
using Trading.API.Data.Enums;

namespace Trading.API.Data.Models.DTO
{
  public class ScanStyleDTO : BaseModelEntityIntDTO
  {
    public string StyleName { get; set; }

    public uint PerPage { get; set; }

    public string ImagePrefix { get; set; }

    public string VehicleTypes { get; set; }

    public string SearchParams { get; set; }

    public ScanContentTypeEnum ContentType { get; set; }

    public uint EntryPoint { get; set; }
    public uint DetailEntryPoint { get; set; }

    public uint ScanServiceId { get; set; }
    public bool? IsDefault { get; set; }

    public ScanServiceDTO ScanService { get; set; }
    public ICollection<ScanStageDTO> ScanStages { get; set; }
    public ScanRequestMethodEnum Method { get; set; }
    public string PostTemplate { get; set; }
    public string RequestVarName1 { get; set; }
    public string RequestVarName2 { get; set; }
    public string RequestVarName3 { get; set; }
    public string CustomHeaders { get; set; }
  }
}