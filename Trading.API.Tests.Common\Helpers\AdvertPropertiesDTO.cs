﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;

namespace Trading.API.Tests.Common.Helpers
{
  public class AdvertPropertiesDTO
  {
    public Guid adId { get; set; }
    public uint? buyItNowPrice { get; set; }
    public uint startPrice { get; set; }
    public bool acceptBids { get; set; }
    public uint bidIncrement { get; set; }
    public SaleTypeEnum saleType { get; set; }
    public DateTime? startDate { get; set; }
    public DateTime? endDate { get; set; }
    public uint? autoAcceptBid { get; set; }
    public uint? autoRejectBid { get; set; }
    public uint reserve { get; set; } = 0;
    public AdvertStatusEnum advertStatus { get; set; } = AdvertStatusEnum.Inactive;
    public uint? vatStatusId { get; set; } = null;
    public uint CurrentPrice { get; set; }
    public SoldStatusEnum soldStatus { get; set; } = SoldStatusEnum.Draft;
  }
}
