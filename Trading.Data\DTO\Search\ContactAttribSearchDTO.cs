﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO
{
  public class ContactAttribSearchDTO : BaseSearchDTO
  {
    public ContactAttribFilters Filters { get; set; } = new ContactAttribFilters() { };
  }

  public class ContactAttribFilters : BaseFilterInt
  {
    public Guid? ContactId;
    public uint? AttribId;
  }
}