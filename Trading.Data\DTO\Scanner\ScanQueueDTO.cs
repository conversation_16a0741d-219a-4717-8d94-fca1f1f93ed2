using System;
using System.Linq;
using Trading.API.Data.DTO;

namespace Trading.API.Data.Models.DTO
{
  public class ScanQueueDTO : BaseModelEntityIntDTO
  {
    public uint ScanBatchId { get; set; }
    public ScanBatchDTO ScanBatch { get; set; }
    public string ScanUrl { get; set; }
    public uint Priority { get; set; }
    public bool Started { get; set; }
    public bool Finished { get; set; }
    public bool Completed { get; set; }
    public bool SaveSample { get; set; }
    public ScanVehicleDTO ScanVehicle { get; set; }
    public uint ScanVehicleId { get; set; }
    public Guid CustomerId { get; set; }
    public CustomerDTO Customer { get; set; }
    public ScanCustomerDTO ScanCustomer { get; set; }
    public uint ScanCustomerId { get; set; }
    public ScanStageDTO ScanStage { get; set; }
    public uint ScanStageId { get; set; }
    public VehicleTypeDTO vehicleType { get; set; }
    public uint VehicleTypeId { get; set; }
    public uint? ScanQueueId { get; set; }
    public uint? ScanPage { get; set; }
  }
}