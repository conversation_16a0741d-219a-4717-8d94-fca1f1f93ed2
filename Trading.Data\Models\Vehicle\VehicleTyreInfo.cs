﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;

namespace Trading.API.Data.Models;

[Table("VehicleTyreInfo")]
public class VehicleTyreInfo : BaseModelEntityInt
{
  public Guid VehicleId { get; set; }
  public virtual Vehicle Vehicle { get; set; }

  [MaxLength(10)]
  public TyrePositionEnum Position { get; set; } // "NSF", "OSF", "NSR", "OSR", "SPARE"

  [MaxLength(50)]
  public string Make { get; set; }

  [MaxLength(20)]
  public string Condition { get; set; }

  public decimal? Depth { get; set; } // For migrated data and future inspections

}

// Position mapping helper
public static class TyrePositionMapper
{
  private static readonly Dictionary<string, TyrePositionEnum> PositionMappings = new()
    {
        { "Tyre NSF", TyrePositionEnum.NSF},
        { "Tyre OSF", TyrePositionEnum.OSF},
        { "Tyre NSR", TyrePositionEnum.NSR},
        { "Tyre OSR", TyrePositionEnum.OSR},
        { "Spare Tyre", TyrePositionEnum.SPARE},
        { "Tyre Front Near Side", TyrePositionEnum.NSF},
        { "Tyre Front Off Side", TyrePositionEnum.OSF},
        { "Tyre Rear Near Side", TyrePositionEnum.NSR},
        { "Tyre Rear Off Side", TyrePositionEnum.OSR},
        { "Tyre Front Left", TyrePositionEnum.NSF},
        { "Tyre Front Right", TyrePositionEnum.OSF},
        { "Tyre Rear Left", TyrePositionEnum.NSR},
        { "Tyre Rear Right", TyrePositionEnum.OSR},
        { "Front Near Side Tyre", TyrePositionEnum.NSF},
        { "Front Off Side Tyre", TyrePositionEnum.OSF},
        { "Rear Near Side Tyre", TyrePositionEnum.NSR},
        { "Rear Off Side Tyre", TyrePositionEnum.OSR},
        { "Near Side Front Tyre", TyrePositionEnum.NSF},
        { "Off Side Front Tyre", TyrePositionEnum.OSF},
        { "Near Side Rear Tyre", TyrePositionEnum.NSR},
        { "Off Side Rear Tyre", TyrePositionEnum.OSR},
        // Add other variations as needed
    };

  public static TyrePositionEnum? NormalizePosition(string rawPosition)
  {
    if (string.IsNullOrWhiteSpace(rawPosition))
      return null;

    var trimmed = rawPosition.Trim();

    return PositionMappings.TryGetValue(trimmed, out var normalized) ? normalized : null;
  }
}
