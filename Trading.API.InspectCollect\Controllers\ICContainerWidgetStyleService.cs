﻿using AutoMapper;
using Microsoft.AspNetCore.JsonPatch;
using Trading.API.Data.Enums;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.InspectCollect.Interfaces;
using System;
using Trading.Services.Extensions;
using Microsoft.EntityFrameworkCore;

namespace Trading.Services.InspectCollect.Classes
{
  public class ICContainerWidgetStyleService : ICContainerWidgetStyleInterface
  {
    private readonly TradingContext _context;
    private readonly IMapper _mapper;

    public ICContainerWidgetStyleService(TradingContext context, IMapper mapper)
    {
      _context = context;
      _mapper = mapper;
    }

    public async Task<ICContainerWidgetStyleDTO> Create(ICContainerWidgetStyleCreateDTO dto)
    {
      var cwi = _mapper.Map<ICContainerWidgetStyle>(dto);

      cwi.StatusId = (uint)StatusEnum.Active;
      cwi.Added = DateTime.Now;
      cwi.Updated = DateTime.Now;

      _context.ICContainerWidgetStyles.Add(cwi);

      await _context.SaveChangesAsync();

      return _mapper.Map<ICContainerWidgetStyleDTO>(cwi);
    }

    public async Task<bool> Delete(Guid id)
    {
      var icwi = _context.ICContainerWidgetStyles
        .Where(x => x.Id == id)
        .FirstOrDefault();

      _context.ICContainerWidgetStyles.Remove(icwi);
      await _context.SaveChangesAsync();

      return true;
    }

    public async Task<ValidatedResultDTO<ICContainerWidgetStyleDTO>> Get(Guid guid, ICContainerWidgetStyleSearchDTO searchDTO, CancellationToken cancellationToken)
    {
      if (searchDTO == null) { searchDTO = new ICContainerWidgetStyleSearchDTO(); }

      searchDTO.Filters.Id = guid;
      var cwi = await Search(searchDTO, cancellationToken);

      return new ValidatedResultDTO<ICContainerWidgetStyleDTO>()
      {
        IsValid = cwi.TotalItems > 0,
        DTO = cwi.Results.FirstOrDefault()
      };
    }

    public async Task<ICContainerWidgetStyleDTO> Patch(Guid id, JsonPatchDocument<ICContainerWidgetStyle> patch)
    {
      var cwi = await _context.ICContainerWidgetStyles.Where(x => x.Id == id).FirstOrDefaultAsync();

      if (cwi == null) { return null; }

      patch.FilterPatch();
      patch.ApplyTo(cwi);
      cwi.Updated = DateTime.Now;

      await _context.SaveChangesAsync();

      return _mapper.Map<ICContainerWidgetStyleDTO>(cwi);
    }

    public async Task<SearchResultDTO<ICContainerWidgetStyleDTO>> Search(ICContainerWidgetStyleSearchDTO searchDTO, CancellationToken cancellationToken)
    {
      var preQuery = _context.ICContainerWidgetStyles.AsQueryable();

      if (searchDTO.Filters != null)
      {
        if (searchDTO.Filters.Id != null)
        {
          preQuery = preQuery.Where(x => x.Id == searchDTO.Filters.Id);
        }
        if (searchDTO.Filters.ICContainerWidgetId != null)
        {
          preQuery = preQuery.Where(x => x.ICContainerWidgetId == searchDTO.Filters.ICContainerWidgetId);
        }
      }

      var query = preQuery;

      if (searchDTO.Limit.HasValue)
      {
        query = query.Take(searchDTO.Limit.Value);
        query = query.Skip(searchDTO.Offset.Value);
      }

      var items = await query
        .AsNoTracking()
        .ToListAsync(cancellationToken);

      return new SearchResultDTO<ICContainerWidgetStyleDTO>
      {
        TotalItems = (searchDTO.Limit.HasValue) ? await preQuery.CountAsync(cancellationToken) : items.Count(),
        Results = _mapper.Map<IEnumerable<ICContainerWidgetStyle>, IEnumerable<ICContainerWidgetStyleDTO>>(items)
      };
    }
  }
}
