﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.LeadCRM;
using Trading.API.Data.Models.LeadCRM;
using Trading.Services.Extensions;
using Trading.Services.LeadCRM.Interfaces;

namespace Trading.API.LeadCRM.Controllers
{
  [Route("api/lead-media")]
  [ApiController]
  [Authorize]
  public class LeadMediaController : ControllerBase
  {
    private readonly ILeadMediaService _leadMediaService;

    public LeadMediaController(ILeadMediaService leadMediaService)
    {
      this._leadMediaService = leadMediaService;
    }

    //[ResponseCache(Duration = 60)]
    [HttpGet]
    [Route("/api/lead-medias")]
    public async Task<IActionResult> Search([FromQuery] string? query, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = new LeadMediaSearchDTO();
        
        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<LeadMediaSearchDTO>(query);
        }

        var result = await _leadMediaService.Search(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create([FromBody] LeadMediaDTO dto, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadMediaService.Create(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<IActionResult> Get(
      Guid leadMediaId, 
      [FromQuery] string query, 
      CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = JsonConvert.DeserializeObject<LeadMediaSearchDTO>(query);
        var result = await _leadMediaService.Get(leadMediaId,dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<LeadMedia> patch, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadMediaService.Patch(id, patch, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<IActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadMediaService.Delete(id, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    private IFormFileCollection DataURLsToFormFiles(string[] files)
    {
      List<IFormFile> formFiles = new List<IFormFile>();
      foreach (var file in files)
      {
        // Remove the file type prefix, eg: data:image/png;base64,

        byte[] bytes = Convert.FromBase64String(file);
        MemoryStream stream = new MemoryStream(bytes);

        IFormFile formFile = new FormFile(stream, 0, bytes.Length, "filename", "filename");
        formFiles.Add(formFile);

      }

      var collection = new FormFileCollection();
      collection.AddRange(formFiles);

      return collection;
    }
  }
}
