﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Trading.Services.Helpers;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/rateCard")]
  [ApiController]
  public class RateCardController : ControllerBase
  {
    private readonly IRateCardService _rateCardService;

    public RateCardController(IRateCardService rateCardService)
    {
      this._rateCardService = rateCardService;
    }

    [HttpGet]
    [Route("{productCode}/platform/{platformId}")]
    public async Task<IActionResult> GetRateCards(string productCode, int platformId, CancellationToken cancellationToken)
    {
      try
      {
        var dtos = await _rateCardService.GetRateCard(productCode, cancellationToken);
        return Ok(dtos);
      } 
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
