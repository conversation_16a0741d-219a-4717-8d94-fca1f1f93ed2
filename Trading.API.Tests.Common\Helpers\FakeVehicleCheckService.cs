﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Vehicle;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.Services.Interfaces;

namespace Trading.API.Tests.Common.Helpers
{
  public class FakeVehicleCheckService : IVehicleCheckService
  {
    public Task Create(VehicleCheckDTO dto, CancellationToken cancellationToken)
    {
      throw new NotImplementedException();
    }

    public Task CreateDummyValuationRecord()
    {
      throw new NotImplementedException();
    }

    public Task<VehicleCheckDTO> GetVehicleCheck(Guid vehicleId, VehicleCheckTypeEnum checkType, CancellationToken cancellationToken)
    {
      throw new NotImplementedException();
    }

    public Task<VehicleCheck> GetVehicleCheckRecord(string vrm, VehicleCheckTypeEnum checkType)
    {
      throw new NotImplementedException();
    }

    public Task<bool> HasVehicleCheck(Guid vehicleId, VehicleCheckTypeEnum checkType, CancellationToken cancellationToken)
    {
      return Task.FromResult(true);
    }

    public Task RemoveAllVehicleChecks(Guid vehicleId)
    {
      throw new NotImplementedException();
    }

    public Task RemoveExistingCheckRecord(string vrm, VehicleCheckTypeEnum checkType)
    {
      throw new NotImplementedException();
    }

    public Task UpdateVehicleCheck(VRMLookupDataDTO lookupDTO, VehicleCheckTypeEnum checkType, CancellationToken cancellationToken)
    {
      throw new NotImplementedException();
    }

    public Task<ICollection<VehicleCheck>> UpdateVehicleChecks(VRMLookupDataDTO lookupDTO, CancellationToken cancellationToken)
    {
      throw new NotImplementedException();
    }

    public Task<VehicleCheck> UpsertProvenanceCheck(VehicleProvenanceUpdateDTO dto)
    {
      throw new NotImplementedException();
    }
  }
}
