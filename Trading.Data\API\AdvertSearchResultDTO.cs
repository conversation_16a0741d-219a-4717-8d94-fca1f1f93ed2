﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Components.AdvertView;
using Trading.API.Data.DTO.Components.MainDashboard;
using Trading.API.Data.Models;

namespace Trading.API.Data.DTO
{
  public class AdvertSearchResultDTO
  {
    public IEnumerable<AdvertDTO> Adverts { get; set; }
    public AdvertDTO Advert { get; set; } = new AdvertDTO();
    public AdvertDTO_Public AdvertDTO_Public { get; set; } = new AdvertDTO_Public();
    public AdvertDealDTO AdvertDeal { get; set; } = new AdvertDealDTO();


    // custom dto for every component view 
    public AdvertViewVehicleInfoDTO AdvertViewVehicleInfo { get; set; }
    public AdvertViewDataDTO AdvertViewData { get; set; }

    public int? Count { get; set; }
  }
  public class SellerCountsResultDTO
  {
    public IDictionary<uint, uint> Counts { get; set; }
  }
}