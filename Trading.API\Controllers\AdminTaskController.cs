using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Comms;
using Trading.API.Data.DTO.Search;
using Trading.API.Data.Models;
using Trading.API.Helpers;
using Trading.Services.Classes;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/admin-task")]
  [ApiController]
  [Authorize(Roles = "ADMIN")]
  public class AdminTaskController : ControllerBase
  {
    private readonly IAdminTaskService _adminTaskService;

    public AdminTaskController(
      IAdminTaskService adminTaskService)
    {
      _adminTaskService = adminTaskService;
    }

    [HttpGet]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var searchDTO = JsonConvert.DeserializeObject<AdminTaskSearchDTO>(query);
      var res = await _adminTaskService.Search(searchDTO, cancellationToken);

      return Ok(res);
    }

    [HttpDelete]
    [Route("{adminTaskId}")]
    public async Task<IActionResult> DeleteAdminTask(Guid adminTaskId, CancellationToken cancellationToken)
    {
      var customerNote = await _adminTaskService.Delete(Guid.Empty, adminTaskId, cancellationToken);
      return Ok(true);
    }

    [HttpPatch]
    [Route("{adminTaskId}")]
    public async Task<IActionResult> PatchAdminTask(Guid adminTaskId, JsonPatchDocument<AdminTask> patch, CancellationToken cancellationToken)
    {
      patch.Add(x => x.LastUpdateByContactId, User.ContactId());

      var customerNote = await _adminTaskService.Patch(adminTaskId, patch, cancellationToken);
      return Ok(true);
    }
  }
}
