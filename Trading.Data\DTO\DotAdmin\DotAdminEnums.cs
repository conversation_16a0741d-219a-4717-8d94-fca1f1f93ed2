using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.DotAdmin
{
  /// <summary>
  /// Vehicle types in dotAdmin (deployment-specific)
  /// </summary>
  public enum DotAdminVehicleType
  {
    MotorVehicleMotorcycle = 1,
    PlantGeneralItem = 2,
    Classic = 3
  }

  /// <summary>
  /// Vehicle classifications in dotAdmin
  /// </summary>
  public enum DotAdminVehicleClassification
  {
    Car = 1,
    LCV = 2,
    Commercial = 3,
    Plant = 4,
    Motorcycle = 5,
    WAV = 6,
    Trailer = 7,
    Other = 8
  }

  /// <summary>
  /// Vehicle status in dotAdmin
  /// </summary>
  public enum DotAdminVehicleStatus
  {
    Selling = 1,
    Sold = 2,
    Provisional = 3,
    Unsold = 4,
    Withdrawn = 5,
    Deleted = 6
  }

  /// <summary>
  /// Helper class for dotAdmin enum mappings
  /// </summary>
  public static class DotAdminEnumHelper
  {
    /// <summary>
    /// Map vehicle type name to dotAdmin vehicle type
    /// </summary>
    public static DotAdminVehicleType? MapVehicleType(string vehicleTypeName)
    {
      if (string.IsNullOrWhiteSpace(vehicleTypeName))
        return null;

      return vehicleTypeName.ToLowerInvariant() switch
      {
        "car" => DotAdminVehicleType.MotorVehicleMotorcycle,
        "motorcycle" => DotAdminVehicleType.MotorVehicleMotorcycle,
        "motorbike" => DotAdminVehicleType.MotorVehicleMotorcycle,
        "plant" => DotAdminVehicleType.PlantGeneralItem,
        "classic" => DotAdminVehicleType.Classic,
        _ => DotAdminVehicleType.MotorVehicleMotorcycle // Default to motor vehicle
      };
    }

    /// <summary>
    /// Map body type to dotAdmin vehicle classification
    /// </summary>
    public static DotAdminVehicleClassification? MapVehicleClassification(string bodyTypeName, string vehicleTypeName = null)
    {
      if (string.IsNullOrWhiteSpace(bodyTypeName))
        return null;

      var bodyType = bodyTypeName.ToLowerInvariant();
      var vehicleType = vehicleTypeName?.ToLowerInvariant();

      // Check vehicle type first
      if (vehicleType == "motorcycle" || vehicleType == "motorbike")
        return DotAdminVehicleClassification.Motorcycle;

      if (vehicleType == "plant")
        return DotAdminVehicleClassification.Plant;

      // Map by body type
      return bodyType switch
      {
        "saloon" => DotAdminVehicleClassification.Car,
        "hatchback" => DotAdminVehicleClassification.Car,
        "estate" => DotAdminVehicleClassification.Car,
        "coupe" => DotAdminVehicleClassification.Car,
        "convertible" => DotAdminVehicleClassification.Car,
        "suv" => DotAdminVehicleClassification.Car,
        "mpv" => DotAdminVehicleClassification.Car,
        "van" => DotAdminVehicleClassification.LCV,
        "pickup" => DotAdminVehicleClassification.LCV,
        "truck" => DotAdminVehicleClassification.Commercial,
        "lorry" => DotAdminVehicleClassification.Commercial,
        "hgv" => DotAdminVehicleClassification.Commercial,
        "trailer" => DotAdminVehicleClassification.Trailer,
        "wav" => DotAdminVehicleClassification.WAV,
        _ => DotAdminVehicleClassification.Car // Default to car
      };
    }
  }
}
