﻿using Microsoft.AspNetCore.Mvc.Filters;
using System;
using Trading.API.Data.Enums;

namespace Trading.API.Filters
{
  public class HasRoleAttribute : ActionFilterAttribute
  {
    public string RequiredRole { get; set; }

    public override void OnActionExecuting(ActionExecutingContext context)
    {
      if (context.ActionArguments.ContainsKey(RequiredRole)) {
        if (!context.HttpContext.User.IsInRole(context.ActionArguments[RequiredRole].ToString())) {
          context.HttpContext.Response.StatusCode = (int)HTTPStatusEnum.Forbidden;
        }
      }
    }
  }
}
