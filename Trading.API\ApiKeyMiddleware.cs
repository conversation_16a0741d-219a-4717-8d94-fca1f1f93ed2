﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Threading.Tasks;

namespace Trading.API
{
  public class ApiKeyMiddleware
  {
    private readonly RequestDelegate _next;
    private const string APIKEYNAME = "EventBridgeAPIKey";
    public ApiKeyMiddleware(RequestDelegate next)
    {
      _next = next;
    }
    public async Task InvokeAsync(HttpContext context)
    {
      if (!context.Request.Headers.TryGetValue(APIKEYNAME, out var extractedApiKey))
      {
        context.Response.StatusCode = 401;
        await context.Response.WriteAsync("Api Key was not provided. (Using ApiKeyMiddleware) ");
        return;
      }

      var appSettings = context.RequestServices.GetRequiredService<IConfiguration>();

      var apiKey = appSettings.GetValue<string>(APIKEYNAME);

      if (!apiKey.Equals(extractedApiKey))
      {
        context.Response.StatusCode = 401;
        await context.Response.WriteAsync("Unauthorized client. (Using ApiKeyMiddleware)");
        return;
      } 
      else
      {
        // since this is an external API request, we can create the identity 
      }

      await _next(context);
    }
  }
}
