﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Trading.API.Data.Migrations
{
    /// <inheritdoc />
    public partial class CityAppraisalRefactor : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AppraisalItem_Damage_DamageId",
                table: "AppraisalItem");

            migrationBuilder.AlterColumn<uint>(
                name: "DamageId",
                table: "AppraisalItem",
                type: "int unsigned",
                nullable: true,
                oldClrType: typeof(uint),
                oldType: "int unsigned");

            migrationBuilder.AlterColumn<uint>(
                name: "BodyPartId",
                table: "AppraisalItem",
                type: "int unsigned",
                nullable: true,
                oldClrType: typeof(uint),
                oldType: "int unsigned");

            migrationBuilder.AddColumn<string>(
                name: "SourceType",
                table: "AppraisalItem",
                type: "varchar(24)",
                maxLength: 24,
                nullable: true,
                collation: "utf8mb4_general_ci")
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddForeignKey(
                name: "FK_AppraisalItem_Damage_DamageId",
                table: "AppraisalItem",
                column: "DamageId",
                principalTable: "Damage",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AppraisalItem_Damage_DamageId",
                table: "AppraisalItem");

            migrationBuilder.DropColumn(
                name: "SourceType",
                table: "AppraisalItem");

            migrationBuilder.AlterColumn<uint>(
                name: "DamageId",
                table: "AppraisalItem",
                type: "int unsigned",
                nullable: false,
                defaultValue: 0u,
                oldClrType: typeof(uint),
                oldType: "int unsigned",
                oldNullable: true);

            migrationBuilder.AlterColumn<uint>(
                name: "BodyPartId",
                table: "AppraisalItem",
                type: "int unsigned",
                nullable: false,
                defaultValue: 0u,
                oldClrType: typeof(uint),
                oldType: "int unsigned",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_AppraisalItem_Damage_DamageId",
                table: "AppraisalItem",
                column: "DamageId",
                principalTable: "Damage",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
