using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Search;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.API.Helpers;
using Trading.Services;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/watchlist")]
  [ApiController]
  [Authorize]
  public class WatchlistController : ControllerBase
  {
    private readonly IWatchlistService _watchlistService;
    private readonly IMapper _mapper;
    private readonly IAdvertService _advertService;

    public WatchlistController(
      IWatchlistService watchlistService,
      IAdvertService advertService,
      IMapper mapper)
    {
      this._watchlistService = watchlistService;
      this._mapper = mapper;
      this._advertService = advertService;
    }

    [HttpGet]
    [Route("")]
    [Route("{id}")]
    public async Task<IActionResult> GetWatchlistItems(Guid id, CancellationToken cancellationToken)
    {
      var contactId = User.ContactId();
      id = (id == Guid.Empty ? User.ContactId().Value : id);

      if (contactId == id || User.IsInRole("USER:ADMIN"))
      {
        try
        {
          var items = await _watchlistService.GetWatchlistItems(id, cancellationToken);

          return Ok(items);
        }
        catch (Exception ex)
        {
          return BadRequest(ex);
        }
      }
      else
      {
        return Forbid();
      }
    }

    [HttpGet]
    [Route("advert/{advertId}/setStatus/{setStatus}")]
    public async Task<IActionResult> SetStatus(Guid advertId, uint setStatus, CancellationToken cancellationToken)
    {
      var watchlistDTO = new WatchlistDTO()
      {
        AdvertId = advertId,
        StatusId = setStatus,
        ContactId = User.ContactId(),
      };

      var contactId = User.ContactId();

      try
      {
        await _watchlistService.SetStatus(watchlistDTO, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }

      return Forbid();
    }

    [HttpGet("/api/contact/{id}/watchListWithAdverts")]
    [HttpGet("WatchListWithAdverts")]
    public async Task<IActionResult> GetWatchListWithAdverts(Guid id, CancellationToken cancellationToken)
    {
      var contactId = User.ContactId();
      id = (id == Guid.Empty ? User.ContactId().Value : id);

      if (contactId == id || User.IsInRole("USER:ADMIN"))
      {
        try
        {
          var response = await _watchlistService.GetWatchlistWithAdverts(id, cancellationToken);
          return Ok(response);
        }
        catch (Exception ex) { return BadRequest(ex); }
      }

      return BadRequest();
    }

    [HttpGet("summary")]
    [Authorize(Roles = "ADMIN, GOD, POWER_USER")]
    public async Task<IActionResult> GetWatchlistSummary([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<WhosWhoAdminSearchDTO>(query);

      var res = await _watchlistService.GetWatchlistSummary(dto, cancellationToken);
      return Ok(res);
    }

    [HttpGet("search")]
    [Authorize(Roles = "ADMIN, GOD")]
    public async Task<IActionResult> SearchWatchlists([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = JsonConvert.DeserializeObject<WatchlistSearchDTO>(query);

        var actions = await _watchlistService.Search(dto, cancellationToken);
        return Ok(actions);
      }
      catch (Exception ex)
      {
        return BadRequest(ex.Message);
      }
    }
  }
}
