﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO
{
  public class BidSearchDTO : BaseSearchDTO
  {
    public BidSearchFiltersDTO Filters { get; set; } = new BidSearchFiltersDTO() { };

  }

  public class BidSearchFiltersDTO
  {
    public uint? Id { get; set; }
    public List<BidStatusEnum> BidStatuses { get; set; } = new List<BidStatusEnum>();
    public BidStatusEnum? BidStatus { get; set; }
    public bool Sent { get; set; } // if false then return incoming (received) bids
    public BidViewTypeEnum BidViewTypeEnum { get; set; }
    public bool? CountOnly { get; set; }
    public Guid? AdvertId { get; set; }
    public bool? PendingOnly { get; set; }
    public Guid? ContactId { get; set; }
    public Guid? BidGuid { get; set; }
    public bool? IsOffer { get; set; }
    public uint? MaxDays { get; set; }
    public bool? CounterOffer { get; set; }
    public uint? DaysAgo { get; set; }
    public bool? OfferNotExpired { get; set; }
    public OfferStatusEnum? OfferStatus { get; set; }
    public List<uint> BidIds { get; set; }

    // We don't use CustomerID as it's ambiguous in a Bid Search
    // is it the bidder or the seller customer, use dto.CurrentCustomer and sent flag
    // public Guid? CustomerId { get; set; }
    public Guid? SellerCustomerId { get; set; }
    public Guid? BidderCustomerId { get; set; }
    public AdvertStatusEnum? AdvertStatus { get; set; }
    public Guid? AssignedTo { get; set; }
  }
}
