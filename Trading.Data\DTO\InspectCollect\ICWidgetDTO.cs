﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICWidgetDTO : BaseModelEntityDTO
  {
    [MaxLength(50)]
    public string Name { get; set; }
    public string Description { get; set; }

    public Guid? ICContainerGroupId { get; set; }

    [MaxLength(100)]
    public string WidgetType { get; set; }
    public ICStyleDTO? ICStyle { get; set; }
    public Guid? ICStyleId { get; set; }
  }

  public class ICWidgetSearchDTO : BaseSearchDTO
  {
    public ICWidgetSearchFilters Filters { get; set; } = new ICWidgetSearchFilters();
  }

  public class ICWidgetSearchFilters : BaseFilter
  {
    public string Name { get; set; }
    public Guid? ICContainerGroupId { get; set; }
  }

  public class ICWidgetCreateDTO
  {
    public string Name { get; set; }  
    public string WidgetType { get; set; }
  }
}
