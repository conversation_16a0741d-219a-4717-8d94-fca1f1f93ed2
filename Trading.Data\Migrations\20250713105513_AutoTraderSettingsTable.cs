﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Trading.API.Data.Migrations
{
    /// <inheritdoc />
    public partial class AutoTraderSettingsTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ICAutoTraderSetting",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "binary(16)", nullable: false),
                    ICContainerGroupId = table.Column<Guid>(type: "binary(16)", nullable: false),
                    DealerName = table.Column<string>(type: "varchar(128)", maxLength: 128, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    DealerId = table.Column<string>(type: "varchar(12)", maxLength: 12, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    HasExtendedMetricsLicense = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    StatusId = table.Column<uint>(type: "int unsigned", nullable: false),
                    Added = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    Updated = table.Column<DateTime>(type: "datetime(6)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ICAutoTraderSetting", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ICAutoTraderSetting_ICContainerGroup_ICContainerGroupId",
                        column: x => x.ICContainerGroupId,
                        principalTable: "ICContainerGroup",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ICAutoTraderSetting_Status_StatusId",
                        column: x => x.StatusId,
                        principalTable: "Status",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_ICAutoTraderSetting_ICContainerGroupId",
                table: "ICAutoTraderSetting",
                column: "ICContainerGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_ICAutoTraderSetting_StatusId",
                table: "ICAutoTraderSetting",
                column: "StatusId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ICAutoTraderSetting");
        }
    }
}
