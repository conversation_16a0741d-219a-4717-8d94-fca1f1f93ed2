﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Models;

namespace Trading.API.Data.DTO.UInspections
{
  public class UInspectDTO : BaseModelEntityDTO
  {
    public string ResultData { get; set; } // json
    public UInspectFormatDTO UInspectFormat { get; set; }
    public uint UInspectFormatId { get; set; }

    public string CustomerName { get; set; }

    public string MobileNumber { get; set; }

    public string Email { get; set; }

    public string VRM { get; set; }

    public string VehicleDesc { get; set; }

    public string ExternalRef { get; set; }

    public DateTime? Opened { get; set; }
    public DateTime? Completed { get; set; }

    public string IPAddress { get; set; }
    public float Longitude { get; set; }
    public float Latitude { get; set; }
  }
  public class UInspectWithAnswerDTO : UInspectDTO
  {
    public UInspectFormatWithAnswerDTO UInspectFormat { get; set; }

  }
}
