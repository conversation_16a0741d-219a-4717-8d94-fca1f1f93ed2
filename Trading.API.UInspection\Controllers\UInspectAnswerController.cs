﻿using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.UInspections;
using Trading.API.Data.Models.UInspections;
using Trading.Services.UInspections.Interfaces;

namespace Trading.API.UInspection.Controllers
{
  [Route("api/uinspect/answer")]
  [ApiController]
  public class UInspectAnswerController : ControllerBase
  {
    private readonly IUInspectAnswerService _uInspectAnswerService;

    public UInspectAnswerController(IUInspectAnswerService uInspectAnswerService)
    {
      _uInspectAnswerService = uInspectAnswerService;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<IActionResult> Get(uint id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = new UInspectAnswerSearchDTO();
        
        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<UInspectAnswerSearchDTO>(query);
        }

        var result = await _uInspectAnswerService.Get(id, cancellationToken, dto);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/uinspect/answers")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = new UInspectAnswerSearchDTO();
        
        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<UInspectAnswerSearchDTO>(query);
        }

        var result = await _uInspectAnswerService.Search(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create([FromBody] UInspectAnswerDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        var result = await _uInspectAnswerService.Create(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(uint id, JsonPatchDocument<UInspectAnswer> patch, CancellationToken cancellationToken)
    {
      try
      {
        var result = await _uInspectAnswerService.Patch(id, patch, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPut]
    [Route("/api/uinspect/{uInspectId}/question/{uInspectQuestionId}/answer")]
    public async Task<IActionResult> Put(Guid uInspectId, Guid uInspectQuestionId, [FromBody] UInspectAnswerDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        dto.AnswerDate = DateTime.Now;
        dto.IPAddress = HttpContext.Connection.RemoteIpAddress.ToString();
        dto.UInspectId = uInspectId;
        dto.UInspectQuestionId = uInspectQuestionId;

        var result = await _uInspectAnswerService.Put(dto, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
