﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.Search.LeadCRM
{
  public class LeadCustomerSearchDTO : BaseSearchDTO
  {
    public LeadCustomerFilters Filters { get; set; } = new LeadCustomerFilters() { };
  }

  public class LeadCustomerFilters : BaseFilterGuid
  {
    public string Name { get; set; }
    public string Email { get; set; }
    public string Phone { get; set; }
    public string Mobile { get; set; }
    public string Postcode { get; set; }
    public Guid? CustomerId { get; set; }

  }
}
