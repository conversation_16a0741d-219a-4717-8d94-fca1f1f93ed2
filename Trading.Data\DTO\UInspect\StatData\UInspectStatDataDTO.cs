﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.UInspect.StatData
{
  public class UInspectStatDataDTO
  {
    public DateTime Date { get; set; }

    // count per appraisal format
    public List<UInspectStatDTO> Requested { get; set; }
    public List<UInspectStatDTO> Opened { get; set; }
    public List<UInspectStatDTO> Completed { get; set; }
  }
}
