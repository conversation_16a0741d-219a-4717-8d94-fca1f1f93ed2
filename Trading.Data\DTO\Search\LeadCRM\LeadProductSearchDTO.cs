﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.DTO.LeadCRM;

namespace Trading.API.Data.DTO.Search.LeadCRM
{
  public class LeadSearchDTO : BaseSearchDTO
  {
    public uint? LeadStatusId { get; set; }
    public Guid? OwnerId { get; set; }
    public string SearchString { get; set; }

    public LeadSearchFilters Filters { get; set; } = new LeadSearchFilters() { };
  }


  public class LeadSearchFilters : BaseFilterGuid
  {
    public bool? ActiveOnly { get; set; }
    public Guid? LeadCustomerId { get; set; }
    public uint? LeadProductId { get; set; }
    public uint? LeadStatusId { get; set; }
    public Guid? CampaignId { get; set; }
    public Guid? OwnerId { get; set; }
    public string SearchString { get; set; }
  }
  public class LeadQueueSearchDTO : BaseSearchDTO
  {
    public LeadQueueSearchFilters Filters { get; set; } = new LeadQueueSearchFilters() { };
  }
  public class LeadQueueSearchFilters : BaseFilterGuid
  {
    public Guid? ContactId { get; set; }
  }

  public class LeadsByOwnerDTO {
    public Guid? OwnerId;
    public uint? LeadCount;
    public IEnumerable<LeadDTO> Leads;
    public ContactDTO Owner;

  }

}
