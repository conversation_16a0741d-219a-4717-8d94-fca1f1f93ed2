﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.LeadCRM;
using Trading.API.Data.Models.LeadCRM;
using Trading.Services.Extensions;
using Trading.Services.LeadCRM.Interfaces;

namespace Trading.API.LeadCRM.Controllers
{
  [Route("api/lead-event-doc-template")]
  [ApiController]
  [Authorize]
  public class LeadEventDocTemplateController : ControllerBase
  {
    private readonly ILeadEventDocTemplateService _leadEventDocTemplateService;

    public LeadEventDocTemplateController(ILeadEventDocTemplateService leadEventDocTemplateService)
    {
      this._leadEventDocTemplateService = leadEventDocTemplateService;
    }

    //[ResponseCache(Duration = 60)]
    [HttpGet]
    [Route("/api/lead-event-doc-templates")]
    public async Task<IActionResult> Search([FromQuery] string? query, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = new LeadEventDocTemplateSearchDTO();
        
        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<LeadEventDocTemplateSearchDTO>(query);
        }

        var result = await _leadEventDocTemplateService.Search(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create([FromBody] LeadEventDocTemplateDTO dto, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadEventDocTemplateService.Create(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<IActionResult> Get(
      uint leadEventDocTemplateId, 
      [FromQuery] string query, 
      CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = JsonConvert.DeserializeObject<LeadEventDocTemplateSearchDTO>(query);
        var result = await _leadEventDocTemplateService.Get(leadEventDocTemplateId, dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(uint id, JsonPatchDocument<LeadEventDocTemplate> patch, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadEventDocTemplateService.Patch(id, patch, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<IActionResult> Delete(uint id, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadEventDocTemplateService.Delete(id, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
