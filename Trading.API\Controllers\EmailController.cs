using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Trading.API.Data;
using Trading.API.Data.Models;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/[controller]")]
  [ApiController]
  public class EmailController : ControllerBase
  {
    private readonly IEmailService _emailService;

    public EmailController(IEmailService emailService)
    {
      _emailService = emailService;
    }

    [HttpGet]
    [Route("test")]
    [ApiExplorerSettings(IgnoreApi = true)] // won't show in swagger doc 
    public async Task<IActionResult> SendTestEmail(CancellationToken cancellationToken)
    {
      var ok = await _emailService.TestSendEmail(cancellationToken);
      return Ok();
    }
  }
}
