﻿using AutoMapper;
using Dapper;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Migrations.Operations;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Components.AdvertSearch;
using Trading.API.Data.DTO.Components.AdvertView;
using Trading.API.Data.DTO.Components.MainDashboard;
using Trading.API.Data.DTO.Export;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.Services.CSVClassMaps;
using Trading.Services.Extensions;
using Trading.Services.ExternalDTO;
using Trading.Services.Helpers;
using Trading.Services.Interfaces;
using static Trading.Services.DTOMappingProfiles.MappingProfile;

namespace Trading.Services.Classes
{
  public class AdvertService : IAdvertService
  {
    private readonly TradingContext _tradingContext;
    private readonly IMapper _mapper;
    private readonly IBidService _bidService;
    private readonly IContactService _contactService;
    private readonly ICustomerService _customerService;
    private readonly ILookupService _lookupService;
    private readonly IMessageService _messageService;
    private readonly IUserService _userService;
    private readonly IDbConnection _db;
    private readonly IContactActionService _contactActionService;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly ICustomerInternalInfoService _customerInternalInfoService;
    private readonly IServiceQueueService _serviceQueueService;
    private readonly IVehicleCheckService _vehicleCheckService;

    private struct ECodes
    {
      public static ExceptionData NoAdvert = new ExceptionData("AD1", "Could not find specified advert");
      public static ExceptionData WrongContact = new ExceptionData("AD2", "Cannot update advert with this contact");
      public static ExceptionData HasBids = new ExceptionData("AD3", "Cannot update an advert that has bids on it");
      public static ExceptionData WrongCustomer = new ExceptionData("AD4", "Cannot update advert for this customer");
      public static ExceptionData NoPrice = new ExceptionData("AD5", "BuyItNow type Advert requires BuyItNow or ReservePrice to be > 0");
      public static ExceptionData CantRelist = new ExceptionData("AD6", "Advert is not able to be relisted");

      public static ExceptionData ReservePriceMet =
        new ExceptionData("AD7", "Cannot end this sale, the reserve price has been met");

      public static ExceptionData SaleNotLive = new ExceptionData("AD8", "The sale is not live");

      public static ExceptionData CantRelistDeal =
        new ExceptionData("AD9", "Cant relist advert, there is a paid deal associated with it");

      public static ExceptionData NoVAT = new ExceptionData("AD10", "Vehicle has no VAT status");
      public static ExceptionData NoImages = new ExceptionData("AD11", "Vehicle has no images");

      public static ExceptionData AlreadyPublished = new ExceptionData("AD12", "Advert is already published");
      public static ExceptionData ExistingVehicleAdvert = new ExceptionData("AD13", "Vehicle is part of an existing advert");
    }

    public AdvertService(
      TradingContext tradingContext,
      //IContactActionService contactActionService,
      IBidService bidService,
      IMapper mapper,
      IContactService contactService,
      ICustomerService customerService,
      ILookupService lookupService,
      IUserService userService,
      IMessageService messageService,
      IDbConnection dbConnection,
      IContactActionService contactActionService,
      IServiceScopeFactory serviceScopeFactory,
      ICustomerInternalInfoService customerInternalInfoService,
      IServiceQueueService serviceQueueService,
      IVehicleCheckService vehicleCheckService
      )
    {
      _tradingContext = tradingContext;
      _mapper = mapper;
      _bidService = bidService;
      _messageService = messageService;
      _contactService = contactService;
      _customerService = customerService;
      _lookupService = lookupService;
      _userService = userService;
      _db = dbConnection;
      _contactActionService = contactActionService;
      _serviceScopeFactory = serviceScopeFactory;
      _customerInternalInfoService = customerInternalInfoService;
      _serviceQueueService = serviceQueueService;
      _vehicleCheckService = vehicleCheckService;
    }

    public async Task<bool> CanAccessAdvert(Guid advertId, CancellationToken ct)
    {
      // if we're a super admin we can view it
      if (_userService.IsGod())
      {
        return true;
      }

      var advert = await _tradingContext.Adverts.FirstOrDefaultAsync(x => x.Id == advertId);
      return advert != null && _userService.GetCustomerId() == advert.CustomerId;
    }

    public async Task<AdvertDTO> UpdateAdvert(Guid contactId, Guid customerId, AdvertDTO advertDTO,
      CancellationToken cancellationToken)
    {
      // test
      // throw new Exception(ExceptionHelper.Conflict(ECodes.HasBids));

      if (customerId != advertDTO.CustomerId)
      {
        throw new Exception(ExceptionHelper.Forbidden(ECodes.WrongCustomer));
      }

      /*
      if (customerId != advertDTO.CustomerId)
      {
        throw new Exception(ExceptionHelper.Forbidden(ECodes.WrongContact));
      }
      */

      // if advert is published or has bids on it, return 
      if (advertDTO.BidCount > 0)
      {
        throw new Exception(ExceptionHelper.Conflict(ECodes.HasBids));
      }

      advertDTO.Updated = DateTime.Now;
      var entity = _mapper.Map<AdvertDTO, Advert>(advertDTO);
      _tradingContext.Entry(entity).State = EntityState.Modified;

      // set the price range id 
      entity.PriceRangeId = await _lookupService.GetPriceRangeId(advertDTO.BuyItNowPrice.Value, cancellationToken);

      await _tradingContext.SaveChangesAsync(cancellationToken);

      return _mapper.Map<Advert, AdvertDTO>(entity);
    }

    public async Task<AdvertDTO> PublishAdvert(Guid contactId, Guid advertId, CancellationToken cancellationToken)
    {
      var advert = await _tradingContext.Adverts
        .Include(x => x.Vehicle).ThenInclude(x => x.VehicleMedia)
        .Include(x => x.Sale)
        .FirstOrDefaultAsync(x => x.Id == advertId, cancellationToken);

      if (advert == null)
      {
        throw new Exception(ExceptionHelper.NotFound(ECodes.NoAdvert));
      }

      if (advert.SoldStatus == SoldStatusEnum.Active && advert.AdvertStatus == AdvertStatusEnum.Active)
      {
        throw new Exception(ExceptionHelper.Conflict(ECodes.AlreadyPublished));
      }

      var adCheck = await GetNonSphinxAdvertBaseQuery()
        .FirstOrDefaultAsync(x => x.VehicleId == advert.VehicleId && x.AdvertStatus == AdvertStatusEnum.Active, cancellationToken);

      if (adCheck != null)
      {
        throw new Exception(ExceptionHelper.Forbidden(ECodes.ExistingVehicleAdvert));
      }

      // cannot publish advert without requisite fields having values
      // check pricing 
      // note: underwrites do not require pricing details 
      if (advert.Sale.SaleTypeId == (uint)SaleTypeEnum.BuyNow
        && (!advert.BuyItNowPrice.HasValue || advert.BuyItNowPrice == 0) && (advert.ReservePrice ?? 0) == 0)
      {
        throw new Exception(ExceptionHelper.NotFound(ECodes.NoPrice));
      }

      // the advert has no VAT status
      if (!advert.Vehicle.VatStatusId.HasValue)
      {
        throw new Exception(ExceptionHelper.NotFound(ECodes.NoVAT));
      }

      // the advert has no images 
      if (advert.Vehicle.VehicleMedia == null || advert.Vehicle.VehicleMedia.Count == 0)
      {
        throw new Exception(ExceptionHelper.NotFound(ECodes.NoImages));
      }

      /*
      if (contactId != advert.ContactId)
      {
        throw new Exception(ExceptionHelper.Forbidden(ECodes.WrongContact));
      }
      */


      // if the vehicle has not been part of an existing advert, add valuation, provenance and ai checks to service queue 
      await AddMissingVehicleChecksToQueue(advert.VehicleId.Value, cancellationToken);

      // use defaults for startPrice and bidIncrement if not specified 
      advert.StartPrice ??= 100;
      advert.BidIncrement ??= 100;
      advert.ReservePrice ??= 0;

      advert.AdvertStatus = AdvertStatusEnum.Active;
      advert.SoldStatus = SoldStatusEnum.Active;
      advert.Updated = DateTime.Now;
      await _tradingContext.SaveChangesAsync(cancellationToken);

      // Update Customer with Last Advert Date
      await _customerService.SetLastAdvert(advert.CustomerId, DateTime.Now, cancellationToken);

      // Update Customer with a Count of Adverts
      await _customerService.UpdateAdvertTotals(advert.CustomerId, DateTime.Now, cancellationToken);
      await _contactActionService.RecordAction(ContactActionEnum.PublishAdvert, advertId, cancellationToken);
      await _customerInternalInfoService.SetAdvertPlaced(advert.CustomerId, cancellationToken);

      var advertDTO = _mapper.Map<Advert, AdvertDTO>(advert);
      return advertDTO;
    }

    private async Task AddMissingVehicleChecksToQueue(Guid vehicleId, CancellationToken cancellationToken)
    {
      var checkTypes = new[]
      {
        (VehicleCheckTypeEnum.Valuation, ServiceQueueTypeEnum.Valuation),
        (VehicleCheckTypeEnum.Provenance, ServiceQueueTypeEnum.Provenance),
        (VehicleCheckTypeEnum.AI, ServiceQueueTypeEnum.AIValuation)
      };

      foreach (var (checkType, queueType) in checkTypes)
      {
        if (!await _vehicleCheckService.HasVehicleCheck(vehicleId, checkType, cancellationToken))
        {
          await _serviceQueueService.AddToQueue(queueType, vehicleId, null);
        }
      }
    }

    public async Task<AdvertDTO> CreateAdvert(CreateAdvertDTO advertDTO, CancellationToken cancellationToken)
    {
      var advert = await GetNonSphinxAdvertBaseQuery()
        .FirstOrDefaultAsync(x => x.VehicleId == advertDTO.VehicleId && x.AdvertStatus == AdvertStatusEnum.Active, cancellationToken);

      if (advert != null)
      {
        if (advertDTO.CustomerId != advert.CustomerId)
        {
          throw new ApplicationException("Advert already exists for a different customer");
        }

        return _mapper.Map<Advert, AdvertDTO>(advert);
      }

      // get 'timed auction' sale 
      // PM: saleId now defaults to null, to allow viewing an imported vehicle via sphUnlotted
      // PM (07/03/2023) - defaulting saleId to timed auction now, if a vehicle needs to be lotted 
      // then the sale type has to be changed to ManagedSale, and the chosen sale set to None 
      var defaultSale = await _tradingContext.Sales.FirstOrDefaultAsync(x => x.SaleTypeId == (uint)SaleTypeEnum.TimedAuction);
      if (defaultSale == null && !advertDTO.SaleId.HasValue)
      {
        throw new ApplicationException("Could not find 'Timed Auction' sale record");
      }

      // set addressId on advertDTO from contact
      var addressId = advertDTO.ContactAddressId;
      if (!addressId.HasValue)
      {
        addressId = await _contactService.GetPrimaryAddressId(advertDTO.ContactId, cancellationToken);
      }

      var ad = new Advert()
      {
        CustomerId = advertDTO.CustomerId,
        ContactId = advertDTO.ContactId,
        SaleId = advertDTO.SaleId ?? defaultSale.Id,
        VehicleId = advertDTO.VehicleId,
        AddressId = addressId,
        BuyItNowPrice = 0,
        StatusId = (uint)StatusEnum.Active,
        AdvertStatus = AdvertStatusEnum.Inactive,
        SoldStatus = SoldStatusEnum.Draft,
        AvailableDate = DateTime.Now,
        EndDateTime = DateTime.Now.AddDays(7),
        StartPrice = 100,
        Added = DateTime.Now,
        Updated = DateTime.Now,
        ReservePrice = advertDTO.ReservePrice > 0 ? advertDTO.ReservePrice : null,
      };


      _tradingContext.Adverts.Add(ad);
      await _tradingContext.SaveChangesAsync(cancellationToken);

      await _contactActionService.RecordAction(ContactActionEnum.CreateAdvert, ad.Id, cancellationToken);

      // return advert without vehicle after creation (vehicle is handled separately)

      advert = await _tradingContext.Adverts.AsNoTracking().Where(x => x.Id == ad.Id)
        // select only the basic advert properties 
        //.Select(x => GetBasicAdvert(x))
        .FirstOrDefaultAsync(cancellationToken);

      // create sphlink record to link advert and sphadvert
      var sphLink = new SphLink()
      {
        AdvertId = ad.Id
      };
      _tradingContext.SphLinks.Add(sphLink);
      await _tradingContext.SaveChangesAsync();

      var dto = _mapper.Map<Advert, AdvertDTO>(advert);
      return dto;
    }

    private static Advert GetBasicAdvert(Advert x)
    {
      return new Advert()
      {
        AcceptBids = x.AcceptBids,
        Added = x.Added,
        AdvertStatus = x.AdvertStatus,
        AutoAcceptBid = x.AutoAcceptBid,
        Sale = SaleService.GetBasicSale(x.Sale)
      };
    }

    public async Task<IEnumerable<Advert>> LiveSaleUpcomingLots(Guid saleId, int limit, CancellationToken ct)
    {
      var query = _tradingContext.Adverts
          .Include(x => x.Vehicle.Make)
          .Include(x => x.Vehicle.Model)
          .Include(x => x.Vehicle.Deriv)
          .Include(x => x.Vehicle.FuelType)
          .Include(x => x.Vehicle.BodyType)
          .Include(x => x.Vehicle.Plate)
          .Include(x => x.Vehicle.TransmissionType)
          .AsNoTracking()
          .Where(x => x.SaleId == saleId)
        ;

      query = UpcomingLotsWhere(query);

      var adverts = await query
        .OrderBy(x => x.LotSeq)
        .Take(limit)
        .ToListAsync();

      // We're returning Adverts so we can parse it into the DTO of our choice after
      return adverts;
    }

    public async Task<IEnumerable<Advert>> LiveSalePreviousLots(Guid saleId, uint currentLotSeq, int limit,
      CancellationToken ct)
    {
      var query = _tradingContext.Adverts
          .Include(x => x.Vehicle.Make)
          .Include(x => x.Vehicle.Model)
          .Include(x => x.Vehicle.Deriv)
          .Include(x => x.Vehicle.FuelType)
          .Include(x => x.Vehicle.BodyType)
          .Include(x => x.Vehicle.Plate)
          .Include(x => x.Vehicle.TransmissionType)
          .AsNoTracking()
          .Where(x => x.SaleId == saleId && x.LotSeq < currentLotSeq)
        ;

      var adverts = await query
        .OrderByDescending(x => x.LotSeq)
        .Take(limit)
        .ToListAsync();

      // We're returning Adverts so we can parse it into the DTO of our choice after
      return adverts;
    }

    private IQueryable<Advert> UpcomingLotsWhere(IQueryable<Advert> query)
    {
      return query
        .Where(x => x.StatusId == (int)StatusEnum.Active)
        .Where(x => x.AdvertStatus == AdvertStatusEnum.Active)
        .Where(x => x.SoldStatus == SoldStatusEnum.Active)
        .Where(x => x.EndDateTime >= DateTime.Now);
    }

    public async Task<IEnumerable<AdvertOfInterestDTO>> GetAdvertsOfInterest(Guid contactId,
      CancellationToken cancellationToken)
    {
      // just return random adverts for now
      // todo: replace this with real algorithm
      var adverts = _tradingContext.SphAdvert
        .Include("SphLink.Advert.Vehicle")
        .Include("SphLink.Advert.Vehicle.Make")
        .Include("SphLink.Advert.Vehicle.Model")
        .Include("SphLink.Advert.Vehicle.Deriv")
        .Include("SphLink.Advert.Vehicle.BodyType")
        .Include("SphLink.Advert.Vehicle.FuelType")
        .Include("SphLink.Advert.Vehicle.TransmissionType")
        .Include("SphLink.Advert.Vehicle.Plate")
        .Include("SphLink.Advert.Vehicle.VehicleColour")
        .Include("SphLink.Advert.Vehicle.Status")
        .Include("SphLink.Advert.Vehicle.VatStatus")
        .Where(x => x.phrase == ";maxmatches=999999;limit=999999"
            && x.SphLink.Advert.StatusId == (int)StatusEnum.Active)
        .OrderBy(i => EF.Functions.Random())
        .Select(x => x.SphLink.Advert)
        .AsNoTracking();

      var t = await adverts.Take(4).ToListAsync();

      var dtos = _mapper.Map<IEnumerable<Advert>, IEnumerable<AdvertOfInterestDTO>>(adverts);

      return dtos;
    }


    private IQueryable<Advert> GetAdvertBaseQuery(string phrase)
    {
      var adverts = _tradingContext.SphAdvert
        .Include("SphLink.Advert.Sale.Address")
        .Include("SphLink.Advert.Sale.SaleType")
        .Include("SphLink.Advert.Vehicle.Address")
        .Include("SphLink.Advert.Vehicle.Make")
        .Include("SphLink.Advert.Vehicle.Model")
        .Include("SphLink.Advert.Vehicle.Deriv")
        .Include("SphLink.Advert.Vehicle.BodyType")
        .Include("SphLink.Advert.Vehicle.FuelType")
        .Include("SphLink.Advert.Vehicle.TransmissionType")
        .Include("SphLink.Advert.Vehicle.Plate")
        .Include("SphLink.Advert.Vehicle.VehicleType")
        .Include("SphLink.Advert.Vehicle.VehicleColour")
        .Include("SphLink.Advert.Vehicle.Status")
        .Include("SphLink.Advert.Vehicle.VatStatus")
        .Where(x => x.phrase == phrase && x.SphLink.Advert.StatusId == (int)StatusEnum.Active)
        .Select(x => x.SphLink.Advert)
        .AsNoTracking();

      return adverts;
    }

    private IQueryable<Advert> GetNonSphinxAdvertBaseQuery(BaseSearchDTO filters = null)
    {
      var query = _tradingContext.Adverts
        .Where(x => x.StatusId == (uint)StatusEnum.Active && x.SoldStatus == SoldStatusEnum.Active &&
                    x.AdvertStatus == AdvertStatusEnum.Active)
        .AsQueryable();

      if (filters != null)
      {
        query = query.BuildComponentQuery(filters);
      }

      var adverts = query.AsNoTracking();

      return adverts;
    }

    public async Task<ValidatedResultDTO<AdvertDTO>> GetAdvert(Guid advertId, AdvertSearchDTO searchDTO,
      CancellationToken cancellationToken)
    {
      if (searchDTO == null)
      {
        searchDTO = new AdvertSearchDTO();
      }

      var response = new ValidatedResultDTO<AdvertDTO>() { IsValid = false };

      searchDTO.Filters.Id = advertId;

      var ads = await Search(searchDTO, cancellationToken);

      var ad = ads.Advert.Id.HasValue ? ads.Advert : ads.Adverts.FirstOrDefault();

      // QUESTION.. DO WE WANT TO MAKE THIS TRIGGERED BY A SWITCH FOR PERFORMANCE ? YES
      if (ad != null && ad.Bids != null && !searchDTO.Filters.ExcludeBidSummary)
      {
        ad.BidsSummary = _bidService.BidsSummary(ad.Bids);
      }

      if (ad != null)
      {
        response.DTO = ad;
        response.IsValid = true;
      }

      return response;
    }

    public async Task<ValidatedResultDTO<AdvertViewDataDTO>> GetAdvertViewData(Guid advertId, AdvertSearchDTO searchDTO,
  CancellationToken cancellationToken)
    {
      if (searchDTO == null)
      {
        searchDTO = new AdvertSearchDTO();
      }

      var response = new ValidatedResultDTO<AdvertViewDataDTO>() { IsValid = false };

      searchDTO.Filters.Id = advertId;

      var ads = await Search(searchDTO, cancellationToken);

      if (ads.AdvertViewData != null)
      {
        response.DTO = ads.AdvertViewData;
        response.IsValid = true;
      }

      return response;
    }

    private async Task AddViewCount(Guid advertId, CancellationToken cancellationToken)
    {
      // save via Dapper for speed
      var updateParams = new
      {
        advertId
      };

      var sql = "UPDATE `Advert` Set ViewCount = ViewCount + 1 Where Id = @advertId;";

      _ = Task.Run(() =>
      {
        _serviceScopeFactory.FireAndForget(sql, updateParams);
      });
    }

    public async Task<int> GetSellerLiveCount(Guid? CustomerId, CancellationToken cancellationToken)
    {
      var x1 = await Search(new AdvertSearchDTO()
      {
        CountOnly = true,
        Component = "SellerHub",
        Filters = { CustomerId = CustomerId, SoldStatus = SoldStatusEnum.Active }
      }, cancellationToken);

      return x1.Count ?? 0;
    }

    public async Task<SellerCountsResultDTO> SellerCounts(AdvertSearchDTO searchDTO, CancellationToken cancellationToken)
    {
      var response = new SellerCountsResultDTO()
      {
        Counts = new Dictionary<uint, uint>
        {
          [(int)SoldStatusEnum.Active] = 0,
          [(int)SoldStatusEnum.Sold] = 0,
          [(int)SoldStatusEnum.Provisional] = 0,
          [(int)SoldStatusEnum.Unsold] = 0,
          [(int)SoldStatusEnum.Withdrawn] = 0,
        }
      };


      var liveCount = await GetSellerLiveCount(searchDTO.Filters.CustomerId, cancellationToken);
      response.Counts[(int)SoldStatusEnum.Active] = (uint)liveCount;

      var x3 = await Search(new AdvertSearchDTO()
      {
        CountOnly = true,
        Component = "SellerHub",
        Filters = { CustomerId = searchDTO.Filters.CustomerId, SoldStatus = SoldStatusEnum.Sold }
      }, cancellationToken);

      response.Counts[(int)SoldStatusEnum.Sold] = (uint)x3.Count;

      var x4 = await Search(new AdvertSearchDTO()
      {
        CountOnly = true,
        Component = "SellerHub",
        Filters = { CustomerId = searchDTO.Filters.CustomerId, SoldStatus = SoldStatusEnum.Provisional }
      }, cancellationToken);

      response.Counts[(int)SoldStatusEnum.Provisional] = (uint)x4.Count;

      var x5 = await Search(new AdvertSearchDTO()
      {
        CountOnly = true,
        Component = "SellerHub",
        Filters = { CustomerId = searchDTO.Filters.CustomerId, SoldStatus = SoldStatusEnum.Unsold }
      }, cancellationToken);

      response.Counts[(int)SoldStatusEnum.Unsold] = (uint)x5.Count;

      var x6 = await Search(new AdvertSearchDTO()
      {
        CountOnly = true,
        Component = "SellerHub",
        Filters = { CustomerId = searchDTO.Filters.CustomerId, SoldStatus = SoldStatusEnum.Withdrawn }
      }, cancellationToken);

      response.Counts[(int)SoldStatusEnum.Withdrawn] = (uint)x6.Count;

      var x7 = await Search(new AdvertSearchDTO()
      {
        CountOnly = true,
        Component = "SellerHub",
        Filters = { CustomerId = searchDTO.Filters.CustomerId, SoldStatus = SoldStatusEnum.Draft }
      }, cancellationToken);

      response.Counts[(int)SoldStatusEnum.Draft] = (uint)x7.Count;

      // TODO: Ideally we would just have a count returned to us
      var x8 = await _bidService.GetBidderOffers(searchDTO.Filters.CustomerId.Value, true, cancellationToken);
      response.Counts[99] = (uint)x8.Count();

      return response;
    }

    public async Task<AdvertSearchResultDTO> Search(AdvertSearchDTO searchDTO, CancellationToken cancellationToken)
    {
      var resultDTO = new AdvertSearchResultDTO() { };

      var obj = (object)searchDTO;
      IEnumerable<Advert> advertList = null;

      switch (searchDTO.Component)
      {
        case "AdvertAdminView":
          if (searchDTO.IsAdmin)
          {
            resultDTO = await AdvertAdminView(searchDTO, cancellationToken);
          }
          break;

        case "AdvertView":
          //AdvertDTO xyz = await AdvertView2(searchDTO.Filters.Id.Value, cancellationToken);
          //xyz.TopBidGuid = xyz.TopBid?.BidGuid;
          //var ad1 = _mapper.Map<AdvertDTO, AdvertDTO_Public>(xyz);
          //var ad2 = _mapper.Map<AdvertDTO_Public, AdvertDTO>(ad1);
          //resultDTO.Advert = ad2;

          // new pattern using component-based dtos
          var viewData = await GetAdvertViewData(searchDTO.Filters.Id.Value, cancellationToken);
          resultDTO.AdvertViewData = viewData;

          // record advert view for current contact
          await _contactActionService.RecordAction(ContactActionEnum.ViewAdvert, viewData.Id, cancellationToken);
          await AddViewCount(viewData.Id, cancellationToken);

          break;

        case "AdvertView_VehicleInfo":
          resultDTO = await AdvertViewVehicleInfo(searchDTO, cancellationToken);

          break;

        case "SellerHub":

          resultDTO = await SellerHubSearch(searchDTO, cancellationToken);
          break;

        case "AdminViewSaleLots":

          resultDTO = await AdminViewSaleLots(searchDTO, cancellationToken);
          break;

        case "AdvertEdit":

          if (!(_userService.IsAdminOrGreater() || searchDTO.CurrentCustomerId == searchDTO.Filters.CustomerId))
          {
            throw new Exception(ExceptionHelper.Forbidden(ECodes.WrongCustomer));
          }

          advertList = AdvertEdit.Invoke(_tradingContext, searchDTO.Filters.Id.Value,
            searchDTO.Filters.CustomerId.Value, _userService.IsGod());
          resultDTO.Advert = _mapper.Map<Advert, AdvertDTO>(advertList.FirstOrDefault());
          break;

        case "AdminViewSaleLottable":

          if (!(_userService.IsAdminOrGreater() || searchDTO.CurrentCustomerId == searchDTO.Filters.CustomerId))
          {
            throw new Exception(ExceptionHelper.Forbidden(ECodes.WrongCustomer));
          }

          var lottable = await AdminViewSaleLottable(searchDTO, cancellationToken);
          return lottable;


        case "AdvertDeal": // view advert deal info for inmail box
          resultDTO.Advert = await GetAdvertDeal(searchDTO.Filters.Id.Value, cancellationToken);
          break;

        case "AuctioneerUpcomingLots": // List all upcoming lots 
          var result =
            await LiveSaleUpcomingLots(searchDTO.Filters.SaleId.Value, searchDTO.Limit.Value, cancellationToken);
          return new AdvertSearchResultDTO()
          { Adverts = _mapper.Map<IEnumerable<Advert>, IEnumerable<AdvertDTO>>(result) };
          break;

        case "AuctioneerPreviousLots": // view advert deal info for inmail box
          resultDTO.Adverts = _mapper.Map<IEnumerable<Advert>, IEnumerable<AdvertDTO>>(
            await LiveSalePreviousLots(searchDTO.Filters.SaleId.Value, searchDTO.Filters.LotSeq.Value, 999,
              cancellationToken));
          break;

        case "BidderLiveSaleViewAdvert": // advert seen when viewing a live sale
          return await BidderLiveSaleViewAdvert(searchDTO.Filters.Id.Value, cancellationToken);
          break;

        case "AuctioneerLiveSaleViewAdvert": // advert seen when viewing a live sale
          return await AuctioneerLiveSaleViewAdvert(searchDTO.Filters.Id.Value, cancellationToken);
          break;

        case "AdvertNegotiationView":
          var ad = AdvertNegotiationView.Invoke(_tradingContext, searchDTO.Filters.Id.Value);
          resultDTO.Advert = _mapper.Map<Advert, AdvertDTO>(ad);
          break;

        case "dotnet:CounterOfferResponse":
          var cor = CounterOfferResponseAdvert.Invoke(_tradingContext, searchDTO.Filters.Id.Value);
          resultDTO.Advert = _mapper.Map<Advert, AdvertDTO>(cor);
          break;

        case "AdvertVrmCheck":
          var vehAdCount = await _tradingContext.Adverts.CountAsync(x => x.Vehicle.Vrm == searchDTO.Filters.VRM
            && x.AdvertStatus == AdvertStatusEnum.Active && x.SoldStatus == SoldStatusEnum.Active);
          return new AdvertSearchResultDTO
          {
            Count = vehAdCount
          };

        case "dotnet:AdvertNote":
          advertList = BaseRecord.Invoke(_tradingContext, searchDTO.Filters.Id.Value);
          resultDTO.Adverts = _mapper.Map<IEnumerable<AdvertDTO>>(advertList);
          break;
        default:
          if (searchDTO.Filters.Id == null)
          {
            throw new Exception("You must supply Component and/or Component not found");
          }

          advertList = BaseRecord.Invoke(_tradingContext, searchDTO.Filters.Id.Value);
          resultDTO.Adverts =
            _mapper.Map<IEnumerable<AdvertDTO_Public>, IEnumerable<AdvertDTO>>(
              _mapper.Map<IEnumerable<Advert>, IEnumerable<AdvertDTO_Public>>(advertList));
          break;
      }

      return resultDTO;
    }

    private async Task<AdvertViewDataDTO> GetAdvertViewData(Guid id, CancellationToken cancellationToken)
    {
      var advert = await _tradingContext.Adverts
        .AsNoTracking()
        .Where(x => x.Id == id)
        .Select(x => new AdvertViewDataDTO
        {
          Id = id,
          StatusId = x.StatusId,
          CustomerId = x.CustomerId,
          AdvertStatus = x.AdvertStatus,
          SaleTypeId = x.Sale.SaleTypeId,
          MakeName = x.Vehicle.Make.MakeName,
          ModelName = x.Vehicle.Model.ModelName,
          DerivName = x.Vehicle.Deriv.DerivName,
          PlateName = x.Vehicle.Plate.PlateName,
          Vrm = x.Vehicle.Vrm,
          GGG = x.GGG,
          CurrentPrice = x.CurrentPrice,
          TopBidGuid = x.TopBidGuid,
          Description = x.Description,
          FuelTypeName = x.Vehicle.FuelType.FuelTypeName,
          TransmissionTypeName = x.Vehicle.TransmissionType.TransmissionTypeName,
          BodyTypeName = x.Vehicle.BodyType.BodyTypeName,
          EngineCc = x.Vehicle.EngineCc,
          LatestVehicleFaultCheckId = x.Vehicle.LatestVehicleFaultCheckId,
          Odometer = x.Vehicle.Odometer,
          Runner = x.Vehicle.Runner,
          Doors = x.Vehicle.Doors,
          Colour = x.Vehicle.VehicleColour.ColourName,
          LogBook = x.Vehicle.LogBook,
          Owners = x.Vehicle.Owners,
          ServiceHistoryType = x.Vehicle.ServiceHistoryType,
          VATStatusName = x.Vehicle.VatStatus.AttribvalName,
          V5StatusName = x.Vehicle.V5Status.AttribvalName,
          LocationPostcode = x.Address.AddressPostcode
        })
        .FirstOrDefaultAsync(cancellationToken);

      return advert;
    }

    public async Task<bool> BroadcastAuctioneerUpcomingLots(Guid saleId, int total, CancellationToken ct)
    {
      var upcomingLots = await LiveSaleUpcomingLots(saleId, total, ct);

      var dtos = _mapper.Map<IEnumerable<Advert>, IEnumerable<AdvertDTO>>(upcomingLots);

      await _messageService.SendAuctioneerMessage(saleId, MessageAreaEnum.Auctioneer, MessageTypeEnum.UpcomingLots,
        dtos);

      return true;
    }

    public async Task<bool> BroadcastAuctioneerPreviousLots(Guid saleId, uint currentLotSeq, int total,
      CancellationToken ct)
    {
      var previousLots = await LiveSalePreviousLots(saleId, currentLotSeq, total, ct);

      var dtos = _mapper.Map<IEnumerable<Advert>, IEnumerable<AdvertDTO>>(previousLots);

      await _messageService.SendAuctioneerMessage(saleId, MessageAreaEnum.Auctioneer, MessageTypeEnum.PreviousLots,
        dtos);

      return true;
    }

    public async Task<bool> ResetLiveSaleLot(Guid advertId, CancellationToken ct)
    {
      // TODO: Should we only be able to do this if the lot hasn't been bought ??

      var advert = await Get(advertId, ct);

      advert.EndDateTime = DateTime.Now.AddDays(1);
      advert.StatusId = (int)StatusEnum.Active;
      advert.SoldStatus = SoldStatusEnum.Active;
      advert.AdvertStatus = AdvertStatusEnum.Active;
      advert.CurrentPrice = 0;
      advert.IsSold = false;
      advert.BidCount = 0;
      advert.SoldToId = null;
      advert.TopBidId = null;

      _tradingContext.Adverts.Update(advert);

      var bids = _tradingContext.Bids.Where(x => x.AdvertId == advertId);

      _tradingContext.Bids.RemoveRange(bids);

      await _tradingContext.SaveChangesAsync(ct);

      return true;
    }

    private async Task<AdvertSearchResultDTO> AdminViewSaleLottable(AdvertSearchDTO dto,
      CancellationToken cancellationToken)
    {
      var query = _tradingContext.Adverts
        .Include(x => x.Vehicle.Address)
        .Include(x => x.Vehicle.Make)
        .Include(x => x.Vehicle.Model)
        .Include(x => x.Vehicle.Deriv)
        .Include(x => x.Vehicle.Customer)
        .Include(x => x.Vehicle.LatestValuation)
        .AsQueryable();

      if (dto.Filters?.CustomerId != null)
      {
        query = query.Where(x => x.CustomerId == dto.Filters.CustomerId);
      }

      if (dto.Filters?.AddressId != null)
      {
        query = query.Where(x => x.Vehicle.AddressId == dto.Filters.AddressId);
      }

      if (dto.Offset != null)
      {
        query = query.Skip(dto.Offset.Value).Take(dto.Limit.Value);
      }

      var adverts = await query
        .Where(x => x.SaleId == null)
        .AsNoTracking()
        .ToListAsync(cancellationToken);

      return new AdvertSearchResultDTO()
      { Adverts = _mapper.Map<IEnumerable<Advert>, IEnumerable<AdvertDTO>>(adverts) };
    }

    private async Task<AdvertDTO> GetAdvertDeal(Guid advertId, CancellationToken cancellationToken)
    {
      var contactId = _userService.GetContactId();

      var advert = await (from ad in _tradingContext.Adverts
                          join vehicle in _tradingContext.Vehicles on ad.VehicleId equals vehicle.Id
                          join make in _tradingContext.Makes on vehicle.MakeId equals make.Id
                          join model in _tradingContext.Models on vehicle.ModelId equals model.Id
                          join deriv in _tradingContext.Derivs on vehicle.DerivId equals deriv.Id
                          join fuelType in _tradingContext.FuelTypes on vehicle.FuelTypeId equals fuelType.Id
                          join bodyType in _tradingContext.BodyTypes on vehicle.BodyTypeId equals bodyType.Id
                          join transmissionType in _tradingContext.TransmissionTypes on vehicle.TransmissionTypeId equals transmissionType
                            .Id
                          join plate in _tradingContext.Plates on vehicle.PlateId equals plate.Id
                          where ad.Id == advertId
                          select new AdvertDTO
                          {
                            Id = ad.Id,
                            Vehicle = new VehicleDTO
                            {
                              Id = vehicle.Id,
                              PrimaryImageId = vehicle.PrimaryImageId,
                              Make = new MakeDTO
                              {
                                Id = make.Id,
                                MakeName = make.MakeName
                              },
                              Model = new ModelDTO
                              {
                                Id = model.Id,
                                ModelName = model.ModelName,
                              },
                              Deriv = new DerivDTO
                              {
                                Id = deriv.Id,
                                DerivName = deriv.DerivName
                              },
                              TransmissionType = new TransmissionTypeDTO
                              {
                                TransmissionTypeName = transmissionType.TransmissionTypeName
                              },
                              FuelType = new FuelTypeDTO
                              {
                                FuelTypeName = fuelType.FuelTypeName
                              },
                              BodyType = new BodyTypeDTO
                              {
                                BodyTypeName = bodyType.BodyTypeName
                              },
                              Vrm = vehicle.Vrm,
                              CustomerId = vehicle.CustomerId,
                              Odometer = vehicle.Odometer,
                              Plate = new PlateDTO
                              {
                                Id = plate.Id,
                                PlateName = plate.PlateName
                              }
                            },
                            ActiveDeal = (from deal in _tradingContext.Deals
                                          where deal.AdvertId == ad.Id && deal.StatusId == (int)StatusEnum.Active
                                          select new DealDTO
                                          {
                                            Id = deal.Id,
                                            BuyerContact = (from contact in _tradingContext.Contacts
                                                            where contact.Id == deal.BuyerContactId
                                                            select GetContactDTO(contact)).FirstOrDefault(),
                                            SellerContact = (from contact in _tradingContext.Contacts
                                                             where contact.Id == deal.SellerContactId
                                                             select GetContactDTO(contact)).FirstOrDefault(),
                                            CustomerOrders = (from custOrd in _tradingContext.CustomerOrders
                                                              where custOrd.DealId == deal.Id
                                                              select new CustomerOrderDTO
                                                              {
                                                                Id = custOrd.Id,
                                                                OrderType = custOrd.OrderType,
                                                                Bill = (from bill in _tradingContext.Bills
                                                                        where bill.Id == custOrd.BillId
                                                                        select new BillDTO
                                                                        {
                                                                          Id = bill.Id,
                                                                          PaidDate = bill.PaidDate
                                                                        }).FirstOrDefault()
                                                              }).ToList()
                                          })
                              .FirstOrDefault()
                          }).FirstOrDefaultAsync(cancellationToken);

      // Moved function out of query
      advert.Vehicle.PrimaryImageURL = URLHelper.PrimaryImageUrl(advert.Vehicle.CustomerId.Value,
        advert.Vehicle.Id.Value, advert.Vehicle.PrimaryImageId);

      // If we don't want it being passed
      advert.Vehicle.CustomerId = null;

      // null out buyer and seller contact details (as appropriate) if the bill has not been paid 
      var isBuyer = advert.ActiveDeal?.BuyerContact?.Id == contactId;
      var isSeller = advert.ActiveDeal?.SellerContact?.Id == contactId;

      if (advert.ActiveDeal != null && advert.ActiveDeal.CustomerOrders
            .Any(x => x.OrderType == CustomerOrderTypeEnum.Sold && isSeller ||
                      x.OrderType == CustomerOrderTypeEnum.Bought && isBuyer &&
                      (x.Bill == null || !x.Bill.PaidDate.HasValue)))
      {
        if (isBuyer)
        {
          advert.ActiveDeal.SellerContact = null;
        }
        else
        {
          advert.ActiveDeal.BuyerContact = null;
        }
      }

      return advert;
    }

    public async Task<uint> ChangeBidIncrement(Guid advertId, Guid customerId, uint currentBidIncrement, bool increase,
      CancellationToken cancellationToken)
    {
      List<uint> bidIncrements = new List<uint> { 25, 50, 100, 200, 500, 1000 };

      int index = bidIncrements.IndexOf(currentBidIncrement);

      int intNewIndex = index + (increase ? 1 : -1);

      if (intNewIndex >= bidIncrements.Count)
      {
        intNewIndex = bidIncrements.Count - 1;
      }
      else if (intNewIndex < 0)
      {
        intNewIndex = 0;
      }

      var newBidIncrement = bidIncrements[intNewIndex];

      var newPatch = new JsonPatchDocument<Advert>();
      newPatch.Add(x => x.BidIncrement, newBidIncrement);

      try
      {
        await Patch(advertId, customerId, newPatch, cancellationToken);
      }
      catch (Exception ex)
      {
      }

      // Tell everyone looking at this sale about the new bidding increments
      await _bidService.BroadcastBiddingUpdate(advertId);

      return newBidIncrement;
    }


    private static ContactDTO GetContactDTO(Contact contact)
    {
      return new ContactDTO
      {
        Id = contact.Id,
        ContactName = contact.ContactName,
        Email = contact.Email,
        Phone1 = contact.Phone1
      };
    }

    private static readonly Func<TradingContext, Guid, IEnumerable<Advert>> BaseRecord =
      EF.CompileQuery((TradingContext context, Guid id) =>
        context.Adverts
          .Where(x => x.Id == id)
          .AsSingleQuery()
          .AsNoTracking()
      );

    private static readonly Func<TradingContext, Guid, Guid, bool, IEnumerable<Advert>> AdvertEdit =
      EF.CompileQuery((TradingContext context, Guid id, Guid customerId, bool isGod) =>
        context.Adverts
          .Include("Sale.Address")
          .Where(x => x.Id == id && (x.CustomerId == customerId || isGod))
          .AsSingleQuery()
          .AsNoTracking()
      );


    private static readonly Func<TradingContext, Guid, Guid, bool, IEnumerable<Advert>> AdvertDeal =
      EF.CompileQuery((TradingContext context, Guid id, Guid customerId, bool isGod) =>
        context.Adverts
          .Include(x => x.Vehicle)
          .Include(x => x.ActiveDeal)
          .ThenInclude(x => x.CustomerOrders).ThenInclude(x => x.Bill)
          .Where(x => x.Id == id && (x.CustomerId == customerId || isGod))
          .AsSingleQuery()
          .AsNoTracking()
      );

    public async Task<AdvertSearchResultDTO> AdminViewSaleLots(AdvertSearchDTO dto, CancellationToken ct)
    {
      var query = _tradingContext.Adverts
        .Include(x => x.Vehicle.Make)
        .Include(x => x.Vehicle.Model)
        .Include(x => x.Vehicle.Deriv)
        .Include(x => x.Vehicle.Customer)
        .Include(x => x.Vehicle.Address)
        .Include(x => x.Vehicle.FuelType)
        .Include(x => x.Vehicle.TransmissionType)
        .Include(x => x.Vehicle.BodyType)
        .Include(x => x.Vehicle.VehicleColour)
        .Include(x => x.Vehicle.LatestValuation)
        .AsQueryable();

      query = AdvertQueryFilters(query, dto);

      var sql = query.ToQueryString();

      var adverts = await query.AsNoTracking().ToListAsync(ct);

      var advertsDTO = _mapper.Map<IEnumerable<Advert>, IEnumerable<AdvertDTO>>(adverts);

      return new AdvertSearchResultDTO() { Adverts = advertsDTO };
    }

    public async Task<AdvertSearchResultDTO> AdvertAdminView(AdvertSearchDTO dto, CancellationToken ct)
    {
      var preQuery = _tradingContext.Adverts
        .AsQueryable();

      if (dto.Filters.Expired == true)
      {
        preQuery = preQuery.Where(x => x.StatusId == (int)StatusEnum.Active);

        // Important: An advert only has EXPIRED status between the advert ending and all bids being processed
        // At which point it becomes ENDED
        preQuery = preQuery.Where(x => x.AdvertStatus == AdvertStatusEnum.Ended);
        preQuery = preQuery.Where(x => x.SoldStatus == SoldStatusEnum.Unsold);
        preQuery = preQuery.OrderByDescending(x => x.EndDateTime);
      }
      else if (dto.Filters.ExpiringSoon == true)
      {
        preQuery = preQuery.Where(x => x.StatusId == (int)StatusEnum.Active);
        preQuery = preQuery.Where(x => x.AdvertStatus == AdvertStatusEnum.Active);
        preQuery = preQuery.Where(x => x.EndDateTime <= DateTime.Now.AddHours(8));
        preQuery = preQuery.Where(x => x.EndDateTime >= DateTime.Now);
        preQuery = preQuery.OrderByDescending(x => x.EndDateTime);
      }
      else
      {
        preQuery = preQuery.OrderByDescending(x => x.AvailableDate);
      }

      if (dto.Filters.AssignedTo.HasValue && dto.Filters.AssignedTo != Guid.Empty)
      {
        preQuery = preQuery.Where(x => x.Customer.CustomerInternalInfo.AssignedTo == dto.Filters.AssignedTo.Value);
      }

      if (dto.Filters.SoldStatuses != null && dto.Filters.SoldStatuses.Count() > 0)
      {
        preQuery = preQuery.Where(x => dto.Filters.SoldStatuses.ToList().Contains((uint)x.SoldStatus));
      }

      var itemCount = await preQuery.CountAsync();

      var query = preQuery.Include(x => x.Vehicle.Make)
        .Include(x => x.Sale)
        .Include(x => x.Vehicle.Model)
        .Include(x => x.Vehicle.Deriv)
        .Include(x => x.Vehicle.FuelType)
        .Include(x => x.Vehicle.Deriv)
        .Include(x => x.Vehicle.TransmissionType)
        .Include(x => x.Vehicle.Plate)
        .Include(x => x.Vehicle.BodyType)
        .Include(x => x.Vehicle.Customer.CustomerInternalInfo)
        .Include(x => x.Vehicle.Address)
        .Include(x => x.Vehicle.LatestValuation)
        .Include(x => x.TopBid)
        .AsQueryable();

      if (dto.Limit != null)
      {
        query = query.Skip(dto.Offset.Value).Take(dto.Limit.Value);
      }

      var adverts = await query.AsNoTracking().ToListAsync(ct);

      var advertsDTO = _mapper.Map<IEnumerable<Advert>, IEnumerable<AdvertDTO>>(adverts);

      return new AdvertSearchResultDTO() { Adverts = advertsDTO, Count = itemCount };
    }

    private async Task<AdvertSearchResultDTO> AdvertViewVehicleInfo(AdvertSearchDTO advertSearchDTO, CancellationToken cancellationToken)
    {
      if (!advertSearchDTO.Filters.Id.HasValue)
      {
        return null;
      }

      var query = await _tradingContext.Adverts
        .Include(x => x.Address)
        .Include(x => x.Vehicle)
        .Include(x => x.Vehicle.FuelType)
        .Include(x => x.Vehicle.TransmissionType)
        .Include(x => x.Vehicle.BodyType)
        .Include(x => x.Vehicle.VehicleColour)
        .Include(x => x.Vehicle.VatStatus)
        .AsNoTracking()
        .Where(x => x.Id == advertSearchDTO.Filters.Id.Value)
        .FirstOrDefaultAsync(cancellationToken);

      if (query == null)
      {
        return null;
      }

      var dto = _mapper.Map<AdvertViewVehicleInfoDTO>(query);

      var res = new AdvertSearchResultDTO
      {
        AdvertViewVehicleInfo = dto
      };

      return res;
    }

    private async Task<AdvertSearchResultDTO> SellerHubSearch(AdvertSearchDTO advertSearchDTO,
      CancellationToken cancellationToken)
    {
      var ids = new List<Guid>();

      if (!string.IsNullOrEmpty(advertSearchDTO.Filters.Keywords))
      {
        var keyword = advertSearchDTO.Filters.Keywords;
        if (!string.IsNullOrEmpty(keyword))
        {
          keyword += "*";
        }

        var phrase = keyword +
                     ";filter=customerIdCRC," +
                     GuidHelper.Guid2Crc(advertSearchDTO.Filters.CustomerId.Value) + ";mode=any";

        ids = await _tradingContext.SphAll
          .Include(x => x.SphLink)
          .Where(x => x.phrase == keyword)
          .Select(x => x.SphLink.AdvertId)
          .ToListAsync(cancellationToken);

        var y = 1;
      }

      var preQuery = _tradingContext.Adverts.AsQueryable();

      preQuery = preQuery.Where(x =>
        x.StatusId == (int)StatusEnum.Active &&
        x.CustomerId == advertSearchDTO.Filters.CustomerId &&
        x.SoldStatus == advertSearchDTO.Filters.SoldStatus);

      // Use the sphinx list of IDs
      if (ids.Count > 0)
      {
        preQuery = preQuery.Where(x => ids.Contains(x.Id));
      }

      var query = preQuery.AsQueryable();

      if (!advertSearchDTO.CountOnly)
      {
        query = query
          .Include(x => x.TopBid)
          .Include(x => x.Sale.SaleType)
          .Include(x => x.Vehicle)
          .Include(x => x.Vehicle.Make)
          .Include(x => x.Vehicle.Model)
          .Include(x => x.Vehicle.Deriv)
          .Include(x => x.Vehicle.BodyType)
          .Include(x => x.Vehicle.FuelType)
          .Include(x => x.Vehicle.TransmissionType)
          .Include(x => x.Vehicle.Plate)
          .Include(x => x.Vehicle.VehicleType)
          .Include(x => x.Vehicle.Status)
          .Include(x => x.Vehicle.Address)
          .Include(x => x.Vehicle.VehicleColour)
          .Include(x => x.Vehicle.VatStatus)
          .Include(x => x.Vehicle.LatestValuation);

        var statuses = new List<SoldStatusEnum> { SoldStatusEnum.Sold, SoldStatusEnum.Unsold, SoldStatusEnum.Provisional };
        if (statuses.Contains(advertSearchDTO.Filters.SoldStatus))
        {
          query = query.OrderByDescending(x => x.EndDateTime);
        }
        else
        {
          query = query.OrderByDescending(x => x.Updated);
        }

        if (advertSearchDTO.Offset != null && advertSearchDTO.Limit != null)
        {
          query = query.Skip(advertSearchDTO.Offset.Value).Take(advertSearchDTO.Limit.Value);
        }
      }

      var result = await query.AsNoTracking().ToListAsync(cancellationToken);

      var count = advertSearchDTO == null || !advertSearchDTO.Limit.HasValue ? result.Count : await preQuery.CountAsync(cancellationToken);

      return new AdvertSearchResultDTO()
      {
        Count = count,
        Adverts = _mapper.Map<IEnumerable<Advert>, IEnumerable<AdvertDTO>>(result)
      };
    }

    private async Task<IEnumerable<VehicleMediaDTO>> GetVehicleMedia(VehicleDTO vehicle, CancellationToken ct)
    {

      /** VEHICLE MEDIA **/

      var media = await _tradingContext.VehicleMedia
        .Where(x => x.VehicleId == vehicle.Id && x.StatusId == (uint)StatusEnum.Active)
        .AsNoTracking()
        .ToListAsync()
        .ConfigureAwait(false);

      return _mapper.Map<IEnumerable<VehicleMedia>, IEnumerable<VehicleMediaDTO>>(media,
        opt => opt.AfterMap((src, dest) =>
        {
          foreach (var i in dest)
          {
            i.MediaURL = URLHelper.ImageUrl(vehicle.CustomerId.Value, vehicle.Id.Value, i.Id.Value);
          }
        }));
    }

    private async Task<IEnumerable<AppraisalDTO>> GetVehicleAppraisals(VehicleDTO vehicle, CancellationToken ct)
    {

      /** VEHICLE APPRAISAL **/

      var appraisals = await _tradingContext.Appraisals
          .Include("AppraisalItems.BodyPart.BodyPartGroup")
          .Include("AppraisalItems.Damage")
          .Include("AppraisalItems.DamageSeverity")
          .Include("AppraisalItems.DamageDetail")
          .Include("AppraisalItems.AppraisalMedia")
          .Where(x => x.VehicleId == vehicle.Id)
          .AsNoTracking()
          .ToListAsync()
          .ConfigureAwait(false);

      var appraisalsDTO = _mapper.Map<IEnumerable<Appraisal>, IEnumerable<AppraisalDTO>>(appraisals);

      foreach (var app in appraisalsDTO)
      {
        foreach (var appItem in app.AppraisalItems)
        {
          foreach (var im in appItem.AppraisalMedia)
          {
            im.MediaURL = URLHelper.AppraisalMediaUrl(new AppraisalMediaURLDTO()
            {
              CustomerId = vehicle.CustomerId.Value,
              VehicleId = vehicle.Id.Value,
              AppraisalId = app.Id.Value,
              AppraisalItemId = appItem.Id.Value,
              AppraisalMediaId = im.Id.Value
            });
          }
        }
      }

      return appraisalsDTO;
    }


    private async Task<IEnumerable<VehicleAttribDTO>> GetVehicleAttribs(VehicleDTO vehicle, CancellationToken ct)
    {
      /** VEHICLE ATTRIBS **/

      var vehicleAttribs = await _tradingContext.VehicleAttribs
          .Include(x => x.Attrib)
          .Include(x => x.Attribval)
          .Where(x => x.VehicleId == vehicle.Id)
          .AsNoTracking()
          .ToListAsync()
          .ConfigureAwait(false);

      return _mapper.Map<IEnumerable<VehicleAttrib>, IEnumerable<VehicleAttribDTO>>(vehicleAttribs);
    }

    private async Task<IEnumerable<ServiceHistoryDTO>> GetServiceHistories(VehicleDTO vehicle, CancellationToken ct)
    {
      /** VEHICLE SERVICE HISTORY **/

      var serviceHistory = await _tradingContext.ServiceHistories
          .Where(x => x.VehicleId == vehicle.Id)
          .AsNoTracking()
          .ToListAsync()
          .ConfigureAwait(false);

      return _mapper.Map<IEnumerable<ServiceHistory>, IEnumerable<ServiceHistoryDTO>>(serviceHistory);
    }

    private async Task<IEnumerable<MOTHistoryDTO>> GetMOTHistories(VehicleDTO vehicle, CancellationToken ct)
    {
      /** VEHICLE MOT HISTORY **/
      var motHistory = await _tradingContext.MOTHistories
        .Include(x => x.MOTItems)
        .Where(x => x.VehicleId == vehicle.Id)
        .AsNoTracking()
        .ToListAsync()
        .ConfigureAwait(false);

      return _mapper.Map<IEnumerable<MOTHistory>, IEnumerable<MOTHistoryDTO>>(motHistory);
    }
    private async Task<VehicleCheckDTO> GetLatestProvenance(VehicleDTO vehicle, CancellationToken ct)
    {
      /** VEHICLE MOT HISTORY **/
      var provenance = await _tradingContext.VehicleChecks
        .Where(x => x.Id == vehicle.LatestProvenanceId)
        .AsNoTracking()
        .FirstOrDefaultAsync()
        .ConfigureAwait(false);

      return _mapper.Map<VehicleCheck, VehicleCheckDTO>(provenance);
    }


    private static Func<TradingContext, Guid, Task<Advert>> AdvertView2Query =
      EF.CompileAsyncQuery((TradingContext context, Guid id) =>
        context.Adverts
          .Include(x => x.Address)
          .Include(x => x.Vehicle)
          .Include(x => x.Vehicle.Make)
          .Include(x => x.Vehicle.Model)
          .Include(x => x.Vehicle.Deriv)
          .Include(x => x.Vehicle.BodyType)
          .Include(x => x.Vehicle.FuelType)
          .Include(x => x.Vehicle.TransmissionType)
          .Include(x => x.Vehicle.Plate)
          .Include(x => x.Vehicle.VehicleType)
          .Include(x => x.Vehicle.Status)
          .Include(x => x.Vehicle.VatStatus)
          .Include(x => x.Vehicle.VehicleColour)
          .Include(x => x.Vehicle.LatestValuation.VehicleCheckProvider)
          .Include(x => x.Sale)
          .Include(x => x.Bids)
          .Include(x => x.TopBid)
          .Where(x => x.Id == id)
          .AsSingleQuery()
          .AsNoTracking()
          .FirstOrDefault()
    );

    private async Task<AdvertDTO> AdvertView2(Guid id, CancellationToken ct)
    {

      var ad = await AdvertView2Query.Invoke(_tradingContext, id);

      var adDTO = _mapper.Map<Advert, AdvertDTO>(ad);


      adDTO.Vehicle.VehicleMedia = await GetVehicleMedia(adDTO.Vehicle, ct).ConfigureAwait(false);
      adDTO.Vehicle.Appraisals = await GetVehicleAppraisals(adDTO.Vehicle, ct).ConfigureAwait(false);
      adDTO.Vehicle.VehicleAttribs = await GetVehicleAttribs(adDTO.Vehicle, ct).ConfigureAwait(false);
      adDTO.Vehicle.ServiceHistories = await GetServiceHistories(adDTO.Vehicle, ct).ConfigureAwait(false);
      adDTO.Vehicle.MOTHistory = await GetMOTHistories(adDTO.Vehicle, ct).ConfigureAwait(false);
      adDTO.Vehicle.LatestProvenance = await GetLatestProvenance(adDTO.Vehicle, ct).ConfigureAwait(false);

      return adDTO;
    }

    private static readonly Func<TradingContext, Guid, Advert> AdvertNegotiationView =
      EF.CompileQuery((TradingContext context, Guid id) =>
        context.Adverts
          .Include(x => x.Contact.Customer.CustomerInternalInfo)
          .Include(x => x.Address)
          .Include(x => x.Vehicle)
          .Include(x => x.Vehicle.Make)
          .Include(x => x.Vehicle.Model)
          .Include(x => x.Vehicle.Deriv)
          .Include(x => x.Vehicle.VehicleColour)
          .Include(x => x.Vehicle.FuelType)
          .Include(x => x.Vehicle.TransmissionType)
          .Include(x => x.Vehicle.BodyType)
          .Include(x => x.Vehicle.Plate)
          .Include(x => x.Vehicle.LatestProvenance)
          .Include(x => x.Vehicle.LatestValuation)
          .Include(x => x.Vehicle.VatStatus)
          .Include(x => x.Sale)
          .Include(x => x.Bids).ThenInclude(x => x.Contact.Customer.CustomerInternalInfo)
          .Include(x => x.Bids).ThenInclude(x => x.Offer)
          .Include(x => x.TopBid).ThenInclude(x => x.Contact.Customer)
          .Include(x => x.Negotiations).ThenInclude(x => x.NegotiationNotes.OrderByDescending(y => y.Added))
          .Where(x => x.Id == id)
          .AsNoTracking()
          .FirstOrDefault()
      );

    private static readonly Func<TradingContext, Guid, Advert> CounterOfferResponseAdvert =
      EF.CompileQuery((TradingContext context, Guid id) =>
        context.Adverts
          .Include(x => x.Contact.Customer)
          .Where(x => x.Id == id)
          .AsNoTracking()
          .FirstOrDefault()
     );

    public static IQueryable<Advert> AdvertQueryFilters(IQueryable<Advert> query, AdvertSearchDTO advertSearch)
    {
      if (advertSearch.Filters.SaleTypeId != null && advertSearch.Filters.SaleTypeId > 0)
      {
        query = query.Where(x => x.Sale.SaleTypeId == advertSearch.Filters.SaleTypeId);
      }

      if (advertSearch.Filters.SaleId != Guid.Empty && advertSearch.Filters.SaleId != null)
      {
        query = query.Where(x => x.SaleId == advertSearch.Filters.SaleId);
      }

      if (advertSearch.Filters.Id != Guid.Empty && advertSearch.Filters.Id != null)
      {
        query = query.Where(x => x.Id == advertSearch.Filters.Id);
      }

      if (advertSearch.Filters.StatusId > 0)
      {
        query = query.Where(x => x.StatusId == advertSearch.Filters.StatusId);
      }

      return query;
    }

    private async Task<AdvertSearchResultDTO> BidderLiveSaleViewAdvert(Guid advertId, CancellationToken ct)
    {
      AdvertDTO xyz = await AdvertView2(advertId, ct);

      var abc = _mapper.Map<AdvertDTO, AdvertDTO_Public>(xyz);

      return new AdvertSearchResultDTO()
      {
        AdvertDTO_Public = abc,
        Advert = xyz
      };
    }

    private async Task<AdvertSearchResultDTO> AuctioneerLiveSaleViewAdvert(Guid advertId, CancellationToken ct)
    {
      // Get the advert
      AdvertDTO advertDTO = await AdvertView2(advertId, ct);

      // Get the bids
      var advertBids = await _bidService.AdvertBidHistory(advertId);
      advertDTO.Bids = _mapper.Map<IEnumerable<Bid>, IEnumerable<BidDTO>>(advertBids.Bids);

      return new AdvertSearchResultDTO()
      {
        Advert = advertDTO
      };
    }

    public async Task<Advert> Get(Guid advertId, CancellationToken ct)
    {
      return await _tradingContext.Adverts
        .FirstOrDefaultAsync(x => x.Id == advertId, ct);
    }

    public async Task<AdvertDTO> Patch(Guid advertId, Guid customerId, JsonPatchDocument<Advert> patch,
      CancellationToken cancellationToken)
    {
      var advert = await Get(advertId, cancellationToken);

      if (advert != null)
      {
        if (customerId != advert.CustomerId && !_userService.IsGod())
        {
          throw new Exception(ExceptionHelper.Forbidden(ECodes.WrongCustomer));
        }

        // if advert is published or has bids on it, return (unless we're admin trying to change bid increment for example)
        if (advert.BidCount > 0 && !_userService.IsAdmin())
        {
          throw new Exception(ExceptionHelper.Conflict(ECodes.HasBids));
        }

        // modify the available and end date.times to be DateTime objects


        patch.FilterPatch();
        patch.ApplyTo(advert);

        // set the price range id if buyItNow is set
        if (advert.BuyItNowPrice.HasValue)
        {
          advert.PriceRangeId = await _lookupService.GetPriceRangeId(advert.BuyItNowPrice.Value, cancellationToken);
        }

        if (advert.StartPrice == null)
        {
          advert.StartPrice = 0;
        }

        if (advert.ReservePrice == null)
        {
          advert.ReservePrice = 0;
        }

        if (advert.BidIncrement == null)
        {
          var sale = await _tradingContext.Sales.AsNoTracking().Where(x => x.Id == advert.SaleId)
            .FirstOrDefaultAsync(cancellationToken);

          if (sale != null && sale.SaleTypeId == (int)SaleTypeEnum.BuyNow && advert.BidCount == 0)
          {
            advert.BidIncrement = advert.BuyItNowPrice;
          }
          else
          {
            advert.BidIncrement = 100;
          }
        }

        await _tradingContext.SaveChangesAsync(cancellationToken);

        var advertDTO = _mapper.Map<Advert, AdvertDTO>(advert);

        if (advertDTO.AdvertStatus == AdvertStatusEnum.Active)
        {
          try
          {
            await _bidService.BroadcastBiddingUpdate(advertDTO.Id.Value);
          }
          catch { }
        }

        return advertDTO;
      }

      return null;
    }

    public async Task<Guid> RelistByVehicle(Guid vehicleId, Guid customerId, Guid contactId, bool publish,
      CancellationToken cancellationToken)
    {
      // get latest non-active advert for this vehicle
      var vehicle = await _tradingContext.Vehicles.FirstOrDefaultAsync(x => x.Id == vehicleId);

      if (vehicle != null)
      {
        var advert = await _tradingContext.Adverts
          .Where(x => x.VehicleId == vehicleId && x.SoldStatus != SoldStatusEnum.Active && x.SoldStatus != SoldStatusEnum.Draft)
          .OrderByDescending(x => x.Added)
          .FirstOrDefaultAsync();

        //var sql = _tradingContext.Adverts
        //  .Where(x => x.VehicleId == vehicleId && x.SoldStatus != SoldStatusEnum.Active && x.SoldStatus != SoldStatusEnum.Draft)
        //  .OrderByDescending(x => x.Added).ToQueryString();

        //var test = _tradingContext.Adverts.ToList();
        //var t1 = test
        //  .Where(x => x.VehicleId == vehicleId && x.SoldStatus != SoldStatusEnum.Active && x.SoldStatus != SoldStatusEnum.Draft)
        //  .FirstOrDefault();

        // ensure advert belongs to customer or user has top-level access
        if (advert != null && (advert.CustomerId == customerId || _userService.IsGod()))
        {
          return await Relist(advert.Id, customerId, contactId, publish, cancellationToken);
        }
      }

      return Guid.Empty;
    }

    public async Task<Guid?> GetNextLotId(Guid saleId, Guid currentAdvertId, CancellationToken ct)
    {
      var query = _tradingContext.Adverts
        .AsNoTracking()
        .Where(x => x.SaleId == saleId);

      query = UpcomingLotsWhere(query);

      var nextLots = await query.OrderBy(x => x.LotSeq).Take(1).ToListAsync(ct);

      if (nextLots != null)
      {
        return nextLots.First().Id;
      }

      return null;
    }


    public async Task<Guid> Relist(Guid advertId, Guid customerId, Guid contactId, bool publish, CancellationToken cancellationToken)
    {
      var advert = await _tradingContext.Adverts
        .Include(x => x.Sale)
        .Include(x => x.Deals)
        .FirstOrDefaultAsync(x => x.Id == advertId, cancellationToken);

      if (advert != null)
      {
        if (customerId != advert.CustomerId && !_userService.IsGod())
        {
          throw new Exception(ExceptionHelper.Forbidden(ECodes.WrongCustomer));
        }

        // ensure advert is not in live, draft etc.
        if (advert.SoldStatus == SoldStatusEnum.Active || advert.SoldStatus == SoldStatusEnum.Draft)
        {
          throw new Exception(ExceptionHelper.Forbidden(ECodes.CantRelist));
        }

        // ensure vehicle is not already listed 
        var existing = await _tradingContext.Adverts.FirstOrDefaultAsync(x => x.VehicleId == advert.VehicleId && x.AdvertStatus == AdvertStatusEnum.Active);
        if (existing != null)
        {
          throw new Exception(ExceptionHelper.Forbidden(ECodes.ExistingVehicleAdvert));
        }

        // set any current deal(s) associated with this advert to status of deleted (deal fell through)
        if (advert.Deals != null && advert.Deals.Count > 0)
        {
          var deals = advert.Deals.ToList();

          // ensure we don't have a paid active deal associated with this advert 
          if (deals.Any(
                x => x.StatusId == (int)StatusEnum.Active && x.CustomerOrders.Any(y => y.Bill.PaidDate.HasValue)))
          {
            throw new Exception(ExceptionHelper.Forbidden(ECodes.CantRelistDeal));
          }

          foreach (var deal in deals)
          {
            deal.StatusId = (int)StatusEnum.Deleted;
          }
        }

        // set advert sold status to unsold 
        advert.AdvertStatus = AdvertStatusEnum.Ended;
        advert.SoldStatus = SoldStatusEnum.Unsold;
        advert.StatusId = (int)StatusEnum.Deleted;
        advert.Updated = DateTime.Now;
        await _tradingContext.SaveChangesAsync(cancellationToken);

        // create new advert from existing advert 
        var newAd = await CreateFromExisting(advert, cancellationToken);

        // republish the advert
        // pm: no longer publishing as advert needs to be in Draft state
        if (publish)
        {
          await PublishAdvert(contactId, newAd.Id, cancellationToken);
        }

        await _contactActionService.RecordAction(ContactActionEnum.RelistAdvert, advertId, cancellationToken);

        return newAd.Id;
      }

      return Guid.Empty;
    }

    private async Task<Advert> CreateFromExisting(Advert existing, CancellationToken cancellationToken)
    {
      var dateLimit = existing.EndDateTime?.Subtract(existing?.AvailableDate ?? existing.Added.Value);
      if (dateLimit.HasValue && dateLimit.Value.TotalDays < 1)
      {
        dateLimit = TimeSpan.FromDays(7);
      }

      // copy according to sale type (managed sale = advertStatus = 2, saleId = null)

      var fromManagedSale = existing.Sale.SaleTypeId == (uint)SaleTypeEnum.ManagedSale;

      Advert newAd = new Advert()
      {
        AcceptBids = existing.AcceptBids,
        Address = existing.Address,
        AddressId = existing.AddressId,
        AutoAcceptBid = existing.AutoAcceptBid,
        AutoRejectBid = existing.AutoRejectBid,
        BidIncrement = existing.BidIncrement,
        BuyItNowPrice = existing.BuyItNowPrice,
        CanAutoExtend = existing.CanAutoExtend,
        ContactId = existing.ContactId,
        CustomerId = existing.CustomerId,
        Description = existing.Description,
        Added = DateTime.Now,
        AdvertStatus = AdvertStatusEnum.Inactive, // return to unlotted for managed sale vehicles
        StatusId = (int)StatusEnum.Active,
        AvailableDate = DateTime.Now,
        EndDateTime = DateTime.Now.Add(dateLimit ?? TimeSpan.FromDays(7)), // todo: verify this
        Headline = existing.Headline,
        LotNo = existing.LotNo,
        PriceRangeId = existing.PriceRangeId,
        ReservePrice = existing.ReservePrice,
        SaleId = fromManagedSale ? null : existing.SaleId,
        StartPrice = existing.StartPrice,
        TakeTopBid = existing.TakeTopBid,
        VehicleId = existing.VehicleId,
        SoldStatus = SoldStatusEnum.Draft,
        CurrentPrice = 0
      };

      _tradingContext.Adverts.Add(newAd);
      await _tradingContext.SaveChangesAsync(cancellationToken);

      // create sphlink record to link advert and sphadvert
      var sphLink = new SphLink()
      {
        AdvertId = newAd.Id
      };
      _tradingContext.SphLinks.Add(sphLink);
      await _tradingContext.SaveChangesAsync();

      await _contactActionService.RecordAction(ContactActionEnum.RelistAdvert, newAd.Id, cancellationToken);

      return newAd;
    }

    public async Task CancelAdvert(Guid advertId, uint reasonId, Guid customerId, Guid contactId,
      CancellationToken cancellationToken)
    {
      var advert = await _tradingContext.Adverts
        .Include(x => x.Sale)
        .Include(x => x.TopBid)
        .FirstOrDefaultAsync(x => x.Id == advertId, cancellationToken);

      if (advert != null)
      {
        if (customerId != advert.CustomerId && !_userService.IsGod())
        {
          throw new Exception(ExceptionHelper.Forbidden(ECodes.WrongCustomer));
        }

        // sale cannot be ended if the reserve has been met
        if (advert.TopBid != null && advert.TopBid.BidAmt >= (advert.ReservePrice ?? 0))
        {
          throw new Exception(ExceptionHelper.Forbidden(ECodes.ReservePriceMet));
        }

        // ensure advert is live
        if (advert.SoldStatus != SoldStatusEnum.Active)
        {
          throw new Exception(ExceptionHelper.Forbidden(ECodes.SaleNotLive));
        }

        // set advert status to Withdrawn
        advert.SoldStatus = SoldStatusEnum.Withdrawn;
        advert.AdvertStatus = AdvertStatusEnum.Ended;
        advert.EndDateTime = DateTime.Now.Subtract(TimeSpan.FromSeconds(1));
        advert.ListingEndReasonId = reasonId;

        await _tradingContext.SaveChangesAsync(cancellationToken);

        await _contactActionService.RecordAction(ContactActionEnum.EndSale, advertId, cancellationToken);
        await _bidService.BroadcastBiddingUpdate(advertId);
      }
    }

    public async Task<IEnumerable<ListingEndReasonDTO>> GetListingEndReasons(CancellationToken cancellationToken)
    {
      var reasons = await _tradingContext.ListingEndReasons.AsNoTracking().ToListAsync(cancellationToken);
      return _mapper.Map<IEnumerable<ListingEndReason>, IEnumerable<ListingEndReasonDTO>>(reasons);
    }

    public async Task<byte[]> ExportToCSV(Guid customerId, SoldStatusEnum soldStatus,
      CancellationToken cancellationToken)
    {
      // get advert data and convert to csvDTO (contains information from Sale, Deal etc. as required)
      var adverts = await _tradingContext.Adverts
        .Include(x => x.Vehicle.Make)
        .Include(x => x.Vehicle.Model)
        .Include(x => x.Vehicle.Deriv)
        .Include(x => x.Deals)
        .AsNoTracking()
        .Where(x => x.CustomerId == customerId && x.SoldStatus == soldStatus).ToListAsync(cancellationToken);
      var dtos = _mapper.Map<List<Advert>, List<AdvertExportDTO>>(adverts);

      var stream = new MemoryStream();
      using (var writeFile = new StreamWriter(stream, System.Text.Encoding.ASCII, 1024, true))
      {
        var csv = new CsvHelper.CsvWriter(writeFile, System.Globalization.CultureInfo.CurrentCulture);

        switch (soldStatus)
        {
          case SoldStatusEnum.Sold:
            csv.Context.RegisterClassMap<SoldStatusMap>();
            break;
          default:
            csv.Context.RegisterClassMap<DefaultAdvertMap>();
            break;
        }

        csv.WriteRecords(dtos);
      }

      stream.Position = 0;
      var bytes = stream.ToArray();
      stream.Close();
      stream.Flush();

      return bytes;
    }

    public async Task DeleteAdvert(Guid advertId, CancellationToken cancellationToken)
    {
      var advert = await _tradingContext.Adverts.FirstOrDefaultAsync(x => x.Id == advertId);

      if (advert == null)
      {
        throw new ApplicationException("Advert does not exist");
      }

      // if this advert doesn't belong to the user and user is not super admin, don't allow deletion
      var contactId = _userService.GetContactId();
      if (advert.ContactId != contactId && !_userService.IsGod())
      {
        throw new ApplicationException("Access denied");
      }

      // update the advert status to deleted 
      advert.StatusId = (int)StatusEnum.Deleted;
      await _tradingContext.SaveChangesAsync(cancellationToken);

      await _contactActionService.RecordAction(ContactActionEnum.DeleteAdvert, advertId, cancellationToken);
    }

    public async Task<int> GetLiveAdvertCount(CancellationToken cancellationToken)
    {
      var query = DapperQueriesHelper.LiveAdvertCount();
      var results = await _db.QueryAsync<int>(query, cancellationToken);
      var count = results.FirstOrDefault();

      return count;
    }

    private async Task<AdvertDTO_Public> LotDataForBidder(Guid advertId, CancellationToken ct)
    {
      var advert =
        await Search(new AdvertSearchDTO() { Component = "BidderLiveSaleViewAdvert", Filters = { Id = advertId } }, ct);
      var dto = advert.AdvertDTO_Public;

      return dto;
    }

    public async Task<AdvertDTO_Public> BidderSaleCurrentLot(Guid saleId, CancellationToken ct)
    {
      var advertId = await _tradingContext.Sales
        .Where(x => x.Id == saleId)
        .AsNoTracking()
        .Select(x => x.CurrentAdvertId)
        .FirstOrDefaultAsync(ct);

      if (advertId == null)
      {
        return null;
      }

      return await LotDataForBidder(advertId.Value, ct);
    }

    public async Task BroadcastBidderCurrentLot(Guid advertId, CancellationToken ct)
    {
      var advert = await LotDataForBidder(advertId, ct);

      await _messageService.SendSaleMessage(advert.SaleId.Value, MessageAreaEnum.Adverts,
        MessageTypeEnum.CurrentLotUpdate, advert);
    }

    public async Task<bool> SetLiveAdvertGGG(SetAdvertGGGDTO dto, CancellationToken ct)
    {
      if (dto.AdvertId != null)
      {
        var _Advert = nameof(Advert);
        var _Advert_GGG = nameof(Advert.GGG);
        var _Advert_Id = nameof(Advert.Id);

        var sql = $"UPDATE {_Advert} Set {_Advert_GGG} = @ggg WHERE {_Advert_Id} = @advertId";
        var updateParams = new { ggg = dto.GGG, advertId = dto.AdvertId };

        try
        {
          await _db.ExecuteAsync(sql, updateParams);
        }
        catch (Exception ex)
        {
        }

        await BroadcastLiveAdvertGGG(dto.AdvertId.Value, dto.GGG.Value, ct);
      }

      return true;
    }

    public async Task<bool> BroadcastLiveAdvertGGG(Guid advertId, uint ggg, CancellationToken ct)
    {
      await _messageService.SendAdvertMessage(advertId, MessageAreaEnum.Adverts, MessageTypeEnum.SetGGG,
        new { advertId, ggg });

      return true;
    }

    public async Task<bool> SetStartPrice(Guid advertId, Guid customerId, JsonPatchDocument<Advert> patch,
      CancellationToken cancellationToken)
    {
      // Update the Advert
      await Patch(advertId, customerId, patch, cancellationToken);

      await _bidService.BroadcastBiddingUpdate(advertId);

      return true;
    }

    public async Task<AdvertLambdaInfoDTO> LambdaInfo(Guid advertId)
    {
      var x = await _tradingContext.Adverts
        .Include(x => x.Address)
        .Where(x => x.Id == advertId)
        .AsNoTracking()
        .Select(x => new AdvertLambdaInfoDTO
        {
          VRM = x.Vehicle.Vrm,
          KerbWeight = (x.Vehicle.Kerbweight == 0) ? 1500 : x.Vehicle.Kerbweight,
          Postcode = x.Address.AddressPostcode
        })
        .FirstOrDefaultAsync();

      return x;
    }
  }
}