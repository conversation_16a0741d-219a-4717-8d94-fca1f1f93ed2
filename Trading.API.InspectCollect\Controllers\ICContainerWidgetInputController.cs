using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/container-widget-input")]
  [ApiController]
  [AllowAnonymous]
  public class ICContainerWidgetInputController : ControllerBase
  {
    private readonly ICContainerWidgetInputInterface _icContainerWidgetInputService;

    public ICContainerWidgetInputController(ICContainerWidgetInputInterface serviceInterface)
    {
      _icContainerWidgetInputService = serviceInterface;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(Guid id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      ICContainerWidgetInputSearchDTO dto = new ICContainerWidgetInputSearchDTO();

      if (!String.IsNullOrEmpty(query))
      {
        dto = JsonConvert.DeserializeObject<ICContainerWidgetInputSearchDTO>(query);
      }

      var res = await _icContainerWidgetInputService.Get(id, dto, cancellationToken);
      return Ok(res);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icContainerWidgetInputService.Delete(id);
      return Ok(res);
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Post([FromBody]ICContainerWidgetInputCreateDTO dto, CancellationToken cancellationToken)
    {
      var res = await _icContainerWidgetInputService.Create(dto);
      return Ok(res);
    }

    [HttpGet]
    [Route("/api/inspect-collect/container-widget-inputs")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICContainerWidgetInputSearchDTO>(query);
      var res = await _icContainerWidgetInputService.Search(dto, cancellationToken);
      return Ok(res);
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<ICContainerWidgetInput> dto)
    {
      var response = await _icContainerWidgetInputService.Patch(id, dto);
      return Ok(response);
    }
  }
}