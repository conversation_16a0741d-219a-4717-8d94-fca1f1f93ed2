using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Common;
using Trading.API.Data.DTO;
using Trading.Services.Extensions;
using Trading.Services;
using Trading.Services.Interfaces;
using Trading.API.Data.DTO.Search;
using Newtonsoft.Json;
using Trading.API.Remarq.Controllers.Extensions;

namespace Trading.API.Controllers
{
  [Route("api/search")]
  [ApiController]
  [Authorize]
  public class SearchController : ControllerBase
  {
    private readonly IAdvertSearchService _adSearchService;
    private readonly ISearchService _searchService;

    public SearchController(IAdvertSearchService adSearchService, ISearchService searchService)
    {
      this._adSearchService = adSearchService;
      this._searchService = searchService;
    }

    [HttpPost]
    public async Task<IActionResult> SaveSearch(SearchDTO search, CancellationToken cancellationToken)
    {
      try
      {
        await _adSearchService.SaveSearch(search, cancellationToken);

        return Ok("SEARCH SAVED");
      } 
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    // DEPRECATED -- REMOVE WHEN DONE
    [HttpGet, Route("process-daily-alerts")]
    [AllowAnonymous]
    public async Task<IActionResult> ProcessDailyAlerts(CancellationToken cancellationToken)
    {
      try
      {
        await Task.Run(() => this.HttpContext.Response.KeepConnectionAlive(cancellationToken), cancellationToken);

        await _adSearchService.ProcessDailyAlerts(cancellationToken);

        await HttpContext.Response.CompleteProcess();
        return Ok(new { Text = "Daily notifications processed" });
      }
      catch (Exception ex)
      {
        await HttpContext.Response.CompleteProcessWithError(ex);
        return BadRequest(ex);
      }

    }

    // DEPRECATED -- REMOVE WHEN DONE
    [HttpGet, Route("process-immediate-alerts")]
    [AllowAnonymous]
    public async Task<IActionResult> ProcessImmediateAlerts(CancellationToken cancellationToken)
    {
      try
      {
        await Task.Run(() => this.HttpContext.Response.KeepConnectionAlive(cancellationToken), cancellationToken);

        await _adSearchService.ProcessImmediateAlerts(cancellationToken);

        await HttpContext.Response.CompleteProcess();
        return Ok(new { Text = "Immediate notifications processed" });
      }
      catch (Exception ex)
      {
        await HttpContext.Response.CompleteProcessWithError(ex);
        return BadRequest(ex);
      }

    }


    [HttpGet("top-searchers")]
    public async Task<IActionResult> GetTopSearchers([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        if (!User.IsAdmin() && !User.IsPowerUser())
        {
          return Forbid();
        }

        var dto = JsonConvert.DeserializeObject<SearchSearchDTO>(query);
        var response = await _searchService.GetTopSearchers(dto, cancellationToken);
        return Ok(response);
      }
      catch (Exception ex) { return BadRequest(ex); }
    }

    [HttpGet]
    public async Task<IActionResult> GetSearches([FromQuery]string query, CancellationToken cancellationToken)
    {
      try
      {
        if (!User.IsAdmin())
        {
          return Forbid();
        }

        var dto = JsonConvert.DeserializeObject<SearchSearchDTO>(query);
        var response = await _searchService.GetSearches(dto, cancellationToken);
        return Ok(response);
      }
      catch (Exception ex) { return BadRequest(ex); }
    }
  }
}
