﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.AutoTrader;
public class AutoTraderValuationDTO
{
  public Guid? ICVehicleId { get; set; }

  public string VRM { get; set; }

  public string VIN { get; set; }
  public int Odometer { get; set; }

  // valuation details

  public decimal Trade { get; set; }

  public decimal PartExchange { get; set; }

  public decimal Retail { get; set; }

  public decimal Private { get; set; }

  // if valuation is based on features or not 
  public bool IsFeatureValuation { get; set; }
}
