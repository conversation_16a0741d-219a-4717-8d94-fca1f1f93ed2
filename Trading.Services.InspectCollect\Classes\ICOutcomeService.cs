using AutoMapper;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.EntityFrameworkCore;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.InspectCollect.VehicleData;
using Trading.API.Data.Models.InspectCollect.VehicleData;
using Trading.Services.InspectCollect.Interfaces;
using Trading.API.Data.Enums;
using Trading.Services.Extensions;

namespace Trading.Services.InspectCollect.Classes
{
  public class ICOutcomeService : ICOutcomeInterface
  {
    private readonly TradingContext _context;
    private readonly IMapper _mapper;

    public ICOutcomeService(TradingContext context, IMapper mapper)
    {
      _context = context;
      _mapper = mapper;
    }

    public async Task<ICOutcomeDTO> Create(ICOutcomeCreateDTO dto)
    {
      var outcome = _mapper.Map<ICOutcome>(dto);

      outcome.StatusId = (uint)StatusEnum.Active;
      outcome.Added = DateTime.Now;
      outcome.Updated = DateTime.Now;

      _context.ICOutcomes.Add(outcome);
      await _context.SaveChangesAsync();

      return _mapper.Map<ICOutcomeDTO>(outcome);
    }

    public async Task<bool> Delete(uint id)
    {
      try
      {
        var patch = new JsonPatchDocument<ICOutcome>();
        patch.Add(x => x.StatusId, (uint)StatusEnum.Deleted);
        await this.Patch(id, patch);

        return true;
      }
      catch (Exception ex)
      {
        // Log exception if needed
        return false;
      }
    }

    public async Task<ValidatedResultDTO<ICOutcomeDTO>> Get(uint id, ICOutcomeSearchDTO searchDTO, CancellationToken cancellationToken)
    {
      if (searchDTO == null) { searchDTO = new ICOutcomeSearchDTO(); }

      searchDTO.Filters.Id = id;
      var outcomes = await Search(searchDTO, cancellationToken);

      return new ValidatedResultDTO<ICOutcomeDTO>()
      {
        IsValid = outcomes.TotalItems > 0,
        DTO = outcomes.Results.FirstOrDefault()
      };
    }

    public async Task<ICOutcomeDTO> Patch(uint id, JsonPatchDocument<ICOutcome> patch)
    {
      var outcome = await _context.ICOutcomes.FirstOrDefaultAsync(x => x.Id == id);

      if (outcome == null)
      {
        throw new ArgumentException($"ICOutcome with ID {id} not found");
      }

      patch.ApplyTo(outcome);
      outcome.Updated = DateTime.Now;

      await _context.SaveChangesAsync();

      return _mapper.Map<ICOutcomeDTO>(outcome);
    }

    public async Task<SearchResultDTO<ICOutcomeDTO>> Search(ICOutcomeSearchDTO search, CancellationToken cancellationToken)
    {
      var query = _context.ICOutcomes
        .Include(x => x.ICContainerGroup)
        .AsQueryable();

      // Apply filters
      if (search.Filters.Id.HasValue)
      {
        query = query.Where(x => x.Id == search.Filters.Id.Value);
      }

      if (search.Filters.StatusId.HasValue)
      {
        query = query.Where(x => x.StatusId == search.Filters.StatusId.Value);
      }

      if (search.Filters.ICContainerGroupId.HasValue)
      {
        query = query.Where(x => x.ICContainerGroupId == search.Filters.ICContainerGroupId.Value);
      }

      if (!string.IsNullOrEmpty(search.Filters.Outcome))
      {
        query = query.Where(x => x.Outcome.Contains(search.Filters.Outcome));
      }

      if (!string.IsNullOrEmpty(search.Filters.Colour))
      {
        query = query.Where(x => x.Colour.Contains(search.Filters.Colour));
      }

      if (search.Filters.OutcomeType.HasValue)
      {
        query = query.Where(x => x.OutcomeType == search.Filters.OutcomeType.Value);
      }

      // Apply ordering
      query = query.OrderByDescending(x => x.Updated);

      // Get total count
      var totalItems = await query.CountAsync(cancellationToken);

      // Apply pagination
      if (search.Offset > 0)
      {
        query = query.Skip(search.Offset.Value);
      }
      if (search.Limit > 0)
      {
        query = query.Take(search.Limit.Value);
      }

      var results = await query.ToListAsync(cancellationToken);
      var dtos = _mapper.Map<List<ICOutcomeDTO>>(results);

      return new SearchResultDTO<ICOutcomeDTO>
      {
        Results = dtos,
        TotalItems = totalItems
      };
    }
  }
}
