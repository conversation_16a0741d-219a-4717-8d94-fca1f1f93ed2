﻿using System;
using Trading.API.Data.Models;
using static Trading.Services.DTOMappingProfiles.MappingProfile;

namespace Trading.Services.Helpers
{
  public static class URLHelper
  {
    private static string proxyUrl = "https://ik.imagekit.io";
    private static string baseMediaUrl = "https://ik.imagekit.io";
    private static string baseDocumentUrl = "https://ik.imagekit.io";
    private static string invoiceUrl = "";

    public static void SetImageKitURL(string account, string platformEndpoint)
    {
      proxyUrl = $"https://ik.imagekit.io/{account}";
      baseMediaUrl = $"https://ik.imagekit.io/{account}/{platformEndpoint}";
      baseDocumentUrl = $"https://ik.imagekit.io/{account}/{platformEndpoint}";
    }

    public static string GetImageKitURL()
    {
      return baseMediaUrl;
    }

    public static void SetInvoiceURL(string url)
    {
      invoiceUrl = url;
    }

    public static string PrimaryImageUrl(Guid customerId, Guid vehicleId, Guid? vehicleMediaId)
    {
      if (vehicleMediaId == null) { return null; }

      return VehicleMediaUrl(new VehicleMediaURLDTO()
      {
        CustomerId = customerId,
        VehicleId = vehicleId,
        VehicleMediaId = vehicleMediaId.Value
      });
    }

    public static string ImageUrl(Guid customerId, Guid vehicleId, Guid vehicleMediaId)
    {
      return VehicleMediaUrl(new VehicleMediaURLDTO()
      {
        CustomerId = customerId,
        VehicleId = vehicleId,
        VehicleMediaId = vehicleMediaId
      });
    }


    public static string VehicleMediaUrl(VehicleMediaURLDTO urlDTO)
    {
      return string.Format("{0}/{1}", baseMediaUrl, VehicleMediaKey(urlDTO));
    }
    public static string VehicleMediaKey(VehicleMediaURLDTO urlDTO)
    {
      var url = string.Format("customer/{0}/vehicle/{1}/media/{2}",
        urlDTO.CustomerId,
        urlDTO.VehicleId,
        urlDTO.VehicleMediaId);

      if (urlDTO.Height.HasValue)
      {
        url += "?tr=h-" + urlDTO.Height.Value;
      }

      return url;
    }

    public static string CustomerMediaUrl(CustomerMediaURLDTO urlDTO)
    {
      return string.Format("{0}/{1}", baseMediaUrl, CustomerMediaKey(urlDTO));
    }

    public static string CustomerMediaKey(CustomerMediaURLDTO urlDTO)
    {
      var mediaCategoryId = (int?)urlDTO.MediaCategory;
      var mediaSubCategoryId = (int?)urlDTO.MediaSubCategory;

      return string.Format("customer/{0}/media/{1}/{2}/{3}",
        urlDTO.CustomerId,
        mediaCategoryId == null ? "-" : mediaCategoryId.ToString(),
        mediaSubCategoryId == null ? "-" : mediaSubCategoryId.ToString(),
        urlDTO.CustomerMediaId);
    }


    public static string VideoMediaUrl(string externalId)
    {
      return $"https://youtu.be/{externalId}";
    }

    public static string MyInvoices()
    {
      return string.Format("/main/invoice/3");
    }

    public static string AppraisalMediaUrl(AppraisalMediaURLDTO urlDTO)
    {
      return string.Format("{0}/{1}", baseMediaUrl, AppraisalMediaKey(urlDTO));
    }
    public static string ProxiedAppraisalMediaUrl(string url)
    {
      return string.Format("{0}/{1}", proxyUrl, url);
    }

    public static string AppraisalMediaKey(AppraisalMediaURLDTO urlDTO)
    {
      return string.Format("customer/{0}/vehicle/{1}/appraisal/{2}/appraisalItem/{3}/{4}",
        urlDTO.CustomerId,
        urlDTO.VehicleId,
        urlDTO.AppraisalId,
        urlDTO.AppraisalItemId,
        urlDTO.AppraisalMediaId);
    }

    public static string InvoiceURL(string customerId, string invoiceId)
    {
      return string.Format("{0}/{1}", invoiceUrl, InvoiceKey(customerId, invoiceId));
    }
    public static string InvoiceKey(string customerId, string invoiceId)
    {
      return string.Format("customer/{0}/invoices/{1}.pdf", customerId, invoiceId);
    }

    public static string GetInvoiceURLKey(Bill bill)
    {
      return $"customer/{bill.CustomerId}/invoices/{bill.InvoiceReference}.pdf";
    }

    public static string GetInvoiceURLKey(string customerId, string invoiceId)
    {
      return $"customer/{customerId}/invoices/{invoiceId}.pdf";
    }

    public static string AdvertURL(Guid advertId, string prefix = "")
    {
      return $"{prefix}/main/listing/{advertId}";
    }

    public static string AlertUnsubscribeURL(Guid alertId, string prefix = "")
    {
      return $"{prefix}/alert/{alertId}/unsubscribe";
    }
  }
}
