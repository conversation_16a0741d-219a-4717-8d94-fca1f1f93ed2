﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.InspectCollect.VehicleData;

public class ICCapProvenanceDTO
{
  public Guid? ICVehicleId { get; set; }

  public string VRM { get; set; }

  public string VIN { get; set; }
  public int Odometer { get; set; }

  // provenance details

  public bool? Scrapped { get; set; }
  public bool? Security { get; set; }
  public bool? Stolen { get; set; }
  public bool? Finance { get; set; }
  public bool? Theft { get; set; }
  public bool? Damage { get; set; }
}
