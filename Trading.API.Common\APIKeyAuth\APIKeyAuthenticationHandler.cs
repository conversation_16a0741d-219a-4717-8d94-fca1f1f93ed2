﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.Encodings.Web;
using System.Threading.Tasks;
using Trading.Services.Interfaces.APIKeyAuth;

namespace Trading.API.Common.APIKeyAuth;

public class ApiKeyAuthenticationHandler : AuthenticationHandler<AuthenticationSchemeOptions>
{
  private const string ApiKeyHeaderName = "X-API-Key";
  private readonly IApiKeyService _apiKeyService;

  public ApiKeyAuthenticationHandler(
      IOptionsMonitor<AuthenticationSchemeOptions> options,
      ILoggerFactory logger,
      UrlEncoder encoder,
      IApiKeyService apiKeyService)
      : base(options, logger, encoder)
  {
    _apiKeyService = apiKeyService;
  }

  protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
  {
    if (!Request.Headers.TryGetValue(ApiKeyHeaderName, out var apiKeyHeaderValues))
    {
      return AuthenticateResult.Fail("API Key is missing");
    }

    var providedApiKey = apiKeyHeaderValues.FirstOrDefault();
    if (string.IsNullOrWhiteSpace(providedApiKey))
    {
      return AuthenticateResult.Fail("API Key is missing");
    }

    var isValid = await _apiKeyService.IsValidApiKeyAsync(providedApiKey);
    if (!isValid)
    {
      return AuthenticateResult.Fail("Invalid API Key");
    }

    // Check if the request requires a specific scope
    var endpoint = Context.GetEndpoint();
    if (endpoint != null)
    {
      var requiredScope = endpoint.Metadata.GetMetadata<RequiredScopeAttribute>()?.Scope;
      if (!string.IsNullOrEmpty(requiredScope))
      {
        var hasRequiredScope = await _apiKeyService.IsValidForScopeAsync(providedApiKey, requiredScope);
        if (!hasRequiredScope)
        {
          return AuthenticateResult.Fail($"API Key does not have the required scope: {requiredScope}");
        }
      }
    }

    var apiKeyDetails = await _apiKeyService.GetApiKeyDetailsAsync(providedApiKey);

    var claims = new List<Claim>();
    claims.Add(new Claim(ClaimTypes.Name, apiKeyDetails.Owner));
    claims.Add(new Claim("ApiKeyId", apiKeyDetails.Id.ToString()));
    claims.Add(new Claim(ClaimTypes.Role, "ApiUser"));

    // Add scopes as claims
    foreach (var scope in apiKeyDetails.Scopes)
    {
      claims.Add(new Claim("scope", scope.Trim()));
    }

    var identity = new ClaimsIdentity(claims, Scheme.Name);
    var principal = new ClaimsPrincipal(identity);
    var ticket = new AuthenticationTicket(principal, Scheme.Name);

    return AuthenticateResult.Success(ticket);
  }
}