using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/widget-input")]
  [ApiController]
  [AllowAnonymous]
  public class ICWidgetInputController : ControllerBase
  {
    private readonly ICContainerWidgetInputInterface _icWidgetInputService;

    public ICWidgetInputController(ICContainerWidgetInputInterface serviceInterface)
    {
      _icWidgetInputService = serviceInterface;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icWidgetInputService.Get(id, null, cancellationToken);
      return Ok(res);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icWidgetInputService.Delete(id);
      return Ok(res);
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Post([FromBody]ICContainerWidgetInputCreateDTO dto, CancellationToken cancellationToken)
    {
      var res = await _icWidgetInputService.Create(dto);
      return Ok(res);
    }

    [HttpGet]
    [Route("search")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICContainerWidgetInputSearchDTO>(query);
      var res = await _icWidgetInputService.Search(dto, cancellationToken);
      return Ok(res);
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<ICContainerWidgetInput> dto)
    {
      var response = await _icWidgetInputService.Patch(id, dto);
      return Ok(response);
    }
  }
}