﻿using AutoMapper;
using DotLiquid.Util;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Moq;
using Stripe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.API.Tests.ClassData;
using Trading.API.Tests.Common;
using Trading.API.Tests.Common.Helpers;
using Trading.API.Tests.Common.Params;
using Trading.Services;
using Trading.Services.Classes;
using Trading.Services.ExternalDTO;
using Trading.Services.Interfaces;
using Xunit;

namespace Trading.API.Tests
{


  [Collection("DatabaseCollection")]
  public class BidService_Test : TestBase
  {
    public DatabaseFixture _fixture;
    
    private struct RemarqUserData
    {
      public Guid CustomerId { get; set; }
      public Guid ContactId { get; set; }
    }

    // for placing bids as a user other than default 
    private readonly List<RemarqUserData> _otherUsers;

    public BidService_Test(DatabaseFixture fixture)
    {
      _fixture = fixture;

      _otherUsers = new List<RemarqUserData>();

      // create new fake contact (if it doesn't already exist)
      var ids = CustomerContactHelper.CreateCustomerAndContact(_context, _common, "Fred Bloggs", "<EMAIL>").GetAwaiter().GetResult();
      _otherUsers.Add(new RemarqUserData { CustomerId = ids.customerId, ContactId = ids.contactId });

      ids = CustomerContactHelper.CreateCustomerAndContact(_context, _common, "Fred Dibnah", "<EMAIL>").GetAwaiter().GetResult();
      _otherUsers.Add(new RemarqUserData { CustomerId = ids.customerId, ContactId = ids.contactId });

    }

    private IBidService GetService()
    {
      var dpDTO = new Mock<IOptionsSnapshot<DefaultPlatformDTO>>();
      dpDTO.SetupAllProperties();
      dpDTO.SetupGet(p => p.Value).Returns(new DefaultPlatformDTO { PlatformId = 1 });
      IBidService service = new BidService(_common.DealService, _common.DbConnection, _context, _mapper, _common.MessageService, 
        _common.InMailService, _common.NegotiationService, _common.EmailService, dpDTO.Object, null, null, _common.StaffNotificationService);

      return service;
    }

    private IAdvertService GetAdvertService(IBidService bidService)
    {
      IAdvertService service = new AdvertService(_context, bidService, _mapper, _common.ContactService, _common.CustomerService
        , _common.LookupService, _common.UserService, _common.MessageService, _common.DbConnection, _common.ContactActionService, null, _common.CustomerInternalInfoService, _common.ServiceQueueService, _common.VehicleCheckService);

      return service;
    }

    private IOfferService GetOfferService(IAdvertService advertService, IBidService bidService, IMessageService messageService)
    {
      IOfferService service = new OfferService(_context, _mapper, advertService, bidService, messageService);

      return service;
    }

    [Fact]
    public async Task MakeOffer_AfterExistingOffer()
    {
      var service = GetService();

      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD21FPX", service);
      await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        buyItNowPrice = 25000,
        startPrice = 1000,
        acceptBids = true,
        bidIncrement = 100,
        saleType = SaleTypeEnum.BuyNow,
        advertStatus = AdvertStatusEnum.Active
      });

      // clear down existing bids for all contacts
      await ClearExistingBids();

      // create an offer bid 
      await CreateOfferBid(service, advertDTO.Id.Value, 18000);

      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      // create an offer bid 
      await CreateOfferBid(service, advertDTO.Id.Value, 20000);

      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      // bid should now exist in db 
      var bids = await service.GetBidderOffers(_testBase.BaseCustomerIdGuid, true, CancellationToken.None);
      Assert.True(bids.Count() == 1);
      Assert.True(bids.First().BidAmt == 20000);
    }

    [Fact]
    public async Task MakeOffer_AfterSaleEnded()
    {
      var service = GetService();

      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD21FPX", service);
      await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        buyItNowPrice = 25000,
        startPrice = 1000,
        acceptBids = true,
        bidIncrement = 100,
        saleType = SaleTypeEnum.BuyNow,
        advertStatus = AdvertStatusEnum.Active
      });

      // clear down existing bids for all contacts
      await ClearExistingBids();

      // end listing 
      await service.EndListing(advertDTO.Id.Value, false, null, CancellationToken.None);

      // create an offer bid 
      await CreateOfferBid(service, advertDTO.Id.Value, 18000);

      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      // bid should now exist in db 
      var bids = await service.GetBidderOffers(_testBase.BaseCustomerIdGuid, true, CancellationToken.None);
      Assert.False(bids.Any());
    }

    [Fact]
    public async Task CounterOffer_LessThanOffer()
    {
      var service = GetService();
      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD21FPX", service);
      await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        buyItNowPrice = 25000,
        startPrice = 1000,
        acceptBids = true,
        bidIncrement = 100,
        saleType = SaleTypeEnum.BuyNow,
        advertStatus = AdvertStatusEnum.Active
      });

      // clear down existing bids for all contacts
      await ClearExistingBids();
      var bidDTO = await CreateOfferBid(service, advertDTO.Id.Value, 5000);

      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      // create counter offer (seller responding as bid not high enough)
      var offerResult = await service.CounterOffer(new BidReplyDTO
      {
        AdvertId = advertDTO.Id,
        BidGuid = bidDTO.BidGuid,
        CustomerId = _testBase.BaseCustomerIdGuid,
        ContactId = _testBase.BaseContactIdGuid,
        OfferAmt = 4000,
        ReplyAction = BidReplyActionEnum.COUNTERBID,
        Expires = DateTime.Now.AddDays(7)
      }
      , CancellationToken.None);

      Assert.False(offerResult.IsValid);
    }

    [Fact]
    public async Task BuyItNow_ExpiredAdvert()
    {
      var service = GetService();

      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD21FPX", service);
      var advert = await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        buyItNowPrice = 25000,
        startPrice = 0,
        acceptBids = false,
        bidIncrement = 0,
        saleType = SaleTypeEnum.BuyNow,
        advertStatus = AdvertStatusEnum.Active
      });

      // clear down existing bids for all contacts
      await ClearExistingBids();

      await service.EndListing(advert.Id, false, null, CancellationToken.None);

      // update advert expiry to a couple of minutes ago 
      advert.EndDateTime = DateTime.Now.Subtract(TimeSpan.FromMinutes(2));
      await _context.SaveChangesAsync();

      // post a buyItNow bid against the advert 
      await CreateStandardBid(service, advertDTO.Id.Value, advert.BuyItNowPrice.Value, false, true);

      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      Assert.True(advert.AdvertStatus == AdvertStatusEnum.Ended);
      Assert.True(advert.SoldStatus != SoldStatusEnum.Sold);

      // check that deal was not created 
      var deal = await _context.Deals.FirstOrDefaultAsync(x => x.AdvertId == advert.Id);
      Assert.Null(deal);
    }

    [Fact]
    public async Task BuyItNow()
    {
      var service = GetService();

      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD21FPX", service);
      var advert = await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        buyItNowPrice = 25000,
        startPrice = 0,
        acceptBids = false,
        bidIncrement = 0,
        saleType = SaleTypeEnum.BuyNow,
        advertStatus = AdvertStatusEnum.Active
      });

      // clear down existing bids for all contacts
      await ClearExistingBids();

      // post a buyItNow bid against the advert 
      await CreateStandardBid(service, advertDTO.Id.Value, advert.BuyItNowPrice.Value, false, true);

      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      // advert should now be sold so check statuses
      Assert.True(advert.AdvertStatus == AdvertStatusEnum.Ended);
      Assert.True(advert.SoldStatus == SoldStatusEnum.Sold);

      // check that deal was created 
      var deal = await _context.Deals.FirstOrDefaultAsync(x => x.AdvertId == advert.Id);
      Assert.NotNull(deal);

      // Check the deal was for the buyItNowPrice
      Assert.True(deal.BidAmt == advert.BuyItNowPrice);

      // check only one deal was created 
      var dealCount = _context.Deals.Count(x => x.AdvertId == advert.Id);
      Assert.True(dealCount == 1);
    }

    [Fact]
    public async Task BuyItNow_ExistingBid()
    {
      var service = GetService();

      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD21FPX", service);
      var advert = await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        buyItNowPrice = 25000,
        startPrice = 0,
        acceptBids = false,
        bidIncrement = 0,
        saleType = SaleTypeEnum.TimedAuction,
        advertStatus = AdvertStatusEnum.Active
      }); 

      // clear down existing bids for all contacts
      await ClearExistingBids();

      // post a buyItNow bid against the advert 
      await CreateStandardBid(service, advertDTO.Id.Value, 3000, false, true);
      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      await CreateStandardBid(service, advertDTO.Id.Value, advert.BuyItNowPrice.Value, true, true);
      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      // advert should now be sold so check statuses
      Assert.True(advert.AdvertStatus != AdvertStatusEnum.Ended);
      Assert.True(advert.SoldStatus != SoldStatusEnum.Sold);

      // check that no deal was created 
      var deal = await _context.Deals.FirstOrDefaultAsync(x => x.AdvertId == advert.Id);
      Assert.Null(deal);
    }

    [Fact]
    public async Task BuyItNow_Two_Sequential_1()
    {
      var service = GetService();

      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD21FPX", service);
      var advert = await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        buyItNowPrice = 25000,
        startPrice = 0,
        acceptBids = false,
        bidIncrement = 0,
        saleType = SaleTypeEnum.BuyNow,
        advertStatus = AdvertStatusEnum.Active
      });

      // clear down existing bids for all contacts
      await ClearExistingBids();

      // post a buyItNow bid against the advert 
      var bid1Id = await CreateStandardBid(service, advertDTO.Id.Value, advert.BuyItNowPrice.Value, false, true);

      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      var bid2Id = await CreateStandardBid(service, advertDTO.Id.Value, advert.BuyItNowPrice.Value, true, true);

      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      // advert should now be sold so check statuses
      Assert.True(advert.AdvertStatus == AdvertStatusEnum.Ended);
      Assert.True(advert.SoldStatus == SoldStatusEnum.Sold);

      // check that deal was created for the first bidder
      var deal = await _context.Deals.FirstOrDefaultAsync(x => x.AdvertId == advert.Id &&
        x.BuyerContactId.Value == _otherUsers[0].ContactId && x.BuyerCustomerId.Value == _otherUsers[0].CustomerId
        && x.BidId == bid1Id);

      Assert.NotNull(deal);

      // check only one deal was created 
      var dealCount = _context.Deals.Count(x => x.AdvertId == advert.Id);
      Assert.True(dealCount == 1);
    }

    [Fact]
    public async Task BuyItNow_Two_Sequential_2()
    {
      var service = GetService();

      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD21FPX", service);
      var advert = await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        buyItNowPrice = 25000,
        startPrice = 0,
        acceptBids = false,
        bidIncrement = 0,
        saleType = SaleTypeEnum.BuyNow,
        advertStatus = AdvertStatusEnum.Active
      });

      // clear down existing bids for all contacts
      await ClearExistingBids();

      // post a buyItNow bid against the advert 
      var bid1Id = await CreateStandardBid(service, advertDTO.Id.Value, advert.BuyItNowPrice.Value, true, true);
      var bid2Id = await CreateStandardBid(service, advertDTO.Id.Value, advert.BuyItNowPrice.Value, false, true);

      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      // advert should now be sold so check statuses
      Assert.True(advert.AdvertStatus == AdvertStatusEnum.Ended);
      Assert.True(advert.SoldStatus == SoldStatusEnum.Sold);

      // check that deal was created 
      var deal = await _context.Deals.FirstOrDefaultAsync(x => x.AdvertId == advert.Id && 
        x.BuyerContactId.Value == _otherUsers[1].ContactId && x.BuyerCustomerId.Value == _otherUsers[1].CustomerId
        && x.BidId == bid1Id);

      Assert.NotNull(deal);

      // check only one deal was created 
      var dealCount = _context.Deals.Count(x => x.AdvertId == advert.Id);
      Assert.True(dealCount == 1);
    }

    [Fact]
    public async Task PlaceBid()
    {
      var service = GetService();

      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD21FPX", service);
      var advert = await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId          = advertDTO.Id.Value,
        buyItNowPrice = 25000,
        startPrice    = 1000,
        acceptBids    = true,
        bidIncrement  = 100,
        saleType      = SaleTypeEnum.BuyNow,
        advertStatus = AdvertStatusEnum.Active,
        vatStatusId = 1
      });

      // clear down existing bids for all contacts
      await ClearExistingBids();

      var advertService = GetAdvertService(service);
      
      // prepare advert for publishing
      advert.Vehicle.VehicleMedia = new List<VehicleMedia> { new VehicleMedia { MediaTypeId = (uint)Data.Enums.MediaTypeEnum.Image } };
      await _context.SaveChangesAsync();

      await advertService.PublishAdvert(_testBase.BaseContactIdGuid, advert.Id, CancellationToken.None);

      // post a standard bid against the advert 
      await CreateStandardBid(service, advertDTO.Id.Value, 1500, true);

      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      // bid should now exist in db 
      var bids = await service.GetActiveBids(_otherUsers[1].ContactId, CancellationToken.None);
      Assert.True(bids.Any());

      // asset that the bid placed has the correct status 
      var bid = bids.First();
      Assert.True(bid.BidStatus == BidStatusEnum.BID_ACCEPTED);
    }


    [Fact]
    public async Task PlaceBid_OnOwnAdvert()
    {
      var service = GetService();

      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD21FPX", service);
      await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        buyItNowPrice = 25000,
        startPrice = 1000,
        acceptBids = true,
        bidIncrement = 100,
        saleType = SaleTypeEnum.BuyNow,
        advertStatus = AdvertStatusEnum.Active
      });

      // clear down existing bids for all contacts
      await ClearExistingBids();

      // post a standard bid against the advert 
      await CreateStandardBid(service, advertDTO.Id.Value, 1500);

      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      // bid should now exist in db 
      var bids = await service.GetActiveBids(_testBase.BaseContactIdGuid, CancellationToken.None);
      Assert.False(bids.Any());
    }

    [Fact]
    public async Task MakeOffer()
    {
      var service = GetService();

      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD21FPX", service);
      await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId          = advertDTO.Id.Value,
        buyItNowPrice = 25000,
        startPrice    = 1000,
        acceptBids    = true,
        bidIncrement  = 100,
        saleType      = SaleTypeEnum.BuyNow,
        advertStatus = AdvertStatusEnum.Active
      });

      // clear down existing bids for all contacts
      await ClearExistingBids();

      // create an offer bid 
      await CreateOfferBid(service, advertDTO.Id.Value, 18000);

      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      // bid should now exist in db 
      var bids = await service.GetBidderOffers(_testBase.BaseCustomerIdGuid, true, CancellationToken.None);
      Assert.True(bids.Any());

      // asset that the bid placed has the correct status 
      var bid = bids.First();
      Assert.True(bid.BidStatus == BidStatusEnum.BID_WITHVENDOR);
    }

    [Fact]
    public async Task AcceptExpiredOffer()
    {
      var service = GetService();

      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD21FPX", service);
      await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        buyItNowPrice = 25000,
        startPrice = 1000,
        acceptBids = true,
        bidIncrement = 100,
        saleType = SaleTypeEnum.BuyNow,
        advertStatus = AdvertStatusEnum.Active
      });

      // clear down existing bids for all contacts
      await ClearExistingBids();

      // create an offer bid 
      var offerBid = await CreateOfferBid(service, advertDTO.Id.Value, 18000, false, DateTime.Now.Subtract(TimeSpan.FromMinutes(1)));

      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);
      
      // attempt to accept the bid 
      var result = await service.AcceptBid(new BidReplyDTO { BidGuid = offerBid.BidGuid, Comment = "Expired" }, CancellationToken.None);

      // bid has expired so result should be invalid 
      Assert.False(result.IsValid);

      // ensure advert has not ended
      var advert = await _context.Adverts.FirstOrDefaultAsync(x => x.Id == advertDTO.Id);
      Assert.True(advert.AdvertStatus != AdvertStatusEnum.Ended);
    }

    [Fact]
    public async Task AcceptCounterOffer_SaleEnded()
    {
      var service = GetService();
      var advertService = GetAdvertService(service);

      var offerService = GetOfferService(advertService, service, _common.MessageService);

      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD21FPX", service);
      var advert = await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        buyItNowPrice = 25000,
        startPrice = 1000,
        acceptBids = true,
        bidIncrement = 100,
        saleType = SaleTypeEnum.BuyNow,
        advertStatus = AdvertStatusEnum.Active
      });

      // clear down existing bids for all contacts
      await ClearExistingBids();

      // create the offer bid
      BidDTO bidOffer = await CreateOfferBid(service, advertDTO.Id.Value, 18000, true);
      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      // create counter offer (seller responding as bid not high enough)
      var offerResult = await service.CounterOffer(new BidReplyDTO
      {
        AdvertId = advertDTO.Id,
        BidGuid = bidOffer.BidGuid,
        CustomerId = _otherUsers[1].CustomerId,
        ContactId = _otherUsers[1].ContactId,
        OfferAmt = 20000,
        ReplyAction = BidReplyActionEnum.COUNTERBID,
        Expires = DateTime.Now.AddDays(7)
      }
      , CancellationToken.None);

      Assert.True(offerResult.IsValid && offerResult.DTO != null);

      await service.EndListing(advert.Id, false, null, CancellationToken.None);

      // bidder accepts counter offer of 20,000 (can either accept or reject)
      var response = await offerService.CounterOfferResponse(offerResult.DTO.Id.Value, new CounterOfferResponseDTO
      {
        Accept = true,
        CustomerId = _otherUsers[1].CustomerId,
        ContactId = _otherUsers[1].ContactId,
      }, CancellationToken.None);

      Assert.False(response.IsValid);
    }

    [Fact]
    public async Task AcceptCounterOffer_Expired()
    {
      var service = GetService();
      var advertService = GetAdvertService(service);

      var offerService = GetOfferService(advertService, service, _common.MessageService);

      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD21FPX", service);
      var advert = await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        buyItNowPrice = 25000,
        startPrice = 1000,
        acceptBids = true,
        bidIncrement = 100,
        saleType = SaleTypeEnum.BuyNow,
        advertStatus = AdvertStatusEnum.Active
      });

      // clear down existing bids for all contacts
      await ClearExistingBids();

      // create the offer bid
      BidDTO bidOffer = await CreateOfferBid(service, advertDTO.Id.Value, 18000, true);
      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      // create counter offer (seller responding as bid not high enough)
      var offerResult = await service.CounterOffer(new BidReplyDTO
      {
        AdvertId = advertDTO.Id,
        BidGuid = bidOffer.BidGuid,
        CustomerId = _otherUsers[1].CustomerId,
        ContactId = _otherUsers[1].ContactId,
        OfferAmt = 20000,
        ReplyAction = BidReplyActionEnum.COUNTERBID,
        Expires = DateTime.Now.Subtract(TimeSpan.FromMinutes(2)),
      }
      , CancellationToken.None);

      Assert.True(offerResult.IsValid && offerResult.DTO != null);

      // bidder accepts counter offer of 20,000 (can either accept or reject)
      var response = await offerService.CounterOfferResponse(offerResult.DTO.Id.Value, new CounterOfferResponseDTO
      {
        Accept = true,
        CustomerId = _otherUsers[1].CustomerId,
        ContactId = _otherUsers[1].ContactId,
      }, CancellationToken.None);

      Assert.False(response.IsValid);
    }

    [Fact]
    public async Task AcceptOffer()
    {
      var service = GetService();

      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD21FPX", service);
      await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        buyItNowPrice = 25000,
        startPrice = 1000,
        acceptBids = true,
        bidIncrement = 100,
        saleType = SaleTypeEnum.BuyNow,
        advertStatus = AdvertStatusEnum.Active
      });

      // clear down existing bids for all contacts
      await ClearExistingBids();

      // create the offer bid
      BidDTO bidOffer = await CreateOfferBid(service, advertDTO.Id.Value, 18000);

      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      // accept the bid 
      await service.AcceptBid(new BidReplyDTO { BidGuid = bidOffer.BidGuid, Comment = "Test Comment" }, CancellationToken.None);

      // test that the advert listing has ended 
      var advert = await _context.Adverts.FirstOrDefaultAsync(x => x.Id == advertDTO.Id);
      Assert.NotNull(advert);

      Assert.True(advert.AdvertStatus == AdvertStatusEnum.Ended);
    }

    [Fact]
    public async Task AcceptCounterOffer()
    {
      var service = GetService();
      var advertService = GetAdvertService(service);

      var offerService = GetOfferService(advertService, service, _common.MessageService);

      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD21FPX", service);
      var advert = await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        buyItNowPrice = 25000,
        startPrice = 1000,
        acceptBids = true,
        bidIncrement = 100,
        saleType = SaleTypeEnum.BuyNow,
        vatStatusId = 1
      });

      // clear down existing bids for all contacts
      await ClearExistingBids();

      // prepare advert for publishing
      advert.Vehicle.VehicleMedia = new List<VehicleMedia> { new VehicleMedia { MediaTypeId = (uint)Data.Enums.MediaTypeEnum.Image } };
      await _context.SaveChangesAsync();

      await advertService.PublishAdvert(_testBase.BaseContactIdGuid, advert.Id, CancellationToken.None);

      // create the offer bid
      BidDTO bidOffer = await CreateOfferBid(service, advertDTO.Id.Value, 18000, true);
      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      // create counter offer (seller responding as bid not high enough)
      var offerResult = await service.CounterOffer(new BidReplyDTO { 
        AdvertId = advertDTO.Id, 
        BidGuid = bidOffer.BidGuid, 
        CustomerId = _otherUsers[1].CustomerId, 
        ContactId = _otherUsers[1].ContactId, 
        OfferAmt = 20000, 
        ReplyAction = BidReplyActionEnum.COUNTERBID, 
        Expires = DateTime.Now.AddDays(7) }
      , CancellationToken.None);

      Assert.True(offerResult.IsValid && offerResult.DTO != null);

      // bidder accepts counter offer of 20,000 (can either accept or reject)
      var response = await offerService.CounterOfferResponse(offerResult.DTO.Id.Value, new CounterOfferResponseDTO { 
        Accept = true,
        CustomerId = _otherUsers[1].CustomerId,
        ContactId = _otherUsers[1].ContactId,
      }, CancellationToken.None);

      Assert.True(response.IsValid);

      // after accepting the counter offer, the listing should end and deal created etc.

      // test that the advert listing has ended 
      var advertT = await _context.Adverts.FirstOrDefaultAsync(x => x.Id == advertDTO.Id && x.AdvertStatus == AdvertStatusEnum.Ended);
      Assert.NotNull(advertT);

      // check that deal is created 
      var deal = await _context.Deals.FirstOrDefaultAsync(x => x.AdvertId == advertDTO.Id);
      Assert.NotNull(deal);

      // check only one deal is created 
      var dealCount = _context.Deals.Count(x => x.AdvertId == advertDTO.Id);
      Assert.True(dealCount == 1);

      // todo: check that orders are created


      // advert should now be sold so check statuses
      Assert.True(advert.AdvertStatus == AdvertStatusEnum.Ended);
      Assert.True(advert.SoldStatus == SoldStatusEnum.Sold);
    }

    [Fact]
    public async Task BidTooSoon()
    {
      var service = GetService();

      // create auction advert that hasn't yet begun 
      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD21FPX", service);
      await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        buyItNowPrice = 25000,
        startPrice = 1000,
        acceptBids = true,
        bidIncrement = 100,
        saleType = SaleTypeEnum.TimedAuction,
        startDate = DateTime.Now.AddHours(1),
        advertStatus = AdvertStatusEnum.Active
      });

      var bidId = await CreateStandardBid(service, advertDTO.Id.Value, 1500);
      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      var bid = await _context.Bids.FirstOrDefaultAsync(x => x.Id == bidId);
      Assert.NotNull(bid);
      Assert.True(bid.BidStatus == BidStatusEnum.BID_TOOSOON);
    }

    [Fact]
    public async Task BidTooLate()
    {
      var service = GetService();

      // create auction advert that hasn't yet begun 
      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD21FPX", service);
      await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        buyItNowPrice = 25000,
        startPrice = 1000,
        acceptBids = true,
        bidIncrement = 100,
        saleType = SaleTypeEnum.TimedAuction,
        startDate = DateTime.Now.Subtract(TimeSpan.FromDays(1)),
        endDate = DateTime.Now.Subtract(TimeSpan.FromMinutes(10)),
        advertStatus = AdvertStatusEnum.Active
      });

      var bidId = await CreateStandardBid(service, advertDTO.Id.Value, 1500);
      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      var bid = await _context.Bids.FirstOrDefaultAsync(x => x.Id == bidId);
      Assert.NotNull(bid);
      Assert.True(bid.BidStatus == BidStatusEnum.BID_TOOLATE);
    }

    [Fact]
    public async Task BidTooLow()
    {
      var service = GetService();

      // create auction advert that hasn't yet begun 
      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD21FPX", service);
      await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        buyItNowPrice = 25000,
        startPrice = 1000,
        acceptBids = true,
        bidIncrement = 100,
        saleType = SaleTypeEnum.TimedAuction,
        autoAcceptBid = 24500,
        autoRejectBid = 19000,
        advertStatus = AdvertStatusEnum.Active
      });

      // clear down existing bids for all contacts
      await ClearExistingBids();

      var bidId = await CreateStandardBid(service, advertDTO.Id.Value, 18000);
      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      var bid = await _context.Bids.FirstOrDefaultAsync(x => x.Id == bidId);
      Assert.NotNull(bid);
      Assert.True(bid.BidStatus == BidStatusEnum.BID_TOOLOW);
    }

    [Fact]
    public async Task BidTooLow_2()
    {
      var service = GetService();

      // create auction advert that hasn't yet begun 
      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD21FPX", service);
      await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        buyItNowPrice = 25000,
        startPrice = 1000,
        acceptBids = true,
        bidIncrement = 100,
        saleType = SaleTypeEnum.TimedAuction,
        advertStatus = AdvertStatusEnum.Active,
      });

      // clear down existing bids for all contacts
      await ClearExistingBids();

      var bidId = await CreateStandardBid(service, advertDTO.Id.Value, 2000);
      var bidId2 = await CreateStandardBid(service, advertDTO.Id.Value, 1500);

      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      var bid = await _context.Bids.FirstOrDefaultAsync(x => x.Id == bidId2);
      Assert.NotNull(bid);
      Assert.True(bid.BidStatus == BidStatusEnum.BID_TOOLOW);
    }

    [Fact]
    public async Task CurrentPrice_WithReserve_MultipleBids_SameBidder()
    {
      // Comment:
      // Same user placing multiple bids on a lot with a reserve, but not meeting reserve
      // Current Price should be users highest bid
      
      var service = GetService();

      // create auction advert that hasn't yet begun 
      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD21FPX", service);
      await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        buyItNowPrice = 25000,
        startPrice = 1000,
        acceptBids = true,
        bidIncrement = 100,
        saleType = SaleTypeEnum.TimedAuction,
        autoAcceptBid = 24500,
        autoRejectBid = 19000,
        reserve = 23500,
        advertStatus = AdvertStatusEnum.Active
      });

      // clear down existing bids for all contacts
      await ClearExistingBids();

      await CreateStandardBid(service, advertDTO.Id.Value, 22000);
      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      // current price on the advert should be 22k
      var advert = await _context.Adverts.FirstOrDefaultAsync(x => x.Id == advertDTO.Id);
      Assert.True(advert.CurrentPrice == 22000);

      // outbid yourself with 22.5k 
      await CreateStandardBid(service, advertDTO.Id.Value, 22500);
      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      // current price should be 22.5k 
      Assert.True(advert.CurrentPrice == 22500);
    }

    [Fact]
    public async Task NoReserve_SingleBidder_ListingEnds()
    {
      var service = GetService();

      // create auction advert
      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD21FPX", service);
      var advert = await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        startPrice = 100,
        acceptBids = true,
        bidIncrement = 100,
        saleType = SaleTypeEnum.TimedAuction,
        reserve = 0,
        advertStatus = AdvertStatusEnum.Active
      });

      // clear down existing bids for all contacts
      await ClearExistingBids();

      await CreateStandardBid(service, advertDTO.Id.Value, 355);
      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      advert.EndDateTime = DateTime.Now;
      await service.EndListing(advert.Id, false, null, CancellationToken.None);

      // check that deal was created 
      var deal = await _context.Deals.FirstOrDefaultAsync(x => x.AdvertId == advert.Id);
      Assert.NotNull(deal);
    }

    [Fact]
    public async Task CurrentPrice_WithReserve_MultipleBids_SameBidder_RetainReserve()
    {
      // Comment:
      // Same user placing multiple bids on a lot with a reserve, ane exceeding reserve
      // Current Price should be the reserve price

      var service = GetService();

      // create auction advert
      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD21FPX", service);
      await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        buyItNowPrice = 25000,
        startPrice = 1000,
        acceptBids = true,
        bidIncrement = 100,
        saleType = SaleTypeEnum.TimedAuction,
        autoAcceptBid = 24500,
        autoRejectBid = 19000,
        reserve = 23500,
        advertStatus = AdvertStatusEnum.Active
      });

      // clear down existing bids for all contacts
      await ClearExistingBids();

      await CreateStandardBid(service, advertDTO.Id.Value, 22000);
      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      // current price on the advert should be 22k
      var advert = await _context.Adverts.FirstOrDefaultAsync(x => x.Id == advertDTO.Id);
      Assert.True(advert.CurrentPrice == 22000);

      // outbid yourself with 22.5k 
      await CreateStandardBid(service, advertDTO.Id.Value, 24000);
      await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);

      // current price should be 23.5k (reserve)
      Assert.True(advert.CurrentPrice == 23500);
    }

    // see BidTestClassData for test descriptions
    [Theory]
    [ClassData(typeof(BidTestClassData))]
    public async Task CurrentPrice_WithReserve_MultipleBids_LeadingBidder_Tests(BidTestData data)
    {
      var service = GetService();

      // create auction advert with reserve
      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD62FPX", service);
      await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        startPrice = data.StartingPrice,
        acceptBids = true,
        bidIncrement = 100,
        saleType = SaleTypeEnum.TimedAuction,
        reserve = data.Reserve,
        advertStatus = AdvertStatusEnum.Active
      });

      // clear down existing bids for all contacts
      await ClearExistingBids();

      foreach(var bid in data.Bids)
      {
        await CreateStandardBid(service, advertDTO.Id.Value, bid.BidAmount, bid.IsOtherUserBid);
        await service.ProcessBids(advertDTO.Id.Value, false, CancellationToken.None);
      }

      var advert = await _context.Adverts.Include(x => x.TopBid)
        .AsNoTracking()
        .FirstOrDefaultAsync(x => x.Id == advertDTO.Id);

      // assert current price is correct
      Assert.True(advert.CurrentPrice == data.ExpectedCurrentPrice);

      // assert leading bidder is 'other' user
      Guid t1 = data.IsOtherUserExpectedTopBidder ? _otherUsers[1].ContactId : _otherUsers[0].ContactId;
      Guid t2 = data.IsOtherUserExpectedTopBidder ? _otherUsers[1].CustomerId : _otherUsers[0].CustomerId;

      if (!data.NoTopBid)
      {
        Assert.True(advert.TopBid != null);
        Assert.True(advert.TopBid.ContactId == t1 && advert.TopBid.CustomerId == t2);
      } 
      else
      {
        Assert.True(advert.TopBid == null);
      }
    }

    [Fact]
    public async Task RejectBidWhenCounterOfferExpired()
    {
      // if the advert owner responds to an offer from a bidder when the offer has expired, it should be rejected 
      var service = GetService();

      // create auction advert with reserve
      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD21FPX", service);
      await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        startPrice = 0,
        acceptBids = true,
        bidIncrement = 100,
        saleType = SaleTypeEnum.TimedAuction,
        autoAcceptBid = 5000,
        autoRejectBid = 4000,
        advertStatus = AdvertStatusEnum.Active
      });

      // clear down existing bids for all contacts
      await ClearExistingBids();

      // create an offer less than auto-accept and greater than auto-reject
      var result = await service.BidderOffer(new BidDTO
      {
        AdvertId = advertDTO.Id.Value,
        BidAmt = 4200,
        ContactId = _otherUsers[0].ContactId,
        CustomerId = _otherUsers[0].CustomerId,
        Expires = DateTime.Now.Subtract(TimeSpan.FromMinutes(1)),
      }, CancellationToken.None);

      // respond to the offer 

    }

    [Fact]
    public async Task EndListing_TimedAuction_NoBids()
    {
      var advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "DG67CTO");
      var advert = await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        startPrice = 100,
        acceptBids = true,
        bidIncrement = 100,
        saleType = SaleTypeEnum.TimedAuction,
        endDate = DateTime.Now.Subtract(TimeSpan.FromSeconds(2)),
        startDate = DateTime.Now.AddMinutes(-2),
        vatStatusId = 1,
        reserve = 0
      });

      // prepare advert for publishing
      advert.Vehicle.VehicleMedia = new List<VehicleMedia> { new VehicleMedia { MediaTypeId = (uint)Data.Enums.MediaTypeEnum.Image } };

      await _context.SaveChangesAsync();

      await ClearExistingBids();

      // create advert service instance 
      var service = GetService();
      var advertService = GetAdvertService(service);

      await advertService.PublishAdvert(_testBase.BaseContactIdGuid, advert.Id, CancellationToken.None);

      // end the listing 
      await service.EndListings(CancellationToken.None);

      // ensure the listing has ended
      Assert.True(advert.AdvertStatus == AdvertStatusEnum.Ended);
      Assert.True(advert.SoldStatus == SoldStatusEnum.Unsold);
    }

    [Fact]
    public async Task EndListing()
    {
      var advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "DG67CTO");
      var advert = await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        saleType = SaleTypeEnum.BuyNow,
        vatStatusId = 1,
        reserve = 2000,
      });

      advert.Vehicle.VehicleMedia = new List<VehicleMedia> { new VehicleMedia { MediaTypeId = (uint)Data.Enums.MediaTypeEnum.Image } };

      // create advert service instance 
      var service = GetService();
      var advertService = GetAdvertService(service);

      await advertService.PublishAdvert(_testBase.BaseContactIdGuid, advert.Id, CancellationToken.None);

      // end the listing 
      await service.EndListing(advert.Id, false, null, CancellationToken.None);

      // ensure the listing has ended
      Assert.True(advert.AdvertStatus == AdvertStatusEnum.Ended);
      Assert.True(advert.SoldStatus == SoldStatusEnum.Unsold);
    }

    private async Task<uint> CreateStandardBid(IBidService service, Guid adId, uint bidAmount, bool useOtherCustomer = false, bool buyItNow = false)
    {
      var bidId = await service.CreateBid(new BidDTO
      {
        AdvertId = adId,
        ContactId = useOtherCustomer ? _otherUsers[1].ContactId : _otherUsers[0].ContactId,
        CustomerId = useOtherCustomer ? _otherUsers[1].CustomerId : _otherUsers[0].CustomerId,
        BidAmt = bidAmount,
        Expires = DateTime.Now.AddDays(7),
        BuyItNow = buyItNow,
        StatusId = (uint)StatusEnum.Active,
        IsOffer = false
      }, CancellationToken.None
      );

      return bidId.Value;
    }


    private async Task<BidDTO> CreateOfferBid(IBidService service, Guid adId, uint bidAmount, bool useOtherCustomer = false, DateTime? expires = null)
    {
      return await service.BidderOffer(
        new BidDTO
        {
          AdvertId = adId,
          ContactId = useOtherCustomer ? _otherUsers[1].ContactId : _otherUsers[0].ContactId,
          CustomerId = useOtherCustomer ? _otherUsers[1].CustomerId : _otherUsers[0].CustomerId,
          BidAmt = bidAmount,
          Expires = expires.HasValue ? expires.Value : DateTime.Now.AddDays(7),
          BuyItNow = false,
          StatusId = (uint)StatusEnum.Active,
          IsOffer = true,
          Offer = new OfferDTO
          {
            ContactId = useOtherCustomer ? _otherUsers[1].ContactId : _otherUsers[0].ContactId,
            CustomerId = useOtherCustomer ? _otherUsers[1].CustomerId : _otherUsers[0].CustomerId,
            Expires = DateTime.Now.AddDays(7),
            OfferAmt = (uint)bidAmount
          }
        }
        , CancellationToken.None
      );
    }

    private async Task ClearExistingBids()
    {
      var existing = _context.Bids.Where(x => x.ContactId == _testBase.BaseContactIdGuid 
        || _otherUsers.Select(y => y.ContactId).ToList().Contains(x.ContactId.Value)).ToList();
      var listIds = existing.Select(x => x.Id).ToList();

      var ads = await _context.Adverts.Where(x => listIds.Contains(x.TopBidId.Value)).ToListAsync();
      foreach (var ad in ads)
      {
        ad.TopBidGuid = null;
        ad.TopBidId = null;
      }

      _context.Bids.RemoveRange(existing);

      await _context.SaveChangesAsync();
    }

  }
}
