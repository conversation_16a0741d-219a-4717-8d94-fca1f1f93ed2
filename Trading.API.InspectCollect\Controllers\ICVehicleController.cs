using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect.VehicleData;
using Trading.API.Data.Models.InspectCollect.VehicleData;
using Trading.Services.Extensions;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/vehicles")]
  [ApiController]
  [Authorize(Policy = "RequireInspectCollect")]
  public class ICVehicleController : ControllerBase
  {
    private readonly ICVehicleInterface _icVehicleService;
    private readonly ICResponseInterface _icResponseService;

    public ICVehicleController(ICVehicleInterface serviceInterface, ICResponseInterface icResponseService)
    {
      _icVehicleService = serviceInterface;
      _icResponseService = icResponseService;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(Guid id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      ICVehicleSearchDTO dto = null;

      if (!string.IsNullOrEmpty(query))
      {
        dto = JsonConvert.DeserializeObject<ICVehicleSearchDTO>(query);
      }

      var res = await _icVehicleService.Get(id, dto, cancellationToken);

      if (!res.IsValid)
      {
        return NotFound();
      }

      // Validate access through the response's container group
      if (res.DTO.CreatedByResponseId.HasValue)
      {
        var response = await _icResponseService.Get(res.DTO.CreatedByResponseId.Value, null, cancellationToken);

        if (response.IsValid && response.DTO.ICContainerGroupId != User.ICContainerGroupId() && !User.IsAdmin())
        {
          return Forbid();
        }
      }

      return Ok(res);
    }

    [HttpGet]
    [Route("search")]
    [Route("/api/inspect-collect/vehicles")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICVehicleSearchDTO>(query);

      // For non-admin users, filter by their container group through responses
      if (!User.IsAdmin())
      {
        dto.Filters.ContainerGroupId = User.ICContainerGroupId();
      }

      // Forbid requests without a container group id for non-admin users
      if (!User.IsAdmin() && dto.Filters.ContainerGroupId == null)
      {
        return BadRequest("ContainerGroupId is required in the search query.");
      }

      var res = await _icVehicleService.Search(dto, cancellationToken);
      return Ok(res);
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<ActionResult> Patch(Guid id, [FromBody] JsonPatchDocument<ICVehicle> patch, CancellationToken cancellationToken)
    {
      // First get the existing vehicle to validate access
      var existing = await _icVehicleService.Get(id, null, cancellationToken);

      if (!existing.IsValid)
      {
        return NotFound();
      }


      // Validate access through the response's container group
      if (existing.DTO.CreatedByResponseId.HasValue)
      {
        var response = await _icResponseService.Get(existing.DTO.CreatedByResponseId.Value, null, cancellationToken);

        if (response.IsValid && response.DTO.ICContainerGroupId != User.ICContainerGroupId() && !User.IsAdmin())
        {
          return Forbid();
        }
      }


      var res = await _icVehicleService.Patch(id, patch, new CancellationToken(), null);
      return Ok(res);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      // First get the existing vehicle to validate access
      var existing = await _icVehicleService.Get(id, null, cancellationToken);

      if (!existing.IsValid)
      {
        return NotFound();
      }

      // Validate access through the response's container group
      if (existing.DTO.CreatedByResponseId.HasValue)
      {
        var response = await _icResponseService.Get(existing.DTO.CreatedByResponseId.Value, null, cancellationToken);

        if (response.IsValid && response.DTO.ICContainerGroupId != User.ICContainerGroupId() && !User.IsAdmin())
        {
          return Forbid();
        }
      }

      // To implement if we want later..
      // var res = await _icVehicleService.Delete(id);
      return Ok();
    }
  }
}
