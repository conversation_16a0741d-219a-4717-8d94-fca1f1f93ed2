using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.Services.Extensions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Threading;
using System;
using Trading.API.Data.Models;
using Trading.API.Data.DTO;
using Trading.Services.Interfaces;
using Trading.API.Helpers;
using Trading.API.Data;
using System.Linq;
using Microsoft.EntityFrameworkCore;

namespace Trading.API.CustomerCRM.Controllers
{
  [Route("api/customer")]
  [ApiController]
  [Authorize]
  public class CustomerMediaController : ControllerBase
  {
    private readonly ICustomerMediaService _customerMediaService;
    private readonly TradingContext _tradingContext;

    public CustomerMediaController(ICustomerMediaService customerMediaService, TradingContext tradingContext)
    {
      _customerMediaService = customerMediaService;
      _tradingContext = tradingContext;
    }

    //[ResponseCache(Duration = 60)]
    [HttpGet]
    [Route("{customerId}/media")]
    public async Task<IActionResult> Search(Guid customerId, [FromQuery] string? query, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin() && User.CustomerId() != customerId)
      {
        return Forbid();
      }

      try
      {
        var dto = new CustomerMediaSearchDTO();

        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<CustomerMediaSearchDTO>(query);
        }

        var result = await _customerMediaService.Search(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("{customerId}/media")]
    public async Task<IActionResult> Create(Guid customerId, [FromBody] CustomerMediaUploadContainerDTO data, CancellationToken cancellationToken)
    {
      if (User.CustomerId() != customerId && !User.IsAdmin())
      {
        return Forbid();
      }

      data.CustomerMediaUploadDTO.Files = FormFileHelper.DataURLsToFormFiles(data.Files);

      try
      {
        var medias = await _customerMediaService.UploadMedia(data.CustomerMediaUploadDTO, cancellationToken);
        return Ok(medias);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{customerId}/media/{id}")]
    public async Task<IActionResult> Get(
      Guid customerMediaId,
      [FromQuery] string query,
      CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = JsonConvert.DeserializeObject<CustomerMediaSearchDTO>(query);
        var result = await _customerMediaService.Get(customerMediaId, dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("{customerId}/media/{id}")]
    public async Task<IActionResult> Patch(Guid customerId, Guid id, JsonPatchDocument<CustomerMedia> patch, CancellationToken cancellationToken)
    {
      if (User.CustomerId() != customerId && !User.IsAdmin())
      {
        return Forbid();
      }

      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _customerMediaService.Patch(id, patch, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpDelete]
    [Route("/api/customer/{customerId}/media/{mediaId}")]
    public async Task<IActionResult> CustomerMediaDelete(Guid customerId, Guid mediaId, CancellationToken cancellationToken)
    {
      if (User.CustomerId() != customerId && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _customerMediaService.SafeDelete(customerId, mediaId, cancellationToken);

        if (result)
        {
          return Ok(result);
        }
        else
        {
          return NotFound(result);
        }
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<IActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      // Admin Only Function
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _customerMediaService.Delete(id, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
