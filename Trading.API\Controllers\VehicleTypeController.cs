using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/[controller]")]
  [ApiController]
  public class VehicleTypeController : ControllerBase
  {
    private IVehicleTypeService _vehicleTypeService;

    public VehicleTypeController(
      IVehicleTypeService vehicleTypeService
     )
    {
      this._vehicleTypeService = vehicleTypeService;
    }

    [HttpGet]
    [Route("/api/vehicleTypes")]
    public async Task<ActionResult<IEnumerable<VehicleTypeDTO>>> GetVehicleTypes([FromQuery] string? searchDTO, CancellationToken ct)
    {
      var search = new VehicleTypeSearchDTO() { };

      if (!String.IsNullOrEmpty(searchDTO))
      {
        search = JsonSerializer.Deserialize<VehicleTypeSearchDTO>(searchDTO);
      }

      var x = await _vehicleTypeService.Search(search, ct);


      return Ok(x);
    }

  }
}
