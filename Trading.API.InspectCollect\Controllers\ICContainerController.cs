using Amazon;
using Amazon.DynamoDBv2;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.InspectCollect.Interfaces;
using Amazon.DynamoDBv2.Model;
using Amazon.DynamoDBv2.DocumentModel;
using Amazon.Runtime;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/container")]
  [ApiController]
  [AllowAnonymous]
  public class ICContainerController : ControllerBase
  {
    private readonly ICContainerInterface _icContainerService;

    public ICContainerController(ICContainerInterface serviceInterface)
    {
      _icContainerService = serviceInterface;
    }

    [HttpGet]
    [Route("saveToDynamo")]
    public async Task<ActionResult> SaveToDynamo()
    {
      var credentials = new BasicAWSCredentials("********************", "nIu+SFE5GwhnIuvsS9CK6CO6xlCBkfb7PNpxkBNw");

      AmazonDynamoDBConfig clientConfig = new AmazonDynamoDBConfig()
      {
        RegionEndpoint = RegionEndpoint.EUWest2
      };

      // This client will access the US East 1 region.
      AmazonDynamoDBClient client = new AmazonDynamoDBClient(credentials);

      var x = new { abc = new { def = new { hij = "abc" } } };

      var z = JsonConvert.SerializeObject(x);

      var document = Document.FromJson(z);

      // Define the item to be inserted or updated
      var item = new Dictionary<string, AttributeValue>
            {
                { "PartitionKey", new AttributeValue { S = "ic_response_id" } }, // Replace with your partition key
                { "ic_response_id", new AttributeValue { S = "1er" } },
        { "data", new AttributeValue(document) }
            };

      var result = await client.PutItemAsync("DynamoDavid",item);

      return Ok();
    }


    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(Guid id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      ICContainerSearchDTO searchDTO;

      if (query == null)
      {
        searchDTO = new ICContainerSearchDTO() { Filters = new ICContainerSearchFilters() { Id = id } };
      }
      else
      {
        searchDTO = JsonConvert.DeserializeObject<ICContainerSearchDTO>(query);
      }

      var res = await _icContainerService.Get(id, searchDTO, cancellationToken);
      return Ok(res);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icContainerService.Delete(id);
      return Ok(res);
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Post([FromBody]ICContainerCreateDTO dto, CancellationToken cancellationToken)
    {
      var res = await _icContainerService.Create(dto);
      return Ok(res);
    }

    [HttpPost]
    [Route("{icContainerGroupId}/{icContainerId}/snapshot")]
    public async Task<ActionResult> SnapShot(Guid icContainerGroupId, Guid icContainerId, IFormFile file, CancellationToken cancellationToken)
    {
      var res = await _icContainerService.SaveSnapshot(icContainerGroupId, icContainerId, file, cancellationToken);

      return Ok(res);
    }

    [HttpGet]
    [Route("/api/inspect-collect/layout/{layoutId}/containers")]
    public async Task<IActionResult> LayoutContainers([FromQuery] Guid layoutId, [FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICContainerSearchDTO>(query) ?? new ICContainerSearchDTO();

      dto.Filters.Id = layoutId;

      var res = await _icContainerService.Search(dto, cancellationToken);
      return Ok(res);
    }

    [HttpGet]
    [Route("/api/inspect-collect/container-group/{containerGroupId}/containers")]
    public async Task<IActionResult> ContainerGroupContainers(Guid containerGroupId, [FromQuery] string query, CancellationToken cancellationToken)
    {
      ICContainerSearchDTO dto = new ICContainerSearchDTO();

      if (!String.IsNullOrEmpty(query))
      {
        dto = JsonConvert.DeserializeObject<ICContainerSearchDTO>(query);
      }

      dto.Filters.ICContainerGroupId = containerGroupId;
      var res = await _icContainerService.Search(dto, cancellationToken);
      return Ok(res);
    }

    [HttpGet]
    [Route("/api/inspect-collect/containers")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICContainerSearchDTO>(query);
      var res = await _icContainerService.Search(dto, cancellationToken);
      return Ok(res);
    }


    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<ICContainer> dto)
    {
      var response = await _icContainerService.Patch(id, dto);
      return Ok(response);
    }
  }
}