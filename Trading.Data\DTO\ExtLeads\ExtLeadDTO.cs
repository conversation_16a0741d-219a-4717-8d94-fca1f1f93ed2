﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.ExtLeads
{
  public class ExtLeadDTO : BaseModelEntityDTO
  {
    public uint? Source { get; set; }
    public string Company { get; set; }
    public string Location { get; set; }
    public string Postcode { get; set; }
    public uint? Last30Days { get; set; }

    public string ContactName { get; set; }
    public string ContactEmail { get; set; }
    public string ContactPhone { get; set; }

    public Guid? CustomerId { get; set; }
    public CustomerDTO Customer { get; set; }
    public string Notes { get; set; }

    public bool CanImport { get; set; }
  }

  public class ExtLeadSearchDTO : BaseSearchDTO
  {
    public ExtLeadFiltersDTO Filters { get; set; } = new ExtLeadFiltersDTO() { };
  }

  public class ExtLeadFiltersDTO : BaseFilterGuid
  {
    public Guid? ExtLeadId { get; set; }
    public string Dealer { get; set; }
    public string Vehicle { get; set; }
    public string Postcode { get; set; }

  }

  public class ExtLeadJSONDTO
  {
    public string json { get; set; }

  }
}
