using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect.VehicleData;
using Trading.API.Data.Models.InspectCollect.VehicleData;
using Trading.Services.Extensions;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/outcome")]
  [ApiController]
  [Authorize(Policy = "RequireInspectCollect")]
  public class ICOutcomeController : ControllerBase
  {
    private readonly ICOutcomeInterface _icOutcomeService;

    public ICOutcomeController(ICOutcomeInterface serviceInterface)
    {
      _icOutcomeService = serviceInterface;
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Create([FromBody] ICOutcomeCreateDTO dto, CancellationToken cancellationToken)
    {
      // Validate container group access
      if (dto.ICContainerGroupId != User.ICContainerGroupId() && !User.IsAdmin())
      {
        return Forbid();
      }

      var res = await _icOutcomeService.Create(dto);
      return Ok(res);
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(uint id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      ICOutcomeSearchDTO dto = null;
      
      if (!string.IsNullOrEmpty(query))
      {
        dto = JsonConvert.DeserializeObject<ICOutcomeSearchDTO>(query);
      }

      var res = await _icOutcomeService.Get(id, dto, cancellationToken);
      
      // Validate container group access
      if (res.DTO?.ICContainerGroupId != User.ICContainerGroupId() && !User.IsAdmin())
      {
        return Forbid();
      }

      return Ok(res);
    }

    [HttpGet]
    [Route("search")]
    [Route("/api/inspect-collect/outcomes")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICOutcomeSearchDTO>(query);

      // Ensure user can only access their container group data
      if (!User.IsAdmin() && !dto.Filters.ICContainerGroupId.HasValue)
      {
        dto.Filters.ICContainerGroupId = User.ICContainerGroupId();
      }

      // Forbid requests without a container group id for non-admin users
      if (!User.IsAdmin() && dto.Filters.ICContainerGroupId == null)
      {
        return BadRequest("ContainerGroupId is required in the search query.");
      }

      // Validate container group access
      if (!User.IsAdmin() && dto.Filters.ICContainerGroupId != User.ICContainerGroupId())
      {
        return Forbid();
      }

      var res = await _icOutcomeService.Search(dto, cancellationToken);
      return Ok(res);
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<ActionResult> Patch(uint id, [FromBody] JsonPatchDocument<ICOutcome> patch, CancellationToken cancellationToken)
    {
      // First get the existing outcome to validate access
      var existing = await _icOutcomeService.Get(id, null, cancellationToken);
      
      if (!existing.IsValid)
      {
        return NotFound();
      }

      // Validate container group access
      if (existing.DTO.ICContainerGroupId != User.ICContainerGroupId() && !User.IsAdmin())
      {
        return Forbid();
      }

      var res = await _icOutcomeService.Patch(id, patch);
      return Ok(res);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(uint id, CancellationToken cancellationToken)
    {
      // First get the existing outcome to validate access
      var existing = await _icOutcomeService.Get(id, null, cancellationToken);
      
      if (!existing.IsValid)
      {
        return NotFound();
      }

      // Validate container group access
      if (existing.DTO.ICContainerGroupId != User.ICContainerGroupId() && !User.IsAdmin())
      {
        return Forbid();
      }

      var res = await _icOutcomeService.Delete(id);
      return Ok(res);
    }
  }
}
