﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums.InspectCollect;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICStyleDTO : BaseModelEntityDTO
  {
    public Guid? Id { get; set; }
    public Guid ICContainerGroupId { get; set; }
    public string Name { get; set; }
    public string BackgroundColour { get; set; }
    public string BackgroundImage { get; set; }
    public string FontColour { get; set; }
    public string FontSize { get; set; }
    public string FontName { get; set; }
    public string BorderColour { get; set; }
    public string BorderWidth { get; set; }
    public string Padding { get; set; }
    public string Margin { get; set; }
    public string Alignment { get; set; }
    public string CSS { get; set; }
    public string Class { get; set; }

  }

  public class ICStyleSearchDTO : BaseSearchDTO
  {
    public ICStyleSearchFilters Filters { get; set; } = new ICStyleSearchFilters();
  }

  public class ICStyleSearchFilters { 
    public Guid? Id { get; set; }
    public Guid? ICContainerGroupId { get; set; }
    public string Name { get; set; }
  }

  public class ICStyleCreateDTO { 
    public Guid ICContainerGroupId { get; set; }
    public string Name { get; set; }
  }
}