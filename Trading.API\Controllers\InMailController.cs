﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using sib_api_v3_sdk.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Prospects;
using Trading.API.Data.DTO.Search;
using Trading.API.Data.Enums;
using Trading.API.Data.Enums.Prospects;
using Trading.API.Helpers;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/inMail")]
  [ApiController]
  public class InMailController : ControllerBase
  {
    private readonly IInMailService _inMailService;
    private readonly IMapper _mapper;
    private readonly IAdminTaskService _adminTaskService;
    private readonly IBrokerageService _brokerageService;
    private readonly IAdvertNoteService _advertNoteService;
    private readonly IProspectService _prospectService;

    public InMailController(IInMailService inMailService, IMapper mapper, 
      IAdminTaskService adminTaskService, IBrokerageService brokerageService, 
      IAdvertNoteService advertNoteService, IProspectService prospectService)
    {
      this._inMailService = inMailService;
      this._mapper = mapper;
      this._adminTaskService = adminTaskService;
      this._brokerageService = brokerageService;
      this._advertNoteService = advertNoteService;
      this._prospectService = prospectService;
    }

    [HttpGet]
    [Route("folder/{inMailFolder}")]
    [Authorize]
    public async Task<IActionResult> GetFolderMail(InMailFolderEnum inMailFolder, [FromQuery] int page, [FromQuery] int perPage, CancellationToken cancellationToken)
    {
      try
      {
        var contactId = User.ContactId();
        var pageSkip = (page - 1) * perPage;
        var dtos = await _inMailService.GetFolder((Guid) contactId, inMailFolder, pageSkip, perPage, cancellationToken);
        return Ok(dtos);
      } 
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("count")]
    [ResponseCache(Duration = 5)] // Cache inbox counts for 5 seconds (what's the worst that can happen)
    [Authorize]
    public async Task<IActionResult> GetMailCounts(CancellationToken cancellationToken)
    {
      try
      {
        var contactId = User.ContactId();
        var data = await _inMailService.GetInMailCounts((Guid) contactId, cancellationToken);
        return Ok(data);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{id}/markAsRead")]
    [Authorize]
    public async Task<IActionResult> MarkAsRead(Guid id, CancellationToken cancellationToken)
    {
      try
      {
        var contactId = User.ContactId();
        var updated = await _inMailService.MarkAsRead((Guid) contactId, id, cancellationToken);
        return Ok(updated);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{id}/mark-as-actioned")]
    [Authorize]
    public async Task<IActionResult> MarkAsActioned(Guid id, CancellationToken cancellationToken)
    {
      try
      {
        var contactId = User.ContactId();
        var updated = await _inMailService.MarkAsActioned((Guid)contactId, id, cancellationToken);
        return Ok(updated);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpGet]
    [Route("mark-all-read")]
    [Authorize]
    public async Task<IActionResult> MarkAllAsRead(CancellationToken cancellationToken)
    {
      try
      {
        var contactId = User.ContactId();
        var updated = await _inMailService.MarkAllAsRead((Guid)contactId, cancellationToken);
        return Ok(updated);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpDelete]
    [Route("{id}")]
    [Authorize]
    public async Task<IActionResult> DeleteInMail(Guid id, CancellationToken cancellationToken)
    {
      try
      {
        var contactId = User.ContactId();
        await _inMailService.DeleteInMail((Guid) contactId, id, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPut]
    [Route("{id}/moveToFolder/{toFolder}")]
    [Authorize]
    public async Task<IActionResult> MoveToFolder(Guid id, InMailFolderEnum toFolder, CancellationToken cancellationToken)
    {
      try
      {
        var contactId = User.ContactId();
        await _inMailService.MoveToFolder((Guid) contactId, id, toFolder, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("")]
    [Authorize]
    public async Task<IActionResult> SendMail([FromBody] InMailDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        var contactId = User.ContactId();

        dto.Subject = dto.Subject.SanitiseString();
        dto.Body = dto.Body.SanitiseString();

        var result = await _inMailService.SendMail((Guid) contactId, dto, cancellationToken);

        if (dto.IsPrivate)
        {
          // create a brokerage record 
          var brokerageRes = await _brokerageService.CreateOrReturnBrokerageFromEnquiry(dto.AdvertId.Value, cancellationToken);

          await _prospectService.CreateProspectNote(new SaveProspectNoteDTO
          {
            BrokerageId = brokerageRes.DTO.Id.Value,
            ReminderDateTime = DateTime.Now.AddHours(1),
            Note = "Enquiry: " + dto.Body,
            ProspectContactId = dto.FromContactId,
            ProspectState = ProspectStateEnum.Interested,
          }, cancellationToken);
        }

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/listings/{advertId}/private-inmails/{contactId}")]
    [Authorize]
    public async Task<IActionResult> GetPrivateMailsForAdvert(Guid advertId, Guid contactId, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin())
      {
        return Forbid();
      }

      var res = await _inMailService.GetPrivateMessagesForAdvert(contactId, advertId, cancellationToken);
      return Ok(res);
    }

    [HttpGet]
    [Route("private")]
    [Authorize]
    public async Task<IActionResult> GetPrivateMails([FromQuery] string query, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin())
      {
        return Forbid();
      }

      var dto = new InMailSearchDTO();

      if (!String.IsNullOrEmpty(query))
      {
        dto = JsonConvert.DeserializeObject<InMailSearchDTO>(query);
      }

      var res = await _inMailService.GetPrivateMessages(dto, cancellationToken);
      return Ok(res);
    }
  }

}
