﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.Search
{
  public class InMailSearchDTO : BaseSearchDTO
  {
    public InMailFilters Filters { get; set; } = new InMailFilters();
  }

  public class InMailFilters : BaseFilter
  {
    public Guid? ContactId { get; set; }
    public Guid? AdvertId { get; set; }
    public bool? IsPrivate { get; set; }
    public bool UnActionedOnly { get; set; }
  }
}
