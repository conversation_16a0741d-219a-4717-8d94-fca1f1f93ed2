using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Threading.Tasks;
using System.Threading;
using System;
using Trading.Services.Extensions;
using Trading.Services.Interfaces.ExtLeads;
using Trading.API.Data.DTO.ExtLeads;
using Trading.API.Data.DTO.Comms;
using Microsoft.AspNetCore.Http;

namespace Trading.API.Controllers.ExtLead
{
  [Route("api/ext-lead")]
  [ApiController]
  [Authorize]
  public class ExtLeadController : ControllerBase
  {
    public IExtLeadService _extLeadService;
    public IExtLeadVehicleService _extLeadVehicleService;
    public IMapper _mapper;

    public ExtLeadController(
      IExtLeadService extLeadService, 
      IExtLeadVehicleService extLeadVehicleService, 
      IMapper mapper)
    {
      _extLeadService = extLeadService;
      _extLeadVehicleService = extLeadVehicleService;
      _mapper = mapper;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<IActionResult> Get(Guid id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = new ExtLeadSearchDTO();

        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<ExtLeadSearchDTO>(query);
        }

        var result = await _extLeadService.Get(id, dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{id}/vehicles")]
    public async Task<IActionResult> GetVehicles(Guid id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = new ExtLeadVehicleSearchDTO();

        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<ExtLeadVehicleSearchDTO>(query);
        }

        dto.Filters.ExtLeadId = id;

        var result = await _extLeadVehicleService.Search(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/ext-leads")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = String.IsNullOrEmpty(query) ? null : JsonConvert.DeserializeObject<ExtLeadSearchDTO>(query);
        var pageres = await _extLeadService.Search(dto, cancellationToken);
        return Ok(pageres);
      }
      catch (Exception e)
      {
        return BadRequest(e);
      }
    }


    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create([FromBody] ExtLeadDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        var result = await _extLeadService.Create(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<IActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        var response = await _extLeadService.Delete(id, cancellationToken);

        return Ok(response);
      }

      return Forbid();
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, [FromBody] JsonPatchDocument<Trading.API.Data.Models.ExtLeads.ExtLead> patch, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        try
        {
          return Ok(await _extLeadService.Patch(id, patch, cancellationToken));
        }
        catch (Exception ex)
        {
          return BadRequest(ex);
        }
      }

      return Forbid();
    }

    [HttpPost]
    [Route("parse")]
    public async Task<IActionResult> ProcessExternalLeads([FromBody] ExtLeadJSONDTO extLeadJSON, CancellationToken cancellationToken)
    {
      string json = "";

      var res = await _extLeadService.ProcessFile(extLeadJSON.json); //, !_webHostEnvironment.IsDevelopment()

      return Ok(res);
    }
  }
}