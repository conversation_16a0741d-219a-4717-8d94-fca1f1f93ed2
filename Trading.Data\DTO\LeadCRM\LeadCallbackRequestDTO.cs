﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.DTO.Movements;
using Trading.API.Data.DTO.Valuation;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;

namespace Trading.API.Data.DTO.LeadCRM
{
  public class LeadCallbackRequestDTO
  {
    public Guid? LeadVehicleId { get; set; }
    public string PreferredTime { get; set; }
    public string PreferredDate { get; set; }
  }
}
