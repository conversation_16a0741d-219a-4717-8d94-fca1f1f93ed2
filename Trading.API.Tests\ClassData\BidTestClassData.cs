﻿using System.Collections;
using System.Collections.Generic;
using Xunit;

namespace Trading.API.Tests.ClassData
{
  public class BidTestClassData : IEnumerable<object[]>
  {
    public IEnumerator<object[]> GetEnumerator()
    {
      yield return new object[]
      {
        // New Test Case: 1
        // Reserve 3000
        // User 1 bids 3100, User 2 bids 3100
        // Current Price should be 3100 (user 1 leading bidder)
        new BidTestData
        {
          TestId = 1,
          Reserve = 3000,
          Bids = new List<BidData> {
            new BidData { BidAmount = 3100, IsOtherUserBid = false },
            new BidData { BidAmount = 3100, IsOtherUserBid = true },
          },
          ExpectedCurrentPrice = 3100,
          IsOtherUserExpectedTopBidder = false
        }
      };

      yield return new object[]
      {
        
        // New Test Case: 2
        // Reserve 3000
        // User 1 bids 4000, User 2 bids 2000
        // Current Price should be 3000 (user 1 leading bidder)
        new BidTestData
        {
          TestId = 2,
          Reserve = 3000,
          Bids = new List<BidData> {
            new BidData { BidAmount = 4000, IsOtherUserBid = false },
            new BidData { BidAmount = 2000, IsOtherUserBid = true },
          },
          ExpectedCurrentPrice = 3000,
          IsOtherUserExpectedTopBidder = false
        }
      };

      yield return new object[]
      {                
        // New Test Case: 3
        // Reserve 3000
        // User 1 bids 2000, User 2 bids 2500
        // Current Price should be 2500 (user 2 leading bidder)
        new BidTestData
        {
          TestId = 3,
          Reserve = 3000,
          Bids = new List<BidData> {
            new BidData { BidAmount = 2000, IsOtherUserBid = false },
            new BidData { BidAmount = 2500, IsOtherUserBid = true },
          },
          ExpectedCurrentPrice = 2500,
          IsOtherUserExpectedTopBidder = true
        },
      };

      yield return new object[]
      {
        // New Test Case: 4
        // Reserve 3000
        // User 1 bids 2000, User 2 bids 4000, User 1 bids 5000
        // Current Price should be 4100 (user 1 leading bidder)
        new BidTestData
        {
          TestId = 4,
          Reserve = 3000,
          Bids = new List<BidData> {
            new BidData { BidAmount = 2000, IsOtherUserBid = false },
            new BidData { BidAmount = 4000, IsOtherUserBid = true },
            new BidData { BidAmount = 5000, IsOtherUserBid = false },
          },
          ExpectedCurrentPrice = 4100,
          IsOtherUserExpectedTopBidder = false
        },
      };

      yield return new object[]
      {        
        // New Test Case: 5
        // Reserve 3000
        // User 1 bids 2000, User 2 bids 4000
        // Current Price should be 3000 (user 2 leading bidder)
        new BidTestData
        {
          TestId = 5,
          Reserve = 3000,
          Bids = new List<BidData> {
            new BidData { BidAmount = 2000, IsOtherUserBid = false },
            new BidData { BidAmount = 4000, IsOtherUserBid = true },
          },
          ExpectedCurrentPrice = 3000,
          IsOtherUserExpectedTopBidder = true
        },
      };

      yield return new object[]
      {      
        // New Test Case: 6
        // Reserve 3000
        // User 1 bids 3200, User 2 bids 3050
        // Current Price should be 3000 (user 1 leading bidder)
        // because bidder 2 hasn't bid more than the bid increment above current bid
        new BidTestData
        {
          TestId = 6,
          Reserve = 3000,
          Bids = new List<BidData> {
            new BidData { BidAmount = 3200, IsOtherUserBid = false },
            new BidData { BidAmount = 3050, IsOtherUserBid = true },
          },
          ExpectedCurrentPrice = 3000,
          IsOtherUserExpectedTopBidder = false
        },
      };

      yield return new object[]
      {
        // New Test Case: 7
        // Reserve 3000
        // User 1 bids 2950, User 2 bids 3000
        // Current Price should be 3000 (user 2 leading bidder)
        new BidTestData
        {
          TestId = 7,
          Reserve = 3000,
          Bids = new List<BidData> {
            new BidData { BidAmount = 2950, IsOtherUserBid = false },
            new BidData { BidAmount = 3000, IsOtherUserBid = true },
          },
          ExpectedCurrentPrice = 3000,
          IsOtherUserExpectedTopBidder = true
        },
      };

      yield return new object[]
      {
        // New Test Case: 8
        // Reserve 3000
        // User 1 bids 3150, User 2 bids 3100
        // Current Price should be 3150 (user 1 leading bidder)
        new BidTestData
        {
          TestId = 8,
          Reserve = 3000,
          Bids = new List<BidData> {
            new BidData { BidAmount = 3150, IsOtherUserBid = false },
            new BidData { BidAmount = 3100, IsOtherUserBid = true },
          },
          ExpectedCurrentPrice = 3150,
          IsOtherUserExpectedTopBidder = false
        },
      };

      yield return new object[]
      {
        // New Test Case: 9
        // Reserve 1000
        // User 1 bids 999, User 2 bids 2000
        // Current Price should be 1000 (user 2 leading bidder)
        new BidTestData
        {
          TestId = 9,
          Reserve = 1000,
          Bids = new List<BidData> {
            new BidData { BidAmount = 999, IsOtherUserBid = false },
            new BidData { BidAmount = 2000, IsOtherUserBid = true },
          },
          ExpectedCurrentPrice = 1000,
          IsOtherUserExpectedTopBidder = true
        },
      };

      yield return new object[]
      {
        // New Test Case: 10
        // NO RESERVE 
        // STARTING PRICE 1000
        // User 1 bids 1000
        // Current Price should be 1000 (user 1 leading bidder)
        new BidTestData
        {
          TestId = 10,
          Reserve = 0,
          StartingPrice = 1000,
          Bids = new List<BidData> {
            new BidData { BidAmount = 1000, IsOtherUserBid = false },
          },
          ExpectedCurrentPrice = 1000,
          IsOtherUserExpectedTopBidder = false
        },
      };

      yield return new object[]
      {
        // New Test Case: 11
        // NO RESERVE 
        // STARTING PRICE 1000
        // User 1 bids 500
        // Current Price 0 (No leading bidder)
        new BidTestData
        {
          TestId = 11,
          Reserve = 0,
          StartingPrice = 1000,
          Bids = new List<BidData> {
            new BidData { BidAmount = 500, IsOtherUserBid = false },
          },
          ExpectedCurrentPrice = 0,
          IsOtherUserExpectedTopBidder = false,
          NoTopBid = true
        },
      };

      yield return new object[]
      {        
        // New Test Case: 12
        // NO RESERVE 
        // STARTING PRICE 1000
        // User 1 bids 999, User 2 bids 1000
        // Current Price 1000 (User 2 leading bidder)
        new BidTestData
        {
          TestId = 12,
          Reserve = 0,
          StartingPrice = 1000,
          Bids = new List<BidData> {
            new BidData { BidAmount = 999, IsOtherUserBid = false },
            new BidData { BidAmount = 1000, IsOtherUserBid = true },
          },
          ExpectedCurrentPrice = 1000,
          IsOtherUserExpectedTopBidder = true
        },
      };

      yield return new object[]
      {
        // New Test Case: 13
        // NO RESERVE 
        // STARTING PRICE 1000
        // User 1 bids 1050
        // User 2 bids 1100
        // Current Price 1100 (User 2 has not outbid user 1 by bid increment)
        new BidTestData
        {
          TestId = 13,
          Reserve = 0,
          StartingPrice = 1000,
          Bids = new List<BidData> {
            new BidData { BidAmount = 1050, IsOtherUserBid = false },
            new BidData { BidAmount = 1100, IsOtherUserBid = true },
          },
          ExpectedCurrentPrice = 1100,
          IsOtherUserExpectedTopBidder = true
        },
      };

      yield return new object[]
      {
        // New Test Case: 14
        // NO RESERVE 
        // STARTING PRICE 0
        // User 1 bids 1050
        // User 2 bids 1000
        // Current Price 1050 (User 1 has autobid to be more than user 2)
        new BidTestData
        {
          TestId = 14,
          Reserve = 0,
          StartingPrice = 100,
          Bids = new List<BidData> {
            new BidData { BidAmount = 1050, IsOtherUserBid = false },
            new BidData { BidAmount = 1000, IsOtherUserBid = true },
          },
          ExpectedCurrentPrice = 1050,
          IsOtherUserExpectedTopBidder = false
        },
      };

      yield return new object[]
      {
        // New Test Case: 15
        // NO RESERVE 
        // STARTING PRICE 0
        // User 1 bids 1000
        // User 2 bids 1000
        // Current Price 1000 (User 1 was first)
        new BidTestData
        {
          TestId = 15,
          Reserve = 0,
          StartingPrice = 100,
          Bids = new List<BidData> {
            new BidData { BidAmount = 1050, IsOtherUserBid = false },
            new BidData { BidAmount = 1000, IsOtherUserBid = true },
          },
          ExpectedCurrentPrice = 1050,
          IsOtherUserExpectedTopBidder = false
        },
      };
    }

    IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();
  }

  public class BidTestData
  {
    public int TestId { get; set; }
    public uint StartingPrice { get; set; }

    public uint Reserve { get; set; }

    public List<BidData> Bids { get; set; } = new List<BidData>();

    public uint ExpectedCurrentPrice { get; set; }
    public bool IsOtherUserExpectedTopBidder { get; set; }
    public bool NoTopBid { get; set; }
  }

  public class BidData
  {
    public uint BidAmount { get; set; }
    public bool IsOtherUserBid { get; set; }
  }
}
