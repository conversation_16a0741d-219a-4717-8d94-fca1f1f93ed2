using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;
using Newtonsoft.Json;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.API.Helpers;
using Trading.Services.Classes;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;
using Trading.Services.Interfaces.Maps;

namespace Trading.API.Controllers
{
  [Route("api/address")]
  [ApiController]
  [Authorize]
  public class AddressController : ControllerBase
  {
    public IAddressService _addressService;
    public IMapper _mapper;
    private readonly IGooglePlacesService _googlePlacesService;

    public AddressController(
      IAddressService addressService,
      IMapper mapper, IGooglePlacesService googlePlacesService)
    {
      _addressService = addressService;
      _mapper = mapper;
      this._googlePlacesService = googlePlacesService;
    }

    [HttpGet]
    [Route("update-all-placeids")]
    [AllowAnonymous]
    public async Task<IActionResult> UpdateAllPlaceIds(CancellationToken cancellationToken)
    {
      await _addressService.UpdateAllPlaceIds(cancellationToken);
      return Ok("Updated");
    }

    [HttpGet]
    [Route("/api/get-place/{postcode}")]
    [AllowAnonymous]
    public async Task<IActionResult> GetPlaceId(string postcode, CancellationToken cancellationToken)
    {
      var res = await _googlePlacesService.GetPlaceInfoAsync(postcode);
      return Ok(res);
    }

    // GET: api/Customer/X/Addresses
    [HttpGet]
    [Route("/api/customer/{customerId}/addresses")]
    public async Task<IActionResult> GetCustomerAddresses(Guid customerId, CancellationToken cancellationToken)
    {
      if (customerId == this.User.CustomerId() || User.IsAdmin()) 
      {
        var response = await _addressService.Search(new AddressSearchDTO() { Filters = new AddressFilters() { CustomerId = customerId } }, cancellationToken);

        return Ok(response.Addresses);
      }

      return Forbid();
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> CreateAddress(AddressDTO dto, CancellationToken cancellationToken)
    {
      if (dto.CustomerId == this.User.CustomerId() || User.IsAdmin()) 
      {
        var response = await _addressService.CreateAddress(dto, cancellationToken);

        return Ok(response);
      }

      return Forbid();
    }

    [HttpDelete]
    [Route("{addressId}")]
    public async Task<IActionResult> DeleteAddress(Guid addressId, CancellationToken cancellationToken)
    {
      var addressDTO = await _addressService.GetAddress(addressId, cancellationToken);

      // Only edit addresses that are ours (or if we're admin)
      if (addressDTO.CustomerId == this.User.CustomerId() || User.IsAdmin())
      {
        var response = await _addressService.DeleteAddress(addressId, cancellationToken);

        return Ok(response);
      }

      return Forbid();
    }

    [HttpPatch]
    [Route("{addressId}")]
    public async Task<IActionResult> PatchAddress(Guid addressId, [FromBody] JsonPatchDocument<Address> patch, CancellationToken cancellationToken)
    {
      var addressDTO = await _addressService.GetAddress(addressId, cancellationToken);

      // Only edit addresses that are ours (or if we're admin)
      if (addressDTO.CustomerId == this.User.CustomerId() || User.IsAdmin())
      {
        var res = await _addressService.PatchAddress(addressId, patch, cancellationToken);
        return Ok(res);
      }

      return Forbid();
    }

    [HttpGet]
    [Route("/api/addresses")]
    public async Task<IActionResult> GetAll([FromQuery] string query, CancellationToken cancellationToken)
    {

      AddressSearchDTO searchDTO = new AddressSearchDTO();

      if (query != null)
      {
        searchDTO = JsonConvert.DeserializeObject<AddressSearchDTO>(query);
      }

      // Enforce this to prevent bad things happening
      if (!User.IsInRole("GOD"))
      {
        searchDTO.Filters.CustomerId = this.User.CustomerId();
      }

      try
      {
        var addresses = await _addressService.Search(searchDTO, cancellationToken);
        return Ok(addresses);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/listing/{advertId}/location")]
    [OutputCache(Duration = 600, PolicyName = "ConditionalCacheWithAuthPolicy")] // incicate policy name

    public async Task<IActionResult> GetAdvertLocation(Guid advertId, CancellationToken cancellationToken)
    {
      try
      {
        var res = await _addressService.GetAdvertLocation(advertId, cancellationToken);
        return Ok(res);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
