﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums.Valuation;
using Trading.API.Data.Interfaces;

namespace Trading.API.Data.DTO.Valuation
{
  public class ValuationPillarDTO : BaseModelEntityDTO
  {
    public Guid ValuationProfileId { get; set; }
    public virtual ValuationProfileDTO ValuationProfile { get; set; }
    public string PillarName { get; set; } // the name of the lookup via the id (i.e. Toyota for Make)
    public ValuationLookupTypeEnum LookupType { get; set; }
    public string LookupId { get; set; }
  }
}
