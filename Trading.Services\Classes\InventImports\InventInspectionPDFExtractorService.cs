﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using iText.Kernel.Pdf;
using iText.Kernel.Pdf.Canvas.Parser;
using iText.Kernel.Pdf.Canvas.Parser.Listener;
using iText.Kernel.Pdf.Canvas.Parser.Data;
using iText.Kernel.Pdf.Xobject;
using iText.Kernel.Geom;
using System.Text.RegularExpressions;
using Trading.Services.Interfaces.InventImports;
using Trading.API.Data.DTO.Imports.Invent;
using System.Net.Http;

namespace Trading.Services.InspectCollect.Classes;

public class InventInspectionPDFExtractorService : IInventInspectionPDFExtractorInterface
{
  private readonly HttpClient _httpClient;

  // Known vehicle parts from analysis of City reports
  private static readonly string[] KnownVehicleParts =
  {
        "boot", "bonnet", "door", "wheel", "bumper", "wing", "roof", "tailgate",
        "qtr panel", "quarter panel", "b post", "c post", "mirror", "headlamp",
        "screen", "spoiler", "moulding", "trim", "flap", "interior", "exterior",
        "tyre inflation kit", "glass roof", "fuel flap", "carpets", "flasher"
    };

  // Damage-related keywords for validation
  private static readonly string[] DamageKeywords =
  {
        "scratched", "chipped", "dented", "cracked", "torn", "broken", "damaged",
        "missing", "corroded", "scuffed", "soiled", "replace", "over", "between",
        "multiple", "light", "with paint", "thru paint", "not thru paint", "insecure",
        "faded", "worn", "stained", "loose", "action required"
    };

  // Noise patterns to ignore
  private static readonly string[] NoisePatterns =
  {
        "condition check", "photos", "interior", "mechanical", "inspection",
        "brightfield", "business park", "peterborough", "important notice",
        "any issue with", "health check", "must be raised", "driven more than",
        "will not be covered", "point of collection", "delivery", "rejection process",
        "carpet condition", "upholstery condition", "seat condition", "odour"
    };

  public InventInspectionPDFExtractorService(HttpClient httpClient)
  {
    _httpClient = httpClient;
  }

  public async Task<CityVehicleInspectionResult> ExtractInspectionDataFromURLAsync(string pdfUrl)
  {
    if (string.IsNullOrWhiteSpace(pdfUrl))
      throw new ArgumentException("PDF URL cannot be null or empty", nameof(pdfUrl));

    if (!Uri.TryCreate(pdfUrl, UriKind.Absolute, out var uri))
      throw new ArgumentException("Invalid URL format", nameof(pdfUrl));

    try
    {
      _httpClient.Timeout = TimeSpan.FromMinutes(5);

      using var response = await _httpClient.GetAsync(pdfUrl);
      response.EnsureSuccessStatusCode();

      var contentType = response.Content.Headers.ContentType?.MediaType;
      if (contentType != "application/pdf")
      {
        throw new InvalidOperationException($"Expected PDF content, but received: {contentType ?? "unknown"}");
      }

      using var pdfStream = await response.Content.ReadAsStreamAsync();
      using var pdfReader = new PdfReader(pdfStream);
      using var pdfDocument = new PdfDocument(pdfReader);

      var damageItems = await ExtractDamageItems(pdfDocument);
      var vehicleInfo = await ExtractVehicleInfo(pdfDocument);
      var images = ExtractImages(pdfDocument);

      return await BuildResult(damageItems, vehicleInfo, images);
    }
    catch (HttpRequestException ex)
    {
      throw new InvalidOperationException($"Failed to download PDF from URL: {pdfUrl}", ex);
    }
    catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
    {
      throw new TimeoutException($"Request timed out while downloading PDF from URL: {pdfUrl}", ex);
    }
    catch (Exception ex) when (!(ex is ArgumentException || ex is InvalidOperationException || ex is TimeoutException))
    {
      throw new InvalidOperationException($"An error occurred while processing PDF from URL: {pdfUrl}", ex);
    }
  }

  public async Task<CityVehicleInspectionResult> ExtractInspectionDataAsync(string pdfFilePath)
  {
    using var pdfReader = new PdfReader(pdfFilePath);
    using var pdfDocument = new PdfDocument(pdfReader);

    var damageItems = await ExtractDamageItems(pdfDocument);
    var vehicleInfo = await ExtractVehicleInfo(pdfDocument);
    var images = ExtractImages(pdfDocument);

    return await BuildResult(damageItems, vehicleInfo, images);
  }

  private async Task<List<CityDamageItem>> ExtractDamageItems(PdfDocument pdfDocument)
  {
    var damageItems = new List<CityDamageItem>();

    // Extract text from all pages
    var fullText = ExtractFullText(pdfDocument);

    // Find condition check bounds
    var bounds = FindConditionCheckBounds(pdfDocument, fullText);

    // Try primary extraction method
    damageItems.AddRange(ParseDamageFromConditionCheck(fullText));

    // If no items found, try fallback methods
    if (damageItems.Count == 0)
    {
      damageItems.AddRange(ParseDamageFromFullText(fullText));
    }

    // Extract dot positions and images - enhanced method
    await EnhanceWithSplatData(pdfDocument, damageItems, bounds);

    return damageItems;
  }

  private ConditionCheckBounds FindConditionCheckBounds(PdfDocument pdfDocument, string fullText)
  {
    // Find the condition check section in the text
    var conditionMatch = Regex.Match(fullText, @"Condition Check", RegexOptions.IgnoreCase);
    if (!conditionMatch.Success)
    {
      // Default to searching all pages if we can't find the section
      return new ConditionCheckBounds
      {
        StartPage = 1,
        EndPage = pdfDocument.GetNumberOfPages(),
        TextStart = 0,
        TextEnd = fullText.Length
      };
    }

    // For now, assume condition check is on pages 1-3 (can be refined)
    return new ConditionCheckBounds
    {
      StartPage = 1,
      EndPage = Math.Min(3, pdfDocument.GetNumberOfPages()),
      TextStart = conditionMatch.Index,
      TextEnd = Math.Min(conditionMatch.Index + 5000, fullText.Length) // Reasonable section size
    };
  }

  private async Task EnhanceWithSplatData(PdfDocument pdfDocument, List<CityDamageItem> damageItems, ConditionCheckBounds bounds)
  {
    try
    {
      // Extract dot positions ONLY from condition check pages
      var dotPositions = ExtractDotPositionsFromConditionCheck(pdfDocument, bounds);

      // Extract images ONLY from condition check pages
      var images = ExtractImagesFromConditionCheck(pdfDocument, bounds);
      var splatImage = FindSplatImage(images);

      // Associate dots with damage items using improved logic
      AssociateDotPositionsImproved(damageItems, dotPositions, splatImage, pdfDocument);

      // Associate images with damage items
      AssociateImagesWithDamageItems(damageItems, images, dotPositions);

      // Clean up spurious/duplicate dots and only add valid standalone ones
      AddValidStandaloneDots(damageItems, dotPositions, splatImage, pdfDocument);
    }
    catch (Exception ex)
    {
      // Log error but don't fail the entire extraction
      Console.WriteLine($"Error extracting splat data: {ex.Message}");
    }
  }

  private List<DotPosition> ExtractDotPositionsFromConditionCheck(PdfDocument pdfDocument, ConditionCheckBounds bounds)
  {
    var dotPositions = new List<DotPosition>();

    for (int pageNum = bounds.StartPage; pageNum <= bounds.EndPage; pageNum++)
    {
      var page = pdfDocument.GetPage(pageNum);

      // Extract text with position information using improved extractor
      var textExtractor = new ConditionCheckTextExtractor();
      var parser = new PdfCanvasProcessor(textExtractor);
      parser.ProcessPageContent(page);

      // Get numbered positions and filter for valid damage item numbers (1-10 typically)
      var pagePositions = textExtractor.GetNumberedPositions()
          .Where(pos => IsValidDamageItemDot(pos, page))
          .ToList();

      foreach (var pos in pagePositions)
      {
        pos.PageNumber = pageNum;
      }

      dotPositions.AddRange(pagePositions);
    }

    // Remove duplicates and spurious dots
    var validDots = FilterValidDots(dotPositions);
    Console.WriteLine($"Found {dotPositions.Count} raw dots, filtered to {validDots.Count} valid dots");

    return validDots;
  }

  private List<ExtractedImage> ExtractImagesFromConditionCheck(PdfDocument pdfDocument, ConditionCheckBounds bounds)
  {
    var images = new List<ExtractedImage>();

    for (int pageNum = bounds.StartPage; pageNum <= bounds.EndPage; pageNum++)
    {
      var page = pdfDocument.GetPage(pageNum);
      var imageExtractor = new ImageExtractor(pageNum);
      var parser = new PdfCanvasProcessor(imageExtractor);
      parser.ProcessPageContent(page);

      // Filter out very small images (likely logos/icons)
      var validImages = imageExtractor.ExtractedImages
          .Where(img => img.Width > 50 && img.Height > 50)
          .ToList();

      images.AddRange(validImages);
    }

    Console.WriteLine($"Found {images.Count} images in condition check section (pages {bounds.StartPage}-{bounds.EndPage})");
    return images;
  }

  private bool IsValidDamageItemDot(DotPosition position, iText.Kernel.Pdf.PdfPage page)
  {
    // Only accept single digits 1-9 that are positioned in reasonable locations
    if (!int.TryParse(position.Label, out var num) || num < 1 || num > 9)
      return false;

    var pageHeight = page.GetPageSize().GetHeight();
    var pageWidth = page.GetPageSize().GetWidth();

    // The dots should be positioned where vehicle diagrams typically appear
    var isInReasonableArea = position.Y > pageHeight * 0.2 && position.Y < pageHeight * 0.95 &&
                            position.X > pageWidth * 0.05 && position.X < pageWidth * 0.95;

    return isInReasonableArea;
  }

  private List<DotPosition> FilterValidDots(List<DotPosition> allDots)
  {
    var validDots = new List<DotPosition>();
    var groupedByLabel = allDots.GroupBy(d => d.Label).ToList();

    foreach (var group in groupedByLabel)
    {
      if (group.Count() == 1)
      {
        // Single occurrence - likely valid
        validDots.Add(group.First());
      }
      else
      {
        // Multiple occurrences - pick the most likely one
        // Prefer dots that are more centrally located and not near page edges
        var bestDot = group
            .OrderBy(d => Math.Abs(d.X - 300)) // Prefer dots near horizontal center
            .ThenBy(d => Math.Abs(d.Y - 500))  // Prefer dots in middle vertical area
            .First();

        validDots.Add(bestDot);
        Console.WriteLine($"Multiple dots found for '{group.Key}', selected best one at X={bestDot.X}, Y={bestDot.Y}");
      }
    }

    return validDots.OrderBy(d => int.Parse(d.Label)).ToList();
  }

  private void AssociateImagesWithDamageItems(List<CityDamageItem> damageItems,
      List<ExtractedImage> images, List<DotPosition> dotPositions)
  {
    foreach (var item in damageItems)
    {
      // Find images that might correspond to this damage item
      // Look for images that are close to the dot position or contain identifying markers
      var correspondingImages = FindImagesForDamageItem(item, images, dotPositions);

      if (correspondingImages.Any())
      {
        // For now, take the first/best matching image
        var bestImage = correspondingImages.First();
        item.ImageData = bestImage.ImageData;
        item.ImageFormat = bestImage.Format;
        item.ImageWidth = bestImage.Width;
        item.ImageHeight = bestImage.Height;

        Console.WriteLine($"Associated image with damage item {item.ItemNumber}: {bestImage.Width}x{bestImage.Height} {bestImage.Format}");
      }
    }
  }

  private List<ExtractedImage> FindImagesForDamageItem(CityDamageItem damageItem,
      List<ExtractedImage> images, List<DotPosition> dotPositions)
  {
    var matchingImages = new List<ExtractedImage>();

    // Find the dot position for this damage item
    var correspondingDot = dotPositions.FirstOrDefault(d => d.Label == damageItem.ItemNumber.ToString());

    if (correspondingDot != null)
    {
      // Look for images that are reasonably close to the dot position
      // This is a heuristic - you might need to adjust based on actual PDF layout
      var nearbyImages = images.Where(img =>
          Math.Abs(img.X - correspondingDot.X) < 200 &&
          Math.Abs(img.Y - correspondingDot.Y) < 200 &&
          img.Width > 100 && img.Height > 100 // Reasonable size for damage photos
      ).ToList();

      matchingImages.AddRange(nearbyImages);
    }

    return matchingImages;
  }

  private void AddValidStandaloneDots(List<CityDamageItem> damageItems,
      List<DotPosition> dotPositions, ExtractedImage splatImage, PdfDocument pdfDocument)
  {
    // Find dots that don't have corresponding damage items
    var existingItemNumbers = damageItems.Select(d => d.ItemNumber).ToHashSet();
    var standaloneDots = dotPositions.Where(dot =>
        int.TryParse(dot.Label, out var num) && !existingItemNumbers.Contains(num)).ToList();

    Console.WriteLine($"Found {standaloneDots.Count} standalone dots: {string.Join(", ", standaloneDots.Select(d => d.Label))}");

    // Use the same diagram bounds calculation
    Rectangle diagramBounds = splatImage != null
        ? new Rectangle(splatImage.X, splatImage.Y, splatImage.Width, splatImage.Height)
        : EstimateDiagramBounds(dotPositions, pdfDocument);

    foreach (var dot in standaloneDots)
    {
      var relativeX = CalculateRelativePositionSimple(dot.X, diagramBounds.GetLeft(), diagramBounds.GetWidth());
      var relativeY = CalculateRelativePositionSimple(dot.Y, diagramBounds.GetBottom(), diagramBounds.GetHeight());

      // Create a damage item for standalone dots
      damageItems.Add(new CityDamageItem
      {
        ItemNumber = int.Parse(dot.Label),
        Location = "Unknown",
        DamageType = "Unknown",
        Severity = "Unknown",
        Description = $"Dot {dot.Label} - No corresponding damage description found",
        SplatPositionX = relativeX,
        SplatPositionY = relativeY,
        SplatDesc = dot.Label
      });
    }
  }

  private bool IsLikelyVehicleDiagramDot(DotPosition position, iText.Kernel.Pdf.PdfPage page)
  {
    // Enhanced heuristics to identify dots on the vehicle diagram
    var pageHeight = page.GetPageSize().GetHeight();
    var pageWidth = page.GetPageSize().GetWidth();

    // From the PDF, we know the vehicle diagram is on the Condition Check page
    // and contains dots numbered 2-7, so we should look for those specific numbers
    var isValidDotNumber = int.TryParse(position.Label, out var num) && num >= 1 && num <= 10;

    // The dots should be positioned in the area where the vehicle diagram appears
    // Based on typical PDF layouts, this is usually in the middle-upper area
    var isInReasonableArea = position.Y > pageHeight * 0.2 && position.Y < pageHeight * 0.95 &&
                            position.X > pageWidth * 0.05 && position.X < pageWidth * 0.95;

    Console.WriteLine($"Checking dot {position.Label}: X={position.X}, Y={position.Y}, Valid={isValidDotNumber}, InArea={isInReasonableArea}");

    return isValidDotNumber && isInReasonableArea;
  }

  private void AssociateDotPositionsImproved(List<CityDamageItem> damageItems,
      List<DotPosition> dotPositions, ExtractedImage splatImage, PdfDocument pdfDocument)
  {
    // Debug output
    Console.WriteLine($"Found {dotPositions.Count} dot positions");
    if (splatImage != null)
    {
      Console.WriteLine($"Splat image: X={splatImage.X}, Y={splatImage.Y}, W={splatImage.Width}, H={splatImage.Height}");
    }
    else
    {
      Console.WriteLine("No splat image found - will use page-relative positioning");
    }

    // If we can't find the splat image, estimate the diagram area from dot positions
    Rectangle diagramBounds = splatImage != null
        ? new Rectangle(splatImage.X, splatImage.Y, splatImage.Width, splatImage.Height)
        : EstimateDiagramBounds(dotPositions, pdfDocument);

    Console.WriteLine($"Using diagram bounds: X={diagramBounds.GetLeft()}, Y={diagramBounds.GetBottom()}, W={diagramBounds.GetWidth()}, H={diagramBounds.GetHeight()}");

    foreach (var item in damageItems)
    {
      // Find matching dot position by item number
      var matchingDot = dotPositions.FirstOrDefault(dot =>
          dot.Label == item.ItemNumber.ToString());

      if (matchingDot != null)
      {
        Console.WriteLine($"Dot {matchingDot.Label}: X={matchingDot.X}, Y={matchingDot.Y}");

        // Calculate relative position using diagram bounds
        var relativeX = CalculateRelativePositionSimple(matchingDot.X, diagramBounds.GetLeft(), diagramBounds.GetWidth());
        var relativeY = CalculateRelativePositionSimple(matchingDot.Y, diagramBounds.GetBottom(), diagramBounds.GetHeight());

        Console.WriteLine($"Calculated relative position for {matchingDot.Label}: X={relativeX}%, Y={relativeY}%");

        item.SplatPositionX = relativeX;
        item.SplatPositionY = relativeY;
        item.SplatDesc = matchingDot.Label;
      }
    }
  }

  private Rectangle EstimateDiagramBounds(List<DotPosition> dotPositions, PdfDocument pdfDocument)
  {
    if (!dotPositions.Any())
      return new Rectangle(0, 0, 600, 400); // Default fallback

    // Find the bounding box of all dots and add some padding
    var minX = dotPositions.Min(d => d.X) - 50;
    var maxX = dotPositions.Max(d => d.X) + 50;
    var minY = dotPositions.Min(d => d.Y) - 50;
    var maxY = dotPositions.Max(d => d.Y) + 50;

    var width = maxX - minX;
    var height = maxY - minY;

    Console.WriteLine($"Estimated diagram bounds from dots: MinX={minX}, MinY={minY}, MaxX={maxX}, MaxY={maxY}, W={width}, H={height}");

    return new Rectangle(minX, minY, width, height);
  }

  private decimal CalculateRelativePositionSimple(float dotPosition, float boundsStart, float boundsSize)
  {
    if (boundsSize == 0) return 0;

    var relativePos = ((dotPosition - boundsStart) / boundsSize) * 100;

    Console.WriteLine($"Simple calc: Dot={dotPosition}, BoundsStart={boundsStart}, BoundsSize={boundsSize}, Relative={relativePos}%");

    // Clamp to reasonable range (allow some overflow for edge cases)
    return (decimal)Math.Max(-20, Math.Min(120, relativePos));
  }

  private decimal CalculateRelativePosition(float dotPosition, float imagePosition, int imageSize)
  {
    if (imageSize == 0) return 0;

    var relativePos = ((dotPosition - imagePosition) / imageSize) * 100;

    // Don't clamp to 0-100 range initially - allow values outside for debugging
    // The dots might be positioned relative to page coordinates rather than image coordinates
    Console.WriteLine($"Dot pos: {dotPosition}, Image pos: {imagePosition}, Size: {imageSize}, Relative: {relativePos}%");

    return (decimal)relativePos;
  }

  private decimal CalculateRelativePositionY(float dotY, float imageY, int imageHeight, float pageHeight)
  {
    if (imageHeight == 0) return 0;

    // PDF coordinates are bottom-up, but we want top-down for web display
    // Convert PDF Y coordinate to top-down
    var dotYTopDown = pageHeight - dotY;
    var imageYTopDown = pageHeight - (imageY + imageHeight);

    var relativePos = ((dotYTopDown - imageYTopDown) / imageHeight) * 100;

    Console.WriteLine($"Dot Y (PDF): {dotY}, Dot Y (top-down): {dotYTopDown}, Image Y (top-down): {imageYTopDown}, Height: {imageHeight}, Relative: {relativePos}%");

    return (decimal)relativePos;
  }

  private List<ExtractedImage> ExtractImages(PdfDocument pdfDocument)
  {
    // This method is kept for backward compatibility but should use the bounded version
    var bounds = new ConditionCheckBounds { StartPage = 1, EndPage = pdfDocument.GetNumberOfPages() };
    return ExtractImagesFromConditionCheck(pdfDocument, bounds);
  }

  private ExtractedImage FindSplatImage(List<ExtractedImage> images)
  {
    // Enhanced heuristics to find the vehicle diagram:
    // 1. The vehicle diagram should be reasonably large
    // 2. Should be one of the larger images
    // 3. Exclude very small images (likely icons/logos)

    Console.WriteLine($"Found {images.Count} total images:");
    foreach (var img in images.Select((image, index) => new { image, index }))
    {
      Console.WriteLine($"Image {img.index}: Page={img.image.PageNumber}, X={img.image.X}, Y={img.image.Y}, W={img.image.Width}, H={img.image.Height}");
    }

    // Look for the vehicle diagram - should be a substantial vector image
    // In many PDFs, the vehicle diagram is not extracted as a bitmap but as vector graphics
    // So we might not find it here, which is why we have the fallback estimation method
    var candidateImages = images
        .Where(img => img.Width >= 300 && img.Height >= 200) // Reasonable size for vehicle diagram
        .Where(img => img.Width < 1000 && img.Height < 800) // Not too large (full page images)
        .OrderByDescending(img => img.Width * img.Height) // Prefer larger images
        .ToList();

    Console.WriteLine($"Found {candidateImages.Count} candidate vehicle diagram images");

    var bestCandidate = candidateImages.FirstOrDefault();
    if (bestCandidate != null)
    {
      Console.WriteLine($"Selected vehicle diagram: {bestCandidate.Width}x{bestCandidate.Height} at X={bestCandidate.X}, Y={bestCandidate.Y}");
    }
    else
    {
      Console.WriteLine("No suitable vehicle diagram image found - will use dot position estimation");
    }

    return bestCandidate;
  }

  private async Task<CityVehicleInspectionResult> BuildResult(
      List<CityDamageItem> damageItems,
      CityVehicleInfo vehicleInfo,
      List<ExtractedImage> images)
  {
    var splatImage = FindSplatImage(images);

    // Sort damage items by item number for consistent output
    damageItems = damageItems.OrderBy(d => d.ItemNumber).ToList();

    return new CityVehicleInspectionResult
    {
      VehicleInfo = vehicleInfo,
      DamageItems = damageItems,
      TotalDamageCount = damageItems.Count,
      ProcessedAt = DateTime.UtcNow,
      SplatImageData = splatImage?.ImageData,
      SplatImageFormat = splatImage?.Format,
      SplatImageWidth = splatImage?.Width,
      SplatImageHeight = splatImage?.Height
    };
  }

  private string ExtractFullText(PdfDocument pdfDocument)
  {
    var fullText = new StringBuilder();

    for (int i = 1; i <= pdfDocument.GetNumberOfPages(); i++)
    {
      var page = pdfDocument.GetPage(i);
      var strategy = new SimpleTextExtractionStrategy();
      var pageText = PdfTextExtractor.GetTextFromPage(page, strategy);
      fullText.AppendLine(pageText);
    }

    return fullText.ToString();
  }

  private List<CityDamageItem> ParseDamageFromConditionCheck(string text)
  {
    var damageItems = new List<CityDamageItem>();

    // Find the "Condition Check" section
    var conditionMatch = Regex.Match(text, @"Condition Check(.*?)(?=Interior|Mechanical|Photos|$)",
        RegexOptions.Singleline | RegexOptions.IgnoreCase);

    if (!conditionMatch.Success)
      return damageItems;

    var conditionText = conditionMatch.Groups[1].Value;
    var lines = conditionText.Split('\n', StringSplitOptions.RemoveEmptyEntries)
        .Select(line => line.Trim())
        .Where(line => !string.IsNullOrEmpty(line))
        .ToArray();

    for (int i = 0; i < lines.Length; i++)
    {
      var line = lines[i];

      // Skip obvious non-damage lines
      if (IsHeaderOrNoise(line))
        continue;

      // Look for numbered damage items
      var itemMatch = Regex.Match(line, @"^(\d+)\s+(.*?)$");
      if (itemMatch.Success)
      {
        var itemNumber = int.Parse(itemMatch.Groups[1].Value);
        var locationText = itemMatch.Groups[2].Value.Trim();

        if (ContainsVehiclePart(locationText))
        {
          var location = ExtractLocationFromLine(locationText);
          var description = BuildDescription(lines, i, location);

          // Extract damage type and severity from description
          var (damageType, severity) = ParseDamageTypeAndSeverity(description);

          if (!string.IsNullOrEmpty(description) && !string.IsNullOrEmpty(location))
          {
            damageItems.Add(new CityDamageItem
            {
              ItemNumber = itemNumber,
              Location = location,
              DamageType = damageType,
              Severity = severity,
              Description = description
            });
          }
        }
      }
    }

    return damageItems;
  }

  private List<CityDamageItem> ParseDamageFromFullText(string text)
  {
    var damageItems = new List<CityDamageItem>();

    // Fallback: Look for any text that contains vehicle parts + damage keywords
    var pattern = @"(\d+)?\s*([^\n]*(?:" + string.Join("|", KnownVehicleParts) + @")[^\n]*(?:" +
                  string.Join("|", DamageKeywords) + @")[^\n]*)";

    var matches = Regex.Matches(text, pattern, RegexOptions.IgnoreCase);
    int fallbackNumber = 1;

    foreach (Match match in matches)
    {
      var itemNumberStr = match.Groups[1].Value;
      var description = match.Groups[2].Value.Trim();

      if (description.Length > 10 && !IsHeaderOrNoise(description))
      {
        var itemNumber = int.TryParse(itemNumberStr, out var num) ? num : fallbackNumber++;
        var location = ExtractLocationFromDescription(description);
        var (damageType, severity) = ParseDamageTypeAndSeverity(description);

        damageItems.Add(new CityDamageItem
        {
          ItemNumber = itemNumber,
          Location = location,
          DamageType = damageType,
          Severity = severity,
          Description = description
        });
      }
    }

    return damageItems;
  }

  private (string damageType, string severity) ParseDamageTypeAndSeverity(string description)
  {
    // Extract damage type
    var damageTypes = new[] { "Scratched", "Chipped", "Dented", "Cracked", "Torn", "Broken", "Damaged", "Missing", "Corroded", "Scuffed", "Soiled" };
    var damageType = damageTypes.FirstOrDefault(type => description.Contains(type, StringComparison.OrdinalIgnoreCase)) ?? "Unknown";

    // Extract severity (everything after the damage type)
    var damageIndex = description.IndexOf(damageType, StringComparison.OrdinalIgnoreCase);
    var severity = damageIndex > -1 && damageIndex + damageType.Length < description.Length
        ? description.Substring(damageIndex + damageType.Length).Trim().TrimStart('-').Trim()
        : "Unknown";

    return (damageType, severity);
  }

  private string ExtractLocationFromLine(string line)
  {
    // Remove leading numbers if present
    var cleanLine = Regex.Replace(line, @"^\d+\s*", "").Trim();

    // Find the first vehicle part mentioned
    var lowerLine = cleanLine.ToLower();
    var matchedPart = KnownVehicleParts.FirstOrDefault(part => lowerLine.Contains(part));

    if (matchedPart != null)
    {
      // Extract the part and any directional indicators (osf, nsr, etc.)
      var partMatch = Regex.Match(cleanLine, @"([^,\n]*" + Regex.Escape(matchedPart) + @"[^,\n]*)", RegexOptions.IgnoreCase);
      if (partMatch.Success)
      {
        return partMatch.Groups[1].Value.Trim();
      }
    }

    return cleanLine;
  }

  private string ExtractLocationFromDescription(string description)
  {
    // Try to extract just the vehicle part from the full description
    var words = description.Split(' ', StringSplitOptions.RemoveEmptyEntries);
    var locationParts = new List<string>();

    foreach (var word in words)
    {
      if (DamageKeywords.Any(keyword => word.ToLower().Contains(keyword)))
        break;
      locationParts.Add(word);
    }

    var location = string.Join(" ", locationParts).Trim();

    // If we didn't find a natural break, try to extract known parts
    if (string.IsNullOrEmpty(location))
    {
      var lowerDesc = description.ToLower();
      var matchedPart = KnownVehicleParts.FirstOrDefault(part => lowerDesc.Contains(part));
      if (matchedPart != null)
      {
        var partMatch = Regex.Match(description, @"([^,\n]*" + Regex.Escape(matchedPart) + @"[^,\n]*)", RegexOptions.IgnoreCase);
        if (partMatch.Success)
        {
          location = partMatch.Groups[1].Value.Trim();
        }
      }
    }

    return location;
  }

  private string BuildDescription(string[] lines, int startIndex, string location)
  {
    var descriptionParts = new List<string>();

    // Start with the location line (cleaned)
    var locationLine = lines[startIndex];
    descriptionParts.Add(locationLine);

    // Look at the next few lines for damage details
    for (int i = startIndex + 1; i < Math.Min(startIndex + 3, lines.Length); i++)
    {
      var line = lines[i].Trim();

      // Stop if we hit another numbered item or obvious new section
      if (Regex.IsMatch(line, @"^\d+\s+\w+") || IsHeaderOrNoise(line) || ContainsVehiclePart(line))
        break;

      // Add lines that look like damage descriptions
      if (ContainsDamageKeywords(line) && line.Length > 2)
      {
        descriptionParts.Add(line);
      }
    }

    return string.Join(" - ", descriptionParts);
  }

  private bool ContainsVehiclePart(string text)
  {
    var lowerText = text.ToLower();
    return KnownVehicleParts.Any(part => lowerText.Contains(part));
  }

  private bool ContainsDamageKeywords(string text)
  {
    var lowerText = text.ToLower();
    return DamageKeywords.Any(keyword => lowerText.Contains(keyword));
  }

  private bool IsHeaderOrNoise(string text)
  {
    if (string.IsNullOrEmpty(text) || text.Length < 3)
      return true;

    var lowerText = text.ToLower();

    // Check against known noise patterns
    if (NoisePatterns.Any(pattern => lowerText.Contains(pattern)))
      return true;

    // Check for common header patterns
    if (Regex.IsMatch(text, @"^\d+$") || // Just numbers
        Regex.IsMatch(text, @"^[A-Z\s]+$") || // All caps (likely headers)
        text.Contains("@") || // Email addresses
        text.Contains("www.") || // URLs
        text.Contains("PE2 6XU")) // Postcodes
      return true;

    return false;
  }

  private async Task<CityVehicleInfo> ExtractVehicleInfo(PdfDocument pdfDocument)
  {
    var page = pdfDocument.GetPage(1);
    var strategy = new SimpleTextExtractionStrategy();
    var text = PdfTextExtractor.GetTextFromPage(page, strategy);

    return new CityVehicleInfo
    {
      Registration = ExtractWithRegex(text, @"([A-Z]{2}\d{2}[A-Z]{3})", "Registration"),
      Make = ExtractWithRegex(text, @"(Nissan|Ford|BMW|Audi|Toyota|Honda|Mercedes|Volkswagen|Vauxhall|Hyundai|Kia|Peugeot|Renault|Fiat|Skoda|Seat|Volvo|Jaguar|Land Rover|Mini|Porsche|Tesla|Dacia|Mazda|Subaru|Mitsubishi|Isuzu|Ssangyong|Jeep|Chrysler|Dodge|Cadillac|Chevrolet|Buick|GMC|Lincoln|Infiniti|Lexus|Acura)", "Make"),
      Model = ExtractWithRegex(text, @"(Qashqai|Focus|Fiesta|Golf|Corsa|Polo|Civic|Accord|A3|A4|A5|A6|A7|A8|Q3|Q5|Q7|320d|X3|X5|C200|E220|E250|E350|S-Class|C-Class|E-Class|Tucson|Sportage|i30|i20|Ceed|Sorento|Picanto|Rio|Optima|Stonic|Niro|Soul|Venga|Carens|Proceed|XCeed|208|308|408|508|2008|3008|5008|Partner|Boxer|Expert|Traveller|Rifter|Berlingo|C3|C4|C5|DS3|DS4|DS5|Megane|Clio|Captur|Kadjar|Koleos|Scenic|Espace|Trafic|Master|Kangoo|Twingo|Zoe|Talisman|Laguna|Fluence|Latitude|Wind|Sandero|Duster|Logan|Lodgy|Dokker|500|Panda|Punto|Tipo|Doblo|Ducato|Fiorino|Qubo|Bravo|Stilo|Marea|Barchetta|Coupe|Spider|Octavia|Fabia|Superb|Rapid|Yeti|Kodiaq|Karoq|Kamiq|Scala|Citigo|Roomster|Praktik|Ibiza|Leon|Altea|Toledo|Alhambra|Tarraco|Arona|Ateca|Formentor|Born|Mii|V40|V50|V60|V70|V90|S40|S60|S70|S80|S90|XC40|XC60|XC70|XC90|C30|C70|740|760|780|850|940|960|240|340|440|740|760|780|850|940|960|F-Pace|E-Pace|I-Pace|XE|XF|XJ|XK|F-Type|S-Type|X-Type|XJS|XJ6|XJ8|XJ12|XJR|XKR|Discovery|Defender|Freelander|Range Rover|Evoque|Velar|Sport|Vogue|Autobiography|SVR|Cooper|Clubman|Countryman|Paceman|Roadster|Coupe|Convertible|Hatch|Cayenne|Macan|Panamera|Boxster|Cayman|Carrera|Turbo|GT3|GT2|Targa|Speedster|Spyder|911|918|919|Taycan|Model S|Model 3|Model X|Model Y|Roadster|Cybertruck|Semi)", "Model"),
      Year = ExtractWithRegex(text, @"Reg\. date.*?(\d{4})", "Year"),
      Mileage = ExtractWithRegex(text, @"Mileage\s+(\d{1,3}(?:,\d{3})*)", "Mileage"),
      VIN = ExtractWithRegex(text, @"VIN\s+([A-Z0-9]{17})", "VIN"),
      Colour = ExtractWithRegex(text, @"Colour\s+(\w+)", "Colour")
    };
  }

  private string ExtractWithRegex(string text, string pattern, string fieldName)
  {
    var match = Regex.Match(text, pattern, RegexOptions.IgnoreCase);
    return match.Success ? match.Groups[1].Value : "";
  }
}

// Enhanced text position extractor specifically for condition check section
internal class ConditionCheckTextExtractor : IEventListener
{
  private readonly List<DotPosition> _positions = new List<DotPosition>();

  public void EventOccurred(IEventData data, EventType type)
  {
    if (type == EventType.RENDER_TEXT)
    {
      var textRenderInfo = (TextRenderInfo)data;
      var text = textRenderInfo.GetText().Trim();

      // Only look for single digit numbers that are standalone (not part of larger text)
      if (text.Length == 1 && char.IsDigit(text[0]))
      {
        var baseline = textRenderInfo.GetBaseline();
        var startPoint = baseline.GetStartPoint();

        Console.WriteLine($"Found single digit: '{text}' at X={startPoint.Get(0)}, Y={startPoint.Get(1)}");

        _positions.Add(new DotPosition
        {
          Label = text,
          X = startPoint.Get(0),
          Y = startPoint.Get(1)
        });
      }
    }
  }

  public List<DotPosition> GetNumberedPositions() => _positions;

  public ICollection<EventType> GetSupportedEvents() => new[] { EventType.RENDER_TEXT };
}

// Helper class for section bounds
internal class ConditionCheckBounds
{
  public int StartPage { get; set; }
  public int EndPage { get; set; }
  public int TextStart { get; set; }
  public int TextEnd { get; set; }
}

// Internal classes for data structures
internal class ExtractedImage
{
  public byte[] ImageData { get; set; }
  public int Width { get; set; }
  public int Height { get; set; }
  public string Format { get; set; }
  public int PageNumber { get; set; }
  public float X { get; set; }
  public float Y { get; set; }
  public bool IsUsed { get; set; }
}

internal class DotPosition
{
  public string Label { get; set; }
  public float X { get; set; }
  public float Y { get; set; }
  public int PageNumber { get; set; }
}

// Custom image extractor
internal class ImageExtractor : IEventListener
{
  private readonly int _pageNumber;
  public List<ExtractedImage> ExtractedImages { get; } = new List<ExtractedImage>();

  public ImageExtractor(int pageNumber)
  {
    _pageNumber = pageNumber;
  }

  public void EventOccurred(IEventData data, EventType type)
  {
    if (type == EventType.RENDER_IMAGE)
    {
      var imageRenderInfo = (ImageRenderInfo)data;
      var image = imageRenderInfo.GetImage();

      if (image != null)
      {
        try
        {
          var imageData = image.GetImageBytes();
          var matrix = imageRenderInfo.GetImageCtm();

          ExtractedImages.Add(new ExtractedImage
          {
            ImageData = imageData,
            Width = (int)image.GetWidth(),
            Height = (int)image.GetHeight(),
            Format = DetermineImageFormat(image),
            PageNumber = _pageNumber,
            X = matrix.Get(4), // Translation X
            Y = matrix.Get(5), // Translation Y
            IsUsed = false
          });
        }
        catch (Exception ex)
        {
          Console.WriteLine($"Error extracting image: {ex.Message}");
        }
      }
    }
  }

  private string DetermineImageFormat(PdfImageXObject image)
  {
    // Simple format detection based on filter
    var filter = image.GetPdfObject().GetAsName(iText.Kernel.Pdf.PdfName.Filter);
    if (filter != null)
    {
      if (filter.Equals(iText.Kernel.Pdf.PdfName.DCTDecode))
        return "JPEG";
      if (filter.Equals(iText.Kernel.Pdf.PdfName.FlateDecode))
        return "PNG";
    }
    return "PNG"; // Default
  }

  public ICollection<EventType> GetSupportedEvents() => new[] { EventType.RENDER_IMAGE };
}