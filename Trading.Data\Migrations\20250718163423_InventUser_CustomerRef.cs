﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Trading.API.Data.Migrations
{
    /// <inheritdoc />
    public partial class InventUser_CustomerRef : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CustomerRef",
                table: "InventUser",
                type: "varchar(5)",
                maxLength: 5,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CustomerRef",
                table: "InventUser");
        }
    }
}
