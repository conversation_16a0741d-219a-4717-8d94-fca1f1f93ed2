﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.Search.LeadCRM
{
  public class CRMAttribDTO : BaseModelEntityIntDTO
  {
    public uint? AttribId { get; set; }
    public virtual AttribDTO Attrib { get; set; }
    public uint? AttribvalId { get; set; }
    public virtual AttribvalDTO Attribval { get; set; }

    public uint? AttribInt { get; set; }
    public string AttribChar { get; set; }
    public DateTime? AttribDatetime { get; set; }
    public decimal? AttribDecimal { get; set; }
    public bool? AttribBool { get; set; }
  }

  public class CRMAttribSearchDTO : BaseSearchDTO
  {
    public CRMAttribSearchFilters Filters { get; set; } = new CRMAttribSearchFilters() { };
  }

  public class CRMAttribSearchFilters : BaseFilterInt
  {
    public uint? AttribId { get; set; }
  }
}
