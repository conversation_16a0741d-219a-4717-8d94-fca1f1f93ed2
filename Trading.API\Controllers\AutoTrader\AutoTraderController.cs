﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using Trading.API.Data.DTO.AutoTrader;
using Trading.Services.Interfaces.AutoTrader;

namespace Trading.API.Remarq.Controllers.AutoTrader
{
  [ApiController]
  [Route("api/[controller]")]
  public class AutoTraderController : ControllerBase
  {
    private readonly IAutoTraderService _autoTrader;
    private readonly ILogger<AutoTraderController> _logger;

    public AutoTraderController(IAutoTraderService autoTrader, ILogger<AutoTraderController> logger)
    {
      _autoTrader = autoTrader;
      _logger = logger;
    }

    [HttpGet]
    public async Task<ActionResult<AdvertiserListResponse>> GetAdvertisers(int page = 1, int pageSize = 20)
    {
      try
      {
        var advertisers = await _autoTrader.GetAdvertisersAsync(page, pageSize);
        return Ok(advertisers);
      }
      catch (AutoTraderRateLimitException ex)
      {
        _logger.LogWarning("Rate limit exceeded, CF-RAY: {CfRayId}", ex.CfRayId);
        return StatusCode(429, "Rate limit exceeded, please wait and retry");
      }
      catch (AutoTraderServiceUnavailableException ex)
      {
        _logger.LogWarning("Service unavailable, CF-RAY: {CfRayId}", ex.CfRayId);
        return StatusCode(503, "Service temporarily unavailable");
      }
      catch (AutoTraderApiException ex) when (ex.StatusCode == 403)
      {
        _logger.LogError("Access forbidden: {Message}, CF-RAY: {CfRayId}", ex.Message, ex.CfRayId);
        return Forbid("Access to this resource is forbidden");
      }
      catch (AutoTraderApiException ex)
      {
        _logger.LogError("AutoTrader API error: {Message}, CF-RAY: {CfRayId}", ex.Message, ex.CfRayId);
        return StatusCode(ex.StatusCode, ex.Message);
      }
    }
  }
}
