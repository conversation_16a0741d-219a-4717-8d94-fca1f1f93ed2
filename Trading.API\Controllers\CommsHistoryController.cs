namespace Trading.API.Controllers
{
  using Microsoft.AspNetCore.Mvc;
  using System.Threading;
  using System.Threading.Tasks;
  using System;
  using Newtonsoft.Json;
  using Trading.Services.Interfaces;
  using Trading.API.Data.DTO.Comms;

  [Route("/api/commsHistory")]
  public class CommsHistoryController : ControllerBase
  {
    private readonly ICommsHistoryService _commsHistoryService;

    public CommsHistoryController(ICommsHistoryService commsHistoryService)

    {
      _commsHistoryService = commsHistoryService;
    }

    [HttpGet]
    [Route("")]
    public async Task<IActionResult> GetCommsHistory([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = String.IsNullOrEmpty(query) ? null : JsonConvert.DeserializeObject<CommsHistorySearchDTO>(query);
        var pageres = await _commsHistoryService.Search(dto, cancellationToken);
        return Ok(pageres);
      }
      catch (Exception e)
      {
        return BadRequest(e);
      }
    }
  }
}