﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.DTO.LeadCRM;
using Trading.API.Data.Enums.Valuation;

namespace Trading.API.Data.DTO.Valuation
{
  public class ResultFromValuationDTO
  {
    public VehicleLookupInfoDTO VehicleInfo { get; set; }
    public double ResultValue { get; set; }
    public ValuationQuoteTypeEnum QuoteType { get; set; }
    public string ExternalRef { get; set; }
    public LeadVehicleDTO LeadVehicle { get; set; }
    public string ContactNote { get; set; }
    public Guid? ContactId { get; set; }
    public List<string> Steps { get; set; } // admin only 
    public List<string> Log { get; set; } // admin only 
    public List<int> GradeValues { get; set; }

    public DateTime? Expires { get; set; }

    public Guid? LeadId { get; set; }

    public string ContactName { get; set; }
    public string ContactPhone { get; set; }
    public string ContactEmail { get; set; }

    public bool CallRequired { get; set; }
    public bool NoQuote { get; set; }
  }
}
