﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO.UInspections;
using Trading.API.Data.Models.UInspections;
using Trading.Services.UInspections.Interfaces;

namespace Trading.API.UInspection.Controllers
{
  [Route("api/uinspect/sectionComplete")]
  [ApiController]
  public class UInspectSectionCompleteController : ControllerBase
  {
    private readonly IUInspectSectionCompleteService _uInspectSectionCompleteService;

    public UInspectSectionCompleteController(IUInspectSectionCompleteService uInspectSectionCompleteService)
    {
      this._uInspectSectionCompleteService = uInspectSectionCompleteService;
    }

    [HttpGet]
    [Route("/api/uinspect/{id}/sectionsComplete")]
    [ResponseCache(Duration = 5)]
    [HttpGet]
    public async Task<IActionResult> GetSectionsComplete(Guid UInspectId, [FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = new UInspectSectionCompleteSearchDTO() { };

        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<UInspectSectionCompleteSearchDTO>(query);
        }

        dto.Filters.UInspectId = UInspectId;

        var result = await _uInspectSectionCompleteService.Search(dto, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<IActionResult> Get(uint id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = new UInspectSectionCompleteSearchDTO();

        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<UInspectSectionCompleteSearchDTO>(query);
        }

        var result = await _uInspectSectionCompleteService.Get(id, cancellationToken, dto);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create([FromBody] UInspectSectionCompleteDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        var result = await _uInspectSectionCompleteService.Create(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPut]
    [Route("/api/uinspect/{uinspectId}/section/{sectionId}/complete/{complete}")]
    public async Task<IActionResult> SetComplete(Guid uinspectId, uint sectionId, string complete, CancellationToken cancellationToken)
    {
      try
      {
        var dto = new UInspectSectionCompleteDTO() { Complete = (complete == "true"), UInspectSectionId = sectionId, UInspectId = uinspectId };
        var result = await _uInspectSectionCompleteService.SetComplete(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(uint id, JsonPatchDocument<UInspectSectionComplete> patch, CancellationToken cancellationToken)
    {
      try
      {
        var result = await _uInspectSectionCompleteService.Patch(id, patch, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
