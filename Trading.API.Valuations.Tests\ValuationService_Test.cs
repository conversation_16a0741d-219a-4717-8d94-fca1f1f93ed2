﻿using AutoMapper;
using Microsoft.Extensions.Options;
using Moq;
using Trading.API.Data.DTO.Valuation;
using Trading.API.Data.Enums;
using Trading.API.Tests.Common;
using Trading.API.Tests.Common.Priority;
using Trading.Services;
using Trading.Services.Classes;
using Trading.Services.ExternalDTO;
using Trading.Services.Interfaces;
using Trading.Services.UInspections.Interfaces;
using Trading.Services.Valuations;
using Trading.Services.Valuations.Interfaces;

namespace Trading.API.Valuations.Tests
{
  [Collection("DatabaseCollection")]
  [TestCaseOrderer("Trading.API.Tests.Priority.PriorityOrderer", "Trading.API.Tests")]
  public class ValuationService_Test : TestBase
  {
    private const string ProfileName = "Toyota Test";

    public ValuationService_Test(DatabaseFixture fixture)
    {

      // create valuation trees for a selection of Toyota cars 
    }

    public IValuationService GetService()
    {
      // need to use the real lookup service API key in these tests
      var ukVehicleDTO = new Mock<IOptionsSnapshot<UKVehicleDTO>>();
      ukVehicleDTO.SetupAllProperties();
      ukVehicleDTO.SetupGet(p => p.Value).Returns(new UKVehicleDTO { APIKey = "936a696b-2a4f-46f9-841b-20ecbf0b800e" });

      IVehicleCheckService checkService = new Mock<IVehicleCheckService>().Object;

      IVehicleMediaService vehicleMediaService = new VehicleMediaService(_context, _mapper, _common.FileStorageService, _common.MessageService, _common.UserService, null, null);
      IAppraisalService appraisalService = new AppraisalService(_context, _mapper, _common.FileStorageService);
      IVehicleService vehicleService = new VehicleService(_context, null, _mapper, _common.FileStorageService, vehicleMediaService, _common.LookupService
        , _common.Configuration, _common.DVLAService, appraisalService, _common.UserService, null, _common.MessageService, null, checkService);

      IVRMLookupService vrmLookupService = new UKVehicleDataService(_context, ukVehicleDTO.Object, _mapper, _common.LookupService);
      IVehicleValueService vehicleValueService = new VehicleValueService(_context, _mapper);
      IRoleService roleService = new RoleService(_context, _mapper);
      IContactRoleService contactRoleService = new ContactRoleService(roleService, _context, _mapper);
      IEmailService emailService = new Mock<IEmailService>().Object;

      ICustomerNoteService noteService = new CustomerNoteService(_context, _mapper, new UserService(null, _context), null);
      IContactActionService contactActionService = new ContactActionService(_context, base._mapper, _common.UserService, null,null, null);
      ICustomerService customerService = new CustomerService(_context, _mapper, emailService, null, contactActionService, _common.UserService);

      ICommsTemplateService templateService = new Mock<ICommsTemplateService>().Object;

      ICustomerInternalInfoService customerInternalInfoService = new CustomerInternalInfoService(_context, _mapper, noteService, null);
      IContactService contactService = new ContactService(null, _context, contactRoleService, roleService, _common.Configuration, customerService, _mapper, contactActionService, templateService, customerInternalInfoService, _common.WhosWhoService);
      //ICRMAttribService crmAttribService = new CRMAttribService(_context, _mapper);
      //ICRMUserService crmUserService = new CRMUserService(_context, _mapper);

      //ILeadCRMService leadCRM = new LeadCRMService(_context, _mapper, _common.UserService, vrmLookupService, vehicleService, contactService, crmAttribService, crmUserService);
      IUInspectService externalAppraisalService = new Fakes.FakeExternalAppraisalService();
      IValuationService valuationService = new ValuationService(_context, _mapper, new List<IVRMLookupService> { vrmLookupService }, vehicleService, vehicleValueService, null, checkService);
      IValuationQuoteService valuationQuoteService = new ValuationQuoteService(_context, _mapper, externalAppraisalService, null, valuationService, _common.EmailService, templateService);
            

      return valuationService;
    }

    [Fact, TestPriority(1)]
    public async Task CreateValuation()
    {
      //var tp = typeof(PriorityOrderer).FullName;
      //var an = typeof(ValuationService_Test).Assembly.GetName().Name;

      _context.ValuationProfiles.RemoveRange(_context.ValuationProfiles.ToList());
      await _context.SaveChangesAsync();

      var service = GetService();

      var valuationDTO = new ValuationProfileDTO { Name = ProfileName };
      var result = await service.AddValuationProfile(valuationDTO, CancellationToken.None);

      Assert.NotNull(result);
      Assert.True(result.IsValid);

      var profile = result.DTO;
      Assert.True(profile.Id != Guid.Empty && profile.Name == ProfileName && profile.StatusId == (uint)StatusEnum.Active);
    }

    /*
    [Fact, TestPriority(2)]
    public async Task AddPillarsToValuation()
    {
      _context.ValuationPillars.RemoveRange(_context.ValuationPillars.ToList());
      await _context.SaveChangesAsync();

      var service = GetService();

      string lookupName = "Toyota";

      var profile = await _context.ValuationProfiles.FirstOrDefaultAsync(x => x.Name == ProfileName);
      
      // create pillar nodes 
      var makeId = await GetLookupId("make", lookupName);
      var make = new ValuationPillarDTO { ValuationProfileId = profile.Id, LookupId = makeId.ToString(), Multiplier = 1.0, LookupType = ValuationLookupTypeEnum.Make, LookupName = lookupName };
      
      lookupName = "Yaris";

      var modelId = await GetLookupId("model", lookupName, makeId);
      var model = new ValuationPillarDTO { ValuationProfileId = profile.Id, LookupId = modelId.ToString(), Multiplier = 1.1, LookupType = ValuationLookupTypeEnum.Model, LookupName = lookupName };
      var age = new ValuationPillarDTO   { ValuationProfileId = profile.Id, LookupId = $"{modelId}", LookupValue = 6, Multiplier = 1.2, LookupType = ValuationLookupTypeEnum.Age, LookupName = "Up to 6 Months old" };
      var age2 = new ValuationPillarDTO  { ValuationProfileId = profile.Id, LookupId = $"{modelId}", LookupValue = 12, Multiplier = 1.1, LookupType = ValuationLookupTypeEnum.Age, LookupName = "Up to 12 Months old" };
      var age3 = new ValuationPillarDTO  { ValuationProfileId = profile.Id, LookupId = $"{modelId}", LookupValue = 60, Multiplier = 0.85, LookupType = ValuationLookupTypeEnum.Age, LookupName = "Up to 5 years old" };
      var age4 = new ValuationPillarDTO  { ValuationProfileId = profile.Id, LookupId = $"{modelId}", LookupValue = 120, Multiplier = 0.75, LookupType = ValuationLookupTypeEnum.Age, LookupName = "Up to 10 years old" };

      lookupName = "YARIS ICON VVT-I CVT";

      var derivId = await GetLookupId("deriv", lookupName, modelId);
      var deriv = new ValuationPillarDTO { ValuationProfileId = profile.Id, LookupId = derivId.ToString(), Multiplier = 1.05, LookupType = ValuationLookupTypeEnum.Deriv, LookupName = lookupName };
      
      await AddPillarNodes(service, make, model, age, age2, age3, age4, deriv);
    }

    private async Task<Guid> GetPillarId(ValuationLookupTypeEnum lookupType, string lookupName)
    {
      var pillar = await _context.ValuationPillars.FirstOrDefaultAsync(x => x.LookupType == lookupType && x.LookupName == lookupName);
      return pillar.Id;
    }

    [Fact, TestPriority(3)]
    public async Task AddNodesToValuationPillars()
    {
      var service = GetService();

      _context.ValuationNodes.RemoveRange(_context.ValuationNodes.ToList());

      // add modifiers (nodes) to pillars
      string lookupName = "Diesel";
      var fuelDId = await GetLookupId("fuel_type", lookupName);
      var makePillarId = await GetPillarId(ValuationLookupTypeEnum.Make, "Toyota");
      var fuelD = new ValuationNodeDTO { ValuationPillarId = makePillarId, LookupId = fuelDId.ToString(), Multiplier = 0.9, LookupType = ValuationLookupTypeEnum.Fuel, LookupName = lookupName };

      lookupName = "Petrol";
      var fuelPId = await GetLookupId("fuel_type", lookupName);
      var fuelP = new ValuationNodeDTO { ValuationPillarId = makePillarId, LookupId = fuelPId.ToString(), Multiplier = 1.05, LookupType = ValuationLookupTypeEnum.Fuel, LookupName = lookupName };

      lookupName = "Electric";
      var fuelEId = await GetLookupId("fuel_type", lookupName);
      var modelPillarId = await GetPillarId(ValuationLookupTypeEnum.Model, "Yaris");
      var fuelE = new ValuationNodeDTO { ValuationPillarId = modelPillarId, LookupId = fuelEId.ToString(), Multiplier = 1.2, LookupType = ValuationLookupTypeEnum.Fuel, LookupName = lookupName };

      var fuelModelP = new ValuationNodeDTO { ValuationPillarId = modelPillarId, LookupId = fuelPId.ToString(), Multiplier = 1.15, LookupType = ValuationLookupTypeEnum.Fuel, LookupName = "Petrol" };

      lookupName = "3 Doors";
      var derivPillarId = await GetPillarId(ValuationLookupTypeEnum.Deriv, "YARIS ICON VVT-I CVT");
      var doors3 = new ValuationNodeDTO { ValuationPillarId = derivPillarId, LookupId = "3", Multiplier = 1.05, LookupType = ValuationLookupTypeEnum.Doors, LookupName = lookupName };

      lookupName = "5 Doors";
      var doors5 = new ValuationNodeDTO { ValuationPillarId = derivPillarId, LookupId = "5", Multiplier = 1.1, LookupType = ValuationLookupTypeEnum.Doors, LookupName = lookupName };

      lookupName = "White";
      var colourId = await GetLookupId("colour", lookupName);
      var colour = new ValuationNodeDTO { ValuationPillarId = modelPillarId, LookupId = colourId.ToString(), Multiplier = 0.85, LookupType = ValuationLookupTypeEnum.Colour, LookupName = lookupName };

      await AddValuationNodes(service, fuelP);
      await AddValuationNodes(service, fuelD, fuelModelP, fuelE, colour);
      await AddValuationNodes(service, doors3, doors5);

      Assert.NotNull(fuelD);
      Assert.NotNull(fuelP);
      Assert.NotNull(fuelE);
      Assert.NotNull(fuelModelP);
      Assert.NotNull(doors3);
      Assert.NotNull(doors5);
      Assert.NotNull(colour);
    }

    [Fact, TestPriority(4)]
    public async Task GetValuation()
    {
      // create a valuation tree for Toyota Yaris
      var service = GetService();

      await _context.SaveChangesAsync();

      var result = await service.GetValuation(new ValuationRequestDTO { VRM = "AE67YTX", BaseCapPrice = 8000 }, CancellationToken.None);

      Assert.NotNull(result);
      Assert.True(result.DTO.ResultValue == 8867);
    }

    private async Task<uint?> GetLookupId(string table, string value, uint? parentId = null)
    {
      var lookup = _common.LookupService;
      return await lookup.GetLookupId(new LookupDTO { TableName = table, LookupValue = value, ParentId = parentId, VehicleType = VehicleTypeEnum.Car }, CancellationToken.None);
    }

    private async Task AddValuationNodes(IValuationService service, params ValuationNodeDTO[] nodes)
    {
      foreach (var node in nodes)
      {
        await service.AddValuationNode(node, CancellationToken.None);
      }
    }

    private async Task AddPillarNodes(IValuationService service, params ValuationPillarDTO[] nodes)
    {
      foreach (var node in nodes)
      {
        await AddPillarNode(service, node);
      }
    }

    private async Task<Guid> AddPillarNode(IValuationService service, ValuationPillarDTO node)
    {
      var result = await service.AddPillarNode(node, CancellationToken.None);
      if (result.IsValid)
      {
        return result.DTO.Id.Value;
      }
      else
      {
        return Guid.Empty;
      }
    }
    */
  }
}
