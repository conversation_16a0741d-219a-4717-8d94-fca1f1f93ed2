﻿﻿using AutoMapper;
using Trading.API.Data;
using Trading.API.Tests.Common;
using Trading.API.Tests.Common.Helpers;

namespace Trading.Services.InspectCollect.Tests
{
    public abstract class TestBase
    {
        protected ITestBase _testBase;
        protected TradingContext _context;
        protected IMapper _mapper;
        protected CommonServices _common;
        protected LookupFactory _lookupFactory;

        public TestBase()
        {
            _testBase = new CommonTestBase();

            _context = _testBase.Context;
            _mapper = _testBase.Mapper;
            _common = _testBase.Common;
            _lookupFactory = _testBase.LookupFactory;
        }
    }
}
