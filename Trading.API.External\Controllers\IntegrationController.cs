using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.UInspections;
using Trading.API.Data.Enums;
using Trading.Services.Extensions;
using Trading.Services.External.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/[controller]")]
  [ApiController]
  public class IntegrationController : ControllerBase
  {
    private readonly IExternalEventService _externalEventService;
    private readonly IEventQueueService _eventQueueService;

    public IntegrationController(
      IExternalEventService externalEventService,
      IEventQueueService eventQueueService
      )
    {
      this._externalEventService = externalEventService;
      this._eventQueueService = eventQueueService;
    }

    [HttpPut]
    [Route("vehicle-vision/event/{eventType}/{externalAppraisalCode}")]
    public async Task<ActionResult> NewVehicleVisionEvent(string eventType, string externalAppraisalCode, CancellationToken ct)
    {
      Request.Headers.TryGetValue("accessId", out var auth);

      if (auth != "sQjYJQn53gWR" && ! this.User.HasRole(UserRoleEnum.Admin))
      {
        return Forbid();
      }

      var response = await _externalEventService.CreateProcessEvent(eventType, externalAppraisalCode, ct);

      if (response == null)
      {
        return Ok(new { success = false, error = "EventType: '" + eventType + "' EventId: '" + externalAppraisalCode + "' already received" });
      }

      var ackId = EFExtensions.GetHashString(response.ToString());

      return Ok(new { Success = true, SuccessId = ackId });
    }

    [HttpGet]
    [Route("initializeEvent/{eventType}/{externalAppraisalCode}")]
    public async Task<ActionResult> InitEvent(string eventType, string externalAppraisalCode,
      CancellationToken cancellationToken)
    {
      var response = await _externalEventService.CreateIntegrationEvent(eventType, externalAppraisalCode, cancellationToken);

      return Ok(response);
    }

    [HttpGet]
    [Route("processEvent/{eventQueueId}")]
    public async Task<ActionResult> ProcessEvent(uint eventQueueId, CancellationToken cancellationToken)
    {
      var response = await _externalEventService.ProcessIntegrationEvent(eventQueueId, cancellationToken);

      return Ok(response);
    }

    [HttpGet]
    [Route("events")]
    public async Task<ActionResult> SearchEvents([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<EventQueueSearchDTO>(query);
      var response = await _eventQueueService.Search(dto, cancellationToken);

      return Ok(response);
    }



    [HttpPost]
    [Route("begin-appraisal-journey")]
    public async Task<ActionResult> BeginAppraisalJourney([FromBody]UInspectRequestDTO request, CancellationToken ct)
    {
      Request.Headers.TryGetValue("accessId", out var auth);

      if (auth != "sQjYJQn53gWR" && !this.User.HasRole(UserRoleEnum.Admin))
      {
        return Forbid();
      }


      var ackId = EFExtensions.GetHashString("");

      return Ok(new { Success = true, SuccessId = ackId });
    }
  }
}