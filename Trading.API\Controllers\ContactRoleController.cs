using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/[controller]")]
  [ApiController]
  [Authorize]
  public class ContactRoleController : ControllerBase
  {
    private readonly IContactRoleService _contactRoleService;

    public ContactRoleController(IContactRoleService contactRoleService)
    {
      _contactRoleService = contactRoleService;
    }



    [HttpGet]
    [Route("/api/contacts/with-role/{role}")]
    public async Task<IActionResult> getWithRole(string role, CancellationToken cancellationToken, [FromQuery] string query)
    {
      try
      {
        var dto = new ContactRoleSearchDTO() { };

        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<ContactRoleSearchDTO>(query);
        }

        dto.Filters.RoleName = role;

        var result = await _contactRoleService.ContactSearch(dto, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
