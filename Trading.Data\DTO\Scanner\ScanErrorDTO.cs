using System;
using System.Linq;
using Trading.API.Data.DTO;

namespace Trading.API.Data.Models.DTO
{
  public class ScanErrorDTO : BaseModelEntityIntDTO
  {
    public CustomerDTO Customer { get; set; }

    public Guid CustomerId { get; set; }

    public uint ScanStyleId { get; set; }

    public ScanStyleDTO ScanStyle { get; set; }

    public string ScanFunction { get; set; }

    public uint ScanVehicleId { get; set; }

    public ScanVehicleDTO ScanVehicle { get; set; }

    public uint ScanQueueId { get; set; }

    public ScanQueueDTO ScanQueue { get; set; }

    public uint ScanStageId { get; set; }
    public ScanStageDTO ScanStage { get; set; }
    public uint ScanFieldId { get; set; }
    public ScanFieldDTO ScanField { get; set; }

    public string Reason { get; set; }

    public bool Cleared { get; set; }
  }
  public class ScanErrorSearchDTO : BaseSearchDTO
  {
    public ScanErrorFilters Filters { get; set; } = new ScanErrorFilters() { };
  }
  public class ClearScanErrorDTO 
  {
    public uint ScanFieldId { get; set; }
    public string ClearType { get; set; }
    public uint ClearValue { get; set; }
  }

  public class ScanErrorFilters : BaseFilterInt
  {
    public uint ScanStyleId { get; set; }
    public uint ScanCustomerId { get; set; }
  }
}