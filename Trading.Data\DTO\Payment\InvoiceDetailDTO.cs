﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO.Payment
{
  public class InvoiceDetailDTO : BaseModelEntityDTO
  {
    public Guid CustomerId { get; set; }
    public InvoiceDetailCustomerDTO Customer { get; set; }
    public decimal TotalNetAmt { get; set; }
    public decimal TotalTaxAmt { get; set; }
    public decimal TotalGrossAmt { get; set; }

    public string InvoiceReference { get; set; }// e.g. link to xero invoice id

    public string InvoiceNumber { get; set; }

    public string PaymentReference { get; set; }// e.g. link to xero invoice id

    public DateTime IssuedDate { get; set; }
    public DateTime? PaidDate { get; set; }
    public string InvoiceURL { get; set; }

    public IEnumerable<InvoiceDetailCustomerOrderDTO> CustomerOrders { get; set; }

    public string PaymentMethodId { get; set; }
    public string PaymentCustomerId { get; set; }
  }

  public class InvoiceDetailCustomerDTO
  {
    public string CustomerName { get; set; }
    public string Email { get; set; }
    public string Phone1 { get; set; }

    public string WebsiteUrl { get; set; }
    public string VATNumber { get; set; }
  }

  public class InvoiceDetailCustomerOrderDTO
  {
    public Guid AdvertId { get; set; }
    public uint? BidAmt { get; set; }

    public string ContactName { get; set; }

    public decimal TotalNetAmt { get; set; }
    public decimal TotalTaxAmt { get; set; }
    public decimal TotalGrossAmt { get; set; }

    public virtual ICollection<InvoiceDetailOrderLineDTO> OrderLines { get; set; }

    public DateTime IssuedDate { get; set; }

    public CustomerOrderTypeEnum OrderType { get; set; }
    public string OrderTypeDesc { get; set; }

    public string ImageURL { get; set; } // if this order is connected to a vehicle, map the image url here
    public string Description { get; set; }
    public string VRM { get; set; } // if this order is connected to a vehicle, map vrm here
  }

  public class InvoiceDetailOrderLineDTO
  {
    public string ProductName { get; set; }
    public string ProductCode { get; set; }

    public decimal NetAmt { get; set; }
    public decimal TaxAmt { get; set; }
    public decimal GrossAmt { get; set; }

    public string ExternalReference { get; set; }
  }
}
