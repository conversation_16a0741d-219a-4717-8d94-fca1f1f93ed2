using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/input-option")]
  [ApiController]
  [AllowAnonymous]
  public class ICInputOptionController : ControllerBase
  {
    private readonly ICInputOptionInterface _icInputOptionService;

    public ICInputOptionController(ICInputOptionInterface serviceInterface)
    {
      _icInputOptionService = serviceInterface;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icInputOptionService.Get(id, null, cancellationToken);
      return Ok(res);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icInputOptionService.Delete(id);
      return Ok(res);
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Post([FromBody]ICInputOptionCreateDTO dto, CancellationToken cancellationToken)
    {
      var res = await _icInputOptionService.Create(dto);
      return Ok(res);
    }

    [HttpGet]
    [Route("search")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICInputOptionSearchDTO>(query);
      var res = await _icInputOptionService.Search(dto, cancellationToken);
      return Ok(res);
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<ICInputOption> dto)
    {
      var response = await _icInputOptionService.Patch(id, dto);
      return Ok(response);
    }
  }
}