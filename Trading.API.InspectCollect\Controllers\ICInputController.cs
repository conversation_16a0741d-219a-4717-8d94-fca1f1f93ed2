using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.Extensions;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/input")]
  [ApiController]
  [Authorize(Policy = "RequireInspectCollect")]

  [AllowAnonymous]
  public class ICInputController : ControllerBase
  {
    private readonly ICInputInterface _icInputService;

    public ICInputController(ICInputInterface serviceInterface)
    {
      _icInputService = serviceInterface;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(Guid id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICInputSearchDTO>(query);
      var res = await _icInputService.Get(id, dto, cancellationToken);
      return Ok(res);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icInputService.Delete(id);
      return Ok(res);
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Post([FromBody]ICInputCreateDTO dto, CancellationToken cancellationToken)
    {
      var res = await _icInputService.Create(dto);
      return Ok(res);
    }

    [HttpGet]
    [Route("search")]
    [Route("/api/inspect-collect/inputs")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICInputSearchDTO>(query);

      var icContainerGroupId = User.ICContainerGroupId();

      if (icContainerGroupId != dto.Filters.ICContainerGroupId && !User.IsAdmin())
      {
        return Forbid();
      }

      var res = await _icInputService.Search(dto, cancellationToken);
      return Ok(res);
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<ICInput> dto)
    {
      var response = await _icInputService.Patch(id, dto);
      return Ok(response);
    }
  }
}