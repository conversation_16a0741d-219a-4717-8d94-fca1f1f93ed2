﻿using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System.Threading;
using System;
using Trading.Services.Classes;
using Trading.Services.Interfaces;

namespace Trading.API.Remarq.Controllers
{
  [Route("api/service-queue")]
  [ApiController]
  public class ServiceQueueController : ControllerBase
  {
    private readonly IServiceQueueService _serviceQueueService;

    public ServiceQueueController(IServiceQueueService serviceQueueService)
    {
      this._serviceQueueService = serviceQueueService;
    }

    [HttpGet]
    [Route("process")]
    public async Task<IActionResult> ProcessQueue()
    {
      var data = await _serviceQueueService.ProcessQueue();
      return Ok("Queue processed: " + data);
    }
  }
}
