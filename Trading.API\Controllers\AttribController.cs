using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.API.Helpers;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Authorize]
  [Route("api/attrib")]
  [ApiController]
  public class AttribController : ControllerBase
  {
    private readonly IAttribService _attribService;

    public AttribController(IAttribService attribService) 
    {
      this._attribService = attribService;
    }

    [HttpGet]
    [Route("/api/attribs")]
    public async Task<IActionResult> Search([FromQuery] int? unique, [FromQuery] string? query, CancellationToken cancellationToken)
    {
      var searchDTO = new AttribSearchDTO() { };

      if (!String.IsNullOrEmpty(query))
      {
        searchDTO = JsonConvert.DeserializeObject<AttribSearchDTO>(query);
      }

      try
      {
        var attribs = await _attribService.Search(searchDTO, cancellationToken);
        return Ok(attribs);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/attrib/{attribId}")]
    public async Task<IActionResult> Get(uint attribId, [FromQuery] int? unique, [FromQuery] string? search, CancellationToken cancellationToken)
    {
      var searchDTO = new AttribSearchDTO() { LockRecord = false };

      if (!String.IsNullOrEmpty(search))
      {
        searchDTO = JsonConvert.DeserializeObject<AttribSearchDTO>(search);
      }

      try
      {
        var attrib = await _attribService.Get(attribId, searchDTO, cancellationToken);

        return Ok(attrib);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create(AttribDTO dto, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        var response = await _attribService.Create(dto, cancellationToken);

        return Ok(response);
      }

      return Forbid();
    }

    [HttpDelete]
    [Route("{attribId}")]
    public async Task<IActionResult> Delete(uint attribId, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        var response = await _attribService.Delete(attribId, cancellationToken);

        return Ok(response);
      }

      return Forbid();
    }

    [HttpPatch]
    [Route("{attribId}")]
    public async Task<IActionResult> Patch(uint attribId, [FromBody] JsonPatchDocument<Attrib> patch, CancellationToken cancellationToken)
    {
      // Only edit addresses that are ours (or if we're admin)
      if (User.IsAdmin())
      {
        try
        {
          return Ok(await _attribService.Patch(attribId, patch, cancellationToken));
        }
        catch (Exception ex)
        {
          return ex.ParseError();
        }
      }

      return Forbid();
    }
  }
}
