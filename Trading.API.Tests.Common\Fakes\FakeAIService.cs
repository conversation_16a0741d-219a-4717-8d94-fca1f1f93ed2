﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.Services.Interfaces.AI;

namespace Trading.API.Tests.Common.Fakes
{
  public class FakeAIService : IAIService
  {
    public Task<string> AskQuestionAsync(string question)
    {
      throw new NotImplementedException();
    }

    public Task<string> GetVehicleValuationJSON(VRMLookupDataDTO lookupData)
    {
      throw new NotImplementedException();
    }
  }
}
