using Microsoft.AspNetCore.Mvc;
using Trading.API.Data.DTO.InspectCollect.VehicleLookup;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/vehicle-lookup")]
  [ApiController]
  public class ICVehicleLookupController : ControllerBase
  {
    private readonly ICVehicleDataInterface _vehicleDataService;

    public ICVehicleLookupController(ICVehicleDataInterface vehicleDataService)
    {
      _vehicleDataService = vehicleDataService;
    }

    [HttpGet("response/{responseId}/vrm/{vrm}/odometer/{odometer}")]
    public async Task<IActionResult> GetValuationData(Guid responseId, string vrm, uint odometer, CancellationToken cancellationToken)
    {
      string validationError = await ValidateRequest(responseId, vrm, "VRM", cancellationToken);
      if (validationError != null)
      {
        return BadRequest(validationError);
      }

      var enquiry = new ICVehicleEnquiryDTO
      {
        ResponseId = responseId,
        VRM = vrm.ToUpper(),
        Odometer = odometer
      };

      return await GetValuationDataInternal(enquiry, cancellationToken);
    }

    [HttpGet("response/{responseId}/vin/{vin}/odometer/{odometer}")]
    public async Task<IActionResult> GetValuationDataByVin(Guid responseId, string vin, uint odometer, CancellationToken cancellationToken)
    {
      string validationError = await ValidateRequest(responseId, vin, "VIN", cancellationToken);
      if (validationError != null)
      {
        return BadRequest(validationError);
      }

      var enquiry = new ICVehicleEnquiryDTO
      {
        ResponseId = responseId,
        VIN = vin,
        Odometer = odometer
      };

      return await GetValuationDataInternal(enquiry, cancellationToken);
    }

    [HttpGet("response/{responseId}/vrm/{vrm}")]
    public async Task<IActionResult> GetVehicleLookupData(Guid responseId, string vrm, uint odometer, CancellationToken cancellationToken)
    {
      string validationError = await ValidateRequest(responseId, vrm, "VRM", cancellationToken);
      if (validationError != null)
      {
        return BadRequest(validationError);
      }

      var enquiry = new ICVehicleEnquiryDTO
      {
        ResponseId = responseId,
        VRM = vrm.ToUpper(),
        Odometer = odometer,
      };

      return await GetLookupDataInternal(enquiry, cancellationToken);
    }

    [HttpGet("response/{responseId}/vin/{vin}")]
    public async Task<IActionResult> GetVehicleLookupDataByVin(Guid responseId, string vin, uint odometer, CancellationToken cancellationToken)
    {
      string validationError = await ValidateRequest(responseId, vin, "VIN", cancellationToken);
      if (validationError != null)
      {
        return BadRequest(validationError);
      }

      var enquiry = new ICVehicleEnquiryDTO
      {
        ResponseId = responseId,
        VIN = vin,
        Odometer = odometer
      };

      return await GetLookupDataInternal(enquiry, cancellationToken);
    }

    private async Task<string> ValidateRequest(Guid responseId, string identifier, string identifierName, CancellationToken cancellationToken)
    {
      if (string.IsNullOrEmpty(identifier))
      {
        return $"{identifierName} is required";
      }

      if (string.IsNullOrEmpty(responseId.ToString()))
      {
        return "Response is required";
      }

      return null;
    }

    private async Task<IActionResult> GetValuationDataInternal(ICVehicleEnquiryDTO enquiry, CancellationToken cancellationToken)
    {
      var result = await _vehicleDataService.GetValuationDataInternal(enquiry, cancellationToken);
      if (!result.Success)
      {
        return BadRequest(result.ErrorMessage);
      }
      return Ok(result.Data);
    }

    private async Task<IActionResult> GetLookupDataInternal(ICVehicleEnquiryDTO enquiry, CancellationToken cancellationToken)
    {
      var result = await _vehicleDataService.GetLookupDataInternal(enquiry, cancellationToken);

      if (!result.Success)
      {


        return BadRequest(result.ErrorMessage);
      }
      return Ok(result.Data);
    }
  }
}