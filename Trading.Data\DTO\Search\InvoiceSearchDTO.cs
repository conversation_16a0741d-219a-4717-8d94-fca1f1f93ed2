﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO
{
  public class InvoiceSearchDTO: BaseSearchDTO
  {
    public bool UnpaidOnly { get; set; }
    public string InvoiceNo { get; set; }
    public string Customer { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }  
    public DateTime? FromDueDate { get; set; }
    public DateTime? ToDueDate { get; set; }
    public bool UnreconciledOnly { get; set; }
  }
}
