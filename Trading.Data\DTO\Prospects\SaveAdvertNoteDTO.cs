﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO.Prospects
{
  public class SaveAdvertNoteDTO
  {
    public Guid AdvertId { get; set; }

    public string Note { get; set; }

    public Guid ContactId { get; set; }

    public DateTime? ReminderDateTime { get; set; }

    // prospect data (when added for a specific prospect)
    public Guid? ProspectId { get; set; }
    public uint? ProspectActionId { get; set; }
    public uint? StatusId { get; set; }

    // 
  }
}
