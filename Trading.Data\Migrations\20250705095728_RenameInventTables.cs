﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Trading.API.Data.Migrations
{
    /// <inheritdoc />
    public partial class RenameInventTables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AucaAuctionLotImage");

            migrationBuilder.DropTable(
                name: "AucaAuctionLot");

            migrationBuilder.DropTable(
                name: "AucaAuction");

            migrationBuilder.CreateTable(
                name: "InventAuction",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "binary(16)", nullable: false),
                    InventUserId = table.Column<Guid>(type: "binary(16)", nullable: true),
                    AuctionId = table.Column<long>(type: "bigint", nullable: false),
                    Title = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    DateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    EndDateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    ActivityStatus = table.Column<int>(type: "int", nullable: false),
                    AllowStandOn = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CurrentLotId = table.Column<int>(type: "int", nullable: true),
                    Information = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    AuctionLocationId = table.Column<int>(type: "int", nullable: false),
                    AuctionLocationTitle = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    AuctionTypeTitle = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    StatusId = table.Column<uint>(type: "int unsigned", nullable: false),
                    Added = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    Updated = table.Column<DateTime>(type: "datetime(6)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InventAuction", x => x.Id);
                    table.ForeignKey(
                        name: "FK_InventAuction_InventUser_InventUserId",
                        column: x => x.InventUserId,
                        principalTable: "InventUser",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_InventAuction_Status_StatusId",
                        column: x => x.StatusId,
                        principalTable: "Status",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "InventAuctionLot",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "binary(16)", nullable: false),
                    AuctionId = table.Column<Guid>(type: "binary(16)", nullable: false),
                    LotId = table.Column<long>(type: "bigint", nullable: false),
                    LotNumber = table.Column<int>(type: "int", nullable: true),
                    Status = table.Column<int>(type: "int", nullable: false),
                    Type = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Description = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Vrm = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Vin = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Manufacturer = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Model = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Variant = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Year = table.Column<int>(type: "int", nullable: true),
                    Mileage = table.Column<int>(type: "int", nullable: true),
                    MileageDenominator = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    MileageWarranted = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    FirstRegistered = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    Colour = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    FuelType = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    EngineSize = table.Column<int>(type: "int", nullable: true),
                    BodyType = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    DoorCount = table.Column<int>(type: "int", nullable: true),
                    Transmission = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    FormerKeeper = table.Column<int>(type: "int", nullable: true),
                    NamaGrade = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CapRetail = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    CapClean = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    CapAverage = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    CapBelow = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    AutotraderRetail = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    AutotraderTrade = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    ClassificationId = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ClassificationTitle = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    VatStatus = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    HasV5 = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    V5OrderStatus = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    NonRunner = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    InspectionReportUrl = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Co2 = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    StandardEuroEmissions = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    NumKeys = table.Column<int>(type: "int", nullable: true),
                    ReservePrice = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    CurrentBid = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    SalePrice = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    SoldToCustomerId = table.Column<int>(type: "int", nullable: true),
                    ImportErrorText = table.Column<string>(type: "varchar(2048)", maxLength: 2048, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    VehicleId = table.Column<Guid>(type: "binary(16)", nullable: true),
                    StatusId = table.Column<uint>(type: "int unsigned", nullable: false),
                    Added = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    Updated = table.Column<DateTime>(type: "datetime(6)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InventAuctionLot", x => x.Id);
                    table.ForeignKey(
                        name: "FK_InventAuctionLot_InventAuction_AuctionId",
                        column: x => x.AuctionId,
                        principalTable: "InventAuction",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_InventAuctionLot_Vehicle_VehicleId",
                        column: x => x.VehicleId,
                        principalTable: "Vehicle",
                        principalColumn: "Id");
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "InventAuctionLotImage",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "binary(16)", nullable: false),
                    AuctionLotId = table.Column<Guid>(type: "binary(16)", nullable: false),
                    ImageUrl = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    IsInterior = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    SortOrder = table.Column<int>(type: "int", nullable: false),
                    StatusId = table.Column<uint>(type: "int unsigned", nullable: false),
                    Added = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    Updated = table.Column<DateTime>(type: "datetime(6)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InventAuctionLotImage", x => x.Id);
                    table.ForeignKey(
                        name: "FK_InventAuctionLotImage_InventAuctionLot_AuctionLotId",
                        column: x => x.AuctionLotId,
                        principalTable: "InventAuctionLot",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_InventAuctionLotImage_Status_StatusId",
                        column: x => x.StatusId,
                        principalTable: "Status",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_InventAuction_InventUserId",
                table: "InventAuction",
                column: "InventUserId");

            migrationBuilder.CreateIndex(
                name: "IX_InventAuction_StatusId",
                table: "InventAuction",
                column: "StatusId");

            migrationBuilder.CreateIndex(
                name: "IX_InventAuctionLot_AuctionId",
                table: "InventAuctionLot",
                column: "AuctionId");

            migrationBuilder.CreateIndex(
                name: "IX_InventAuctionLot_VehicleId",
                table: "InventAuctionLot",
                column: "VehicleId");

            migrationBuilder.CreateIndex(
                name: "IX_InventAuctionLotImage_AuctionLotId",
                table: "InventAuctionLotImage",
                column: "AuctionLotId");

            migrationBuilder.CreateIndex(
                name: "IX_InventAuctionLotImage_StatusId",
                table: "InventAuctionLotImage",
                column: "StatusId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "InventAuctionLotImage");

            migrationBuilder.DropTable(
                name: "InventAuctionLot");

            migrationBuilder.DropTable(
                name: "InventAuction");

            migrationBuilder.CreateTable(
                name: "AucaAuction",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "binary(16)", nullable: false),
                    InventUserId = table.Column<Guid>(type: "binary(16)", nullable: true),
                    StatusId = table.Column<uint>(type: "int unsigned", nullable: false),
                    ActivityStatus = table.Column<int>(type: "int", nullable: false),
                    Added = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    AllowStandOn = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    AuctionId = table.Column<long>(type: "bigint", nullable: false),
                    AuctionLocationId = table.Column<int>(type: "int", nullable: false),
                    AuctionLocationTitle = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    AuctionTypeTitle = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CurrentLotId = table.Column<int>(type: "int", nullable: true),
                    DateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    EndDateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    Information = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Title = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Updated = table.Column<DateTime>(type: "datetime(6)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AucaAuction", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AucaAuction_InventUser_InventUserId",
                        column: x => x.InventUserId,
                        principalTable: "InventUser",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AucaAuction_Status_StatusId",
                        column: x => x.StatusId,
                        principalTable: "Status",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "AucaAuctionLot",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "binary(16)", nullable: false),
                    AuctionId = table.Column<Guid>(type: "binary(16)", nullable: false),
                    VehicleId = table.Column<Guid>(type: "binary(16)", nullable: true),
                    Added = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    AutotraderRetail = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    AutotraderTrade = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    BodyType = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CapAverage = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    CapBelow = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    CapClean = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    CapRetail = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    ClassificationId = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ClassificationTitle = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Co2 = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Colour = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CurrentBid = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    Description = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    DoorCount = table.Column<int>(type: "int", nullable: true),
                    EngineSize = table.Column<int>(type: "int", nullable: true),
                    FirstRegistered = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    FormerKeeper = table.Column<int>(type: "int", nullable: true),
                    FuelType = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    HasV5 = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    ImportErrorText = table.Column<string>(type: "varchar(2048)", maxLength: 2048, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    InspectionReportUrl = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    LotId = table.Column<long>(type: "bigint", nullable: false),
                    LotNumber = table.Column<int>(type: "int", nullable: true),
                    Manufacturer = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Mileage = table.Column<int>(type: "int", nullable: true),
                    MileageDenominator = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    MileageWarranted = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    Model = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    NamaGrade = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    NonRunner = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    NumKeys = table.Column<int>(type: "int", nullable: true),
                    ReservePrice = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    SalePrice = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    SoldToCustomerId = table.Column<int>(type: "int", nullable: true),
                    StandardEuroEmissions = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Status = table.Column<int>(type: "int", nullable: false),
                    StatusId = table.Column<uint>(type: "int unsigned", nullable: false),
                    Transmission = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Type = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Updated = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    V5OrderStatus = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Variant = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    VatStatus = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Vin = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Vrm = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Year = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AucaAuctionLot", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AucaAuctionLot_AucaAuction_AuctionId",
                        column: x => x.AuctionId,
                        principalTable: "AucaAuction",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AucaAuctionLot_Vehicle_VehicleId",
                        column: x => x.VehicleId,
                        principalTable: "Vehicle",
                        principalColumn: "Id");
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "AucaAuctionLotImage",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "binary(16)", nullable: false),
                    AuctionLotId = table.Column<Guid>(type: "binary(16)", nullable: false),
                    StatusId = table.Column<uint>(type: "int unsigned", nullable: false),
                    Added = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    ImageUrl = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    IsInterior = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    SortOrder = table.Column<int>(type: "int", nullable: false),
                    Updated = table.Column<DateTime>(type: "datetime(6)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AucaAuctionLotImage", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AucaAuctionLotImage_AucaAuctionLot_AuctionLotId",
                        column: x => x.AuctionLotId,
                        principalTable: "AucaAuctionLot",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AucaAuctionLotImage_Status_StatusId",
                        column: x => x.StatusId,
                        principalTable: "Status",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_AucaAuction_InventUserId",
                table: "AucaAuction",
                column: "InventUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AucaAuction_StatusId",
                table: "AucaAuction",
                column: "StatusId");

            migrationBuilder.CreateIndex(
                name: "IX_AucaAuctionLot_AuctionId",
                table: "AucaAuctionLot",
                column: "AuctionId");

            migrationBuilder.CreateIndex(
                name: "IX_AucaAuctionLot_VehicleId",
                table: "AucaAuctionLot",
                column: "VehicleId");

            migrationBuilder.CreateIndex(
                name: "IX_AucaAuctionLotImage_AuctionLotId",
                table: "AucaAuctionLotImage",
                column: "AuctionLotId");

            migrationBuilder.CreateIndex(
                name: "IX_AucaAuctionLotImage_StatusId",
                table: "AucaAuctionLotImage",
                column: "StatusId");
        }
    }
}
