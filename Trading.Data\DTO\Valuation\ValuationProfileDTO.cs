﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.Valuation
{
  public class ValuationProfileDTO : BaseModelEntityDTO
  {
    public string Name { get; set; }
    //public IEnumerable<ValuationPillarDTO> ValuationPillars { get; set; }
    public IEnumerable<ValuationNodeDTO> ValuationNodes { get; set; }
    public bool IsDefault { get; set; }
  }
}
