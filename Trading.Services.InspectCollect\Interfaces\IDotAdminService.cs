using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO.DotAdmin;

namespace Trading.Services.InspectCollect.Interfaces;

/// <summary>
/// Interface for dotAdmin business logic operations
/// </summary>
public interface IDotAdminService
{
  /// <summary>
  /// Create a dotAdmin vehicle from an existing ICVehicle record
  /// </summary>
  Task<DotAdminVehicle> CreateVehicleFromICVehicleAsync(
    Guid icResponseId,
    Guid icVehicleId,
    int locationId,
    int customerId,
    CancellationToken cancellationToken = default);

  /// <summary>
  /// Create a simple dotAdmin vehicle with basic details
  /// </summary>
  Task<DotAdminVehicle> CreateVehicleAsync(
    string registration,
    string? vin,
    int customerId,
    int locationId,
    bool useLookup = true,
    CancellationToken cancellationToken = default);

  /// <summary>
  /// Create a dotAdmin vehicle with detailed specifications
  /// </summary>
  Task<DotAdminVehicle> CreateVehicleWithDetailsAsync(
    DotAdminCreateVehicleRequest request,
    CancellationToken cancellationToken = default);

  /// <summary>
  /// Get available customers from dotAdmin
  /// </summary>
  Task<List<DotAdminCustomer>> GetAvailableCustomersAsync(CancellationToken cancellationToken = default);

  /// <summary>
  /// Get available locations from dotAdmin
  /// </summary>
  Task<Dictionary<string, string>> GetAvailableLocationsAsync(CancellationToken cancellationToken = default);

  /// <summary>
  /// Select a customer and location for operations
  /// </summary>
  Task<bool> SelectCustomerLocationAsync(
    int customerId,
    int locationId,
    CancellationToken cancellationToken = default);

  /// <summary>
  /// Map a vehicle type name to dotAdmin vehicle type enum
  /// </summary>
  DotAdminVehicleType GetVehicleType(string vehicleTypeName);

  /// <summary>
  /// Map body type and vehicle type to dotAdmin classification enum
  /// </summary>
  DotAdminVehicleClassification GetVehicleClassification(string bodyType, string vehicleType = null);

  /// <summary>
  /// Validate that a customer and location combination is valid
  /// </summary>
  Task<bool> ValidateCustomerLocationAsync(int customerId, int locationId, CancellationToken cancellationToken = default);
}
