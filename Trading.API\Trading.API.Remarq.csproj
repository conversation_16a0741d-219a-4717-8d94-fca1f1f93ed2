﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <!-- Set a default Configuration if one hasn't been passed in -->
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <PlatformTarget>x64</PlatformTarget>
    <OutputType>Exe</OutputType>
    <Configurations>RemarqDevelopment;RemarqProd;RemarqLocal-DevDB;RemarqLocal-ProdDB;ICLocal-Dev;ICLocal-Prod;IC-Dev;IC-Prod</Configurations>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <DocumentationFile></DocumentationFile>
  </PropertyGroup>

  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">RemarqLocal-DevDB</Configuration>
    <EnvironmentName>remarqlocal-devDB</EnvironmentName>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RemarqDevelopment|AnyCPU'">
    <Optimize>True</Optimize>
    <EnvironmentName>remarqdev</EnvironmentName>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RemarqProd|AnyCPU'">
    <Optimize>True</Optimize>
    <EnvironmentName>remarqprod</EnvironmentName>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RemarqLocal-ProdDB|AnyCPU'">
    <Optimize>False</Optimize>
    <EnvironmentName>remarqlocal-prodDB</EnvironmentName>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RemarqLocal-DevDB|AnyCPU'">
    <Optimize>False</Optimize>
    <EnvironmentName>remarqlocal-devDB</EnvironmentName>
  </PropertyGroup>


  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='IC-Prod|AnyCPU'">
    <Optimize>True</Optimize>
    <EnvironmentName>ICprod</EnvironmentName>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='IC-Dev|AnyCPU'">
    <Optimize>True</Optimize>
    <EnvironmentName>ICdev</EnvironmentName>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ICLocal-Dev|AnyCPU'">
    <Optimize>False</Optimize>
    <EnvironmentName>IClocal-dev</EnvironmentName>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ICLocal-Prod|AnyCPU'">
    <Optimize>False</Optimize>
    <EnvironmentName>IClocal-prod</EnvironmentName>
  </PropertyGroup>



  <ItemGroup>
    <PackageReference Include="Amazon.AspNetCore.Identity.Cognito" Version="3.0.2" />
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="DotLiquid" Version="2.2.692" />
    <PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="11.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Certificate" Version="8.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Google" Version="8.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.5" NoWarn="NU1605" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.5" NoWarn="NU1605" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.5">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.5">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.PlatformAbstractions" Version="1.1.0" />
    <PackageReference Include="NetCore.AutoRegisterDi" Version="2.2.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.2" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.5.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Trading.API.Common\Trading.API.Common.csproj" />
    <ProjectReference Include="..\Trading.API.External\Trading.API.External.csproj" />
    <ProjectReference Include="..\Trading.API.InspectCollect\Trading.API.InspectCollect.csproj" />
    <ProjectReference Include="..\Trading.API.LeadCRM\Trading.API.LeadCRM.csproj" />
    <ProjectReference Include="..\Trading.API.SiteScan\Trading.API.SiteScan.csproj" />
    <ProjectReference Include="..\Trading.API.UInspection\Trading.API.UInspection.csproj" />
    <ProjectReference Include="..\Trading.API.Valuations\Trading.API.Valuations.csproj" />
    <ProjectReference Include="..\Trading.Data\Trading.API.Data.csproj" />
    <ProjectReference Include="..\Trading.Services.External\Trading.Services.External.csproj" />
    <ProjectReference Include="..\Trading.Services.InspectCollect\Trading.Services.InspectCollect.csproj" />
    <ProjectReference Include="..\Trading.Services.LeadCRM\Trading.Services.LeadCRM.csproj" />
    <ProjectReference Include="..\Trading.Services.SiteScan\Trading.Services.SiteScan.csproj" />
    <ProjectReference Include="..\Trading.Services.UInspection\Trading.Services.UInspection.csproj" />
    <ProjectReference Include="..\Trading.Services.Valuations\Trading.Services.Valuations.csproj" />
    <ProjectReference Include="..\Trading.Services\Trading.Services.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Content\Email Templates\BasicLayout.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Content\Email Templates\BasicLayout.txt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Content Remove="appsettings.*.json" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="appsettings.$(EnvironmentName).json" CopyToOutputDirectory="PreserveNewest" />
  </ItemGroup>

  <Target Name="RenameAppsettingDev" AfterTargets="Build">
    <Copy SourceFiles="$(OutDir)\appsettings.$(EnvironmentName).json" DestinationFiles="$(OutDir)\appsettings.json" />
  </Target>

  <Target Name="RenameAppsettingsProd" AfterTargets="Publish">
    <Copy SourceFiles="$(PublishDir)\appsettings.$(EnvironmentName).json" DestinationFiles="$(PublishDir)\appsettings.json" Condition="'$(Optimize)' == 'true' " />
  </Target>

  <PropertyGroup Condition="'$(Optimize)' == 'true'">
    <OutputPath>bin\Release</OutputPath>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Optimize)' == 'false'">
    <OutputPath>bin\Debug</OutputPath>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='IC-Dev|AnyCPU'">
    <Optimize>True</Optimize>
  </PropertyGroup>

  <ProjectExtensions>
    <VisualStudio>
      <UserProperties />
    </VisualStudio>
  </ProjectExtensions>

</Project>
