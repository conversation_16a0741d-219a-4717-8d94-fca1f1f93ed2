﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using Trading.Services.Helpers;
using Trading.API.Data.DTO;
using Trading.API.Data.Enums;

namespace Trading.API.Helpers
{
  public static class ErrorResponseHelper
  {
    public static IActionResult ParseError(this Exception ex)
    {
      // convert the message portion of the exception into an errorDTO
      var error = ex.Message;
      var errorDTO = JsonConvert.DeserializeObject<ErrorDTO>(error);

      switch (errorDTO.HTTPStatus)
      {
        case HTTPStatusEnum.Conflict:
          return new ConflictObjectResult(error);
        case HTTPStatusEnum.Forbidden:
          return new ForbidResult(JsonConvert.SerializeObject(error));
        default:
          return new BadRequestObjectResult(error);
      }
    }
  }
}
