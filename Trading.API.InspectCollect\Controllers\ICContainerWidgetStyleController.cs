using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/container-widget-style")]
  [ApiController]
  [AllowAnonymous]
  public class ICContainerWidgetStyleController : ControllerBase
  {
    private readonly ICContainerWidgetStyleInterface _icContainerWidgetStyleService;

    public ICContainerWidgetStyleController(ICContainerWidgetStyleInterface serviceInterface)
    {
      _icContainerWidgetStyleService = serviceInterface;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icContainerWidgetStyleService.Get(id, null, cancellationToken);
      return Ok(res);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icContainerWidgetStyleService.Delete(id);
      return Ok(res);
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Post([FromBody] ICContainerWidgetStyleCreateDTO dto, CancellationToken cancellationToken)
    {
      var res = await _icContainerWidgetStyleService.Create(dto);
      return Ok(res);
    }

    [HttpGet]
    [Route("/api/inspect-collect/container-widget-styles")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICContainerWidgetStyleSearchDTO>(query);
      var res = await _icContainerWidgetStyleService.Search(dto, cancellationToken);
      return Ok(res);
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<ICContainerWidgetStyle> dto)
    {
      var response = await _icContainerWidgetStyleService.Patch(id, dto);
      return Ok(response);
    }
  }
}