﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Models;
using Trading.API.Data.Models.DTO;

namespace Trading.API.Data.DTO
{
  public class ScanCache
  {
    public string RawResponse { get; set; }
    public IDictionary<uint?, Dictionary<uint?, dynamic>> Raw { get; set; } = new Dictionary<uint?, Dictionary<uint?, dynamic>>();
    public IDictionary<uint?, Dictionary<uint?, dynamic>> Eval { get; set; } = new Dictionary<uint?, Dictionary<uint?, dynamic>>();
    public IDictionary<uint?, Dictionary<uint?, bool>> Error { get; set; } = new Dictionary<uint?, Dictionary<uint?, bool>>();
    public IDictionary<uint?, Dictionary<uint?, string>> ErrorMessage { get; set; } = new Dictionary<uint?, Dictionary<uint?, string>>();
    public IDictionary<uint?, Dictionary<uint?, string>> AttribPair { get; set; } = new Dictionary<uint?, Dictionary<uint?, string>>();
  }
}