﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICInputValidationDTO: BaseModelEntityDTO
  {
    public string Name { get; set; }
    public Guid ICInputId { get; set; }
    public string ValidationType { get; set; }
    public string ValidationRegExp { get; set; }
  }

  public class ICInputValidationSearchDTO : BaseSearchDTO
  {
    public ICInputValidationSearchFilters Filters { get; set; } = new ICInputValidationSearchFilters();
  }

  public class ICInputValidationSearchFilters : BaseFilter
  {
    public string Name { get; set; }
  }

  public class ICInputValidationCreateDTO
  {
    public string Name { get; set; }
    public Guid ICInputId { get; set; } 
    public string ValidationType { get; set; } 
    public string ValidationRegExp { get; set; } 
  }
}
