﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;
using Trading.API.Data.Models.UInspections;

namespace Trading.API.Data.DTO.UInspections
{
  public class UInspectFormatDTO : BaseModelEntityIntDTO
  {
    public VehicleTypeEnum VehicleTypeId { get; set; }
    public string Title { get; set; }
    public string Description { get; set; }
    public bool Disabled { get; set; }
    public bool Default { get; set; }
    public bool IsRemarq { get; set; }
    public IEnumerable<UInspectSectionDTO> Sections { get; set; }
    public IEnumerable<UInspectDTO> UInspects { get; set; }
    public uint StartSectionId { get; set; }
    public Guid CustomerId { get; set; }
    public CustomerDTO Customer { get; set; }
  }
  public class UInspectFormatWithAnswerDTO : UInspectFormatDTO
  {
    public new IEnumerable<UInspectSectionWithAnswerDTO> Sections { get; set; }
  }
}
