namespace Trading.API.Controllers
{
  using Microsoft.AspNetCore.Mvc;
  using System.Threading;
  using System.Threading.Tasks;
  using System;
  using Newtonsoft.Json;
  using Trading.API.Data.DTO.Comms;
  using Trading.Services;
  using Trading.Services.Interfaces;

  [Route("/api/commsTemplate")]
  public class CommsTemplateController : ControllerBase
  {
    private readonly ICommsTemplateService _commsTemplateService;

    public CommsTemplateController(ICommsTemplateService commsTemplateService)
    {
      _commsTemplateService = commsTemplateService;
    }

    [HttpGet]
    [Route("")]
    public async Task<IActionResult> GetTemplates([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = String.IsNullOrEmpty(query) ? null : JsonConvert.DeserializeObject<CommsTemplateSearchDTO>(query);
        var pageres = await _commsTemplateService.Search(dto, cancellationToken);
        return Ok(pageres);
      }
      catch (Exception e)
      {
        return BadRequest(e);
      }
    }

    [HttpGet]
    [Route("getExcludedCustomers/{templateId}")]
    public async Task<IActionResult> GetExcludedCustomers(uint templateId, CancellationToken cancellationToken)
    {
      try
      {
        var pageres = await _commsTemplateService.GetExcludedCustomers(templateId, cancellationToken);
        return Ok(pageres);
      }
      catch (Exception e)
      {
        return BadRequest(e);
      }
    }

    [HttpGet]
    [Route("getTemplateWrappers")]
    public async Task<IActionResult> GetTemplateWrappers(CancellationToken cancellationToken)
    {
      try
      {
        var pageres = await _commsTemplateService.GetTemplateWrappers(cancellationToken);
        return Ok(pageres);
      }
      catch (Exception e)
      {
        return BadRequest(e);
      }
    }

    [HttpPost]
    [Route("addExcludedCustomer")]
    public async Task<IActionResult> AddExcludedCustomer([FromBody] AddExcludedCustomerDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        var pageres = await _commsTemplateService.AddExcludedCustomer((uint)dto.CommsTemplateId, (Guid)dto.CustomerId, cancellationToken);
        return Ok(pageres);
      }
      catch (Exception e)
      {
        return BadRequest(e);
      }
    }

    [HttpDelete]
    [Route("removeExcludedCustomer/{commsTemplateId}/{customerId}")]
    public async Task<IActionResult> RemoveExcludedCustomer(uint commsTemplateId, Guid customerId, CancellationToken cancellationToken)
    {
      try
      {
        var pageres = await _commsTemplateService.RemoveExcludedCustomer(commsTemplateId, customerId, cancellationToken);
        return Ok(pageres);
      }
      catch (Exception e)
      {
        return BadRequest(e);
      }
    }

    [HttpPost]
    [Route("preview")]
    public async Task<IActionResult> Preview([FromBody] CommsTemplatePreviewRequestDTO requestDTO, CancellationToken cancellationToken)
    {
      try
      {
        var pageres = await _commsTemplateService.PreviewTemplate(requestDTO, cancellationToken);

        return Ok(pageres);
      }
      catch (Exception e)
      {
        return BadRequest(e);
      }
    }

    [HttpPost]
    [Route("previewModel")]
    public async Task<IActionResult> PreviewModel([FromBody] CommsTemplatePreviewRequestDTO requestDTO, CancellationToken cancellationToken)
    {
      var pageres = await _commsTemplateService.PreviewModel(requestDTO, cancellationToken);

      try
      {
        return Ok(pageres);
      }
      catch (Exception e)
      {
        Console.WriteLine("Exception thrown Serializing object");
        return BadRequest(e);
      }
    }

    [HttpPost]
    [Route("sendTest")]
    public async Task<IActionResult> SentTest([FromBody] CommsTemplatePreviewRequestDTO previewRequest, CancellationToken cancellationToken)
    {
      try
      {
        var pageres = await _commsTemplateService.SendTest(previewRequest, cancellationToken);
        return Ok(pageres);
      }
      catch (Exception e)
      {
        return BadRequest(e);
      }
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<IActionResult> Delete(uint id, CancellationToken cancellationToken)
    {
      try
      {
        var pageres = await _commsTemplateService.Delete(id, cancellationToken);

        return Ok(pageres);
      }
      catch (Exception e)
      {
        return BadRequest(e);
      }
    }

    [HttpPut]
    [Route("{id}")]
    public async Task<IActionResult> Update(uint id, [FromBody] CommsTemplateDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        var pageres = await _commsTemplateService.Update(id, dto, cancellationToken);
        return Ok(pageres);
      }
      catch (Exception e)
      {
        return BadRequest(e);
      }
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create([FromBody] CommsTemplateDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        var pageres = await _commsTemplateService.Create(dto, cancellationToken);
        return Ok(pageres);
      }
      catch (Exception e)
      {
        return BadRequest(e);
      }
    }
  }
}

