﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Trading.API.Data.Migrations
{
    /// <inheritdoc />
    public partial class Validations : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CustomValidation",
                table: "ICContainerWidgetInput");

            migrationBuilder.CreateTable(
                name: "ICValidation",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "binary(16)", nullable: false),
                    Name = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ICContainerGroupId = table.Column<Guid>(type: "binary(16)", nullable: false),
                    ValidationCode = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    StatusId = table.Column<uint>(type: "int unsigned", nullable: false),
                    Added = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    Updated = table.Column<DateTime>(type: "datetime(6)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ICValidation", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ICValidation_ICContainerGroup_ICContainerGroupId",
                        column: x => x.ICContainerGroupId,
                        principalTable: "ICContainerGroup",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ICValidation_Status_StatusId",
                        column: x => x.StatusId,
                        principalTable: "Status",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "ICContainerWidgetInputValidation",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "binary(16)", nullable: false),
                    ICContainerWidgetInputId = table.Column<Guid>(type: "binary(16)", nullable: false),
                    Position = table.Column<uint>(type: "int unsigned", nullable: true),
                    ICValidationId = table.Column<Guid>(type: "binary(16)", nullable: true),
                    StatusId = table.Column<uint>(type: "int unsigned", nullable: false),
                    Added = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    Updated = table.Column<DateTime>(type: "datetime(6)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ICContainerWidgetInputValidation", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ICContainerWidgetInputValidation_ICContainerWidgetInput_ICCo~",
                        column: x => x.ICContainerWidgetInputId,
                        principalTable: "ICContainerWidgetInput",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ICContainerWidgetInputValidation_ICValidation_ICValidationId",
                        column: x => x.ICValidationId,
                        principalTable: "ICValidation",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_ICContainerWidgetInputValidation_Status_StatusId",
                        column: x => x.StatusId,
                        principalTable: "Status",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_ICContainerWidgetInputValidation_ICContainerWidgetInputId",
                table: "ICContainerWidgetInputValidation",
                column: "ICContainerWidgetInputId");

            migrationBuilder.CreateIndex(
                name: "IX_ICContainerWidgetInputValidation_ICValidationId",
                table: "ICContainerWidgetInputValidation",
                column: "ICValidationId");

            migrationBuilder.CreateIndex(
                name: "IX_ICContainerWidgetInputValidation_StatusId",
                table: "ICContainerWidgetInputValidation",
                column: "StatusId");

            migrationBuilder.CreateIndex(
                name: "IX_ICValidation_ICContainerGroupId",
                table: "ICValidation",
                column: "ICContainerGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_ICValidation_StatusId",
                table: "ICValidation",
                column: "StatusId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ICContainerWidgetInputValidation");

            migrationBuilder.DropTable(
                name: "ICValidation");

            migrationBuilder.AddColumn<string>(
                name: "CustomValidation",
                table: "ICContainerWidgetInput",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }
    }
}
