using System;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.API.Helpers;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/platform")]
  [ApiController]
  public class PlatformController : ControllerBase
  {
    public IPlatformService _platformService;
    public IMapper _mapper;

    public PlatformController(
      IPlatformService platformService,
      IMapper mapper)
    {
      _platformService = platformService;
      _mapper = mapper;
    }

    [HttpGet]
    [ResponseCache(Duration = 300)]
    [Route("/api/platforms")]
    public async Task<IActionResult> Search([FromQuery] int? unique, [FromQuery] string? search, CancellationToken cancellationToken)
    {
      var searchDTO = new PlatformSearchDTO() { };

      if (!String.IsNullOrEmpty(search))
      {
        searchDTO = JsonSerializer.Deserialize<PlatformSearchDTO>(search);
      }

      var response = await _platformService.Search(searchDTO, cancellationToken);

      return Ok(response);
    }

    [HttpGet]
    [Route("/api/customer/{customerId}/platform")]
    public async Task<IActionResult> GetPlatform(Guid customerId, CancellationToken cancellationToken)
    {
      try
      {
        // todo: check the user is a contact of the specified customerId or an ADMIN or higher level user

        var platform = await _platformService.GetCustomerPlatform(customerId, cancellationToken);
        return Ok(platform);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{platformId}")]
    public async Task<IActionResult> GetPlatform(uint platformId, [FromQuery] string search, CancellationToken cancellationToken)
    {
      var searchDTO = new PlatformSearchDTO() { };

      if (!String.IsNullOrEmpty(search))
      {
        searchDTO = JsonSerializer.Deserialize<PlatformSearchDTO>(search);
      }

      var response = await _platformService.Get(platformId, searchDTO, cancellationToken);

      if (response != null)
      {
        return Ok(response);
      }

      return NotFound();
    }

    [HttpDelete]
    [Route("{platformId}")]
    public async Task<IActionResult> Delete(uint platformId, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        var response = await _platformService.Delete(platformId, cancellationToken);

        return Ok(response);
      }

      return Forbid();
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create(PlatformDTO dto, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        var response = await _platformService.Create(dto, cancellationToken);

        return Ok(response);
      }

      return Forbid();
    }

    [HttpPatch]
    [Route("{platformId}")]
    public async Task<IActionResult> Patch(uint platformId, [FromBody] JsonPatchDocument<Platform> patch, CancellationToken cancellationToken)
    {
      // Only edit addresses that are ours (or if we're admin)
      if (User.IsAdmin())
      {
        try
        {
          return Ok(await _platformService.Patch(platformId, patch, cancellationToken));
        }
        catch (Exception ex)
        {
          return ex.ParseError();
        }
      }

      return Forbid();
    }
  }
}
