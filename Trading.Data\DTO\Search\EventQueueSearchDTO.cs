﻿using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO
{
  public class EventQueueSearchDTO: BaseSearchDTO
  {
    public EventQueueFilters Filters { get; set; } = new EventQueueFilters() { };
  }

  public class EventQueueFilters : BaseFilterInt
  {
    public string ExternalId { get; set; }
    public string EventType { get; set; }
    public EventSourceEnum? EventSource { get; set; }
  }
}
