﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Tests.Common.Helpers
{
    internal class DBStrings
    {
        public const string db = "trading_test";
        public const string server = "localhost";
        public const string uid = "root";
        public const string pwd = "barney";

        public const string serverConn = $"GuidFormat=TimeSwapBinary16;Server={server};Uid={uid};Pwd=*****;";
        public const string dbConn = $"GuidFormat=TimeSwapBinary16;Server={server};Database={db};Uid={uid};Pwd=*****;";
    }
}
