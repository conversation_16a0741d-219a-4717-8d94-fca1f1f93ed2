﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/sale-profile")]
  [ApiController]
  [Authorize]
  public class SaleProfileController : ControllerBase
  {
    private readonly ISaleProfileService _saleProfileService;

    public SaleProfileController(ISaleProfileService saleProfileService)
    {
      this._saleProfileService = saleProfileService;
    }

    [HttpGet]
    [Route("")]
    public async Task<IActionResult> GetSaleProfiles(CancellationToken cancellationToken)
    {
      if (!User.IsAdmin() && !User.IsAuctionAdmin())
      {
        return Forbid();
      }

      try
      {
        var results = await _saleProfileService.GetSaleProfiles(cancellationToken);
        return Ok(results);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> CreateSaleProfile([FromBody] SaleProfileDTO dto, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin() && !User.IsAuctionAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _saleProfileService.CreateSaleProfile(dto, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpDelete]
    [Route("{saleProfileId}")]
    public async Task<IActionResult> DeleteSaleProfile(Guid saleProfileId, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin() && !User.IsAuctionAdmin())
      {
        return Forbid();
      }

      try
      {
        await _saleProfileService.DeleteSaleProfile(saleProfileId, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("add-search-profile")]
    public async Task<IActionResult> AddSearchProfile([FromBody] SaleSearchProfileDTO dto, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin() && !User.IsAuctionAdmin())
      {
        return Forbid();
      }

      try
      {
        await _saleProfileService.AddSaleSearchProfile(dto, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpDelete]
    [Route("delete-sale-search-profile/{saleSearchProfileId}")]
    public async Task<IActionResult> DeleteSaleSearchProfile(Guid saleSearchProfileId, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin() && !User.IsAuctionAdmin())
      {
        return Forbid();
      }

      try
      {
        await _saleProfileService.DeleteSaleSearchProfile(saleSearchProfileId, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpPost]
    [Route("create-sale-from-profile")]
    public async Task<IActionResult> CreateSaleFromProfile([FromBody] CreateSaleFromProfileDTO dto, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin() && !User.IsAuctionAdmin())
      {
        return Forbid();
      }

      try
      {
        var sale = await _saleProfileService.CreateSaleFromProfile(dto, cancellationToken);
        return Ok(sale);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

  }
}
