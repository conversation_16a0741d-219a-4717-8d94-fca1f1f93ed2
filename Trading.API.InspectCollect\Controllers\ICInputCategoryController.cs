using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/input-category")]
  [ApiController]
  [AllowAnonymous]
  public class ICInputCategoryController : ControllerBase
  {
    private readonly ICInputCategoryInterface _icInputCategoryService;

    public ICInputCategoryController(ICInputCategoryInterface serviceInterface)
    {
      _icInputCategoryService = serviceInterface;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icInputCategoryService.Get(id, null, cancellationToken);
      return Ok(res);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icInputCategoryService.Delete(id);
      return Ok(res);
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Post([FromBody]ICInputCategoryCreateDTO dto, CancellationToken cancellationToken)
    {
      var res = await _icInputCategoryService.Create(dto);
      return Ok(res);
    }

    [HttpGet]
    [Route("search")]
    [Route("/api/inspect-collect/input-categories")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICInputCategorySearchDTO>(query);
      var res = await _icInputCategoryService.Search(dto, cancellationToken);
      return Ok(res);
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<ICInputCategory> dto)
    {
      var response = await _icInputCategoryService.Patch(id, dto);
      return Ok(response);
    }
  }
}