﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.DTO.Movements;
using Trading.API.Data.DTO.UInspections;
using Trading.API.Data.DTO.Valuation;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;

namespace Trading.API.Data.DTO.LeadCRM
{
  public class LeadVehicleDTO : LeadBaseModelDTO
  {
    public string Vrm { get; set; }
    public FinanceStatusEnum FinanceStatus { get; set; }
    public string FinanceHouse { get; set; }
    public string FinanceReference { get; set; }
    public uint SettlementAmount { get; set; }
    public string SettlementEmail { get; set; }
    public string SettlementPhone { get; set; }
    public string SettlementPaymentReference { get; set; }
    public string CustomerPaymentReference { get; set; }
    public uint? CustomerWants { get; set; }
    public bool PrivateHireClear { get; set; }
    public bool DrivingSchoolClear { get; set; }
    public uint TransportCosts { get; set; }
    public DateTime PreferredCollectionDate { get; set; }

    public LeadDTO Lead { get; set; }
    public Guid LeadId { get; set; }

    public string ExternalAppraisalCode { get; set; }  // external valuation processId (i.e. Vehicle Vision appraisal Id)

    public Guid? CurrentValuationQuoteId { get; set; }

    public virtual ValuationQuoteDTO CurrentValuationQuote { get; set; }

    public VehicleDTO Vehicle { get; set; }
    public Guid? VehicleId { get; set; }

    public uint Odometer { get; set; }

    public Guid? VehicleLookupInfoId { get; set; }
    public VehicleLookupInfoDTO VehicleLookupInfo { get; set; }

    public uint? MakeId { get; set; }
    public uint? ModelId { get; set; }
    public uint? DerivId { get; set; }
    public uint? FuelTypeId { get; set; }
    public uint? BodyTypeId { get; set; }
    public uint? TransmissionTypeId { get; set; }
    public uint? PlateId { get; set; }
    public uint? VatStatusId { get; set; }
    public uint? V5StatusId { get; set; }
    public uint? VehicleColourId { get; set; }
    public uint? VehicleTypeId { get; set; }
    public ServiceHistoryTypeEnum ServiceHistoryType { get; set; }
    public uint? NoOfKeys { get; set; }
    public uint? EngineCC { get; set; }
    public uint? Owners { get; set; }
    public bool? Runner { get; set; }
    public uint? Grade { get; set; }
    public DateTime? LastService { get; set; }

    public string VIN { get; set; }

    public virtual Make Make { get; set; }
    public virtual Model Model { get; set; }
    public virtual Deriv Deriv { get; set; }
    public virtual Plate Plate { get; set; }
    public virtual BodyType BodyType { get; set; }
    public virtual FuelType FuelType { get; set; }
    public virtual TransmissionType TransmissionType { get; set; }
    public virtual VehicleType VehicleType { get; set; }
    public virtual VehicleColour VehicleColour { get; set; }
    public virtual UInspectDTO ExternalAppraisal { get; set; }
    
    public Guid? ExternalAppraisalId { get; set; }  

    public Guid? ContactId { get; set; }
    public ContactDTO Contact { get; set; }
    public ICollection<MovementDTO> Movements { get; set; }

    public string Weight { get; set; }
  }
}
