﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

#nullable disable

namespace Trading.API.Data.DTO.Movements
{
  public partial class MovementAddressDTO : BaseModelEntityIntDTO
  {
    public bool Temporary { get; set; }

    public Guid? CustomerId { get; set; }
    public CustomerDTO Customer { get; set; }
    public string Postcode { get; set; }
    public string Address1 { get; set; }
    public string Address2 { get; set; }
    public string Town { get; set; }
    public string County { get; set; }
    public string ContactName { get; set; }
    public string LocationName { get; set; }
    public string Phone1 { get; set; }
    public string Phone2 { get; set; }
    public string Email { get; set; }
    public string WTW { get; set; }
    public decimal Lat { get; set; }
    public decimal Lon { get; set; }
  }
}
