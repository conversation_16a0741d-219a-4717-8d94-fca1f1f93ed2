﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.InspectCollect.VehicleLookup;

public class ICProvenanceDataDTO
{
  public bool? Scrapped { get; set; }
  public bool? Security { get; set; }
  public bool? Stolen { get; set; }
  public bool? Finance { get; set; }
  public bool? VCAR_Theft { get; set; }
  public bool? VCAR_Damage { get; set; }
}
