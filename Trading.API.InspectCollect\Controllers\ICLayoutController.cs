using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.Extensions;
using Trading.Services.InspectCollect.Classes;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/layout")]
  [ApiController]
  [Authorize(Policy = "RequireInspectCollect")]
  [AllowAnonymous]
  public class ICLayoutController : ControllerBase
  {
    private readonly ICLayoutInterface _icLayoutService;

    public ICLayoutController(ICLayoutInterface icLayoutService)
    {
      _icLayoutService = icLayoutService;
    }

    [HttpGet]
    [Route("search")]
    [Route("/api/inspect-collect/layouts")]
    public async Task<ActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {

      var searchDTO = JsonConvert.DeserializeObject<ICLayoutSearchDTO>(query) ?? new ICLayoutSearchDTO();

      var icContainerGroupId = User.ICContainerGroupId();

      if (icContainerGroupId != searchDTO.Filters.ICContainerGroupId && ! User.IsAdmin())
      {
        return Forbid();
      }

      var ok = await _icLayoutService.Search(searchDTO, cancellationToken);

      return Ok(ok);
    }

    [HttpPut]
    [Route("/api/inspect-collect/container-group/{icContainerGroupId}/default-layout/{icLayoutId}")]
    public async Task<IActionResult> SetDefaultLayout(Guid icContainerGroupId, Guid icLayoutId)
    {
      var response = await _icLayoutService.SetContainerGroupDefaultLayout(icContainerGroupId, icLayoutId);
      return Ok(response);
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Create([FromBody] ICLayoutCreateDTO dto)
    {
      if (dto.ICContainerGroupId != User.ICContainerGroupId() && !User.IsAdmin())
      {
        return Forbid();
      }

      var ok = await _icLayoutService.Create(dto);

      return Ok(ok);
    }

    [HttpPatch]
    [Route("{layoutId}")]
    public async Task<ActionResult> Patch(Guid layoutId, JsonPatchDocument<ICLayout> patch)
    {
      var ok = await _icLayoutService.Patch(layoutId, patch);

      return Ok(ok);
    }

    [HttpDelete]
    [Route("{layoutId}")]
    public async Task<ActionResult> Delete(Guid layoutId)
    {
      var ok = await _icLayoutService.Delete(layoutId);

      return Ok(ok);
    }

    [HttpGet]
    [Route("{layoutId}")]
    public async Task<ActionResult> Get(Guid layoutId, [FromQuery] string query, CancellationToken ct)
    {
      ICLayoutSearchDTO searchDTO = new ICLayoutSearchDTO();

      if (!String.IsNullOrEmpty(query))
      {
        searchDTO = JsonConvert.DeserializeObject<ICLayoutSearchDTO>(query);
      }

      var ok = await _icLayoutService.Get(layoutId, searchDTO, ct);

      return Ok(ok);
    }
  }
}