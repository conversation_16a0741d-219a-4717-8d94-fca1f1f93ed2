using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.API.Helpers;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/product")]
  [ApiController]
  public class ProductController : ControllerBase
  {
    public IProductService _productService;
    public IMapper _mapper;

    public ProductController(
      IProductService productService,
      IMapper mapper)
    {
      _productService = productService;
      _mapper = mapper;
    }

    [HttpGet]
    [Route("/api/products")]
    [ResponseCache(Duration = 300)]
    public async Task<IActionResult> Search(CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        var response = await _productService.Search(cancellationToken);

        return Ok(response);
      }

      return Forbid();
    }

    [HttpGet]
    [Route("{productId}")]
    public async Task<IActionResult> GetProduct(uint productId, [FromQuery] string search, CancellationToken cancellationToken)
    {
      var searchDTO = new ProductSearchDTO() { };

      if (!String.IsNullOrEmpty(search))
      {
        searchDTO = JsonSerializer.Deserialize<ProductSearchDTO>(search);
      }

      var response = await _productService.Get(productId, searchDTO, cancellationToken);

      if (response != null)
      {
        return Ok(response);
      }

      return NotFound();
    }

    [HttpPost]
    [Route("/api/product")]
    public async Task<IActionResult> Create(ProductDTO dto, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        var response = await _productService.Create(dto, cancellationToken);

        return Ok(response);
      }

      return Forbid();
    }

    [HttpPatch]
    [Route("{productId}")]
    public async Task<IActionResult> Patch(uint productId, [FromBody] JsonPatchDocument<Product> patch, CancellationToken cancellationToken)
    {
      // Only edit addresses that are ours (or if we're admin)
      if (User.IsAdmin())
      {
        try
        {
          return Ok(await _productService.Patch(productId, patch, cancellationToken));
        }
        catch (Exception ex)
        {
          return ex.ParseError();
        }
      }

      return Forbid();
    }
  }
}
