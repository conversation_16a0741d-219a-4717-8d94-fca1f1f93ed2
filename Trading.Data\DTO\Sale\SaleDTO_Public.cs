using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;

namespace Trading.API.Data.DTO
{
  public class SaleDTO_Public : BaseModelEntityDTO
  {
    public string SaleName { get; set; }

    public DateTime? SaleStart { get; set; }

    public DateTime? SaleEnd { get; set; }

    public AddressDTO Address { get; set; }

    public PlatformDTO Platform { get; set; }

    public SaleTypeDTO SaleType { get; set; }

    public uint? SaleTypeId { get; set; }
    public uint? AdvertCount { get; set; }
    public LiveBiddingStatusEnum? LiveBiddingStatus { get; set; }
    public Guid? CurrentAdvertId { get; set; }
    public AdvertDTO_Public CurrentAdvert { get; set; }
    public IEnumerable<AdvertDTO_Public> Adverts { get; set; }

    public Guid? SaleProfileId { get; set; }
    public SaleProfileDTO SaleProfile { get; set; }
  }
}