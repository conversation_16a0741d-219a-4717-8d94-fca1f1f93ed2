﻿using System;
using Trading.API.Data.DTO.Search.LeadCRM;

namespace Trading.API.Data.DTO.Comms
{
  public class CommsHistoryDTO : BaseModelEntityIntDTO
  {
    public CommsTemplateDTO CommsTemplate { get; set; }
    public uint? CommsTemplateId { get; set; }
    public string UniqueId { get; set; }
    public string ModelId { get; set; }
    public string ResponseId { get; set; }
    public uint ResponseStatus { get; set; }
    public string ResponseMessage { get; set; }
    public string RecipientAddress { get; set; }
    public string BccAddress { get; set; }
    public string Subject { get; set; }
    public string Body { get; set; }
    public DateTime DateSent { get; set; }
  }

  public class CommsHistorySearchDTO : BaseSearchDTO
  {
    public CommsHistoryFiltersDTO Filters { get; set; } = new CommsHistoryFiltersDTO() { };
  }


  public class CommsHistoryFiltersDTO : BaseFilterInt
  { 
    public Guid? CustomerId { get; set; }
    public uint? CommsTemplateId { get; set; }
    public uint? CommsEventId { get; set; }
    public string RecipientAddress { get; set; }
    public string UniqueId { get; set; }
    public string TemplateName { get; set; }
  }
}