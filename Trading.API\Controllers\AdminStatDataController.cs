using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/adminStatData")]
  [ApiController]
  [Authorize(Roles = "ADMIN, POWER_USER")]
  public class AdminStatDataController : ControllerBase
  {
    private readonly IChartDataService _chartDataService;

    public AdminStatDataController(IChartDataService chartDataService)
    {
      _chartDataService = chartDataService;
    }

    [HttpGet]
    [Route("activeCustomers")]
    public async Task<IActionResult> GetActiveCustomers(
        [FromQuery] Guid? assigneeId,
        [FromQuery] bool isWeekly,
        CancellationToken cancellationToken)
    {
      try
      {
        var results = await _chartDataService.GetActiveCustomers(assigneeId, isWeekly, cancellationToken);
        return Ok(results);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("liveAdverts")]
    public async Task<IActionResult> GetLiveAdverts([FromQuery] Guid? assigneeId, [FromQuery] bool isWeekly, CancellationToken cancellationToken)
    {
      try
      {
        var results = await _chartDataService.GetLiveAdverts(assigneeId, isWeekly, cancellationToken);
        return Ok(results);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("salesByMarque")]
    public async Task<IActionResult> GetSalesByMarque(CancellationToken cancellationToken)
    {
      try
      {
        var results = await _chartDataService.GetSalesByMarque(cancellationToken);
        return Ok(results);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("salesByCustomer")]
    public async Task<IActionResult> GetSalesByCustomer([FromQuery] Guid? assigneeId, CancellationToken cancellationToken)
    {
      try
      {
        var results = await _chartDataService.GetSalesByCustomer(assigneeId, cancellationToken);
        return Ok(results);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("totalSalesByMonth")]
    [ResponseCache(Duration = 120)]
    public async Task<IActionResult> GetTotalSalesByMonth(CancellationToken cancellationToken)
    {
      try
      {
        var results = await _chartDataService.GetTotalSalesByMonth(cancellationToken);
        return Ok(results);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("monthlyListingTotals")]
    public async Task<IActionResult> GetMonthlyListingTotals(CancellationToken cancellationToken)
    {
      try
      {
        var results = await _chartDataService.GetMonthlyListingTotals(cancellationToken);
        return Ok(results);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("monthlyVendorSignups")]
    public async Task<IActionResult> GetMonthlyVendorSignups(CancellationToken cancellationToken)
    {
      try
      {
        var results = await _chartDataService.GetMonthlyVendorSignups(cancellationToken);
        return Ok(results);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("monthlyBuyerSignups")]
    public async Task<IActionResult> GetMonthlyBuyerSignups(CancellationToken cancellationToken)
    {
      try
      {
        var results = await _chartDataService.GetMonthlyBuyerSignups(cancellationToken);
        return Ok(results);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("staff-call-ratio-stats")]
    [ResponseCache(Duration = 120)]
    public async Task<IActionResult> GetStaffCallRatioStats(CancellationToken cancellationToken)
    {
      try
      {
        var results = await _chartDataService.GetStaffCallRatioStats(cancellationToken);
        return Ok(results);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("staff-call-ratio-totals")]
    [ResponseCache(Duration = 120)]
    public async Task<IActionResult> GetStaffCallRatioTotals(CancellationToken cancellationToken)
    {
      try
      {
        var results = await _chartDataService.GetStaffCallStatTotals(cancellationToken);
        return Ok(results);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("staff-call-daily-totals")]
    [ResponseCache(Duration = 120)]
    public async Task<IActionResult> GetStaffCallDailyTotals(CancellationToken cancellationToken)
    {
      try
      {
        var results = await _chartDataService.GetStaffCallDailyTotals(cancellationToken);
        return Ok(results);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("site-totals")]
    [ResponseCache(Duration = 120)]
    public async Task<IActionResult> GetSiteTotals(CancellationToken cancellationToken)
    {
      try
      {
        var results = await _chartDataService.GetSiteTotals(cancellationToken);
        return Ok(results);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

  }
}
