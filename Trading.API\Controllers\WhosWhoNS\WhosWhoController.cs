﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Threading;
using System;
using Trading.Services.Extensions;
using Newtonsoft.Json;
using System.Threading.Tasks;
using Trading.API.Data.DTO.Comms;
using Trading.Services.Interfaces.WhosWhoNS;
using Trading.API.Remarq.Controllers.Extensions;
using System.Text.Json.Serialization;
using System.Text.Json;
using Trading.API.Data.DTO.WhosWhoNS;
using System.Dynamic;
using Newtonsoft.Json.Converters;

namespace Trading.API.Remarq.Controllers.WhosWhoNS
{
  [Route("api/whoswho")]
  [ApiController]
  [Authorize]
  public class WhosWhoController : ControllerBase
  {
    private readonly IWhosWhoService _whosWhoService;

    public WhosWhoController(IWhosWhoService whosWhoService)
    {
      _whosWhoService = whosWhoService;
    }

    [HttpGet]
    [Route("update")]
    [AllowAnonymous]
    public async Task Update(CancellationToken cancellationToken)
    {
      try
      {
        await Task.Run(() => this.HttpContext.Response.KeepConnectionAlive(cancellationToken), cancellationToken);

        await _whosWhoService.UpdateAggregates();

        await HttpContext.Response.CompleteProcess();
      }
      catch (Exception ex)
      {
        await HttpContext.Response.CompleteProcessWithError(ex);
      }
    }

    [HttpGet]
    [Authorize(Roles = "ADMIN, POWER_USER")]
    [Route("main-data")]
    public async Task<IActionResult> GetMainData([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<WhosWhoSearchDTO>(query);

      var res = await _whosWhoService.GetMainData(dto, cancellationToken);

      return Ok(res);
    }

    [HttpGet]
    [Authorize(Roles = "ADMIN, POWER_USER")]
    [Route("export-to-csv")]
    public async Task<IActionResult> ExportCSV(CancellationToken cancellationToken)
    {
      var res = await _whosWhoService.ExportToCSV(cancellationToken);
      return File(res, "text/csv", "export.csv");
    }

    [HttpGet]
    [Authorize(Roles = "ADMIN, POWER_USER")]
    [Route("pending-count")]
    public async Task<IActionResult> GetPendingCount()
    {
      var res = _whosWhoService.GetPendingCount();

      return Ok(new { Pending = res });
    }

    [HttpGet]
    [Authorize(Roles = "ADMIN, POWER_USER")]
    [Route("unread-seller-messages-count")]
    public async Task<IActionResult> GetUnreadSellerMessagesCount()
    {
      var res = _whosWhoService.GetUnreadSellerMessagesCount();

      return Ok(new { Unread = res });
    }

    [HttpGet]
    [Authorize(Roles = "ADMIN, POWER_USER")]
    [Route("/api/contacts/unverified")]
    public async Task<IActionResult> GetUnverifiedContacts([FromQuery] bool showDeleted)
    {
      var res = await _whosWhoService.GetUnverifiedContacts(showDeleted);

      return Ok(res);
    }

  }
}
