﻿using System;

namespace Trading.API.Data.DTO.MechanicalFaults
{
  public class VehicleFaultCheckItemDTO : BaseModelEntityDTO
  {
    public Guid VehicleFaultCheckId { get; set; }
    public virtual VehicleFaultCheckDTO VehicleFaultCheck { get; set; }

    public uint FaultItemId { get; set; }
    public virtual FaultItemDTO FaultItem { get; set; }

    public uint FaultStatusId { get; set; }
    public virtual FaultStatusDTO FaultStatus { get; set; }

    public string FreeText { get; set; }
  }
}
