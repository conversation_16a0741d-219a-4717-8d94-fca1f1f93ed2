﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.IO;

namespace Trading.API.Helpers
{
  public static class FormFileHelper
  {
    public static IFormFileCollection DataURLsToFormFiles(string[] files)
    {
      List<IFormFile> formFiles = new List<IFormFile>();
      foreach (var file in files)
      {
        // Remove the file type prefix, eg: data:image/png;base64,

        byte[] bytes = Convert.FromBase64String(file);
        MemoryStream stream = new MemoryStream(bytes);

        IFormFile formFile = new FormFile(stream, 0, bytes.Length, "filename", "filename");
        formFiles.Add(formFile);

      }

      var collection = new FormFileCollection();
      collection.AddRange(formFiles);

      return collection;
    }
  }
}
