﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Trading.API.Data.Migrations
{
    /// <inheritdoc />
    public partial class InventData_VehicleId : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<long>(
                name: "InventVehicleId",
                table: "InventAuctionLot",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "ServiceHistory",
                table: "InventAuctionLot",
                type: "tinyint(1)",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "InventVehicleId",
                table: "InventAuctionLot");

            migrationBuilder.DropColumn(
                name: "ServiceHistory",
                table: "InventAuctionLot");
        }
    }
}
