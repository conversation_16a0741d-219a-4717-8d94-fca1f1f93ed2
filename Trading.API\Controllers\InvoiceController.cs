﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Common;
using Trading.API.Data.DTO;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/invoice")]
  [ApiController]
  [Authorize]
  public class InvoiceController : ControllerBase
  {
    private readonly IInvoiceService _invoiceService;

    public InvoiceController(IInvoiceService InvoiceService)
    {
      this._invoiceService = InvoiceService;
    }


    [HttpGet]
    [Route("/api/order/{id}/generateInvoice")]
    public async Task<IActionResult> GenerateInvoice(Guid id, CancellationToken cancellationToken)
    {
      try
      {
        var bill = await _invoiceService.ProcessInvoice(new List<Guid> { id }, cancellationToken);

        return Ok(bill);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/customer/{customerId}/unreconciled")]
    public async Task<IActionResult> GetUnreconciledCustomerInvoices(string customerId, CancellationToken cancellationToken)
    {
      try
      {
        var orders = await _invoiceService.GetUnreconciledCustomerInvoices(customerId, cancellationToken);
        return Ok(orders);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/customer/{customerId}/invoices")]
    public async Task<IActionResult> GetCustomerInvoices(Guid customerId, CancellationToken cancellationToken)
    {
      try
      {
        var invoices = await _invoiceService.GetCustomerInvoices(customerId, cancellationToken);
        return Ok(invoices);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("processInvoices")]
    [AllowAnonymous]
    public async Task<IActionResult> ProcessInvoices(CancellationToken cancellationToken)
    {
      try
      {
        //await _invoiceService.ProcessInvoices(Data.Enums.UpdateFrequencyEnum.Daily, cancellationToken);
        await _invoiceService.ProcessInvoices(Data.Enums.UpdateFrequencyEnum.Immediate, cancellationToken);

        return Ok("Invoices generated");
      } catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet("{billId}/make-payment")]
    public async Task<IActionResult> MakePaymentForInvoice(Guid billId)
    {
      var res = await _invoiceService.ChargeCustomerAsync(billId);
      return Ok(res);
    }

    /// <summary>
    /// If the specified bill has an invoice reference and is paid, this will update the accounting system with the payment details. (in case it errors)
    /// </summary>
    /// <param name="billId"></param>
    /// <returns></returns>
    [HttpGet("{billId}/reconcile-invoice")]
    public async Task<IActionResult> ReconcileInvoice(Guid billId)
    {
      var res = await _invoiceService.ReconcileInvoice(billId);
      return Ok(res);
    }


    [HttpGet]
    [Route("")]
    public async Task<IActionResult> SearchInvoices([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = JsonConvert.DeserializeObject<InvoiceSearchDTO>(query);
        var result = await _invoiceService.SearchInvoices(dto, cancellationToken);
        
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{billId}")]
    public async Task<IActionResult> GetInvoice(Guid billId, CancellationToken cancellationToken)
    {
      var result = await _invoiceService.GetInvoice(billId, cancellationToken);
      return Ok(result);
    }
  }
}
