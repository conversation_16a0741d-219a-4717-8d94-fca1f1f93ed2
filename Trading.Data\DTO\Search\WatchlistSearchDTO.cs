﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO.Search
{
  public class WatchlistSearchDTO : BaseSearchDTO
  {
    public WatchlistSearchFiltersDTO Filters { get; set; } = new WatchlistSearchFiltersDTO() { };
  }

  public class WatchlistSearchFiltersDTO : BaseFilterGuid
  {
    public Guid? CustomerId { get; set; }
    public Guid? ContactId { get; set; }
    public int? DaysAgo { get; set; }
  }
}
