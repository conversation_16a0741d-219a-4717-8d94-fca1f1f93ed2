﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums.Prospects;

namespace Trading.API.Data.DTO.Search.Prospects
{
  public class ProspectSearchDTO : BaseSearchDTO
  {
    public ProspectFilters Filters { get; set; } = new ProspectFilters() { };
  }
  
  public class ProspectFilters : BaseFilterGuid
  {
    public Guid? AdvertId { get; set; }
    public Guid? BrokerageId { get; set; }
    public Guid? ProspectContactId { get; set; }
  }
}
