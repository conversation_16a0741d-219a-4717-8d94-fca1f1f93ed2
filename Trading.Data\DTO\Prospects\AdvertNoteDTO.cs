﻿using System;
using System.Collections.Generic;

namespace Trading.API.Data.DTO.Prospects
{
  public class AdvertNoteDTO : BaseModelEntityDTO
  {
    public Guid AdvertId { get; set; }

    public string Note { get; set; }

    // the contact of the user who created the history record
    public Guid ContactId { get; set; }

    public virtual ContactDTO Contact { get; set; }

    public bool Overdue { get; set; }

    public Guid? ProspectId { get; set; }
    public string ProspectCustomerName { get; set; }
    public uint? ProspectActionId { get; set; }

    public Guid? AdminTaskId { get; set; }
    public AdminTaskDTO AdminTask { get; set; }
    public AdvertDTO Advert { get; set; }
  }

  public class AdvertNoteSearchDTO : BaseSearchDTO
  {
    public AdvertNoteSearchFilters Filters { get; set; } = new AdvertNoteSearchFilters() { };
  }

  public class AdvertNoteSearchFilters : BaseFilterGuid
  {
    public Guid? AdvertId { get; set; }
  }
}
