﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.Models.InventData;

[Table("InventAuctionLotImage")]
public class InventAuctionLotImage : BaseModelEntity
{
  public Guid AuctionLotId { get; set; }

  public string ImageUrl { get; set; }

  public bool IsInterior { get; set; }

  public int SortOrder { get; set; }


  public virtual InventAuctionLot AuctionLot { get; set; }
}