﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Models;
using Trading.API.Data.Models.DTO;

namespace Trading.API.Data.DTO
{
  public class ScanResponseDTO
  {
    public ScanCustomerDTO ScanCustomer;
    public ScanStyleDTO ScanStyle;
    public ScanServiceDTO ScanService;

    public ScanQueueDTO ScanQueue { get; set; }

    public uint EntryPoint;

    public dynamic Body { get; set; }
    public int Status { get; set; }
    public string AttribPairs { get; set; }

    public string ItemCache { get; set; }
    public IEnumerable<IGrouping<ScanStageDTO, ScanConfigDTO>> ScanConfigsByStage { get; set; }
    public ILookup<uint?, ScanConfigDTO> ScanConfigLookup { get; set; }

  }
}