using System;
using System.Collections.Generic;
using System.Linq;
using Trading.API.Data.DTO;
using Trading.API.Data.Enums;

namespace Trading.API.Data.Models.DTO
{
    public class ScanFieldDTO : BaseModelEntityIntDTO
    {
        public string FieldName { get; set; }

        public string Description { get; set; }

        public uint Sequence { get; set; }

        public ScanFieldTypeEnum FieldType { get; set; }

        public bool Mandatory { get; set; }

        public bool IsImage { get; set; }

        public bool NewScan { get; set; }
        public bool IsUrl { get; set; }

        public bool IsAttribPair { get; set; }

        public ICollection<ScanConfigDTO> ScanConfigs { get; set; }
    }
}