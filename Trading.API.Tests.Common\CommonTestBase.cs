﻿using AutoMapper;
using Dapper;
using Microsoft.EntityFrameworkCore;
using MySqlConnector;
using Trading.API.Data;
using Trading.API.Tests.Common.Helpers;

namespace Trading.API.Tests.Common
{
  public class CommonTestBase : ITestBase
  {
    public IMapper Mapper { get; }
    public TradingContext Context { get; }
    public CommonServices Common { get; }
    public LookupFactory LookupFactory { get; }

    public string BaseCustomerId { get; }
    public string BaseContactId { get; }
    
    public Guid BaseCustomerIdGuid { get; }
    public Guid BaseContactIdGuid { get; }

    public CommonTestBase()
    {
      var config = new MapperConfiguration(cfg =>
      {
        cfg.AddMaps(typeof(Services.DTOMappingProfiles.MappingProfile));
        cfg.AddMaps(typeof(Services.DTOMappingProfiles.AdvertMappingProfile));
      });

      Mapper = config.CreateMapper();

      // create trading context and helpers
      Context = CreateContext();
      Common = CommonServices.Create(Context);
      LookupFactory = new LookupFactory(Common);

      // set the base customer and contact ids
      var baseIds = CustomerContactHelper.CreateCustomerAndContact(Context, Common, "Test Customer", "<EMAIL>").GetAwaiter().GetResult();

      BaseContactId = baseIds.contactId.ToString();
      BaseContactIdGuid = new Guid(BaseContactId);
      BaseCustomerId = baseIds.customerId.ToString();
      BaseCustomerIdGuid = new Guid(BaseCustomerId);
    }

    private TradingContext CreateContext()
    {
      var optionsBuilder = new DbContextOptionsBuilder<TradingContext>();
      optionsBuilder.UseMySql(DBStrings.dbConn, ServerVersion.AutoDetect(DBStrings.dbConn));

      return new TradingContext(optionsBuilder.Options);
    }
  }
}
