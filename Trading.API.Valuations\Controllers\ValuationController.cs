﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.Search.Valuation;
using Trading.API.Data.DTO.Valuation;
using Trading.API.Data.Enums.Valuation;
using Trading.API.Data.Models.Valuation;
using Trading.Services.Extensions;
using Trading.Services.Valuations.Interfaces;

namespace Trading.API.Valuations.Controllers
{
  [Route("api/valuation")]
  [ApiController]
  [Authorize]
  public class ValuationController : ControllerBase
  {
    private readonly IValuationService _valuationService;
    private readonly IValuationQuoteService _valuationQuoteService;

    public ValuationController(IValuationService valuationService, IValuationQuoteService valuationQuoteService)
    {
      this._valuationService = valuationService;
      this._valuationQuoteService = valuationQuoteService;
    }

    [HttpGet]
    [Route("lookupAttributes")]
    [ResponseCache(Duration = 60)]
    public async Task<IActionResult> GetLookupAttributes(CancellationToken cancellationToken)
    {
      try
      {
        var result = await _valuationService.GetLookupAttributesAsync(cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("/api/valuationProfile")]
    public async Task<IActionResult> AddProfile([FromBody] ValuationProfileDTO profileDTO, CancellationToken cancellationToken)
    {
      try
      {
        if (!User.IsAdmin())
        {
          return Forbid();
        }

        var result = await _valuationService.AddValuationProfile(profileDTO, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/valuationProfile/{id}/makePrimary")]
    public async Task<IActionResult> MakeProfilePrimary(Guid id, CancellationToken cancellationToken)
    {
      try
      {
        if (!User.IsAdmin())
        {
          return Forbid();
        }

        var result = await _valuationService.MakeProfilePrimary(id, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("/api/valuationPillar")]
    public async Task<IActionResult> AddPillar([FromBody] ValuationPillarDTO pillarDTO, CancellationToken cancellationToken)
    {
      try
      {
        if (!User.IsAdmin())
        {
          return Forbid();
        }

        var result = await _valuationService.AddPillarNode(pillarDTO, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/valuationProfiles")]
    public async Task<IActionResult> SearchProfiles([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        if (!User.IsAdmin())
        {
          return Forbid();
        }

        var dto = JsonConvert.DeserializeObject<ValuationSearchDTO>(query);
        var result = await _valuationService.Search(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpPatch]
    [Route("/api/valuationPillar/{id}")]
    public async Task<IActionResult> PatchPillar([FromBody] JsonPatchDocument<ValuationPillar> patch, Guid id, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _valuationService.PatchPillarNode(id, patch, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("/api/valuationProfile/{id}")]
    public async Task<IActionResult> PatchProfile([FromBody] JsonPatchDocument<ValuationProfile> patch, Guid id, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _valuationService.PatchProfile(id, patch, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpDelete]
    [Route("/api/valuationNode/{id}")]
    public async Task<IActionResult> DeleteNode(Guid id, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin())
      {
        return Forbid();
      }

      var success = await _valuationService.DeleteValuationNode(id, cancellationToken);
      return Ok(success);
    }

    [HttpPut]
    [Route("/api/valuationNode/{profileId}/{pillar1}/{pillar2}/{pillar3}/{pillar4}/{pillar5}/{lookupType}/{lookupId}")]
    public async Task<IActionResult> SetNode(
      Guid profileId,
      uint? pillar1,
      uint? pillar2,
      uint? pillar3,
      uint? pillar4,
      uint? pillar5,
      ValuationLookupTypeEnum lookupType,
      string lookupId,
      [FromBody] ValuationNodeDTO data, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        data.ValuationProfileId = profileId;
        data.Pillar1 = (pillar1 == 0) ? null : pillar1;
        data.Pillar2 = (pillar2 == 0) ? null : pillar2;
        data.Pillar3 = (pillar3 == 0) ? null : pillar3;
        data.Pillar4 = (pillar4 == 0) ? null : pillar4;
        data.Pillar5 = (pillar5 == 0) ? null : pillar5;
        data.LookupType = lookupType;
        data.LookupId = lookupId;

        var result = await _valuationService.SetValuationNode(data, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("calculate")]
    public async Task<IActionResult> GetValuation([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = JsonConvert.DeserializeObject<ValuationRequestDTO>(query);

        // test version is admin only 
        if (dto.TestValuation && !(User.IsAdmin() || User.IsAuctionAdmin()))
        {
          return Forbid();
        }

        var result = await _valuationService.CalculateValuation(dto, cancellationToken);
        var res = new ObjectResult(result);
        res.StatusCode = (int)result.HTTPStatus;

        // if we require a quote, create a manual pending status quote now
        if (result.IsValid && dto.CreateTestQuote)
        {
          await _valuationQuoteService.AddValuationQuoteRecord(result.DTO, new Data.DTO.LeadCRM.LeadVehicleDTO { Vrm = result.DTO.VehicleInfo.VRM }, cancellationToken);
        }

        return res;
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/valuationProfile/{valuationProfileId}/valuation-nodes")]
    public async Task<IActionResult> GetValuationNodes(Guid valuationProfileId, [FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = JsonConvert.DeserializeObject<ValuationNodeSearchDTO>(query);
        dto.Filters.ValuationProfileId = valuationProfileId;
        var result = await _valuationService.SearchValuationNodes(dto, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

  }
}
