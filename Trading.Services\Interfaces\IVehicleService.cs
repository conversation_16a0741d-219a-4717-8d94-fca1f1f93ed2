﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Components.AdvertView;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.Services.ExternalDTO;

namespace Trading.Services.Interfaces
{
  public interface IVehicleService
  {
    Task<VehicleListDTO> GetActiveVehicles(VehicleListDTO vehicleListDTO, CancellationToken cancellationToken);
    Task<IEnumerable<VehicleDTO>> GetAllByCustomer(Guid customerId, CancellationToken cancellationToken);

    Task<VehicleDTO> GetVehicle(Guid id, CancellationToken cancellationToken);
    Task<IEnumerable<VehicleDTO>> Search(VehicleSearchDTO dto, CancellationToken cancellationToken);
    Task<IEnumerable<VehicleStatusCheckDTO>> VehicleStatusCheck(VehicleStatusSearchDTO searchDTO, CancellationToken cancellationToken);
    Task DeleteVehicle(Guid customerId, Guid vehicleId, CancellationToken cancellationToken);
    Task<VehicleDTO> AddVehicle(VehicleDTO dto, CancellationToken cancellationToken);
    Task<VehicleDTO> UpdateVehicle(VehicleDTO dto, CancellationToken cancellationToken);
    Task<VehicleDTO> CreateVehicle(CreateVehicleDTO info, CancellationToken cancellationToken);

    Task UpdateMOTHistory(Vehicle vehicle, CancellationToken cancellationToken);
    Task UpdateMOTHistory(Guid vehicleId, CancellationToken cancellationToken);


    Task<VehicleDTO> Patch(Guid vehicleId, Guid customerId, JsonPatchDocument<Vehicle> patchDTO, CancellationToken cancellationToken);
    Task UpdateAllDVLAData(CancellationToken cancellationToken);
    Task<DVLAVehicleDTO> GetDVLAData(string vrm, CancellationToken cancellationToken);
    Task<ServiceHistoryDTO> AddServiceHistory(Guid vehicleId, ServiceHistoryDTO historyDTO, CancellationToken cancellationToken);
    Task<bool> DeleteServiceHistory(Guid vehicleId, Guid id, CancellationToken cancellationToken);
    Task<bool> PatchServiceHistory(Guid vehicleId, Guid id, JsonPatchDocument<ServiceHistory> patch, CancellationToken cancellationToken);

    Task<IEnumerable<Vehicle>> GetMatchingVehicles(VehicleSearchDTO dto, CancellationToken cancellationToken);
    Task<VehicleDTO> CreateVehicleDTO(VehicleLookupInfoDTO info, CancellationToken cancellationToken);

    Task<DVLAData> GetOrCreateDVLAData(string vrm, CancellationToken cancellationToken);

    Task AddMOTAndFinalizeVehicleDetails(DVLAData dvlaData, Vehicle vehicle, CancellationToken cancellationToken);

    Task<VehicleLookupInfoDTO> GetVehicleLookupInfoDTO(CreateVehicleDTO info, CancellationToken cancellationToken);

    Task<AdvertViewVehicleProvenanceDTO> GetVehicleProvenanceByAdvert(Guid advertId, CancellationToken cancellationToken);
    Task<AdvertViewVehicleValuationDTO> GetVehicleValuationByAdvert(Guid advertId, CancellationToken cancellationToken);
    Task<List<MOTHistoryDTO>> GetMOTHistoryByAdvert(Guid advertId, CancellationToken cancellationToken);
    Task<List<AdvertViewVehicleMediaDTO>> GetVehicleMediaByAdvert(Guid advertId, CancellationToken cancellationToken);
    Task<AdvertViewVehicleServiceHistoryDTO> GetVehicleServiceHistoryByAdvert(Guid advertId, CancellationToken cancellationToken);
    Task<AdvertViewVehicleTyreInfoDTO> GetVehicleTyreInfoByAdvert(Guid advertId, CancellationToken cancellationToken);
    Task<List<VehicleAttribDTO>> GetVehicleOptionsByAdvert(Guid advertId, CancellationToken cancellationToken);
    Task<AdvertViewContactSellerDataDTO> GetContactSellerDataByAdvert(Guid advertId, CancellationToken cancellationToken);
    Task<bool> SetTyreDepths(Guid advertId, Guid customerId, JsonPatchDocument<VehicleTyrePatchDTO> patch, CancellationToken cancellationToken);
  }
}
