using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.API.Helpers;
using Trading.Services;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/[controller]")]
  [ApiController]
  public class OfferController : ControllerBase
  {
    public IOfferService _offerService;
    public IBidService _bidService;

    public OfferController(IOfferService offerService, IBidService bidService)
    {
      _offerService = offerService;
      _bidService = bidService;
    }

    // GET: api/Customers
    [HttpGet("/api/offers")]
    public async Task<IActionResult> GetOffers([FromQuery] string query, CancellationToken cancellationToken)
    {
      if (!User.IsInRole("ADMIN"))
      {
        return Forbid();
      }

      OfferSearchDTO dto = new OfferSearchDTO() { };

      if (query != null)
      {
        dto = JsonConvert.DeserializeObject<OfferSearchDTO>(query);
      }

      var all = await _offerService.Search(dto, cancellationToken);
      return Ok(all);
    }

    [HttpPut]
    [Route("{offerId}/response")]
    public async Task<IActionResult> CounterOfferResponse(Guid offerId, [FromBody] CounterOfferResponseDTO dto, CancellationToken cancellationToken)
    {
      if (dto.ContactId == User.ContactId() || User.IsInRole("USER:ADMIN"))
      {
        var response = await _offerService.CounterOfferResponse(offerId, dto, cancellationToken);
        return Ok(response);
      }

      return BadRequest();
    }

    [HttpPut]
    [Route("/api/bid/{bidGuid}/counterOffer")]
    [Route("/api/offer/{bidGuid}/counterOffer")]
    public async Task<IActionResult> CounterOffer(Guid bidGuid, [FromBody] BidReplyDTO bidRepDTO, CancellationToken cancellationToken)
    {
      try
      {
        bidRepDTO.BidGuid = bidGuid;
        bidRepDTO.AdminAction = User.IsAdmin();

        if (User.IsAdmin())
        {
          bidRepDTO.ContactId = bidRepDTO.ContactId ?? this.User.ContactId();
          bidRepDTO.CustomerId = bidRepDTO.CustomerId ?? this.User.CustomerId();
        }
        else
        {
          bidRepDTO.ContactId = this.User.ContactId();
          bidRepDTO.CustomerId = this.User.CustomerId();
        }

        var ok = await _bidService.CanReplyToBid(bidRepDTO, cancellationToken);

        // post the bid to the db
        var offers = await _offerService.CounterOffer(bidRepDTO, cancellationToken);
        return Ok(offers);
      }
      catch (Exception ex)
      {
        return ex.ParseError();
      }
    }


    [HttpPut]
    [Route("{offerId}/cancel")]
    public async Task<IActionResult> CancelOffer(Guid offerId, [FromBody] CounterOfferResponseDTO dto, CancellationToken cancellationToken)
    {
      if (dto.ContactId == User.ContactId() || User.IsInRole("USER:ADMIN"))
      {
        var response = await _offerService.Cancel(offerId, dto.ContactId.Value, cancellationToken);
        return Ok(response);
      }

      return BadRequest();
    }
  }
}
