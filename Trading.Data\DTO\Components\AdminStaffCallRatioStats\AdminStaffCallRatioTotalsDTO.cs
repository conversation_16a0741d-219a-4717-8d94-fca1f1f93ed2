﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.Components.AdminStaffCallRatioStats
{
  public class AdminStaffCallRatioTotalsDTO
  {
    /// <summary>
    /// The name of the staff member (Contact).
    /// </summary>
    public string ContactName { get; set; }

    /// <summary>
    /// Total number of customers assigned to the staff member in the last 6 months.
    /// </summary>
    public int TotalCustomerCount { get; set; }

    /// <summary>
    /// Number of customers assigned to the staff member in the last 30 days.
    /// </summary>
    public int Last30DaysCustomerCount { get; set; }

    /// <summary>
    /// Total number of outbound calls made by the staff member in the last 6 months.
    /// </summary>
    public int TotalCalls { get; set; }

    /// <summary>
    /// Ratio of total calls to total customers in the last 6 months (percentage).
    /// </summary>
    public double TotalCallToCustomerRatio { get; set; }

    /// <summary>
    /// Number of outbound calls made by the staff member in the last 30 days.
    /// </summary>
    public int Last30DaysCalls { get; set; }

    /// <summary>
    /// Ratio of calls to customers in the last 30 days (percentage).
    /// </summary>
    public double Last30DaysCallToCustomerRatio { get; set; }
  }
}
