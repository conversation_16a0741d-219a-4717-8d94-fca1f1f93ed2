﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Trading.API.Data.DTO.AutoTrader;

// Authentication response model
public class AuthTokenResponse
{
  [JsonPropertyName("access_token")]
  public string AccessToken { get; set; } = string.Empty;

  [JsonPropertyName("expires_at")]
  public DateTime ExpiresAt { get; set; }
}

// Error response models
public class AutoTraderWarning
{
  public string Type { get; set; } = string.Empty;
  public string Feature { get; set; } = string.Empty;
  public string Message { get; set; } = string.Empty;
}

public class AutoTraderApiResponse<T>
{
  public T? Data { get; set; }
  public List<AutoTraderWarning> Warnings { get; set; } = new();
}

// Advertiser models
public class Advertiser
{
  public string AdvertiserId { get; set; } = string.Empty;
  public string Name { get; set; } = string.Empty;
  public string WebsiteUrl { get; set; } = string.Empty;
  public string PhoneNumber { get; set; } = string.Empty;
  public AdvertiserLocation Location { get; set; } = new();
  public string Strapline { get; set; } = string.Empty;
}

public class AdvertiserLocation
{
  public string Address { get; set; } = string.Empty;
  public string PostCode { get; set; } = string.Empty;
  public string Town { get; set; } = string.Empty;
  public string County { get; set; } = string.Empty;
}

public class AdvertiserListResponse
{
  public List<Advertiser> Advertisers { get; set; } = new();
  public int Page { get; set; }
  public int PageSize { get; set; }
  public int TotalCount { get; set; }
}

// Vehicle models
public class ATVehicleDTO
{
  public string OwnershipCondition { get; set; } = string.Empty;
  public string Registration { get; set; } = string.Empty;
  public string Vin { get; set; } = string.Empty;
  public string Make { get; set; } = string.Empty;
  public string Model { get; set; } = string.Empty;
  public string Generation { get; set; } = string.Empty;
  public string Derivative { get; set; } = string.Empty;
  public string DerivativeId { get; set; } = string.Empty;
  public string VehicleType { get; set; } = string.Empty;
  public string Trim { get; set; } = string.Empty;
  public string BodyType { get; set; } = string.Empty;
  public string FuelType { get; set; } = string.Empty;
  public string? CabType { get; set; }
  public string TransmissionType { get; set; } = string.Empty;
  public string WheelbaseType { get; set; } = string.Empty;
  public string? RoofHeightType { get; set; }
  public string Drivetrain { get; set; } = string.Empty;
  public int? Seats { get; set; }
  public int? Doors { get; set; }
  public int? Cylinders { get; set; }
  public int? Valves { get; set; }
  public int? EngineTorqueNM { get; set; }
  public int? Co2EmissionGPKM { get; set; }
  public int? TopSpeedMPH { get; set; }
  public double? ZeroToSixtyMPHSeconds { get; set; }
  public double? ZeroToOneHundredKMPHSeconds { get; set; }
  public double? BadgeEngineSizeLitres { get; set; }
  public int? EngineCapacityCC { get; set; }
  public int? EnginePowerBHP { get; set; }
  public double? FuelCapacityLitres { get; set; }
  public string EmissionClass { get; set; } = string.Empty;
  public int? Owners { get; set; }
  public double? FuelEconomyNEDCExtraUrbanMPG { get; set; }
  public double? FuelEconomyNEDCUrbanMPG { get; set; }
  public double? FuelEconomyNEDCCombinedMPG { get; set; }
  public double? FuelEconomyWLTPLowMPG { get; set; }
  public double? FuelEconomyWLTPMediumMPG { get; set; }
  public double? FuelEconomyWLTPHighMPG { get; set; }
  public double? FuelEconomyWLTPExtraHighMPG { get; set; }
  public double? FuelEconomyWLTPCombinedMPG { get; set; }
  public double? BootSpaceSeatsUpLitres { get; set; }
  public string InsuranceGroup { get; set; } = string.Empty;
  public string InsuranceSecurityCode { get; set; } = string.Empty;
  public DateTime? FirstRegistrationDate { get; set; }
  public string Colour { get; set; } = string.Empty;
  public string? Style { get; set; }
  public string? SubStyle { get; set; }
  public int? LengthMM { get; set; }
  public int? HeightMM { get; set; }
  public int? WidthMM { get; set; }
  public int? PayloadLengthMM { get; set; }
  public int? PayloadWidthMM { get; set; }
  public int? PayloadHeightMM { get; set; }
  public int? PayloadWeightKG { get; set; }
  public int? MinimumKerbWeightKG { get; set; }
  public int? GrossVehicleWeightKG { get; set; }
  public string EngineNumber { get; set; } = string.Empty;
  public string FuelDelivery { get; set; } = string.Empty;
  public int? Gears { get; set; }
  public bool? StartStop { get; set; }
  public int? EnginePowerPS { get; set; }
  public double EngineTorqueLBFT { get; set; }
  public double? BootSpaceSeatsDownLitres { get; set; }
  public int? BatteryRangeMiles { get; set; }
  public double? BatteryCapacityKWH { get; set; }
  public double? BatteryUsableCapacityKWH { get; set; }
  public int? WheelbaseMM { get; set; }
  public int? GrossCombinedWeightKG { get; set; }
  public int? GrossTrainWeightKG { get; set; }
  public int? BoreMM { get; set; }
  public int? StrokeMM { get; set; }
  public string CylinderArrangement { get; set; } = string.Empty;
  public string EngineMake { get; set; } = string.Empty;
  public string ValveGear { get; set; } = string.Empty;
  public int? Axles { get; set; }
  public string CountryOfOrigin { get; set; } = string.Empty;
  public string DriveType { get; set; } = string.Empty;
  public double? PayloadVolumeCubicMetres { get; set; }
  public bool? Rde2Compliant { get; set; }
  public int? VehicleExciseDutyWithoutSupplementGBP { get; set; }
  public string Sector { get; set; } = string.Empty;
  public ATVehicleOemDTO Oem { get; set; } = new();
}

public class ATVehicleOemDTO
{
  public string Make { get; set; } = string.Empty;
  public string Model { get; set; } = string.Empty;
  public string Derivative { get; set; } = string.Empty;
  public string BodyType { get; set; } = string.Empty;
  public string TransmissionType { get; set; } = string.Empty;
  public string Drivetrain { get; set; } = string.Empty;
  public string WheelbaseType { get; set; }
  public string RoofHeightType { get; set; }
  public string EngineType { get; set; }
  public string EngineTechnology { get; set; }
  public string EngineMarketing { get; set; }
  public string EditionDescription { get; set; }
  public string Colour { get; set; } = string.Empty;
}

// Features models
public class ATVehicleFeatureDTO
{
  public string Name { get; set; } = string.Empty;
  public string GenericName { get; set; } = string.Empty;
  public string Type { get; set; } = string.Empty; // "Standard" or "Optional"
  public string Category { get; set; } = string.Empty;
  public decimal? BasicPrice { get; set; }
  public decimal? VatPrice { get; set; }
  public bool? FactoryFitted { get; set; }
  public string Finish { get; set; }
  public string GenericFinish { get; set; }
}

public class ATVehicleFeaturesResponse
{
  public ATVehicleDTO Vehicle { get; set; } = new();
  public List<ATVehicleFeatureDTO> Features { get; set; } = new();
}

// History models
public class ATVehicleHistoryDTO
{
  public bool Scrapped { get; set; }
  public bool Stolen { get; set; }
  public bool Imported { get; set; }
  public bool Exported { get; set; }
  public int? PreviousOwners { get; set; }
  public int? YearOfManufacture { get; set; }
  public List<ATKeeperChangeDTO> KeeperChanges { get; set; } = new();
  public List<ATV5cDTO> V5cs { get; set; } = new();
}

public class ATKeeperChangeDTO
{
  public DateTime DateOfLastKeeper { get; set; }
}

public class ATV5cDTO
{
  public DateTime IssuedDate { get; set; }
}

public class ATVehicleHistoryResponse
{
  public ATVehicleDTO Vehicle { get; set; } = new();
  public ATVehicleHistoryDTO History { get; set; } = new();
}

// Full check models
public class ATVehicleCheckDTO
{
  public string InsuranceWriteoffCategory { get; set; }
  public bool? Scrapped { get; set; }
  public bool? Stolen { get; set; }
  public bool? Imported { get; set; }
  public bool? Exported { get; set; }
  public int? PreviousOwners { get; set; }
  public List<ATKeeperChangeDTO> KeeperChanges { get; set; } = new();
  public List<ATV5cDTO> V5cs { get; set; } = new();
  public bool? HighRisk { get; set; }
  public bool? PrivateFinance { get; set; }
  public bool? TradeFinance { get; set; }
  public bool? MileageDiscrepancy { get; set; }
  public bool? RegistrationChanged { get; set; }
  public bool? ColourChanged { get; set; }
  public ATDvlaVehicleDTO DvlaVehicle { get; set; } = new();
  public ATPoliceMarkerDTO PoliceStolenMarker { get; set; } = new();
  public List<ATFinanceAgreementDTO> FinanceAgreements { get; set; } = new();
  public List<ATPlateChangeDTO> PlateChanges { get; set; } = new();
  public List<ATColourChangeDTO> ColourChanges { get; set; } = new();
  public List<ATOdometerReadingDTO> OdometerReadings { get; set; } = new();
  public List<ATHighRiskMarkerDTO> HighRiskMarkers { get; set; } = new();
  public List<ATPreviousSearchDTO> PreviousSearches { get; set; } = new();
  public List<ATInsuranceHistoryDTO> InsuranceHistory { get; set; } = new();
  public string Report { get; set; } = string.Empty;
}

public class ATDvlaVehicleDTO
{
  public string Make { get; set; } = string.Empty;
  public string Model { get; set; } = string.Empty;
  public string BodyType { get; set; } = string.Empty;
  public string FuelType { get; set; } = string.Empty;
  public string TransmissionType { get; set; }
  public int? EngineCapacityCC { get; set; }
  public string Colour { get; set; } = string.Empty;
  public int? Co2EmissionsGKM { get; set; }
  public DateTime? DateScrapped { get; set; }
  public DateTime? DateExported { get; set; }
}

public class ATPoliceMarkerDTO
{
  public DateTime? RecordedDate { get; set; }
  public string PoliceForce { get; set; }
  public string TelephoneNumber { get; set; }
}

public class ATFinanceAgreementDTO
{
  public string AgreementId { get; set; } = string.Empty;
  public string Company { get; set; } = string.Empty;
  public string TelephoneNumber { get; set; } = string.Empty;
  public DateTime? StartDate { get; set; }
  public int? Term { get; set; }
  public string Type { get; set; } = string.Empty;
}

public class ATPlateChangeDTO
{
  public DateTime StartDate { get; set; }
  public string PreviousRegistration { get; set; }
}

public class ATColourChangeDTO
{
  public DateTime StartDate { get; set; }
  public string PreviousColour { get; set; }
}

public class ATOdometerReadingDTO
{
  public DateTime Performed { get; set; }
  public string Source { get; set; } = string.Empty;
  public int OdometerReadingMiles { get; set; }
}

public class ATHighRiskMarkerDTO
{
  public DateTime? StartDate { get; set; }
  public string Type { get; set; } = string.Empty;
  public string ExtraInfo { get; set; } = string.Empty;
  public string Company { get; set; } = string.Empty;
  public string TelephoneNumber { get; set; } = string.Empty;
  public string Reference { get; set; } = string.Empty;
}

public class ATPreviousSearchDTO
{
  public DateTime Performed { get; set; }
  public string TypeOfBusiness { get; set; } = string.Empty;
}

public class ATInsuranceHistoryDTO
{
  public string Type { get; set; } = string.Empty;
  public DateTime? LossDate { get; set; }
  public DateTime? RemovedDate { get; set; }
}

public class ATVehicleCheckResponse
{
  public ATVehicleDTO Vehicle { get; set; } = new();
  public ATVehicleCheckDTO Check { get; set; } = new();
}

// Valuations models
public class ATVehicleValuationDTO
{
  public ATValuationDTO Trade { get; set; } = new();
  public ATValuationDTO PartExchange { get; set; } = new();
  public ATValuationDTO Retail { get; set; } = new();
  public ATPrivateValuationDTO Private { get; set; } = new();

  public bool IsFeatureValuation { get; set; } 
}

public class ATValuationDTO
{
  public decimal? AmountGBP { get; set; }
  public decimal? AmountExcludingVatGBP { get; set; }
}

public class ATPrivateValuationDTO
{
  public decimal? AmountGBP { get; set; }
}

public class ATVehicleValuationsResponse
{
  public ATVehicleDTO Vehicle { get; set; } = new();
  public ATVehicleValuationDTO Valuations { get; set; } = new();
}

// Metrics models
public class ATVehicleMetricDTO
{
  public ATRetailMetricsDTO Retail { get; set; } = new();
}

public class ATRetailMetricsDTO
{
  public ATMetricValue Supply { get; set; } = new();
  public ATMetricValue Demand { get; set; } = new();
  public ATMetricValue MarketCondition { get; set; } = new();
  public ATMetricValue Rating { get; set; } = new();
  public ATMetricValue DaysToSell { get; set; } = new();
}

public class ATMetricValue
{
  public double? Value { get; set; }
}

public class ATVehicleMetricsResponse
{
  public ATVehicleDTO Vehicle { get; set; } = new();
  public ATVehicleMetricDTO VehicleMetrics { get; set; } = new();
}
public class ATVehicleResponse
{
  public ATVehicleDTO Vehicle { get; set; } = new();
}



// Combined response DTO class
public class ATCombinedVehicleResponse
{
  public ATVehicleDTO Vehicle { get; set; }
  public List<ATVehicleFeatureDTO> Features { get; set; }
  public ATVehicleHistoryDTO History { get; set; }
  public ATVehicleValuationDTO Valuations { get; set; }
  public ATVehicleMetricDTO VehicleMetrics { get; set; }
}