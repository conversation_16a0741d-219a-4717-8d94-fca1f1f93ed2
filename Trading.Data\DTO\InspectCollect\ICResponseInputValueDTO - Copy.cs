﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICResponseInputValueDTO: BaseModelEntityDTO
  {
    public Guid? ICResponseInputId { get; set; }
    public string Value { get; set; }
    public Guid? ICInputOptionId { get; set; }
    public ICInputOptionDTO ICInputOption { get; set; }
  }

  public class ICResponseInputValueSearchDTO : BaseSearchDTO
  {
    public ICResponseInputValueSearchFilters Filters { get; set; } = new ICResponseInputValueSearchFilters();
  }

  public class ICResponseInputValueSearchFilters : BaseFilter
  {

  }

  public class ICResponseInputValueCreateDTO
  {
    public Guid? ICResponseInputId { get; set; }
    public string Value { get; set; }
  }
}
