using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.Extensions;
using Trading.Services.InspectCollect.Classes;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/trigger")]
  [ApiController]
  [Authorize(Policy = "RequireInspectCollect")]
  [AllowAnonymous]
  public class ICTriggerController : ControllerBase
  {
    private readonly ICTriggerInterface _icTriggerService;

    public ICTriggerController(ICTriggerInterface icTriggerService)
    {
      _icTriggerService = icTriggerService;
    }

    [HttpGet]
    [Route("search")]
    [Route("/api/inspect-collect/triggers")]
    public async Task<ActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {

      var searchDTO = JsonConvert.DeserializeObject<ICTriggerSearchDTO>(query) ?? new ICTriggerSearchDTO();

      var icContainerGroupId = User.ICContainerGroupId();

      if (icContainerGroupId != searchDTO.Filters.ICContainerGroupId && ! User.IsAdmin())
      {
        return Forbid();
      }

      var ok = await _icTriggerService.Search(searchDTO, cancellationToken);

      return Ok(ok);
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Create([FromBody] ICTriggerCreateDTO dto)
    {
      if (dto.ICContainerGroupId != User.ICContainerGroupId() && !User.IsAdmin())
      {
        return Forbid();
      }

      var ok = await _icTriggerService.Create(dto);

      return Ok(ok);
    }

    [HttpPatch]
    [Route("{triggerId}")]
    public async Task<ActionResult> Patch(Guid triggerId, JsonPatchDocument<ICTrigger> patch)
    {
      var ok = await _icTriggerService.Patch(triggerId, patch);

      return Ok(ok);
    }

    [HttpDelete]
    [Route("{triggerId}")]
    public async Task<ActionResult> Delete(Guid triggerId)
    {
      var ok = await _icTriggerService.Delete(triggerId);

      return Ok(ok);
    }

    [HttpGet]
    [Route("{triggerId}")]
    public async Task<ActionResult> Get(Guid triggerId, [FromQuery] string query, CancellationToken ct)
    {
      ICTriggerSearchDTO searchDTO = new ICTriggerSearchDTO();

      if (!String.IsNullOrEmpty(query))
      {
        searchDTO = JsonConvert.DeserializeObject<ICTriggerSearchDTO>(query);
      }

      var ok = await _icTriggerService.Get(triggerId, searchDTO, ct);

      return Ok(ok);
    }
  }
}