﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.UInspections;
using Trading.API.Data.Models.UInspections;
using Trading.Services.Extensions;
using Trading.Services.UInspections.Interfaces;

namespace Trading.API.UInspection.Controllers
{
  [Route("api/uinspect/media")]
  [ApiController]
  public class UInspectMediaController : ControllerBase
  {
    private readonly IUInspectMediaService _uInspectMediaService;

    public UInspectMediaController(IUInspectMediaService uInspectMediaService)
    {
      this._uInspectMediaService = uInspectMediaService;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<IActionResult> Get(Guid id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = new UInspectMediaSearchDTO();
        
        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<UInspectMediaSearchDTO>(query);
        }

        var result = await _uInspectMediaService.Get(id, cancellationToken, dto);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/uinspect/{uinspectid}/media")]
    public async Task<IActionResult> GetAppraisalMedia(Guid uinspectid, CancellationToken cancellationToken)
    {      
      var dto = new UInspectMediaSearchDTO();
      dto.Filters = new UInspectMediaSearchDTO.UInspectMediaSearchFilters { UInspectId = uinspectid };

      var result = await _uInspectMediaService.Search(dto, cancellationToken);

      return Ok(result); 
    }

    [HttpDelete]
    [Route("{mediaId}")]
    [Route("/api/uInspect/{uInspectId}/section/{sectionId}/media/{mediaId}")]
    [Route("/api/uInspect/{uInspectId}/question/{questionId}/media/{mediaId}")]
    public async Task<IActionResult> Delete(Guid mediaId, CancellationToken cancellationToken, Guid? uInspectId, uint? sectionId, uint? questionId)
    {
      try
      {
        var result = await _uInspectMediaService.DeleteMedia(new UInspectMediaDeleteDTO()
        {
          UInspectId = uInspectId,
          SectionId = sectionId,
          QuestionId = questionId,  
          MediaId = mediaId
        }, cancellationToken);

        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("/api/uinspect/{uInspectId}/media")]
    [RequestFormLimits(ValueLengthLimit = int.MaxValue, MultipartBodyLengthLimit = int.MaxValue)]
    [DisableRequestSizeLimit]
    public async Task<IActionResult> UploadUInspectMedia(Guid uInspectId, [FromBody] UInspectMediaUploadDTO data, CancellationToken cancellationToken)
    {
      var dto = data.MediaUpload;

      dto.UInspectId = uInspectId;

      try
      {
        var files = DataURLsToFormFiles(data.Files);

        var medias = await _uInspectMediaService.UploadAppraisalImages(dto, files, cancellationToken);
        return Ok(medias);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/uinspect/medias")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = new UInspectMediaSearchDTO();
        
        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<UInspectMediaSearchDTO>(query);
        }

        var result = await _uInspectMediaService.Search(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create([FromBody] UInspectMediaDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        var result = await _uInspectMediaService.Create(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<UInspectMedia> patch, CancellationToken cancellationToken)
    {
      try
      {
        var result = await _uInspectMediaService.Patch(id, patch, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    private IFormFileCollection DataURLsToFormFiles(string[] files)
    {
      List<IFormFile> formFiles = new List<IFormFile>();
      foreach (var file in files)
      {
        // Remove the file type prefix, eg: data:image/png;base64,

        byte[] bytes = Convert.FromBase64String(file);
        MemoryStream stream = new MemoryStream(bytes);

        IFormFile formFile = new FormFile(stream, 0, bytes.Length, "filename", "filename");
        formFiles.Add(formFile);

      }

      var collection = new FormFileCollection();
      collection.AddRange(formFiles);

      return collection;
    }
  }
}
