﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.Imports.Invent;

// Data models
public class CityVehicleInspectionResult
{
  public CityVehicleInfo VehicleInfo { get; set; }
  public List<CityDamageItem> DamageItems { get; set; }
  public int TotalDamageCount { get; set; }
  public DateTime ProcessedAt { get; set; }

  // New properties for splat image
  public byte[] SplatImageData { get; set; }
  public string SplatImageFormat { get; set; }
  public int? SplatImageWidth { get; set; }
  public int? SplatImageHeight { get; set; }
}

public class CityVehicleInfo
{
  public string Registration { get; set; }
  public string Make { get; set; }
  public string Model { get; set; }
  public string Year { get; set; }
  public string Mileage { get; set; }
  public string VIN { get; set; }
  public string Colour { get; set; }
}

public class CityDamageItem
{
  public int ItemNumber { get; set; }
  public string Location { get; set; }
  public string DamageType { get; set; }
  public string Severity { get; set; }
  public string Description { get; set; }

  // Properties for dot positions on vehicle diagram
  public decimal? SplatPositionX { get; set; } // x-position in percentage (0-100)
  public decimal? SplatPositionY { get; set; } // y-position in percentage (0-100)
  public string SplatDesc { get; set; } // the number/character label

  // Properties for associated damage photos
  public byte[] ImageData { get; set; } // The actual image bytes
  public string ImageFormat { get; set; } // JPEG, PNG, etc.
  public int? ImageWidth { get; set; } // Image width in pixels
  public int? ImageHeight { get; set; } // Image height in pixels
}