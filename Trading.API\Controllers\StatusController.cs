using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/status")]
  [ApiController]
  public class StatusController : ControllerBase
  {
    private readonly IStatusService _statusService;

    public StatusController(IStatusService statusService)
    {
      this._statusService = statusService;
    }

    [HttpGet]
    [ResponseCache(Duration = 300)]
    [Route("/api/statuses")]
    public async Task<IActionResult> Search(CancellationToken cancellationToken)
    {
      try
      {
        return Ok(await this._statusService.Search(cancellationToken));
      }
      catch (Exception ex)
      {
        return BadRequest(ex);

      }

    }
  }
}
