﻿using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NetCore.AutoRegisterDi;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Reflection;
using System.Text.Json.Serialization;
using Trading.API.Common;
using Trading.Services;
using Trading.Services.Azure;
using Trading.Services.Custom;
using Trading.Services.WhosWhoNS;
using Trading.Services.Imports;
using Trading.Services.Interfaces;
using Trading.Services.Interfaces.Custom;
using Trading.Services.Interfaces.WhosWhoNS;
using Trading.Services.Interfaces.Documents;
using Trading.Services.Interfaces.Imports;
using Trading.Services.Interfaces.MechanicalFaults;
using Trading.Services.Interfaces.Movements;
using Trading.Services.MechanicalFaults;
using Trading.Services.Movements;
using Trading.Services.Classes;
using Trading.Services.Classes.SuiteCRM;
using Trading.Services.Classes.Telephony;
using Trading.Services.Interfaces.Maps;
using Trading.Services.Classes.Maps;
using Trading.Services.Classes.AI;
using Trading.Services.InspectCollect.Interfaces;
using Trading.Services.InspectCollect.Classes.DotAdmin;
using Trading.Services.InspectCollect.Classes.VehicleLookup;
using Trading.Services.Interfaces.AI;
using Trading.Services.Classes.DVLA;
using Trading.Services.Interfaces.DVLA;
using Trading.Services.Interfaces.LambdaFunctions;
using Trading.Services.Classes.LambdaFunctions;
using Stripe;
using FileService = Trading.Services.Classes.FileService;
using InvoiceService = Trading.Services.Classes.InvoiceService;
using CustomerService = Trading.Services.Classes.CustomerService;
using ProductService = Trading.Services.Classes.ProductService;
using TaxService = Trading.Services.Classes.TaxService;
using Trading.Services.InspectCollect.Interfaces;
using Trading.Services.InspectCollect.Classes;
using Trading.Services.Classes.ExtLeads;
using Trading.Services.Interfaces.ExtLeads;
using Trading.Services.InspectCollect.Classes.VehicleLookup;
using Trading.Services.Interfaces.APIKeyAuth;
using Trading.Services.Classes.APIKeyAuth;
using Trading.Services.Interfaces.InventImports;
using Trading.Services.Classes.InventImports;
using Trading.Services.Interfaces.AutoTrader;
using Trading.Services.Classes.AutoTrader;

namespace Trading.API.Remarq.ServiceConfiguration
{
  public static partial class ServiceConfigurationExtensions
  {
    //public static void RegisterScopedCRUD<T, TEntityDTO>(this IServiceCollection services) where T : BaseModelEntity
    //{
    //services.AddScoped<ICrudReadService<T, TEntityDTO>, CrudReadService<T, TEntityDTO>>();
    //services.AddScoped<ICrudWriteService<T, TEntityDTO>, CrudWriteService<T, TEntityDTO>>();
    //services.AddScoped<ICrudService<T, TEntityDTO>, CrudService<T, TEntityDTO>>();
    //}

    public static void RegisterModuleServices(this IServiceCollection services)
    {
      //// add scoped DI service for all types in the specified assembly
      AddModuleServices(services, "Trading.Services.SiteScan");
      AddModuleServices(services, "Trading.Services.LeadCRM");
      AddModuleServices(services, "Trading.Services.Valuations");
      AddModuleServices(services, "Trading.Services.UInspection");
      AddModuleServices(services, "Trading.Services.External");
      AddModuleServices(services, "Trading.Services.InspectCollect");

      OverrideSingletonServices(services);

      // add module controllers
      AddControllers(services, "Trading.API.LeadCRM");
      AddControllers(services, "Trading.API.SiteScan");
      AddControllers(services, "Trading.API.UInspection");
      AddControllers(services, "Trading.API.Valuations");
      AddControllers(services, "Trading.API.External");
      AddControllers(services, "Trading.API.InspectCollect");
    }

    private static void AddModuleServices(IServiceCollection services, string assemblyName)
    {
      var assembly = Assembly.Load(assemblyName);

      services.RegisterAssemblyPublicNonGenericClasses(assembly)
        .Where(c => c.Name.EndsWith("Service"))
        .AsPublicImplementedInterfaces();
    }

    private static void OverrideSingletonServices(IServiceCollection services)
    {
      // Remove the existing scoped registration and add as singleton
      var serviceToOverride = services.FirstOrDefault(s => s.ServiceType == typeof(ICAutoTraderSettingsInterface));
      if (serviceToOverride != null)
      {
        services.Remove(serviceToOverride);
        services.AddSingleton<ICAutoTraderSettingsInterface, ICAutoTraderSettingsService>();
      }
    }

    private static void AddControllers(IServiceCollection services, string assemblyName)
    {
      //var assembly = aType.Assembly;
      var assembly = Assembly.Load(assemblyName);

      services.AddControllers()
        .AddNewtonsoftJson(options =>
        {
          options.SerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Ignore;
          options.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;
        })
        .AddJsonOptions(options =>
        {
          options.JsonSerializerOptions.PropertyNameCaseInsensitive = false;
          options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
        })
        .PartManager.ApplicationParts.Add(new AssemblyPart(assembly))
        ;
    }

    public static void RegisterCorePlatformServices(this IServiceCollection services, IConfiguration configuration)
    {
      //services.AddScoped<IBIGImportService, BIGImportService>();
      //services.AddScoped<IImportProvider, BIGImportProvider>();
      //services.AddScoped<IImportLoggingService, ImportLoggingService>();

      services.AddScoped<IAdvertService, AdvertService>();
      services.AddScoped<IAdvertSearchService, AdvertSearchService>();
      services.AddScoped<IAltService, AltService>();
      services.AddScoped<IAttribService, AttribService>();
      services.AddScoped<IAttribvalService, AttribvalService>();
      services.AddScoped<IAddressService, AddressService>();
      services.AddScoped<ICountryService, CountryService>();
      services.AddScoped<IVehicleMediaService, VehicleMediaService>();
      services.AddScoped<IAdviewService, AdviewService>();
      services.AddScoped<IAppraisalService, AppraisalService>();
      services.AddScoped<IBidService, BidService>();
      services.AddScoped<ICustomerNoteService, CustomerNoteService>();
      services.AddScoped<IStatService, StatService>();
      services.AddScoped<ISiteService, SiteService>();
      services.AddScoped<IUtilityService, UtilityService>();
      services.AddScoped<IVehicleTypeService, VehicleTypeService>();
      services.AddScoped<ICognitoManagementService, CognitoManagementService>();
      services.AddScoped<IContactActionService, ContactActionService>();
      services.AddScoped<IContactService, ContactService>();
      services.AddScoped<IContactAttribService, ContactAttribService>();
      services.AddScoped<IInMailService, InMailService>();
      services.AddScoped<IContactRoleService, ContactRoleService>();
      services.AddSingleton<IMessageService, MessageService>();
      services.AddScoped<ICustomerService, CustomerService>();
      services.AddScoped<ICustomerAttribService, CustomerAttribService>();
      services.AddScoped<ICustomerMediaService, CustomerMediaService>();
      services.AddScoped<IDealService, DealService>();
      services.AddScoped<ILookupService, LookupService>();
      services.AddScoped<IOfferService, OfferService>();
      services.AddScoped<IProductService, ProductService>();
      services.AddScoped<ICountryProductService, CountryProductService>();
      services.AddScoped<ISphAllService, SphAllService>();
      services.AddScoped<IPlatformService, PlatformService>();
      services.AddScoped<IRateCardService, RateCardService>();
      services.AddScoped<IRoleService, RoleService>();
      services.AddScoped<ISaleService, SaleService>();
      services.AddScoped<ISavedSearchService, SavedSearchService>();
      services.AddScoped<IStatusService, StatusService>();
      services.AddScoped<ITaxService, TaxService>();

      services.AddScoped<IVehicleMediaService, VehicleMediaService>();
      services.AddScoped<IVehicleService, VehicleService>();
      services.AddScoped<IWatchlistService, WatchlistService>();
      services.AddScoped<IDashboardService, DashboardService>();
      services.AddScoped<IModelService, ModelService>();
      services.AddScoped<IMakeService, MakeService>();
      services.AddScoped<IMergeService, MergeService>();
      services.AddScoped<IDerivService, DerivService>();
      services.AddScoped<IBodyTypeService, BodyTypeService>();
      services.AddScoped<IVehicleColourService, VehicleColourService>();
      services.AddScoped<IFuelTypeService, FuelTypeService>();
      services.AddScoped<ITransmissionTypeService, TransmissionTypeService>();
      services.AddTransient<IDVLAService, DVLAService>();

      services.AddTransient<IEmailService, SIBEmailService>();

      services.AddSingleton<IFileService, FileService>();
      services.AddScoped<IUserService, UserService>();
      services.AddScoped<IInvoiceService, InvoiceService>();
      services.AddScoped<IRostrumMessageService, RostrumMessageService>();

      services.AddScoped<IYTService, YTService>();
      services.AddScoped<ITestService, TestService>();

      services.AddScoped<IXeroService, XeroService>();

      /* STRIPE */

      services.AddSingleton<IStripeClient>(sp =>
          new StripeClient(configuration.GetSection("Stripe:SecretKey").Value));

      // Register Stripe services as transient, since they need StripeClient injected
      services.AddTransient<PaymentIntentService>(sp =>
          new PaymentIntentService(sp.GetRequiredService<IStripeClient>()));
      services.AddTransient<SetupIntentService>(sp =>
          new SetupIntentService(sp.GetRequiredService<IStripeClient>()));
      services.AddTransient<Stripe.CustomerService>(sp =>
          new Stripe.CustomerService(sp.GetRequiredService<IStripeClient>()));
      services.AddTransient<Stripe.InvoiceService>(sp =>
          new Stripe.InvoiceService(sp.GetRequiredService<IStripeClient>()));

      services.AddTransient<Stripe.PaymentMethodService>(sp =>
          new Stripe.PaymentMethodService(sp.GetRequiredService<IStripeClient>()));

      services.AddScoped<IStripeService, StripeService>();

      /*   */

      services.AddScoped<ICustomerOrderService, CustomerOrderService>();
      services.AddScoped<IAdminTaskService, AdminTaskService>();
      services.AddScoped<ISMSService, SMSService>();

      services.AddScoped<IChartDataService, ChartDataService>();
      services.AddSingleton<IDataCacheService, DataCacheService>();
      services.AddScoped<INegotiationService, NegotiationService>();
      services.AddScoped<ISaleProfileService, SaleProfileService>();
      services.AddScoped<IVehicleAttribService, VehicleAttribService>();
      services.AddScoped<IMovementService, MovementService>();
      services.AddScoped<IMovementAddressService, MovementAddressService>();
      services.AddScoped<IVehicleValueService, VehicleValueService>();

      services.AddScoped<ICommsTemplateService, CommsTemplateService>();
      services.AddScoped<ICommsEventService, CommsEventService>();
      services.AddScoped<ICommsHistoryService, CommsHistoryService>();
      services.AddScoped<ICommsTemplateDeliveryService, CommsTemplateDeliveryService>();

      services.AddScoped<IAuthService, AuthService>();

      services.AddScoped<ISimpleImportService, SimpleImportService>();

      services.AddScoped<IVehicleCheckService, VehicleCheckService>();
      services.AddScoped<IDocumentService, DocumentService>();

      services.AddScoped<IApiKeyService, ApiKeyService>();
      services.AddScoped<IVehicleFaultService, VehicleFaultService>();
      services.AddScoped<IStaffNotificationService, StaffNotificationService>();


      var useAzureStorage = Convert.ToBoolean(configuration.GetSection("ServiceConfig:UseAzureStorage").Value);

      if (useAzureStorage)
      {
        services.AddTransient<IFileStorageService, AzureFileStorageService>();
      }
      else
      {
        services.AddTransient<IFileStorageService, FileStorageService>(); // AWS S3
      }

      var vehicleLookup = configuration.GetSection("ServiceConfig:VehicleLookup").Value;

      // when using other providers, register here
      if (vehicleLookup == "CapHPI")
      {
        services.AddScoped<IVRMLookupService, CapHpiVehicleLookupService>();
      }
      else
      {
        services.AddScoped<IVRMLookupService, UKVehicleDataService>();
      }

      // add USA vehicle lookup service (will be part of the IVRMLookupService collection)
      // default will be last one added below, so to choose the provider use a collection in the controller 
      services.AddScoped<IVRMLookupService, PlateToVinVehicleLookupService_USA>();

      services.AddScoped<IWhosWhoService, WhosWhoService>();
      services.AddScoped<ICustomerInternalInfoService, CustomerInternalInfoService>();
      services.AddScoped<ISearchService, SearchService>();
      services.AddScoped<IBrokerageService, BrokerageService>();
      services.AddScoped<IProspectService, ProspectService>();
      services.AddScoped<INotificationService, NotificationService>();
      services.AddScoped<IAdvertNoteService, AdvertNoteService>();
      services.AddScoped<IVOIPService, ZoomVOIPService>();
      services.AddScoped<ISuiteCRMService, SuiteCRMService>();

      services.AddScoped<ITermsTemplateService, TermsTemplateService>();
      services.AddScoped<IGooglePlacesService, GooglePlacesService>();
      services.AddScoped<IAIService, PerplexityAIService>();

      services.AddScoped<IServiceQueueService, ServiceQueueService>();

      services.AddScoped<IAICacheService, AICacheService>();

      services.AddScoped<IDVLAClient, DVLAClient>();

      services.AddSingleton<BackgroundTaskService>();
      services.AddHostedService(provider => provider.GetRequiredService<BackgroundTaskService>());

      services.AddScoped<ILambdaFunctionsService, LambdaFunctionsService>();
      services.AddScoped<IExtLeadService, ExtLeadService>();
      services.AddScoped<IExtLeadVehicleService, ExtLeadVehicleService>();

      /* Inspect & Collect */
      services.AddScoped<ICLayoutInterface, ICLayoutService>();
      services.AddScoped<ICOutcomeInterface, ICOutcomeService>();
      services.AddScoped<IZohoCampaignService, ZohoCampaignService>();

      services.AddHttpClient<IAuctionApiClient, AuctionApiClient>();

      // Register services
      services.AddScoped<IAuctionApiClient, AuctionApiClient>();
      services.AddScoped<IAuctionVehicleMappingService, AuctionVehicleMappingService>();
      services.AddScoped<IAuctionImportService, AuctionImportService>();

      services.AddHttpClient<IAutoTraderClient, AutoTraderClient>();
      services.AddScoped<IAutoTraderClient, AutoTraderClient>();

      services.AddScoped<IAutoTraderService, AutoTraderService>();
      //services.AddScoped<IAutoTraderService, DummyAutoTraderService>();

      // Register HttpClient for ICCapHPIVehicleLookupService
      services.AddHttpClient<ICCapHPIVehicleLookupService>();

      services.AddScoped<ICVehicleDataInterface, ICVehicleDataService>();

      // Register dotAdmin services (part of InspectCollect)
      services.AddHttpClient<IDotAdminClient, DotAdminClient>();
      services.AddScoped<IDotAdminClient, DotAdminClient>();
      services.AddScoped<IDotAdminService, DotAdminService>();

      services.AddScoped<IInventInspectionPDFExtractorInterface, InventInspectionPDFExtractorService>();
    }
  }
}
