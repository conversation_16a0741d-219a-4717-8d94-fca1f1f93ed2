﻿﻿using Trading.API.Data.DTO.InspectCollect.VehicleLookup;

namespace Trading.Services.InspectCollect.Tests.Helpers
{
    public static class TestDataHelpers
    {
        public static ICVehicleEnquiryDTO CreateValidEnquiryWithVRM(string vrm = "TEST123", Guid? responseId = null, uint? odometer = null)
        {
            return new ICVehicleEnquiryDTO
            {
                ResponseId = responseId ?? Guid.NewGuid(),
                VRM = vrm,
                VIN = null,
                Odometer = odometer ?? 50000,
                IgnoreCache = false
            };
        }

        public static ICVehicleEnquiryDTO CreateValidEnquiryWithVIN(string vin = "TESTVIN123456789", Guid? responseId = null, uint? odometer = null)
        {
            return new ICVehicleEnquiryDTO
            {
                ResponseId = responseId ?? Guid.NewGuid(),
                VRM = null,
                VIN = vin,
                Odometer = odometer ?? 50000,
                IgnoreCache = false
            };
        }

        public static ICVehicleEnquiryDTO CreateValidEnquiryWithBoth(string vrm = "TEST123", string vin = "TESTVIN123456789", Guid? responseId = null, uint? odometer = null)
        {
            return new ICVehicleEnquiryDTO
            {
                ResponseId = responseId ?? Guid.NewGuid(),
                VRM = vrm,
                VIN = vin,
                Odometer = odometer ?? 50000,
                IgnoreCache = false
            };
        }

        public static ICVehicleEnquiryDTO CreateInvalidEnquiry(Guid? responseId = null)
        {
            return new ICVehicleEnquiryDTO
            {
                ResponseId = responseId ?? Guid.NewGuid(),
                VRM = null,
                VIN = null,
                Odometer = 50000,
                IgnoreCache = false
            };
        }

        public static ICVehicleEnquiryDTO? CreateNullEnquiry()
        {
            return null;
        }

        public static ICVehicleEnquiryDTO CreateEnquiryWithEmptyStrings(Guid? responseId = null)
        {
            return new ICVehicleEnquiryDTO
            {
                ResponseId = responseId ?? Guid.NewGuid(),
                VRM = "",
                VIN = "",
                Odometer = 50000,
                IgnoreCache = false
            };
        }

        public static ICVehicleEnquiryDTO CreateEnquiryWithWhitespace(Guid? responseId = null)
        {
            return new ICVehicleEnquiryDTO
            {
                ResponseId = responseId ?? Guid.NewGuid(),
                VRM = "   ",
                VIN = "   ",
                Odometer = 50000,
                IgnoreCache = false
            };
        }

        public static ICVehicleEnquiryDTO CreateEnquiryWithNegativeOdometer(string vrm = "TEST123", Guid? responseId = null)
        {
            return new ICVehicleEnquiryDTO
            {
                ResponseId = responseId ?? Guid.NewGuid(),
                VRM = vrm,
                VIN = null,
                Odometer = null, // This will be converted to 0 in the service
                IgnoreCache = false
            };
        }
    }
}
