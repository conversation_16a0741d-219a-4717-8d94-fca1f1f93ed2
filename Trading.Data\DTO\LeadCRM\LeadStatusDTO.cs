﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.LeadCRM
{
  public class LeadStatusDTO : BaseModelEntityIntDTO
  {
    public string StatusName { get; set; }
    public uint LeadProductId { get; set; }

    public LeadProductDTO LeadProduct { get; set; }

    public byte Sequence { get; set; }
    public bool TerminatesLead { get; set; }

    public string StatusRGB { get; set;}


  }
}
