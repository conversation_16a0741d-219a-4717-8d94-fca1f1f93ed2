﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/saleSearchProfile")]
  [ApiController]
  [Authorize]
  public class SaleSearchProfileController : ControllerBase
  {
    private readonly ISaleProfileService _saleProfileService;

    public SaleSearchProfileController(ISaleProfileService saleProfileService)
    {
      this._saleProfileService = saleProfileService;
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> AddSearchProfile([FromBody] SaleSearchProfileDTO dto, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin() && !User.IsAuctionAdmin())
      {
        return Forbid();
      }

      try
      {
        await _saleProfileService.AddSaleSearchProfile(dto, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpDelete]
    [Route("{saleSearchProfileId}")]
    public async Task<IActionResult> DeleteSaleSearchProfile(Guid saleSearchProfileId, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin() && !User.IsAuctionAdmin())
      {
        return Forbid();
      }

      try
      {
        await _saleProfileService.DeleteSaleSearchProfile(saleSearchProfileId, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpPost]
    [Route("createSale")]
    public async Task<IActionResult> CreateSaleFromProfile([FromBody] CreateSaleFromProfileDTO dto, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin() && !User.IsAuctionAdmin())
      {
        return Forbid();
      }

      try
      {
        var sale = await _saleProfileService.CreateSaleFromProfile(dto, cancellationToken);
        return Ok(sale);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

  }
}
