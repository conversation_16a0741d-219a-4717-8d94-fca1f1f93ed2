﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using Dapper;
using DocumentFormat.OpenXml.EMMA;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Components.AdvertView;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.API.Data.Models.DTO;
using Trading.Services.Extensions;
using Trading.Services.ExternalDTO;
using Trading.Services.Helpers;
using Trading.Services.Interfaces;
using Trading.Services.Interfaces.DVLA;
using static Trading.Services.DTOMappingProfiles.MappingProfile;

namespace Trading.Services.Classes
{
  public class VehicleService : IVehicleService
  {
    private readonly TradingContext _tradingContext;
    private readonly IDbConnection _db;
    private readonly IMapper _mapper;
    private readonly IFileStorageService _fileStorageService;
    private readonly ILookupService _lookupService;
    private readonly IDVLAService _dvlaService;
    private readonly IAppraisalService _appraisalService;
    private readonly IUserService _userService;
    private readonly IMessageService _messageService;
    private readonly IEnumerable<IVRMLookupService> _vrmLookups;
    private readonly IVehicleCheckService _vehicleCheckService;
    private readonly IVehicleMediaService _vehicleMediaService;
    private readonly IYTService _ytService;


    public VehicleService(
      TradingContext tradingContext,
      IDbConnection dbConnection,
      IMapper mapper,
      IFileStorageService fileStorageService,
      IVehicleMediaService vehicleMediaService,
      ILookupService lookupService,
      IConfiguration configuration,
      IDVLAService dvlaService,
      IAppraisalService appraisalService,
      IUserService userService,
      IYTService ytService,
      IMessageService messageService,
      IEnumerable<IVRMLookupService> vrmLookups,
      IVehicleCheckService vehicleCheckService)
    {
      _tradingContext = tradingContext;
      _mapper = mapper;
      _fileStorageService = fileStorageService;
      _lookupService = lookupService;
      _dvlaService = dvlaService;
      _appraisalService = appraisalService;
      _userService = userService;
      _ytService = ytService;
      _messageService = messageService;
      _vrmLookups = vrmLookups;
      _vehicleCheckService = vehicleCheckService;
      _vehicleMediaService = vehicleMediaService;
      _db = dbConnection;
    }

    private struct ECodes
    {
      public static ExceptionData NoVehicle = new ExceptionData("VE1", "Could not find specified vehicle");
      public static ExceptionData Permissions = new ExceptionData("VE2", "Do not have permissions to edit vehicle");
    }


    public async Task<VehicleDTO> ViewVehicle(VehicleDTO vehicleDTO, CancellationToken cancellationToken)
    {
      VehicleDTO returnDTO = new VehicleDTO();

      var vehicle = await _tradingContext.Vehicles.Where(
              x => vehicleDTO.Vrm == x.Vrm
              && x.StatusId == (int)StatusEnum.Active)
              .ToListAsync(cancellationToken);

      returnDTO = _mapper.Map<VehicleDTO>(vehicle);
      return returnDTO;
    }

    public async Task<VehicleDTO> GetVehicle(Guid id, CancellationToken cancellationToken)
    {
      var entity = await _tradingContext.Vehicles
        .Include(x => x.Make)
        .Include(x => x.Model)
        .Include(x => x.Deriv)
        .Include(x => x.BodyType)
        .Include(x => x.FuelType)
        .Include(x => x.TransmissionType)
        .Include(x => x.Address)
        .Include(x => x.Status)
        .Include(x => x.Plate)
        .Include(x => x.Tyres)
        //.Include(x => x.MOTHistory).ThenInclude(x => x.MOTItems)
        .Include(x => x.Appraisals).ThenInclude(x => x.AppraisalItems).ThenInclude(x => x.AppraisalMedia.Where(x => x.StatusId == (int)StatusEnum.Active))
        .Include(x => x.Appraisals).ThenInclude(x => x.AppraisalItems).ThenInclude(x => x.BodyPart).ThenInclude(x => x.BodyPartGroup)
        .Include(x => x.Appraisals).ThenInclude(x => x.AppraisalItems).ThenInclude(x => x.Damage)
        .Include(x => x.Appraisals).ThenInclude(x => x.AppraisalItems).ThenInclude(x => x.DamageDetail)
        .Include(x => x.Appraisals).ThenInclude(x => x.AppraisalItems).ThenInclude(x => x.DamageSeverity)
        .Include(x => x.VehicleColour)

        // all vehicle media should be retrieved independently of the vehicle, since updates to media are realtime and don't require saving draft in advert screen
        .Include(x => x.VehicleMedia.Where(m => m.StatusId == (uint)StatusEnum.Active || m.StatusId == (uint)StatusEnum.Pending))
        .Include(x => x.VehicleMedia).ThenInclude(x => x.Media)
        .Include(x => x.VehicleMedia).ThenInclude(x => x.MediaCategory)

        .Include(x => x.VehicleAttribs).ThenInclude(x => x.Attrib)
        .Include(x => x.VehicleAttribs).ThenInclude(x => x.Attribval)
        .Include(x => x.ServiceHistories.Where(x => x.StatusId == (int)StatusEnum.Active))
        .AsNoTracking()
        .AsSplitQuery()
        .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);

      if (entity == null)
        throw new ApplicationException($"Could not find vehicle with id: {id}");

      var vehicle = _mapper.Map<VehicleDTO>(entity);

      return vehicle;
    }

    public async Task<VehicleListDTO> GetActiveVehicles(VehicleListDTO vehicleListDTO, CancellationToken cancellationToken)
    {
      VehicleListDTO returnDTO = new VehicleListDTO();

      var list = await _tradingContext.Vehicles.Where(x => vehicleListDTO.vehicles.Contains(x.Vrm)
          && (x.StatusId == (int)StatusEnum.Active || x.StatusId == (int)StatusEnum.Pending))
        .Select(x => x.Vrm).ToListAsync(cancellationToken);

      returnDTO.vehicles = list;
      return returnDTO;
    }


    public async Task<IEnumerable<VehicleDTO>> GetAllByCustomer(Guid customerId, CancellationToken cancellationToken)
    {
      var list = await _tradingContext.Vehicles
        .Include(x => x.Status)
        .Include(x => x.Make)
        .Include(x => x.Model)
        .Include(x => x.Deriv)
        .Where(x => x.CustomerId == customerId
          &&
          (x.StatusId == (int)StatusEnum.Active || x.StatusId == (int)StatusEnum.Pending)).ToListAsync(cancellationToken);

      var dtos = _mapper.Map<IEnumerable<Vehicle>, IEnumerable<VehicleDTO>>(list);

      return dtos;
    }

    public async Task DeleteVehicle(Guid customerId, Guid vehicleId, CancellationToken cancellationToken)
    {
      // only the highest role can permanently delete a vehicle and attendant data (advert etc.)
      if (!_userService.IsGod())
      {
        throw new Exception("You do not have permission to delete this vehicle");
      }

      var vehicle = await _tradingContext.Vehicles
        .Include(x => x.Adverts)
        .Include(x => x.Appraisals).ThenInclude(x => x.AppraisalItems).ThenInclude(x => x.AppraisalMedia)
        .Include(x => x.VehicleAttribs)
        .Include(x => x.VehicleMedia)
        .FirstOrDefaultAsync(x => x.Id == vehicleId);
      if (vehicle != null)
      {
        // remove s3 files (vehicleMedia, appraisalMedia, logBook, serviceBook)
        await RemoveVehicleMediaFromS3(customerId, vehicle, cancellationToken);

        // remove advert 
        var ad = await _tradingContext.Adverts.FirstOrDefaultAsync(x => x.VehicleId == vehicleId);
        if (ad != null)
        {
          ad.TopBidId = null;
          ad.TopBidGuid = null;

          var bids = await _tradingContext.Bids.Where(x => x.AdvertId == ad.Id).ToListAsync();
          _tradingContext.Bids.RemoveRange(bids);

          var inMails = await _tradingContext.InMails.Where(x => x.AdvertId == ad.Id).ToListAsync();
          _tradingContext.InMails.RemoveRange(inMails);

          var deals = await _tradingContext.Deals.Where(x => x.AdvertId == ad.Id).ToListAsync();
          foreach (var deal in deals)
          {
            var orders = await _tradingContext.CustomerOrders.Where(x => x.DealId == deal.Id).ToListAsync();
            _tradingContext.CustomerOrders.RemoveRange(orders);
          }

          _tradingContext.Deals.RemoveRange(deals);

          await _tradingContext.SaveChangesAsync();

          var adViewsA = await _tradingContext.Adviews.Where(x => x.AdvertId == ad.Id).ToListAsync();
          _tradingContext.RemoveRange(adViewsA);

          var watchlistsA = await _tradingContext.Watchlists.Where(x => x.AdvertId == ad.Id).ToListAsync();
          _tradingContext.RemoveRange(watchlistsA);

          _tradingContext.Adverts.Remove(ad);
        }

        await _tradingContext.SaveChangesAsync();

        await _vehicleCheckService.RemoveAllVehicleChecks(vehicleId);

        var adViewsB = await _tradingContext.Adviews.Where(x => x.VehicleId == vehicleId).ToListAsync();
        _tradingContext.RemoveRange(adViewsB);

        var watchlistsB = await _tradingContext.Watchlists.Where(x => x.VehicleId == vehicleId).ToListAsync();
        _tradingContext.RemoveRange(watchlistsB);

        var leadVehicles = await _tradingContext.LeadVehicles.Where(x => x.VehicleId == vehicle.Id).ToListAsync();
        foreach (var leadVehicle in leadVehicles)
        {
          var leadAppraisals = await _tradingContext.LeadVehicleAppraisals.Where(x => x.LeadVehicleId == leadVehicle.Id).ToListAsync();
          _tradingContext.RemoveRange(leadAppraisals);
        }

        var serviceQueues = await _tradingContext.ServiceQueues.Where(x => x.VehicleId == vehicleId).ToListAsync();
        _tradingContext.RemoveRange(serviceQueues);

        var inventAuctionLot = await _tradingContext.InventAuctionLots.Where(x => x.VehicleId == vehicleId).ToListAsync();
        _tradingContext.RemoveRange(inventAuctionLot);

        await _tradingContext.SaveChangesAsync();

        // this removes all attendant data as well
        _tradingContext.Vehicles.Remove(vehicle);

        await _tradingContext.SaveChangesAsync(cancellationToken);
      }
    }

    private async Task RemoveVehicleMediaFromS3(Guid customerId, Vehicle vehicle, CancellationToken cancellationToken)
    {
      // remove vehicleMedia
      foreach (var media in vehicle.VehicleMedia)
      {
        string key = URLHelper.VehicleMediaKey(
         new VehicleMediaURLDTO() { CustomerId = customerId, VehicleId = vehicle.Id, VehicleMediaId = media.Id }
       );

        await _fileStorageService.RemoveFileFromStorage(key, cancellationToken);
      }

      // remove appraisalMedia
      foreach (var appraisal in vehicle.Appraisals)
      {
        var vehicleId = vehicle.Id;
        var appraisalId = appraisal.Id;

        foreach (var appraisalItem in appraisal.AppraisalItems)
        {
          var appraisalItemId = appraisalItem.Id;

          foreach (var appraisalMedia in appraisalItem.AppraisalMedia)
          {
            var appraisalMediaId = appraisalMedia.Id;

            string key = URLHelper.AppraisalMediaKey(new AppraisalMediaURLDTO()
            {
              CustomerId = customerId,
              VehicleId = vehicleId,
              AppraisalId = appraisalId,
              AppraisalItemId = appraisalItemId,
              AppraisalMediaId = appraisalMediaId
            });

            await _fileStorageService.RemoveFileFromStorage(key, cancellationToken);
          }
        }
      }
    }

    public async Task<IEnumerable<VehicleStatusCheckDTO>> VehicleStatusCheck(VehicleStatusSearchDTO searchDTO, CancellationToken cancellationToken)
    {
      var whereClauses = new List<string>();

      if (!string.IsNullOrEmpty(searchDTO.VRM))
      {
        whereClauses.Add("Vehicle.vrm LIKE @VRM");
      }

      if (searchDTO.CustomerId.HasValue)
      {
        whereClauses.Add("Vehicle.CustomerId = UUID_TO_BIN(@CustomerId)");
      }

      if (searchDTO.Relistable.HasValue && searchDTO.Relistable.Value)
      {
        whereClauses.Add(GetVehicleCheckFilter() + " AND Advert.AdvertStatus <> 1");
      }

      if (searchDTO.NoMOT.HasValue && searchDTO.NoMOT.Value)
      {
        whereClauses.Add("MOTHistory.Id IS NULL AND Advert.AdvertStatus = 1");
      }

      var whereClause = whereClauses.Any() ? "WHERE " + string.Join(" AND ", whereClauses) : "";
      var offset = (searchDTO.Page - 1) * searchDTO.PerPage;

      var sql = $@"WITH RankedMOTHistory AS (
        SELECT 
            Vehicle.Id AS Id, 
            Vehicle.Vrm, 
            Vehicle.Added AS VehicleAdded,
            Make.Id AS MakeId, 
            Make.MakeName, 
            Model.Id AS ModelId, 
            Model.ModelName, 
            Deriv.Id AS DerivId, 
            Deriv.DerivName, 
            VehicleType.Id AS VehicleTypeId, 
            VehicleType.VehicleTypeName, 
            Plate.Id AS PlateId, 
            Plate.PlateName, 
            TransmissionType.Id AS TransmissionTypeId, 
            TransmissionType.TransmissionTypeName, 
            BodyType.Id AS BodyTypeId, 
            BodyType.BodyTypeName, 
            FuelType.Id AS FuelTypeId, 
            FuelType.FuelTypeName, 
            Customer.Id AS CustomerId, 
            Customer.CustomerName, 
            Advert.Id AS AdvertId, 
            Advert.AdvertStatus AS AdvertStatus, 
            Advert.AddressId AS AdvertAddressId, 
            Vehicle.AddressId AS VehicleAddressId, 
            Sale.Id AS SaleId, 
            Sale.StatusId AS sale_status_name, 
            SphLink.Id AS SphLinkId, 
            Address.Id AS AddressId, 
            Address.AddressName, 
            Platform.Id AS PlatformId, 
            Platform.PlatformName, 
            SaleType.id AS SaleTypeId, 
            SaleType.SaleTypeName, 
            MOTHistory.Id AS MotId, 
            ROW_NUMBER() OVER (PARTITION BY Vehicle.Id ORDER BY MOTHistory.Id) AS MotHistoryRank 
        FROM Vehicle 
        LEFT JOIN Make ON Make.Id = Vehicle.MakeId 
        LEFT JOIN Model ON Model.Id = Vehicle.ModelId 
        LEFT JOIN Deriv ON Deriv.Id = Vehicle.DerivId 
        LEFT JOIN VehicleType ON VehicleType.Id = Vehicle.VehicleTypeId 
        LEFT JOIN Plate ON Plate.Id = Vehicle.PlateId 
        LEFT JOIN TransmissionType ON TransmissionType.Id = Vehicle.TransmissionTypeId 
        LEFT JOIN BodyType ON BodyType.Id = Vehicle.BodyTypeId 
        LEFT JOIN FuelType ON FuelType.Id = Vehicle.FuelTypeId 
        LEFT JOIN Customer ON Customer.Id = Vehicle.CustomerId 
        LEFT JOIN Advert ON Advert.VehicleId = Vehicle.Id 
        LEFT JOIN SphLink ON SphLink.AdvertId = Advert.Id 
        LEFT JOIN Sale ON Sale.Id = Advert.SaleId 
        LEFT JOIN Address ON Address.Id = Sale.AddressId 
        LEFT JOIN Platform ON Platform.Id = Sale.PlatformId 
        LEFT JOIN SaleType ON SaleType.Id = Sale.SaleTypeId 
        LEFT JOIN MOTHistory ON Vehicle.Vrm = MOTHistory.Vrm
        {whereClause}
      )
      SELECT 
          Id, Vrm, MakeId, MakeName, ModelId, ModelName, DerivId, DerivName, 
          VehicleTypeId, VehicleTypeName, PlateId, PlateName, TransmissionTypeId, 
          TransmissionTypeName, BodyTypeId, BodyTypeName, FuelTypeId, FuelTypeName, 
          CustomerId, CustomerName, AdvertId, AdvertStatus, AdvertAddressId, 
          VehicleAddressId, SaleId, sale_status_name, SphLinkId, AddressId, 
          AddressName, PlatformId, PlatformName, SaleTypeId, SaleTypeName, MotId 
      FROM RankedMOTHistory 
      WHERE (MotHistoryRank = 1 OR MotId IS NULL)
      ORDER BY VehicleAdded DESC 
      LIMIT {offset}, {searchDTO.PerPage + 1}";

      var parameters = new DynamicParameters();
      if (!string.IsNullOrEmpty(searchDTO.VRM))
      {
        parameters.Add("@VRM", $"%{searchDTO.VRM}%");
      }
      if (searchDTO.CustomerId.HasValue)
      {
        parameters.Add("@CustomerId", searchDTO.CustomerId.Value);
      }

      var vehicles = await _db.QueryAsync<VehicleStatusCheckDTO>(sql, parameters);

      return vehicles;
    }

    private string GetVehicleCheckFilter()
    {
      /*
      AND Vehicle.ModelId Is Not Null 
AND Vehicle.DerivId Is Not Null 
AND Vehicle.VehicleTypeId Is Not Null 
AND Vehicle.PlateId Is Not Null 
AND Vehicle.TransmissionTypeId Is Not Null
AND Vehicle.BodyTypeId Is Not Null
AND Vehicle.FuelTypeId Is Not Null 
AND Vehicle.AddressId Is Not Null 
AND Vehicle.CustomerId Is Not Null 
AND Advert.Id Is Not Null 
AND Advert.AddressId Is Not Null 
AND Sale.Id Is Not Null 
AND SphLink.Id Is Not Null 
AND Platform.Id Is Not Null 
AND SaleType.Id Is Not Null 
      */

      List<string> filterCols = new List<string>
      {
        "Vehicle.MakeId",
        "Vehicle.ModelId",
        "Vehicle.DerivId",
        "Vehicle.VehicleTypeId",
        "Vehicle.PlateId",
        "Vehicle.TransmissionTypeId",
        "Vehicle.BodyTypeId",
        "Vehicle.FuelTypeId",
        "Vehicle.AddressId",
        "Vehicle.CustomerId",
        "Advert.Id",
        "Advert.AddressId",
        "Sale.Id",
        "SphLink.Id",
        "Platform.Id",
        "SaleType.Id"
      };

      // construct the sql filter to return only vehicles passing all checks except adStatus
      string filter = string.Join(" AND ", filterCols.Select(column => $"{column} Is Not Null"));

      return filter;
    }

    public async Task<IEnumerable<VehicleDTO>> Search(VehicleSearchDTO vehicleSearchDTO, CancellationToken cancellationToken)
    {
      if (vehicleSearchDTO.Component == "vehicleHistory")
      {
        var vehicle = await GetVehicleHistory(vehicleSearchDTO, cancellationToken);

        var returnVehicles = new List<VehicleDTO>();
        returnVehicles.Add(vehicle);
        return returnVehicles;
      }

      var query = _tradingContext.Vehicles.AsQueryable();

      var allowedJoins = new List<string> {
        "Make",
        "Model",
        "Deriv",
        "Adverts",
        "Plate",
        "Customer",
        "BodyType",
        "FuelType",
        "TransmissionType",
      };

      if (vehicleSearchDTO.Includes != null)
      {
        // TODO: Put this in a common method
        foreach (var table in vehicleSearchDTO.Includes)
        {
          if (allowedJoins.Contains(table))
          {
            query = query.Include(table);
          }
        }
      }

      // TODO ADD POSSIBLE SEARCH CRITERIA
      if (vehicleSearchDTO.Filters.VehicleId != null)
      {

      }

      if (!string.IsNullOrEmpty(vehicleSearchDTO.Filters.vrm))
      {
        query = query.Where(x => x.Vrm == vehicleSearchDTO.Filters.vrm);
      }

      // TODO ADD ORDER BY AS A PARAMETER
      query = query.OrderByDescending(x => x.Added);

      var vehicles = await query.ToListAsync();

      var dtos = _mapper.Map<IEnumerable<Vehicle>, IEnumerable<VehicleDTO>>(vehicles);

      return dtos;
    }

    public async Task<VehicleDTO> UpdateVehicle(VehicleDTO dto, CancellationToken cancellationToken)
    {
      var mapped = _mapper.Map<Vehicle>(dto);

      try
      {
        mapped.MileageRangeId = await _lookupService.GetMileageRangeId(dto.Odometer.Value, cancellationToken);
        mapped.CapacityRangeId = await _lookupService.GetCapacityRangeId((uint)dto.EngineCc, cancellationToken);

        mapped.Updated = DateTime.Now;
        _tradingContext.Entry(mapped).State = EntityState.Modified;
        await _tradingContext.SaveChangesAsync(cancellationToken);

        return _mapper.Map<Vehicle, VehicleDTO>(mapped);
      }
      catch (DbUpdateConcurrencyException)
      {
        var dbSet = _tradingContext.Set<Vehicle>();
        var exists = await dbSet.AnyAsync(e => e == mapped);

        if (!exists)
        {
          throw new ApplicationException("A concurrency error prevented the vehicle being saved");
        }
        else
        {
          throw;
        }
      }
    }

    public async Task<VehicleDTO> AddVehicle(VehicleDTO dto, CancellationToken cancellationToken)
    {
      var mapped = _mapper.Map<Vehicle>(dto);
      _tradingContext.Vehicles.Add(mapped);

      await _tradingContext.SaveChangesAsync(cancellationToken);
      return _mapper.Map<VehicleDTO>(mapped);
    }


    public async Task UpdateAllDVLAData(CancellationToken cancellationToken)
    {
      // update dvla data for all existing vehicles (temp routine, delete after run)
      var vehicles = await _tradingContext.Vehicles.ToListAsync(cancellationToken);
      foreach (var vehicle in vehicles)
      {
        await GetOrCreateDVLAData(vehicle.Vrm, cancellationToken);
      }
    }

    public async Task<VehicleDTO> CreateVehicle(CreateVehicleDTO info, CancellationToken cancellationToken)
    {
      // manual check since a reference to advert service would cause a circular reference issue 
      // todo: potentially re-architect this aspect of the system 
      var alreadyListed = await IsVehicleForSale(info.Vrm);
      if (alreadyListed)
      {
        throw new ApplicationException("The specified VRM is already part of a live listing");
      }

      // if we have a scanVehicleId as part of the DTO, we need to set data from the scanned record in the vehicle
      // we also need to import all images associated with the scanned vehicle

      var contactId = _userService.GetContactId();
      await _messageService.SendContactMessage(contactId.Value, MessageAreaEnum.Adverts, MessageTypeEnum.CreateAdvertProgress, "Fetching DVLA data");

      // check if we have DVLA data for the specified VRM, if not create one (only one per vrm)
      var dvlaData = await GetOrCreateDVLAData(info.Vrm, cancellationToken);

      await _messageService.SendContactMessage(contactId.Value, MessageAreaEnum.Adverts, MessageTypeEnum.CreateAdvertProgress, "Checking for existing advert");

      // search the db for the vrm, if it exists return the existing record 
      var existing = await _tradingContext.Vehicles
        .AsNoTracking()
        .Where(x => (x.Vrm == info.Vrm || x.Vin == info.Vin) && x.CustomerId == info.CustomerId)
        .FirstOrDefaultAsync();

      if (existing != null)
      {
        // ensure vehicle is not part of an advert that is sold 
        await _messageService.SendContactMessage(contactId.Value, MessageAreaEnum.Adverts, MessageTypeEnum.CreateAdvertProgress, "Checking sold status");

        var isSold = info.CustomerId.HasValue ? await IsVehicleSold(info.Vrm, info.CustomerId.Value, cancellationToken) : false;

        if (!isSold)
        {
          return await GetVehicle(existing.Id, cancellationToken);
        }
      }

      await _messageService.SendContactMessage(contactId.Value, MessageAreaEnum.Adverts, MessageTypeEnum.CreateAdvertProgress, "Creating vehicle record");

      Vehicle vehicle = null;

      if (info.ScanVehicle != null)
      {
        try
        {
          var scanVehicle = info.ScanVehicle; // await _scanVehicleService.GetScannedVehicle(info.ImportScanVehicle, cancellationToken);

          vehicle = _mapper.Map<ScanVehicleDTO, Vehicle>(scanVehicle);

          // While we don't have vehicletypeId on scanvehicle (to be fixed)
          vehicle.VehicleTypeId = scanVehicle.Make.VehicleTypeId;
          vehicle.CustomerId = info.CustomerId.Value;
          vehicle.ContactId = info.ContactId;
          vehicle.DateOfReg = scanVehicle.DateRegistered;
          vehicle.Runner = true;

          // create dummy valuation record
          await _vehicleCheckService.CreateDummyValuationRecord();

          // set all rangeIds
          vehicle.MileageRangeId = await _lookupService.GetMileageRangeId(scanVehicle.Odometer.Value, cancellationToken);
          vehicle.CapacityRangeId = await _lookupService.GetCapacityRangeId((uint)scanVehicle.EngineCC, cancellationToken);

          sbyte seq = 0;

          foreach (var image in scanVehicle.ScanImages)
          {
            vehicle.VehicleMedia.Add(new VehicleMedia()
            {
              StatusId = (int)StatusEnum.Pending,
              MediaCategoryId = null,
              Sequence = seq++,
              MediaTypeId = (int)MediaTypeEnum.Image,
              Added = DateTime.Now,
              Updated = DateTime.Now,
              Media = new Media()
              {
                MediaURL = image.OriginalUrl,
                StatusId = (int)StatusEnum.Pending,
                Added = DateTime.Now,
                Updated = DateTime.Now,
              }
            });
          }

          await _tradingContext.SaveChangesAsync(cancellationToken);

          // assign first media record as primaryImageId
          if (!vehicle.PrimaryImageId.HasValue)
          {
            var firstImage = await _tradingContext.VehicleMedia.AsNoTracking().Where(x => x.VehicleId == vehicle.Id).OrderBy(x => x.Sequence).FirstOrDefaultAsync(cancellationToken);
            if (firstImage != null)
            {
              vehicle.PrimaryImageId = firstImage.Id;
            }
          }
        }
        catch (Exception ex)
        {
          Console.WriteLine(ex);
        }
      }
      else
      {
        VehicleLookupInfoDTO lookupData = await GetVehicleLookupInfoDTO(info, cancellationToken);

        if (string.IsNullOrEmpty(lookupData.MakeName))
        {
          lookupData.MakeName = dvlaData?.Make; // ensure minimum data to create vehicle
        }
        if (string.IsNullOrEmpty(lookupData.CO2))
        {
          lookupData.CO2 = dvlaData.Co2Emissions.ToString();
        }

        lookupData.LogBook = info.LogBook;

        // if we have no make name still, throw an exception 
        if (string.IsNullOrEmpty(lookupData.MakeName))
        {
          if (vehicle == null)
          {
            throw new ApplicationException("No vehicle make was provided");
          }
        }


        var vehicleDTO = await CreateVehicleDTO(lookupData, cancellationToken);
        vehicle = _mapper.Map<VehicleDTO, Vehicle>(vehicleDTO);
      }

      if (vehicle == null)
      {
        throw new ApplicationException("Vehicle data could not be found");
      }

      await AddMOTAndFinalizeVehicleDetails(dvlaData, vehicle, cancellationToken);

      await _messageService.SendContactMessage(contactId.Value, MessageAreaEnum.Adverts, MessageTypeEnum.CreateAdvertProgress, "Adding Provenance Data");

      await _tradingContext.SaveChangesAsync();

      await _messageService.SendContactMessage(contactId.Value, MessageAreaEnum.Adverts, MessageTypeEnum.CreateAdvertProgress, "Creating advert");

      // add an initial appraisal to the vehicle
      try
      {
        await _messageService.SendContactMessage(contactId.Value, MessageAreaEnum.Adverts,
          MessageTypeEnum.CreateAdvertProgress, "Initialising appraisal");

        var appDate = DateTime.Now.AddYears(-2);

        await _appraisalService.CreateAppraisal(
          new AppraisalDTO()
          { AppraisalDate = appDate, StatusId = (uint)StatusEnum.Active, VehicleId = vehicle.Id },
          cancellationToken);

        await _messageService.SendContactMessage(contactId.Value, MessageAreaEnum.Adverts,
          MessageTypeEnum.CreateAdvertProgress, "Launching advert");
      }
      catch (Exception ex)
      {
        Console.WriteLine(ex);
      }

      return await GetVehicle(vehicle.Id, cancellationToken);
    }

    public async Task<VehicleLookupInfoDTO> GetVehicleLookupInfoDTO(CreateVehicleDTO info, CancellationToken cancellationToken)
    {
      // get vehicle data from lookup 
      var lookup = _vrmLookups.GetVRMLookupService(info.Vrm);

      VRMLookupDataDTO lookupDataDTO = new VRMLookupDataDTO { vrm = info.Vrm, odometer = (uint?)info.Odometer };

      var lookupData = await lookup.GetVehicleData(lookupDataDTO, cancellationToken);
      if (lookupData == null)
      {
        lookupData = new VehicleLookupInfoDTO();
        lookupData.VRM = info.Vrm;
      }

      lookupData.VIN = info.Vin;

      if (info.Odometer.HasValue)
      {
        lookupData.Odometer = info.Odometer.Value.ToString();
      }

      lookupData.CustomerId = info.CustomerId;
      lookupData.ContactId = info.ContactId;
      lookupData.AddressId = info.AddressId;
      return lookupData;
    }

    public async Task AddMOTAndFinalizeVehicleDetails(DVLAData dvlaData, Vehicle vehicle, CancellationToken cancellationToken)
    {
      var contactId = vehicle.ContactId ?? _userService.GetContactId();

      // if vehicle is under 3 years, mot expiry should be 3 years from registration 
      if (!vehicle.DateOfReg.HasValue)
      {
        var dateStr = string.Format("{0}-01", dvlaData.MonthOfFirstRegistration);
        var regDate = DateTime.ParseExact(dateStr, "yyyy-MM-dd", CultureInfo.InvariantCulture);

        vehicle.DateOfReg = regDate;
      }

      DateTime true_age = DateTime.MinValue + (DateTime.Now - vehicle.DateOfReg.Value); // Minimum value as 1/1/1
      int yr = true_age.Year - 1;

      if (yr <= 3)
      {
        vehicle.MotExpires = vehicle.DateOfReg.Value.AddYears(3);
      }
      else
      {
        vehicle.MotExpires = dvlaData?.MotExpiryDate;
      }

      vehicle.DVLADataId = dvlaData?.Id;
      vehicle.Updated = DateTime.Now;

      await _messageService.SendContactMessage(contactId.Value, MessageAreaEnum.Adverts, MessageTypeEnum.CreateAdvertProgress, "Fetching MOT history");

      await UpdateMOTHistory(vehicle, cancellationToken);

      // save the vehicle to the db
      _tradingContext.Vehicles.Add(vehicle);
      await _tradingContext.SaveChangesAsync(cancellationToken);
    }


    public async Task<DVLAData> GetOrCreateDVLAData(string vrm, CancellationToken cancellationToken)
    {
      var dvlaData = await _dvlaService.GetDVLAVehicleData(vrm, cancellationToken);
      var dvlaEntity = _mapper.Map<DVLAVehicleDTO, DVLAData>(dvlaData);

      if (dvlaData.Errors != null && dvlaData.Errors.Length > 0)
      {
        SetErrorData(dvlaEntity, vrm, dvlaData.Errors);
      }

      return dvlaEntity;
    }

    private void SetErrorData(DVLAData entity, string vrm, DVLADataErrorDTO[] errorData)
    {
      // the vrm will also be blank so store from lookup param
      entity.RegistrationNumber = vrm;

      // store the first error detail
      entity.ErrorStatus = errorData[0].Detail;
    }

    public async Task UpdateMOTHistory(Guid vehicleId, CancellationToken cancellationToken)
    {
      var vehicle = await _tradingContext.Vehicles.FirstOrDefaultAsync(x => x.Id == vehicleId);
      await UpdateMOTHistory(vehicle, cancellationToken);
    }

    public async Task UpdateMOTHistory(Vehicle vehicle, CancellationToken cancellationToken)
    {
      // add mot history to vehicle 
      var motData = await _dvlaService.GetMOTHistory(vehicle.Vrm, cancellationToken);
      List<MOTHistory> mots = new List<MOTHistory>();

      if (motData != null)
      {
        // delete any existing MOT history for the vehicle 
        if (vehicle.MOTHistory != null)
        {
          foreach (var mot in vehicle.MOTHistory)
          {
            vehicle.MOTHistory.Remove(mot);
          }

          await _tradingContext.SaveChangesAsync();
        }

        //if (motData.MOTTests == null || motData.MOTTests.Count() == 0)
        //{
        //  // add only top-level data if present (i.e. expiry date)
        //  var mot = new MOTHistory()
        //  {
        //    Vehicle = vehicle,
        //    VRM = vehicle.Vrm,
        //    DVLAId = motData.DVLAId,
        //    StatusId = (uint)StatusEnum.Pending,
        //    ExpiryDate = motData.MOTTestExpiryDate,
        //    Passed = false
        //  };

        //  mots.Add(mot);
        //}
        //else
        //{
        if (motData.MOTTests != null)
        {
          foreach (var data in motData.MOTTests)
          {
            var mot = new MOTHistory()
            {
              VRM = vehicle.Vrm,
              DVLAId = motData.DVLAId,
              StatusId = (uint)StatusEnum.Active,
              CompletedDate = data.CompletedDate,
              ExpiryDate = data.ExpiryDate,
              Passed = data.TestResult?.ToLower().Trim() == "passed",
              OdometerValue = (data.OdometerValue ?? 0),
              OdometerUnit = data.OdometerUnit,
              TestNumber = data.MOTTestNumber
            };

            var motItems = new List<MOTItem>();
            foreach (var item in data.RFRAndComments)
            {
              var motItem = new MOTItem()
              {
                StatusId = (uint)StatusEnum.Active,
                MOT = mot,
                Type = item.Type,
                Text = item.Text
              };

              motItems.Add(motItem);
            }

            mot.MOTItems = motItems;
            mots.Add(mot);
          }
        }
        //}

        vehicle.MOTHistory = mots;

        // check mileages across all tests for discrepancies 
        var orderedMOTs = mots.OrderBy(x => x.CompletedDate);
        vehicle.HasMOTIssues = IsNotSequenced(orderedMOTs.ToList());

        // get latest mot history for expiry date
        var latestMot = vehicle.MOTHistory.OrderByDescending(x => x.ExpiryDate).FirstOrDefault();
        if (latestMot != null)
        {
          vehicle.MotExpires = latestMot.ExpiryDate;
        }
        else
        {
          vehicle.MotExpires = motData.MOTTestExpiryDate;
        }

        await _tradingContext.SaveChangesAsync();
      }
    }

    private bool IsNotSequenced(List<MOTHistory> items)
    {
      return items.Zip(items.Skip(1), (current, next) => new { current, next })
                  .Any(pair => pair.next.OdometerValue < pair.current.OdometerValue);
    }

    public async Task<VehicleDTO> CreateVehicleDTO(VehicleLookupInfoDTO info, CancellationToken cancellationToken)
    {
      // this is mandatory
      var makeId = await _lookupService.GetLookupId(cancellationToken, "make", info.MakeName);

      var modelId = !string.IsNullOrEmpty(info.ModelName)
        ? await _lookupService.GetLookupId(cancellationToken, "model", info.ModelName, makeId)
        : null;

      var derivId = !string.IsNullOrEmpty(info.DerivName) && modelId.HasValue
        ? await _lookupService.GetLookupId(cancellationToken, "deriv", info.DerivName, modelId)
        : null;

      var bodyTypeId = !string.IsNullOrEmpty(info.BodyTypeName)
        ? await _lookupService.GetLookupId(cancellationToken, "body_type", info.BodyTypeName)
        : null;

      var fuelTypeId = !string.IsNullOrEmpty(info.FuelTypeName)
        ? await _lookupService.GetLookupId(cancellationToken, "fuel_type", info.FuelTypeName)
        : null;

      var transmissionTypeId = !string.IsNullOrEmpty(info.TransmissionTypeName)
        ? await _lookupService.GetLookupId(cancellationToken, "transmission_type", info.TransmissionTypeName)
        : null;

      var colourId = !string.IsNullOrEmpty(info.Colour)
        ? await _lookupService.GetLookupId(cancellationToken, "colour", info.Colour)
        : null;

      var lookup = new LookupDTO()
      {
        TableName = "plate",
        LookupValue = info.VRM,
        VehicleType = VehicleTypeEnum.Car
      };

      // only UK plates will have a plateId 
      uint? plateId = null;
      if (ServiceHelper.PlateRequired(info.VRM))
      {
        plateId = await _lookupService.GetPlateId(info.DateRegistered, cancellationToken);
      }

      ushort owners;
      ushort doors;
      uint weight;
      uint co2;
      uint bhp;
      uint odometer;
      uint cc;

      if (!ushort.TryParse(info.PreviousKeepers, out owners))
        owners = 1;
      if (!ushort.TryParse(info.Doors, out doors))
        doors = 4;
      if (!uint.TryParse(info.Weight, out weight))
        weight = 0;
      if (!uint.TryParse(info.CO2, out co2))
        co2 = 0;
      if (!uint.TryParse(info.BHP, out bhp))
        bhp = 0;
      if (!uint.TryParse(info.Odometer, out odometer))
        odometer = 0;
      if (!uint.TryParse(info.EngineCC, out cc))
        cc = 0;

      if (!string.IsNullOrEmpty(info.DateRegistered))
      {

        var parts = info.DateRegistered.Split(" ");

        if (parts.Length > 0)
        {
          info.DateRegistered = parts[0];
        }
      }

      // todo:
      DateTime? dateOfReg = !string.IsNullOrEmpty(info.DateRegistered)
        ? DateTime.Parse(info.DateRegistered, new CultureInfo("en-GB"))
        : null;

      var vehicle = new VehicleDTO()
      {
        Vrm = info.VRM,
        Vin = info.VIN,
        VehicleColourId = colourId,
        DateOfReg = dateOfReg,
        BodyTypeId = bodyTypeId,
        FuelTypeId = fuelTypeId,
        StatusId = (uint)StatusEnum.Active,
        TransmissionTypeId = transmissionTypeId,
        VehicleTypeId = (uint)VehicleTypeEnum.Car,
        MakeId = makeId,
        ModelId = modelId,
        DerivId = derivId,
        PlateId = plateId,
        Doors = doors,
        Odometer = odometer,
        EngineCc = ConvertCC(info.EngineCC),
        Runner = true,
        Owners = owners,
        Kerbweight = weight,
        Co2 = co2,
        Added = DateTime.Now,
        BHP = bhp,
        OdometerUnit = (int)info.OdometerUnit,
        MileageRangeId = await _lookupService.GetMileageRangeId(odometer, cancellationToken),
        CapacityRangeId = await _lookupService.GetCapacityRangeId(cc, cancellationToken),
        LogBook = info.LogBook,

        CustomerId = info.CustomerId,
        ContactId = info.ContactId,
        AddressId = info.AddressId
      };

      return vehicle;

    }

    private ushort? ConvertCC(string cc)
    {
      if (decimal.TryParse(cc, out decimal ccValue))
      {
        if (ccValue < 10)
          return (ushort)(ccValue * 1000);
        else
          return (ushort)ccValue;
      }

      return null;
    }

    private async Task<bool> IsVehicleSold(string vrm, Guid customerId, CancellationToken cancellationToken)
    {
      var exists = await _tradingContext.Adverts.AsNoTracking().AnyAsync(x => x.Vehicle.CustomerId == customerId && x.CustomerId == customerId
        && x.Vehicle.Vrm == vrm
        && (x.SoldStatus == SoldStatusEnum.Sold || x.SoldStatus == SoldStatusEnum.Provisional));

      return exists;
    }

    private async Task<bool> IsVehicleForSale(string vrm)
    {
      var adCount = await _tradingContext.Adverts.CountAsync(x => x.Vehicle.Vrm == vrm
        && x.AdvertStatus == AdvertStatusEnum.Active && x.SoldStatus == SoldStatusEnum.Active);

      return adCount > 0;
    }

    public async Task<VehicleDTO> Patch(Guid vehicleId, Guid customerId, JsonPatchDocument<Vehicle> patch, CancellationToken cancellationToken)
    {
      var vehicle = await _tradingContext.Vehicles.FirstOrDefaultAsync(x => x.Id == vehicleId, cancellationToken);

      if (vehicle != null)
      {
        // Must be the customer ID of the vehicle, or must be god
        if (customerId != vehicle.CustomerId && !_userService.IsGod())
        {
          throw new Exception(ExceptionHelper.Forbidden(ECodes.Permissions));
        }

        // var patch = _mapper.Map<JsonPatchDocument<VehicleDTO>, JsonPatchDocument<Vehicle>>(patchDTO);
        patch.FilterPatch();
        patch.ApplyTo(vehicle);

        // update capacity/mileage details if they are in the patch
        if (patch.Operations.Select(x => x.path.ToLower()).Any(x => x.Contains("odometer")))
        {
          vehicle.MileageRangeId = await _lookupService.GetMileageRangeId(vehicle.Odometer.Value, cancellationToken);
        }

        if (patch.Operations.Select(x => x.path.ToLower()).Any(x => x.Contains("colour")))
        {
          var colourId = await _lookupService.GetLookupId(cancellationToken, "colour", vehicle.Colour);
          vehicle.VehicleColourId = colourId;
        }

        vehicle.Updated = DateTime.Now;

        await _tradingContext.SaveChangesAsync(cancellationToken);
      }

      var vehicleDTO = _mapper.Map<Vehicle, VehicleDTO>(vehicle);

      return vehicleDTO;
    }

    public async Task<DVLAVehicleDTO> GetDVLAData(string vrm, CancellationToken cancellationToken)
    {
      var data = await _tradingContext.DVLADatas.Where(x => x.RegistrationNumber == vrm).AsNoTracking().FirstOrDefaultAsync(cancellationToken);

      if (data == null)
      {
        return new DVLAVehicleDTO();
      }
      else
      {
        return _mapper.Map<DVLAData, DVLAVehicleDTO>(data);
      }
    }

    public async Task<bool> DeleteServiceHistory(Guid vehicleId, Guid serviceHistoryId, CancellationToken cancellationToken)
    {
      var serviceHistory = await _tradingContext.ServiceHistories
        .Where(x => x.VehicleId == vehicleId && x.Id == serviceHistoryId)
        .FirstOrDefaultAsync(cancellationToken);

      if (serviceHistory == null)
      {
        return false;
      }

      serviceHistory.StatusId = (uint)StatusEnum.Deleted;
      serviceHistory.Updated = DateTime.Now;

      await _tradingContext.SaveChangesAsync(cancellationToken);

      return true;
    }

    public async Task<ServiceHistoryDTO> AddServiceHistory(Guid vehicleId, ServiceHistoryDTO dto, CancellationToken cancellationToken)
    {
      var vehicleExistsId = await _tradingContext.Vehicles
        .Where(x => x.Id == vehicleId)
        .AsNoTracking()
        .Select(x => x.Id)
        .FirstOrDefaultAsync(cancellationToken);

      if (vehicleExistsId == null)
      {
        throw new ApplicationException($"Could not find vehicle with id: {vehicleId}");
      }

      var serviceHistory = _mapper.Map<ServiceHistoryDTO, ServiceHistory>(dto);

      serviceHistory.VehicleId = vehicleId;
      serviceHistory.Added = DateTime.Now;
      serviceHistory.Updated = DateTime.Now;
      serviceHistory.StatusId = (uint)StatusEnum.Active;

      if (dto.ServiceDate == null)
      {
        serviceHistory.ServiceDate = DateTime.Now;
      }

      _tradingContext.Add(serviceHistory);

      await _tradingContext.SaveChangesAsync(cancellationToken);

      var returnDTO = _mapper.Map<ServiceHistory, ServiceHistoryDTO>(serviceHistory);

      return returnDTO;
    }

    public async Task<bool> PatchServiceHistory(
      Guid vehicleId,
      Guid serviceHistoryId,
      JsonPatchDocument<ServiceHistory> patch,
      CancellationToken cancellationToken)
    {
      var vehicleExistsId = await _tradingContext.Vehicles
        .Where(x => x.Id == vehicleId)
        .AsNoTracking()
        .Select(x => x.Id)
        .FirstOrDefaultAsync(cancellationToken);

      if (vehicleExistsId == null)
      {
        throw new ApplicationException($"Could not find vehicle with id: {vehicleId}");
      }

      var serviceHistory = await _tradingContext.ServiceHistories
        .Where(x => x.VehicleId == vehicleId && x.Id == serviceHistoryId)
        .FirstOrDefaultAsync(cancellationToken);


      if (serviceHistory == null)
      {
        throw new ApplicationException($"Could not find serviceHistory with id: {serviceHistoryId}");
      }

      patch.FilterPatch();
      patch.ApplyTo(serviceHistory);

      await _tradingContext.SaveChangesAsync(cancellationToken);

      return true;
    }

    public async Task<IEnumerable<Vehicle>> GetMatchingVehicles(VehicleSearchDTO dto, CancellationToken cancellationToken)
    {
      var vehicles = await _tradingContext.Vehicles
        .Include(x => x.Make)
        .Include(x => x.Model)
        .Include(x => x.Deriv)
        .Include(x => x.Customer)
        .Where(x => x.Vrm.Contains(dto.Filters.vrmMatches))
        .OrderByDescending(x => x.Updated)
        .AsNoTracking()
        .Take(dto.Limit.Value)
        .ToListAsync(cancellationToken);

      return vehicles;
    }

    public async Task<VehicleDTO> GetVehicleHistory(VehicleSearchDTO searchDTO, CancellationToken cancellationToken)
    {
      // create vehicle history from contact actions relating to adverts (question: AddToWatchlist links to watchlist, should it link to advert?)
      var actionTypes = new[] {
        ContactActionEnum.CreateAdvert,
        ContactActionEnum.BidOnAdvert,
        ContactActionEnum.AddToWatchlist,
        ContactActionEnum.RemoveFromWatchlist,
        ContactActionEnum.VehiclePurchase, // sold
        ContactActionEnum.ViewAdvert,
        ContactActionEnum.EndSale,
        ContactActionEnum.PublishAdvert,
        ContactActionEnum.RelistAdvert
      };

      var vehicle = await _tradingContext.Vehicles
        .Include(x => x.Adverts)
        .Include(x => x.Make)
        .Include(x => x.Model)
        .Include(x => x.Deriv)
        .Include(x => x.Customer)
        .Include(x => x.Plate)
        .Include(x => x.TransmissionType)
        .Include(x => x.FuelType)
        .Include(x => x.BodyType)
        .AsNoTracking().FirstOrDefaultAsync(x => x.Id == searchDTO.Filters.VehicleId);

      var dto = _mapper.Map<Vehicle, VehicleDTO>(vehicle);

      var result = from action in _tradingContext.ContactActions.Include(x => x.Contact.Customer)
                      .AsNoTracking().Where(x => actionTypes.Contains(x.ContactActionType))
                   join advert in _tradingContext.Adverts on action.ExternalId equals advert.Id
                   where advert.VehicleId == searchDTO.Filters.VehicleId
                   select new
                   {
                     ActionDate = action.Added.Value,
                     AdvertId = advert.Id,
                     ActionDesc = MappingHelper.GetContactActionName(action.ContactActionType),
                     action.Contact,
                     Vehicle = vehicle
                   };

      var tmpList = new List<VehicleActionHistoryDTO>();

      await result.ForEachAsync(x =>
      {
        tmpList.Add(new VehicleActionHistoryDTO()
        {
          ActionDate = x.ActionDate,
          ActionDesc = x.ActionDesc,
          AdvertId = x.AdvertId,
          Contact = _mapper.Map<Contact, ContactDTO>(x.Contact),
        });
      });

      // Consider moving this action to the front end to reduce server load
      dto.ActionHistory = tmpList.OrderByDescending(x => x.ActionDate).ToList();

      return dto;
    }

    private static string GetAdvertActionDesc(ContactActionEnum action, Advert advert)
    {
      if (action == ContactActionEnum.CreateAdvert)
      {
        return $"Starting price {advert.StartPrice}, Reserve {advert.ReservePrice ?? 0}";
      }

      return "";
    }

    public async Task<AdvertViewVehicleProvenanceDTO> GetVehicleProvenanceByAdvert(Guid advertId, CancellationToken cancellationToken)
    {
      var check = await _tradingContext.Adverts
          .Include(x => x.Vehicle)
          .ThenInclude(v => v.VehicleChecks)
          .AsNoTracking()
          .Where(x => x.Id == advertId)
          .Select(x => x.Vehicle.VehicleChecks
              .Where(vc => vc.VehicleCheckType == VehicleCheckTypeEnum.Provenance)
              .OrderByDescending(vc => vc.Added)
              .FirstOrDefault())
          .Select(x => x != null ? new AdvertViewVehicleProvenanceDTO
          {
            Finance = x.Finance,
            Scrapped = x.Scrapped,
            Stolen = x.Stolen
          } : null)
          .FirstOrDefaultAsync();

      return check;
    }

    public async Task<AdvertViewVehicleValuationDTO> GetVehicleValuationByAdvert(Guid advertId, CancellationToken cancellationToken)
    {
      var check = await _tradingContext.Adverts
          .Include(x => x.Vehicle)
          .ThenInclude(v => v.VehicleChecks)
          .AsNoTracking()
          .Where(x => x.Id == advertId)
          .Select(x => x.Vehicle.VehicleChecks
              .Where(vc => vc.VehicleCheckType == VehicleCheckTypeEnum.Valuation)
              .OrderByDescending(vc => vc.Added)
              .FirstOrDefault())
          .Select(x => new AdvertViewVehicleValuationDTO
          {
            PriceAvg = x.PriceAvg,
            PriceClean = x.PriceClean,
            PriceRetail = x.PriceRetail
          })
          .FirstOrDefaultAsync();

      return check;
    }

    public async Task<List<MOTHistoryDTO>> GetMOTHistoryByAdvert(Guid advertId, CancellationToken cancellationToken)
    {
      var mots = await _tradingContext.Adverts
        .Include(x => x.Vehicle)
        .ThenInclude(v => v.MOTHistory).ThenInclude(x => x.MOTItems)
        .AsNoTracking()
        .Where(x => x.Id == advertId)
        .SelectMany(x => x.Vehicle.MOTHistory)
        .ProjectTo<MOTHistoryDTO>(_mapper.ConfigurationProvider)
        .ToListAsync(cancellationToken);

      return mots;
    }

    public async Task<List<AdvertViewVehicleMediaDTO>> GetVehicleMediaByAdvert(Guid advertId, CancellationToken cancellationToken)
    {
      var mediaInfo = await _tradingContext.Adverts
          .AsNoTracking()
          .Where(x => x.Id == advertId)
          .Select(x => new
          {
            CustomerId = x.Vehicle.CustomerId,
            VehicleId = x.Vehicle.Id,
            Media = x.Vehicle.VehicleMedia.Where(y => y.StatusId == (uint)StatusEnum.Active)
          })
          .FirstOrDefaultAsync(cancellationToken);

      if (mediaInfo == null || mediaInfo.CustomerId == null)
      {
        return null;
      }

      var media = mediaInfo.Media.ToList();

      var result = _mapper.Map<List<VehicleMedia>, List<AdvertViewVehicleMediaDTO>>(media,
          opt => opt.AfterMap((src, dest) =>
          {
            foreach (var i in dest)
            {
              i.MediaURL = URLHelper.ImageUrl(mediaInfo.CustomerId.Value, mediaInfo.VehicleId, i.Id.Value);
            }
          }));

      return result;
    }

    public async Task<AdvertViewVehicleServiceHistoryDTO> GetVehicleServiceHistoryByAdvert(Guid advertId, CancellationToken cancellationToken)
    {
      var historyData = await _tradingContext.Adverts
        .Include(x => x.Vehicle.ServiceHistories)
        .AsNoTracking()
        .Where(x => x.Id == advertId)
        .Select(x => new AdvertViewVehicleServiceHistoryDTO
        {
          ServiceHistories = _mapper.Map<List<ServiceHistoryDTO>>(x.Vehicle.ServiceHistories.ToList()),
          ServiceHistoryType = x.Vehicle.ServiceHistoryType
        })
        .FirstOrDefaultAsync(cancellationToken);

      return historyData;
    }

    //public async Task<AdvertViewVehicleTyreInfoDTO> GetVehicleTyreInfoByAdvert(Guid advertId, CancellationToken cancellationToken)
    //{
    //  var tyres = await _tradingContext.Adverts
    //    .Include(x => x.Vehicle)
    //    .AsNoTracking()
    //    .Where(x => x.Id == advertId)
    //    .Select(x => new AdvertViewVehicleTyreInfoDTO
    //    {
    //      TyreDepth_NSF = x.Vehicle.TyreDepth_NSF,
    //      TyreDepth_NSR = x.Vehicle.TyreDepth_NSR,
    //      TyreDepth_OSF = x.Vehicle.TyreDepth_OSF,
    //      TyreDepth_OSR = x.Vehicle.TyreDepth_OSR
    //    }).FirstOrDefaultAsync(cancellationToken);  

    //  return tyres;
    //}

    public async Task<AdvertViewVehicleTyreInfoDTO> GetVehicleTyreInfoByAdvert(Guid advertId, CancellationToken cancellationToken)
    {
      var tyres = await _tradingContext.Adverts
          .Include(x => x.Vehicle)
          .ThenInclude(v => v.Tyres)
          .AsNoTracking()
          .Where(x => x.Id == advertId)
          .SelectMany(x => x.Vehicle.Tyres)
          .Select(t => new VehicleTyreInfoDTO
          {
            Position = t.Position,
            Make = t.Make,
            Condition = t.Condition,
            Depth = t.Depth
          })
          .OrderBy(t => t.Position)
          .ToListAsync(cancellationToken);

      return new AdvertViewVehicleTyreInfoDTO { Tyres = tyres };
    }


    public async Task<List<VehicleAttribDTO>> GetVehicleOptionsByAdvert(Guid advertId, CancellationToken cancellationToken)
    {
      var vehicleId = await _tradingContext.Adverts
        .AsNoTracking()
        .Where(x => x.Id == advertId)
        .Select(x => x.VehicleId)
        .FirstOrDefaultAsync(cancellationToken);

      if (vehicleId.HasValue)
      {
        var options = await _tradingContext.VehicleAttribs.Include(x => x.Attribval)
          .AsNoTracking()
          .Where(x => x.VehicleId == vehicleId)
          .ToListAsync(cancellationToken);

        return _mapper.Map<List<VehicleAttrib>, List<VehicleAttribDTO>>(options);
      }

      return null;
    }

    public async Task<AdvertViewContactSellerDataDTO> GetContactSellerDataByAdvert(Guid advertId, CancellationToken cancellationToken)
    {
      var data = await _tradingContext.Adverts
        .AsNoTracking()
        .Where(x => x.Id == advertId)
        .Select(x => new AdvertViewContactSellerDataDTO
        {
          MakeName = x.Vehicle.Make.MakeName,
          ModelName = x.Vehicle.Model.ModelName,
          PlateName = x.Vehicle.Plate.PlateName
        })
        .FirstOrDefaultAsync(cancellationToken);

      return data;
    }

    public async Task<bool> SetTyreDepths(Guid vehicleId, Guid customerId, JsonPatchDocument<VehicleTyrePatchDTO> patch, CancellationToken cancellationToken)
    {
      var positionMap = new Dictionary<string, TyrePositionEnum>
      {
        { "/tyreDepth_NSF", TyrePositionEnum.NSF },
        { "/tyreDepth_OSF", TyrePositionEnum.OSF },
        { "/tyreDepth_NSR", TyrePositionEnum.NSR },
        { "/tyreDepth_OSR", TyrePositionEnum.OSR },
        { "/tyreDepth_SPARE", TyrePositionEnum.SPARE }
      };

      var vehicle = await _tradingContext.Vehicles
          .Where(x => x.Id == vehicleId && x.CustomerId == customerId)
          .Include(x => x.Tyres)
          .FirstOrDefaultAsync();

      if (vehicle == null)
      {
        return false;
      }

      foreach (var operation in patch.Operations)
      {
        var tyrePosition = positionMap.FirstOrDefault(x => x.Key.Equals(operation.path, StringComparison.OrdinalIgnoreCase)).Value;

        decimal? depth = operation.value != null ? Decimal.Parse(operation.value.ToString()) : null;

        var exists = vehicle.Tyres
          .FirstOrDefault(x => x.Position == tyrePosition);

        if (exists == null)
        {
          var tyreInfo = new VehicleTyreInfo()
          {
            VehicleId = vehicleId,
            Position = tyrePosition,
            Depth = depth,
            Make = string.Empty, // default value
            Condition = null,
            Added = DateTime.Now,
            Updated = DateTime.Now,
            StatusId = (int) StatusEnum.Active
          };

          _tradingContext.VehicleTyreInfos.Add(tyreInfo);
        }
        else
        {
          exists.Updated = DateTime.Now;
          exists.Depth = depth; // default value
        }

        await _tradingContext.SaveChangesAsync(cancellationToken);
      }

      return true;
    }
  }
}
