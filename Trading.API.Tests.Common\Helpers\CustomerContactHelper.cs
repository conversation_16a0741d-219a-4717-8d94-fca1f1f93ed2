﻿using Microsoft.EntityFrameworkCore;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;

namespace Trading.API.Tests.Common.Helpers
{
  public class CustomerContactHelper
  {
    public static async Task<(Guid customerId, Guid contactId)> CreateCustomerAndContact(TradingContext context, CommonServices common, string name, string email)
    {
      var customer = await context.Customers.FirstOrDefaultAsync(x => x.Email == email);

      if (customer == null)
      {
        customer = new Customer
        {
          CustomerName = name,
          Email = email,
          IsBuyer = true,
          IsSeller = true,
          PlatformId = 1,
          CustomerType = CustomerTypeEnum.Independent,
          LastAdvert = DateTime.Now,
          LastLogin = DateTime.Now,
          CustomerIdCRC = 0,
          Added = DateTime.Now,
          StatusId = 1
        };

        context.Customers.Add(customer);
        await context.SaveChangesAsync();
      }

      var contactService = common.ContactService;
      var contactDTO = await contactService.Create(new ContactDTO
      {
        CustomerId = customer.Id,
        Email = email,
        ContactName = name
      }, CancellationToken.None);

      return (customer.Id, contactDTO.Id.Value);
    }

  }
}
