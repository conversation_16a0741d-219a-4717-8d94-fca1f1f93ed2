﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Models;
using Trading.API.Data.Models.DTO;

namespace Trading.API.Data.DTO
{
  public class ScanSampleSearchDTO : BaseSearchDTO
  {
    public ScanSampleFilters Filters { get; set; } = new ScanSampleFilters() { };
  }

  public class ScanSampleFilters : BaseFilterInt
  {
    public uint? ScanQueueId { get; set; }
    public uint? ScanStageId { get; set; }
    public uint? ScanCustomerId { get; set; }
  }

  public class ScanSampleResponseDTO 
  {
    public LambdaScanStageResponseDTO ScanSample { get; set; }
    public string ScanSampleUrl { get; set; }
    public IEnumerable<ScanErrorDTO> ScanErrors { get; set; }
  }
}