﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO
{
  public class SetScanVehicleDTO 
  {
    public uint? ScanQueueId { get; set; }
    public uint? ScanVehicleId { get; set; }
    public List<SetScanVehicleDataDTO> VehicleData { get; set; }

  }
  public class SetScanVehicleImagesDTO 
  {
    public List<string> image_list { get; set; }

  }
  public class SetScanVehicleDataDTO
  {
    public uint scan_field_id { get; set; }
    public string scan_field_name { get; set; }
    public string? response { get; set; }
    public List<string> errors { get; set; }
    public bool? ok { get; set; }


  }
  public class SetScanVehicleResponseDTO()
  {
    public bool success { get; set; }
    public string response { get; set; }
    public uint? scan_vehicle_id { get; set; }
  } 
}