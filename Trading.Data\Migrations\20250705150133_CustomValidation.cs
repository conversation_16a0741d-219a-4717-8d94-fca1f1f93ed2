﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Trading.API.Data.Migrations
{
    /// <inheritdoc />
    public partial class CustomValidation : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ICInputValidation_ICInput_ICInputId",
                table: "ICInputValidation");

            migrationBuilder.DropIndex(
                name: "IX_ICInputValidation_ICInputId",
                table: "ICInputValidation");

            migrationBuilder.AddColumn<string>(
                name: "CustomValidation",
                table: "ICContainerWidgetInput",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CustomValidation",
                table: "ICContainerWidgetInput");

            migrationBuilder.CreateIndex(
                name: "IX_ICInputValidation_ICInputId",
                table: "ICInputValidation",
                column: "ICInputId");

            migrationBuilder.AddForeignKey(
                name: "FK_ICInputValidation_ICInput_ICInputId",
                table: "ICInputValidation",
                column: "ICInputId",
                principalTable: "ICInput",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
