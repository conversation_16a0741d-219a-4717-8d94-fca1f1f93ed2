﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.Components.MainDashboard
{
  public class AdvertOfInterestDTO : BaseModelEntityDTO
  {
    public string? Description { get; set; }
    public string? Headline { get; set; }
    public DateTime? EndDateTime { get; set; }

    public VehicleOfInterestDTO Vehicle { get; set; }
  }

  public class VehicleOfInterestDTO
  {
    public string? Vrm { get; set; }
    public string? Colour { get; set; }
    public ushort? Doors { get; set; }
    public uint? Odometer { get; set; }
    public ushort? Owners { get; set; }

    public string? PrimaryImageURL { get; set; }
  }
}
