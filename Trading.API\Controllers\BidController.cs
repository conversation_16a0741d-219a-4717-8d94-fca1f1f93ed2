using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading;
using System.Threading.Tasks;
using Amazon.S3.Model;
using AngleSharp.Dom;
using Newtonsoft.Json;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Helpers;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;
using System.Collections.Generic;
using System.Linq;
using Trading.Services.Classes;

namespace Trading.API.Controllers
{
  [Route("api/bid")]
  [ApiController]
  [Authorize]
  public class BidController : ControllerBase
  {
    private readonly IBidService _bidService;
    private readonly IOfferService _offerService;
    private readonly IAdvertService _advertService;
    private readonly IMapper _mapper;

    public BidController(IBidService bidService, IAdvertService advertService, IOfferService offerService, IMapper mapper)
    {
      this._bidService = bidService;
      this._offerService = offerService;
      this._advertService = advertService;
      this._mapper = mapper;
    }

    // GET: api/Customers/X/BidsReceived
    [HttpGet("/api/customer/{customerId}/bidsReceived")]
    public async Task<IActionResult> GetCustomerBidsReceived(Guid customerId, CancellationToken cancellationToken)
    {
      string withJoins = null;
      var all = await _bidService.GetBidsReceived(customerId, withJoins, cancellationToken);
      return Ok(all);
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> CreateBid([FromBody] BidDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        // post the bid to the db
        var updatedBid = await _bidService.CreateBid(dto, cancellationToken);
        return Ok(updatedBid);
      }
      catch (Exception ex)
      {
        return ex.ParseError();
      }
    }

    [HttpPost]
    [Route("offlineBid")]
    public async Task<IActionResult> SilentBid([FromBody] SilentBidDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        dto.ContactId = User.ContactId();
        dto.CustomerId = User.CustomerId();

        await _bidService.SilentBid(dto, cancellationToken);
        await _bidService.ProcessBids(dto.AdvertId, false, cancellationToken);
      }
      catch (Exception ex)
      {
        return ex.ParseError();
      }

      return Ok();
    }

    [HttpGet]
    [Route("/api/listing/{advertId}/end")]
    public async Task<IActionResult> EndLot(Guid advertId, uint reasonId, CancellationToken cancellationToken)
    {
      try
      {
        var customerId = User.CustomerId();
        var contactId = User.ContactId();

        await _bidService.EndListing(advertId, true, null, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPut]
    [Route("{bidGuid}/cancel")]
    public async Task<IActionResult> CancelBid(Guid bidGuid, [FromBody] BidDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        if (dto.ContactId == User.ContactId() || User.IsAdmin())
        {
          await _bidService.CancelBidderOffer(bidGuid, dto.ContactId, cancellationToken);
          return Ok();
        }

        return Unauthorized();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/customer/{customerId}/bidderOffers")]
    public async Task<IActionResult> BidderOffers(Guid customerId, CancellationToken cancellationToken, [FromQuery] bool pendingOnly = false)
    {
      try
      {
        if (customerId == User.CustomerId())
        {
          // post the bid to the db
          var offers = await _bidService.GetBidderOffers((Guid)customerId, pendingOnly, cancellationToken);
          return Ok(offers);
        }
        return BadRequest();
      }
      catch (Exception ex)
      {
        return ex.ParseError();
      }
    }

    [HttpGet]
    [Route("/api/contact/{contactId}/activeBids")]
    public async Task<IActionResult> GetActiveBids(Guid contactId, CancellationToken cancellationToken)
    {
      try
      {
        if (contactId == User.ContactId())
        {
          // post the bid to the db
          var bids = await _bidService.GetActiveBids((Guid)contactId, cancellationToken);
          return Ok(bids);
        }
        return BadRequest();
      }
      catch (Exception ex)
      {
        return ex.ParseError();
      }
    }

    [HttpGet]
    [Route("/api/contact/{contactId}/lostBids")]
    public async Task<IActionResult> GetLostBids(Guid contactId, CancellationToken cancellationToken)
    {
      try
      {
        if (contactId == User.ContactId())
        {
          // post the bid to the db
          var bids = await _bidService.GetLostBids((Guid)contactId, cancellationToken);
          return Ok(bids);
        }
        return BadRequest();
      }
      catch (Exception ex)
      {
        return ex.ParseError();
      }
    }

    [HttpPut]
    [Route("{bidGuid}/accept")]
    public async Task<IActionResult> AcceptBid(Guid bidGuid, CancellationToken cancellationToken)
    {
      var dto = new BidReplyDTO()
      {
        BidGuid = bidGuid,
        AdminAction = this.User.IsAdmin(),
        ContactId = this.User.ContactId(),
        CustomerId = this.User.CustomerId()
      };

      try
      {
        var ok = await _bidService.CanReplyToBid(dto, cancellationToken);
      }
      catch (Exception ex)
      {
        return ex.ParseError();
      }

      try
      {
        // post the bid to the db
        var response = await _bidService.AcceptBid(dto, cancellationToken);
        return Ok(response);
      }
      catch (Exception ex)
      {
        return ex.ParseError();
      }
    }

    [HttpPut]
    [Route("{bidGuid}/reject")]
    public async Task<IActionResult> RejectBid(Guid bidGuid, [FromBody] BidReplyDTO bidRepDTO, CancellationToken cancellationToken)
    {
      if (this.User.IsAdmin())
      {
        bidRepDTO.ContactId = bidRepDTO.ContactId ?? this.User.ContactId();
        bidRepDTO.CustomerId = bidRepDTO.CustomerId ?? this.User.CustomerId();
      }
      else
      {
        bidRepDTO.ContactId = this.User.ContactId();
        bidRepDTO.CustomerId = this.User.CustomerId();
      }

      bidRepDTO.BidGuid = bidGuid;
      bidRepDTO.AdminAction = this.User.IsAdmin();

      try
      {
        var ok = await _bidService.CanReplyToBid(bidRepDTO, cancellationToken);
      }
      catch (Exception ex)
      {
        return ex.ParseError();
      }

      try
      {
        // post the bid to the db
        var response = await _bidService.RejectBid(bidRepDTO, cancellationToken);
        return Ok(response);
      }
      catch (Exception ex)
      {
        return ex.ParseError();
      }
    }



    [HttpGet]
    [Route("/api/contact/{contactId}/counterOffers")]
    public async Task<IActionResult> GetCounterOffers([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = new OfferSearchDTO();
      var returnValue = new SearchResultDTO<VehicleBidDTO_Public>() { TotalItems = 0 };

      try
      {
        if (!string.IsNullOrWhiteSpace(query))
        {
          dto = JsonConvert.DeserializeObject<OfferSearchDTO>(query);
        }

        if (dto.Filters.ContactId == User.ContactId() || User.IsAdmin())
        {
          // post the bid to the db 
          var bids = await _bidService.SearchCounterOffers(dto, cancellationToken);

          if (bids.TotalItems == 0)
          {
            return Ok(returnValue);
          }

          // List the components that are allowed to show ADMIN level BidDTO
          if (dto.Component == "XYZ")
          {

          }
          else
          {
            if (dto.Filters.CountOnly == true)
            {
              returnValue.TotalItems = bids.TotalItems;
            }
            else
            {
              returnValue.Results = _mapper.Map<IEnumerable<VehicleBidDTO>, IEnumerable<VehicleBidDTO_Public>>(bids.Results);
              returnValue.TotalItems = returnValue.Results.Count();
            }

            return Ok(returnValue);
          }
        }
        return BadRequest();
      }
      catch (Exception ex)
      {
        return ex.ParseError();
      }
    }


    [HttpPost]
    [Route("/api/listing/{advertId}/bid")]
    public async Task<IActionResult> BidderOffer(Guid advertId, [FromBody] BidDTO bidDTO, CancellationToken cancellationToken)
    {
      var contactId = User.ContactId();
      var customerId = User.CustomerId();

      bidDTO.ContactId = (Guid)this.User.ContactId();
      bidDTO.CustomerId = (Guid)this.User.CustomerId();

      if (this.User.ContactId() == bidDTO.ContactId || this.User.IsAdmin())
      {
        bidDTO.ContactId = bidDTO.ContactId;
        bidDTO.CustomerId = bidDTO.CustomerId;
      }

      try
      {
        // post the bid to the db
        var updatedBid = await _bidService.BidderOffer(bidDTO, cancellationToken);
        await _bidService.ProcessBids(bidDTO.AdvertId, true, cancellationToken);
        return Ok(updatedBid);
      }
      catch (Exception ex)
      {
        return ex.ParseError();
      }
    }

    [HttpGet]
    [Route("")]
    [ResponseCache(Duration = 5)]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = JsonConvert.DeserializeObject<BidSearchDTO>(query);


        if (dto.CurrentCustomerId == null)
        {
          dto.CurrentCustomerId = User.CustomerId();
        }

        if (dto.CurrentCustomerId == User.CustomerId() || this.User.IsAdmin())
        {

          var result = await _bidService.Search(dto, cancellationToken);

          return Ok(result);
        }

        return Unauthorized();
      }
      catch (Exception ex)
      {
        return ex.ParseError();
      }
    }

    [HttpGet]
    [Route("/api/bids")]
    public async Task<IActionResult> GetBids([FromQuery] string query, CancellationToken cancellationToken)
    {

      if (!this.User.IsAdmin())
      {
        return Unauthorized();
      }

      var dto = JsonConvert.DeserializeObject<BidSearchDTO>(query);
      var result = await _bidService.Search(dto, cancellationToken);
      return Ok(result);
    }

    [HttpGet]
    [Route("byAdvert")]
    [ResponseCache(Duration = 5)]
    public async Task<IActionResult> BidsByAdvert([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = JsonConvert.DeserializeObject<BidSearchDTO>(query);

        if (dto.CurrentCustomerId == null)
        {
          dto.CurrentCustomerId = User.CustomerId();
        }

        if (dto.CurrentCustomerId == User.CustomerId() || this.User.IsAdmin())
        {
          var result = await _bidService.BidsByAdvert(dto, cancellationToken);

          return Ok(result);
        }

        return Unauthorized();
      }
      catch (Exception ex)
      {
        return ex.ParseError();
      }
    }




    [HttpGet]
    [Route("/api/end-listings")]
    [AllowAnonymous]
    public async Task<IActionResult> EndListings(CancellationToken cancellationToken)
    {
      try
      {
        await _bidService.EndListings(cancellationToken);
        return Ok("Expired listings for non-managed sales ended");
      }
      catch (Exception ex)
      {
        return ex.ParseError();
      }
    }

    [HttpGet]
    [Route("/api/listing/{advertId}/bid-box-data")]
    public async Task<IActionResult> GetBidBoxDataByAdvert(Guid advertId, CancellationToken cancellationToken)
    {
      try
      {
        var res = await _bidService.GetBidBoxDataByAdvert(advertId, cancellationToken);
        return Ok(res);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
