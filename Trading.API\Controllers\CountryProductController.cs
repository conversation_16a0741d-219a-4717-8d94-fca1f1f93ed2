using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.API.Helpers;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/countryProduct")]
  [ApiController]
  [Authorize]
  public class CountryProductController : ControllerBase
  {
    public ICountryProductService _countryProductService;

    public CountryProductController(ICountryProductService countryProductService) { 
      _countryProductService = countryProductService;
    }

    [HttpGet]
    [Route("/api/countryProducts")]
    [ResponseCache(Duration = 600)]

    public async Task<IActionResult> Search([FromQuery] int? unique, [FromQuery] string? search, CancellationToken cancellationToken)
    {
      var searchDTO = new BaseSearchDTO() { };

      if (! String.IsNullOrEmpty(search))
      {
        searchDTO = JsonSerializer.Deserialize<BaseSearchDTO>(search);
      }

      var response = await _countryProductService.Search(searchDTO, cancellationToken);

      return Ok(response);
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create(CountryProductDTO dto, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        var response = await _countryProductService.Create(dto, cancellationToken);

        return Ok(response);
      }

      return Forbid();
    }

    [HttpDelete]
    [Route("{countryProductId}")]
    public async Task<IActionResult> Delete(Guid countryProductId, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        var response = await _countryProductService.Delete(countryProductId, cancellationToken);

        return Ok(response);
      }

      return Forbid();
    }

    [HttpGet]
    [Route("{countryProductId}")]
    public async Task<IActionResult> GetCountryProduct(Guid countryProductId, [FromQuery] string search, CancellationToken cancellationToken)
    {
      var searchDTO = new BaseSearchDTO() { };

      if (!String.IsNullOrEmpty(search))
      {
        searchDTO = JsonSerializer.Deserialize<BaseSearchDTO>(search);
      }

      var response = await _countryProductService.Get(countryProductId, searchDTO, cancellationToken);

      if (response != null)
      {
        return Ok(response);
      }

      return NotFound();
    }

    [HttpPatch]
    [Route("{countryProductId}")]
    public async Task<IActionResult> Patch(Guid countryProductId, [FromBody] JsonPatchDocument<CountryProduct> patch, CancellationToken cancellationToken)
    {
      // Only edit addresses that are ours (or if we're admin)
      if (User.IsAdmin())
      {
        try
        {
          return Ok(await _countryProductService.Patch(countryProductId, patch, cancellationToken));
        }
        catch (Exception ex)
        {
          return ex.ParseError();
        }
      }

      return Forbid();
    }
  }
}
