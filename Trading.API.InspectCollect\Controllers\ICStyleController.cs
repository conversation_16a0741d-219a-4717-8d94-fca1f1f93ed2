using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/style")]
  [ApiController]
  [AllowAnonymous]
  public class ICStyleController : ControllerBase
  {
    private readonly ICStyleInterface _icStyleService;

    public ICStyleController(ICStyleInterface serviceInterface)
    {
      _icStyleService = serviceInterface;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(Guid id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICStyleSearchDTO>(query);
      var res = await _icStyleService.Get(id, dto, cancellationToken);
      return Ok(res);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icStyleService.Delete(id);
      return Ok(res);
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Post([FromBody]ICStyleCreateDTO dto, CancellationToken cancellationToken)
    {
      var res = await _icStyleService.Create(dto);
      return Ok(res);
    }

    [HttpGet]
    [Route("search")]
    [Route("/api/inspect-collect/styles")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICStyleSearchDTO>(query);
      var res = await _icStyleService.Search(dto, cancellationToken);
      return Ok(res);
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<ICStyle> dto)
    {
      var response = await _icStyleService.Patch(id, dto);
      return Ok(response);
    }
  }
}