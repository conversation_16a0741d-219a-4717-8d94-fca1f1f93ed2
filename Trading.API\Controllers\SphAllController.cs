using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/[controller]")]
  [ApiController]
  [Authorize]
  public class SphAllController : ControllerBase
  {
    private readonly ISphAllService _searchService;

    public SphAllController(ISphAllService searchService, IMapper mapper)
    {
      this._searchService = searchService;
    }

    [HttpGet]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var search = JsonConvert.DeserializeObject<AdvertSearchDTO>(query);

        var response = await _searchService.Search(search, cancellationToken);

        return Ok(response);
      } 
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
