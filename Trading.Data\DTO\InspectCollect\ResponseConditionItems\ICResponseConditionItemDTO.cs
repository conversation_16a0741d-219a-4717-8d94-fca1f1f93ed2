﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Trading.API.Data.DTO.InspectCollect.ResponseConditionItems;

public class ICResponseConditionItemDTO
{
  public Guid Id { get; set; }
  public Guid ICResponseId { get; set; }

  public uint BodyPartId { get; set; }
  public string BodyPartName { get; set; }

  public uint DamageId { get; set; }
  public string DamageName { get; set; }

  public uint? DamageSeverityId { get; set; }
  public string DamageSeverityName { get; set; }

  public uint? DamageDetailId { get; set; }
  public string DamageDetailName { get; set; }

  public decimal? RepairCost { get; set; }

  [MaxLength(255)]
  public string ItemDesc { get; set; }

  [MaxLength(255)]
  public string RepairDesc { get; set; }

  public decimal? Score { get; set; }

  [MaxLength(45)]
  public string ItemLocation { get; set; }

  public decimal SplatPositionX { get; set; }
  public decimal SplatPositionY { get; set; }

  [MaxLength(10)]
  public string SplatDesc { get; set; }

  public bool Internal { get; set; }

  public ICollection<ICResponseConditionItemMediaDTO> Media { get; set; } = new List<ICResponseConditionItemMediaDTO>();
}

public class ICResponseConditionItemMediaDTO
{
  public Guid Id { get; set; }

  public uint MediaTypeId { get; set; }
  public string MediaTypeName { get; set; }

  public string FileExtension { get; set; } // file extension of the media item

  public Guid? ICResponseConditionItemId { get; set; }

  public string ImageURL { get; set; } // URL to the image in S3
}

// Create/Update DTOs with validation attributes
public class CreateICResponseConditionItemDTO
{
  public Guid ICResponseId { get; set; }

  [Required]
  public uint BodyPartId { get; set; }

  [Required]
  public uint DamageId { get; set; }

  public uint? DamageSeverityId { get; set; }
  public uint? DamageDetailId { get; set; }
  public decimal? RepairCost { get; set; }

  [MaxLength(255)]
  public string ItemDesc { get; set; }

  [MaxLength(255)]
  public string RepairDesc { get; set; }

  public decimal? Score { get; set; }

  [MaxLength(45)]
  public string ItemLocation { get; set; }

  [Required]
  public decimal SplatPositionX { get; set; }

  [Required]
  public decimal SplatPositionY { get; set; }

  [MaxLength(10)]
  public string SplatDesc { get; set; }

  public bool Internal { get; set; }
}

public class UpdateICResponseConditionItemDTO
{
  public uint? BodyPartId { get; set; }
  public uint? DamageId { get; set; }
  public uint? DamageSeverityId { get; set; }
  public uint? DamageDetailId { get; set; }
  public decimal? RepairCost { get; set; }

  [MaxLength(255)]
  public string ItemDesc { get; set; }

  [MaxLength(255)]
  public string RepairDesc { get; set; }

  public decimal? Score { get; set; }

  [MaxLength(45)]
  public string ItemLocation { get; set; }

  public decimal? SplatPositionX { get; set; }
  public decimal? SplatPositionY { get; set; }

  [MaxLength(10)]
  public string SplatDesc { get; set; }

  public bool? Internal { get; set; }
}

public class CreateICResponseConditionItemMediaDTO
{
  [Required]
  public uint MediaTypeId { get; set; }

  public Guid? ICResponseConditionItemId { get; set; }

  public string FileExtension { get; set; } // file extension of the media item
}

public class UpdateICResponseConditionItemMediaDTO
{
  public uint? MediaTypeId { get; set; }

  public string FileExtension { get; set; } // file extension of the media item
}