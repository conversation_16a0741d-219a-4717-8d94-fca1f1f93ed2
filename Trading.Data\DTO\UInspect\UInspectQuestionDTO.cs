﻿using System.Collections.Generic;
using Trading.API.Data.Enums;
using Trading.API.Data.Enums.UInspections;

namespace Trading.API.Data.DTO.UInspections
{
  public class UInspectQuestionDTO : BaseModelEntityDTO
  {
    public UInspectQuestionTypeEnum QuestionType { get; set; }
    public uint UInspectSectionId { get; set; }

    public string QuestionText { get; set; }
    public string QuestionMask { get; set; }
    public uint MinMedia { get; set; }
    public uint MaxMedia { get; set; }
    public FieldMappingEnum? MappedTo { get; set; }
    public MediaTypeEnum MediaType { get; set; }
    public List<UInspectQuestionOptionDTO> Options { get; set; }
  }

  public class UInspectQuestionWithAnswerDTO : UInspectQuestionDTO 
  {
    public UInspectAnswerDTO Answer { get; set; }
    public List<UInspectMediaDTO> Medias { get; set; }
  }
}
