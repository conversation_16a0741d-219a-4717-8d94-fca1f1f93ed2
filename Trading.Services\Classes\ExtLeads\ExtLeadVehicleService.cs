﻿using AutoMapper;
using Microsoft.AspNetCore.JsonPatch;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO.ExtLeads;
using Trading.API.Data.DTO;
using Trading.API.Data.Enums;
using Trading.API.Data.Models.ExtLeads;
using Trading.API.Data;
using Trading.Services.Interfaces.ExtLeads;
using Microsoft.EntityFrameworkCore;
using Trading.Services.Extensions;
using Trading.API.Data.Models;
using Trading.Services.Interfaces;
using System.Runtime.Versioning;
using Trading.Services.ExternalDTO;

namespace Trading.Services.Classes.ExtLeads
{
  public class ExtLeadVehicleService : IExtLeadVehicleService
  {
    private readonly TradingContext _tradingContext;
    private readonly IMapper _mapper;
    private readonly IVehicleService _vehicleService;
    private readonly IAdvertService _advertService;

    public class ExtVehicleImportResult
    {
      public bool Success { get; set; }
      public Guid VehicleId { get; set; }
      public bool AlreadyImported { get; set; }
      public bool NoVRM { get; set; }
    }


    public ExtLeadVehicleService(TradingContext tradingContext, IMapper mapper, 
      IVehicleService vehicleService, IAdvertService advertService)
    {
      this._tradingContext = tradingContext;
      this._mapper = mapper;
      this._vehicleService = vehicleService;
      this._advertService = advertService;
    }

    public async Task<ValidatedResultDTO<ExtLeadVehicleDTO>> Create(ExtLeadVehicleDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        var extLeadVehicle = _mapper.Map<ExtLeadVehicleDTO, ExtLeadVehicle>(dto);
        extLeadVehicle.Updated = DateTime.Now;
        extLeadVehicle.Added = DateTime.Now;

        _tradingContext.Add(extLeadVehicle);

        await _tradingContext.SaveChangesAsync();

        var returnDTO = _mapper.Map<ExtLeadVehicle, ExtLeadVehicleDTO>(extLeadVehicle);

        return new ValidatedResultDTO<ExtLeadVehicleDTO>() { DTO = returnDTO, IsValid = true };
      }
      catch (Exception ex)
      {
        return new ValidatedResultDTO<ExtLeadVehicleDTO>() { IsValid = false, HTTPStatus = HTTPStatusEnum.InternalServerError };
      }
    }

    public async Task<ValidatedResultDTO<ExtLeadVehicleDTO>> Get(Guid id, ExtLeadVehicleSearchDTO dto, CancellationToken ct)
    {
      dto.Filters.Id = id;

      var res = await Search(dto, ct);

      if (res.Results.Count() > 0)
      {
        return new ValidatedResultDTO<ExtLeadVehicleDTO>() { DTO = res.Results.First(), IsValid = true };
      }
      else
      {
        return new ValidatedResultDTO<ExtLeadVehicleDTO>() { IsValid = false, HTTPStatus = HTTPStatusEnum.NotFound };
      }
    }

    public async Task<bool> Delete(Guid extLeadVehicleId, CancellationToken ct)
    {
      JsonPatchDocument<ExtLeadVehicle> patch = new JsonPatchDocument<ExtLeadVehicle>();

      patch.Replace(c => c.StatusId, (uint)StatusEnum.Deleted);

      await this.Patch(extLeadVehicleId, patch, ct);
      return true;
    }

    public async Task<ValidatedResultDTO<ExtLeadVehicleDTO>> Patch(Guid extLeadVehicleId, JsonPatchDocument<ExtLeadVehicle> patch, CancellationToken ct)
    {
      var extLeadVehicle = await _tradingContext.ExtLeadVehicles.Where(x => x.Id == extLeadVehicleId).FirstOrDefaultAsync();

      patch.FilterPatch();
      patch.ApplyTo(extLeadVehicle);

      extLeadVehicle.Updated = DateTime.Now;

      await _tradingContext.SaveChangesAsync(ct);

      var extLeadVehicleDTO = _mapper.Map<ExtLeadVehicle, ExtLeadVehicleDTO>(extLeadVehicle);

      return new ValidatedResultDTO<ExtLeadVehicleDTO>() { DTO = extLeadVehicleDTO, IsValid = true };
    }

    public async Task<SearchResultDTO<ExtLeadVehicleDTO>> Search(ExtLeadVehicleSearchDTO dto, CancellationToken cancellationToken)
    {
      var preQuery = _tradingContext.ExtLeadVehicles.AsQueryable();

      if (dto.Filters != null)
      {
        if (dto.Filters.ExtLeadId != null)
        {
          preQuery = preQuery.Where(x => x.ExtLeadId == dto.Filters.ExtLeadId);
        }
      }

      var query = preQuery.AsQueryable();

      var res = await query.AsNoTracking().ToListAsync(cancellationToken);
      var count = (dto == null || !dto.Limit.HasValue) ? res.Count : await preQuery.CountAsync(cancellationToken);
      var items = _mapper.Map<IEnumerable<ExtLeadVehicleDTO>>(res);
      var result = new SearchResultDTO<ExtLeadVehicleDTO> { TotalItems = count, Results = items };

      return result;
    }

    public async Task<ExtVehicleImportResult> ImportVehicleAsync(Guid vehicleId, CancellationToken ct)
    {
      // Get the vehicle
      var extVehicle = await _tradingContext.ExtLeadVehicles
          .Include(x => x.ExtLeadVehicleImages)
          .Include(x => x.ExtLead)
          .FirstOrDefaultAsync(v => v.Id == vehicleId, ct);

      if (extVehicle == null)
        throw new Exception($"Vehicle with ID {vehicleId} not found");

      // Check if already imported
      if (extVehicle.Imported)
        return new ExtVehicleImportResult { AlreadyImported = true };

      // if no vrm then exit early
      if (string.IsNullOrEmpty(extVehicle.Vrm))
      {
        return new ExtVehicleImportResult { NoVRM = true };
      }

      // get the customer and contact details from the lead 
      var extLead = await _tradingContext.ExtLeads
          .Include(x => x.Customer.PrimaryContact.PrimaryAddress)
          .FirstOrDefaultAsync(x => x.Id == extVehicle.ExtLeadId, ct);

      if (extLead == null)
        throw new Exception($"Lead with ID {extVehicle.ExtLeadId} not found");

      if (extLead.CustomerId == null)
        throw new Exception($"Customer ID not found for lead with ID {extVehicle.ExtLeadId}");

      var customerId = extLead.CustomerId.Value;
      var contactId = extLead.Customer.PrimaryContactId;
      var addressId = extLead.Customer.PrimaryContact.PrimaryAddressId;

      if (contactId == null)
        throw new Exception($"Primary Contact ID not found for customer with ID {customerId}");

      if (addressId == null)
        throw new Exception($"Address ID not found for contact with ID {contactId}");

      try
      {
        // Check if a vehicle with the same VRM already exists
        var existingVehicle = await _tradingContext.Vehicles
            .Where(v => v.Vrm == extVehicle.Vrm && !string.IsNullOrEmpty(v.Vrm))
            .FirstOrDefaultAsync(ct);

        if (existingVehicle == null)
        {
          var vehicleDTO = await _vehicleService.CreateVehicle(new ExternalDTO.CreateVehicleDTO {
            LogBook = extVehicle.V5Status == "1",
            Vrm = extVehicle.Vrm,
            AddressId = addressId,
            ContactId = contactId.Value,
            CustomerId = customerId,
            Odometer = (int?)extVehicle.Mileage
          }, ct);

          // update relevant data 
          var vehicle = await _tradingContext.Vehicles
              .FirstOrDefaultAsync(x => x.Id == vehicleDTO.Id, ct);

          vehicle.ServiceHistoryType = DetermineServiceHistoryType(extVehicle.ServiceHistory);
          vehicle.ServiceHistory = vehicle.ServiceHistoryType != ServiceHistoryTypeEnum.None;
          vehicle.Colour = extVehicle.Colour;

          _tradingContext.Update(vehicle);

          // create the draft advert 
          var advertDTO = await _advertService.CreateAdvert(
            new CreateAdvertDTO { VehicleId = vehicle.Id, ContactId = contactId.Value, CustomerId = customerId }, CancellationToken.None);
        }

        // Mark as imported
        extVehicle.Imported = true;
        _tradingContext.Update(extVehicle);

        await _tradingContext.SaveChangesAsync(ct);

        return new ExtVehicleImportResult
        {
          Success = true,
          VehicleId = existingVehicle?.Id ?? Guid.Empty
        };
      }
      catch (Exception ex)
      {
        //_logger.LogError(ex, "Error importing vehicle {VehicleId}. VRM: {Vrm}", vehicleId, extVehicle.Vrm);
        throw;
      }
    }

    private ServiceHistoryTypeEnum DetermineServiceHistoryType(string serviceHistory)
    {
      if (string.IsNullOrEmpty(serviceHistory))
        return ServiceHistoryTypeEnum.None;

      if (serviceHistory == "Part Service History" || serviceHistory == "Service History")
      {
        return ServiceHistoryTypeEnum.Partial;
      }
      else if (serviceHistory == "Full Service History")
      {
        return ServiceHistoryTypeEnum.Full;
      }
      else if (serviceHistory == "Full Dealership History")
      {
        return ServiceHistoryTypeEnum.FullFranchise;
      }

      return ServiceHistoryTypeEnum.None;
    }
  }
}
