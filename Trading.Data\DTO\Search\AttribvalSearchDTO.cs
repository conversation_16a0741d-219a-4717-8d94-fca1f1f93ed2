﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO
{
  public class AttribvalSearchDTO : BaseSearchDTO
  {
    public AttribvalFilters Filters { get; set; } = new AttribvalFilters() { };
  }

  public class AttribvalFilters : BaseFilterInt
  {
    public uint? AttribId { get; set; }
    public string AttribName { get; set; }
    public string AttribCode { get; set; }

  }
}