namespace Trading.API.Controllers
{
  using Microsoft.AspNetCore.Mvc;
  using System.Threading;
  using System.Threading.Tasks;
  using System;
  using Newtonsoft.Json;
  using Trading.Services.Interfaces;
  using Trading.API.Data.DTO.Comms;

  [Route("/api/commsEvent")]
  public class CommsEventController : ControllerBase
  {
    private readonly ICommsEventService _commsEventService;

    public CommsEventController(
          ICommsEventService commsEventService)
    {
      _commsEventService = commsEventService;
    }

    [HttpGet]
    [Route("/api/commsEvents")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var searchDTO = String.IsNullOrEmpty(query) ? null : JsonConvert.DeserializeObject<CommsEventSearchDTO>(query);
        var pageres = await _commsEventService.Search(searchDTO, cancellationToken);

        return Ok(pageres);
      }
      catch (Exception e)
      {
        return BadRequest(e);
      }
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<IActionResult> Delete(uint id, CancellationToken cancellationToken)
    {
      try
      {
        var pageres = await _commsEventService.Delete(id, cancellationToken);
        return Ok(pageres);
      }
      catch (Exception e)
      {
        return BadRequest(e);
      }
    }

    [HttpPut]
    [Route("{id}")]
    public async Task<IActionResult> Update([FromBody] CommsEventDTO dto, uint id, CancellationToken cancellationToken)
    {
      try
      {
        var pageres = await _commsEventService.Update(id, dto, cancellationToken);
        return Ok(pageres);
      }
      catch (Exception e)
      {
        return BadRequest(e);
      }
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create([FromBody] CommsEventDTO commsEvent, CancellationToken cancellationToken)
    {
      try
      {
        var pageres = await _commsEventService.Create(commsEvent, cancellationToken);
        return Ok(pageres);
      }
      catch (Exception e)
      {
        return BadRequest(e);
      }
    }
  }
}

