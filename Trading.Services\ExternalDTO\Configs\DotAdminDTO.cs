using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.Services.ExternalDTO.Configs
{
  public class DotAdminDTO
  {
    public string BaseUrl { get; set; } = "https://dev-stack-admin.dotadmin.net";
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public int TimeoutSeconds { get; set; } = 30;
    public int TokenRefreshBufferSeconds { get; set; } = 300; // 5 minutes before expiry
    public int DefaultCustomerId { get; set; }
    public int DefaultLocationId { get; set; }
    public bool UseCookieAuth { get; set; } = true; // Prefer cookie auth over bearer token
  }
}
