﻿using AutoMapper;
using Microsoft.AspNetCore.JsonPatch;
using Trading.API.Data.Enums;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.InspectCollect.Interfaces;
using System;
using Trading.Services.Extensions;
using Microsoft.EntityFrameworkCore;

namespace Trading.Services.InspectCollect.Classes
{
  public class ICContainerWidgetInputValidationService : ICContainerWidgetInputValidationInterface
  {
    private readonly TradingContext _context;
    private readonly IMapper _mapper;

    public ICContainerWidgetInputValidationService(TradingContext context, IMapper mapper)
    {
      _context = context;
      _mapper = mapper;
    }

    public async Task<ICContainerWidgetInputValidationDTO> Create(ICContainerWidgetInputValidationCreateDTO dto)
    {
      var cwi = _mapper.Map<ICContainerWidgetInputValidation>(dto);

      cwi.StatusId = (uint)StatusEnum.Active;
      cwi.Added = DateTime.Now;
      cwi.Updated = DateTime.Now;

      _context.ICContainerWidgetInputValidations.Add(cwi);

      await _context.SaveChangesAsync();

      return _mapper.Map<ICContainerWidgetInputValidationDTO>(cwi);
    }

    public async Task<bool> Delete(Guid id)
    {
      var icwi = _context.ICContainerWidgetInputValidations
        .Where(x => x.Id == id)
        .FirstOrDefault();

      _context.ICContainerWidgetInputValidations.Remove(icwi);
      await _context.SaveChangesAsync();

      return true;
    }

    public async Task<ValidatedResultDTO<ICContainerWidgetInputValidationDTO>> Get(Guid guid, ICContainerWidgetInputValidationSearchDTO searchDTO, CancellationToken cancellationToken)
    {
      if (searchDTO == null) { searchDTO = new ICContainerWidgetInputValidationSearchDTO(); }

      searchDTO.Filters.Id = guid;
      var cwi = await Search(searchDTO, cancellationToken);

      return new ValidatedResultDTO<ICContainerWidgetInputValidationDTO>()
      {
        IsValid = cwi.TotalItems > 0,
        DTO = cwi.Results.FirstOrDefault()
      };
    }

    public async Task<ICContainerWidgetInputValidationDTO> Patch(Guid id, JsonPatchDocument<ICContainerWidgetInputValidation> patch)
    {
      var cwi = await _context.ICContainerWidgetInputValidations.Where(x => x.Id == id).FirstOrDefaultAsync();

      if (cwi == null) { return null; }

      patch.FilterPatch();
      patch.ApplyTo(cwi);
      cwi.Updated = DateTime.Now;

      await _context.SaveChangesAsync();

      return _mapper.Map<ICContainerWidgetInputValidationDTO>(cwi);
    }

    public async Task<SearchResultDTO<ICContainerWidgetInputValidationDTO>> Search(ICContainerWidgetInputValidationSearchDTO searchDTO, CancellationToken cancellationToken)
    {
      var preQuery = _context.ICContainerWidgetInputValidations.AsQueryable();

      if (searchDTO.Filters != null)
      {
        if (searchDTO.Filters.Id != null)
        {
          preQuery = preQuery.Where(x => x.Id == searchDTO.Filters.Id);
        }
        if (searchDTO.Filters.ICContainerWidgetInputId != null)
        {
          preQuery = preQuery.Where(x => x.ICContainerWidgetInputId == searchDTO.Filters.ICContainerWidgetInputId);
        }
      }

      var query = preQuery;

      if (searchDTO.Limit.HasValue)
      {
        query = query.Take(searchDTO.Limit.Value);
        query = query.Skip(searchDTO.Offset.Value);
      }

      var items = await query
        .AsNoTracking()
        .ToListAsync(cancellationToken);

      return new SearchResultDTO<ICContainerWidgetInputValidationDTO>
      {
        TotalItems = (searchDTO.Limit.HasValue) ? await preQuery.CountAsync(cancellationToken) : items.Count(),
        Results = _mapper.Map<IEnumerable<ICContainerWidgetInputValidation>, IEnumerable<ICContainerWidgetInputValidationDTO>>(items)
      };
    }
  }
}
