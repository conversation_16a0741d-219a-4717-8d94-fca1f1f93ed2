using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using Trading.API.Data.DTO;
using Trading.API.Data.Enums;

namespace Trading.API.Data.Models.DTO
{
  public class ScanVehicleDTO : BaseModelEntityIntDTO
  {
    public Guid ScanVehicleGuid { get; set; }
    public uint? Errors { get; set; }
    public string CO2 { get; set; }
    public uint? Doors { get; set; }
    public string Vrm { get; set; }
    public string Title { get; set; }
    public uint? Odometer { get; set; }
    public OdometerUnitEnum OdometerUnit { get; set; }
    public string UniqueId { get; set; }
    public string Colour { get; set; }
    public string CapId { get; set; }
    public string CapCode { get; set; }
    public string Phone { get; set; }
    public string Email { get; set; }
    public string FullDesc { get; set; }
    public string DetailUrl { get; set; }
    public string PrimaryImageUrl { get; set; }
    public uint? Modelyear { get; set; }
    public uint? ImageCount { get; set; }
    public uint? EngineCC { get; set; }
    public uint? BatteryKwH { get; set; }
    public uint? PlateId { get; set; }
    public PlateDTO Plate { get; set; }
    public uint? BodyTypeId { get; set; }
    public BodyTypeDTO Body { get; set; }
    public CustomerDTO Customer { get; set; }
    public uint? CustomerId { get; set; }
    public ScanCustomerDTO ScanCustomer { get; set; }
    public uint? ScanCustomerId { get; set; }
    public DerivDTO Deriv { get; set; }
    public uint? DerivId { get; set; }
    public MakeDTO Make { get; set; }
    public uint? MakeId { get; set; }
    public ModelDTO Model { get; set; }
    public uint? ModelId { get; set; }
    public uint? FuelTypeId { get; set; }
    public FuelTypeDTO FuelType { get; set; }
    public uint? VehicleColourId { get; set; }
    public VehicleColourDTO VehicleColour { get; set; }
    public uint? TransmissionTypeId { get; set; }
    public TransmissionTypeDTO TransmissionType { get; set; }
    public uint? VehicleTypeId { get; set; }
    public VehicleTypeDTO VehicleType { get; set; }
    public uint Price { get; set; }
    public uint? MPG { get; set; }
    public DateTime? DateRegistered { get; set; }
    public virtual IEnumerable<ScanImageDTO> ScanImages { get; set; }
  }
}