﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.Search
{
  public class SearchCallRecordsDTO : BaseSearchDTO
  {
    public SearchCallRecordsFiltersDTO Filters { get; set; } = new SearchCallRecordsFiltersDTO() { };
  }


  public class SearchCallRecordsFiltersDTO : BaseFilterInt
  {
    public Guid? ContactId { get; set; } // who made the call 
    public int? DaysAgo { get; set; }
    public DateTime? CallDate {  get; set; }
    public string CallDirection { get; set; }
  }
}
