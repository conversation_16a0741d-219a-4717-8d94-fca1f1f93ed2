﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Remarq.Controllers.Accounting
{
  [Route("api/accounts")]
  [ApiController]
  [Authorize]
  public class AccountsController : ControllerBase
  {
    private readonly IXeroService _accountingService;

    public AccountsController(IXeroService accountingService)
    {
      _accountingService = accountingService;
    }

    [HttpGet("tenants")]
    [Authorize]
    public async Task<ActionResult> GetTentants()
    {
      if (!User.IsAdmin())
      {
        return Forbid();
      }

      var res = await _accountingService.ListConnectionsAsync();
      return Ok(res);
    }
  }
}
