﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.DTO.UInspections;
using Trading.API.Data.Enums.AppraisalLink;

namespace Trading.API.Data.DTO.AppraisalLinks
{
  public class AppraisalLinkDTO : BaseModelEntityDTO
  {
    // this is for external appraisal providers
    public Guid? ExternalLinkId { get; set; }

    public AppraisalLinkSourceEnum LinkSource { get; set; }

    public Guid? AppraisalLinkDataId { get; set; }
    public AppraisalLinkDataDTO AppraisalLinkData { get; set; }

    public Guid UInspectId { get; set; }
    public virtual UInspectDTO UInspect { get; set; }
  }
}
