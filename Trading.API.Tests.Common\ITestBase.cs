﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Tests.Common.Helpers;

namespace Trading.API.Tests.Common
{
  public interface ITestBase
  {
    IMapper Mapper { get; }
    TradingContext Context { get; }
    CommonServices Common { get; }
    LookupFactory LookupFactory { get; }

    string BaseCustomerId { get; }
    string BaseContactId { get; }

    Guid BaseCustomerIdGuid { get; }
    Guid BaseContactIdGuid { get; }
  }
}
