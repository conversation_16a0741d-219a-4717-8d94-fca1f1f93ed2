﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.Services;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/alt")]
  [ApiController]
  [Authorize]
  public class AltController : ControllerBase
  {
    private readonly IAltService _altService;
    private readonly IMergeService _mergeService;

    public struct PatchAltDTO
    {
      public string newValue { get; set; }
    }

    public AltController(IAltService altService, IMergeService mergeService)
    {
      _altService = altService;
      _mergeService = mergeService;
    }

    [HttpGet]
    [Route("pending/counts")]
    public async Task<IActionResult> Counts(CancellationToken cancellationToken)
    {
      return Ok(await _altService.PendingCounts(new AltSearchDTO(), cancellationToken));
    }

    [HttpGet]
    [Route("/api/altParents/{altTable}/vehicleType/{vehicleTypeId}")]
    public async Task<IActionResult> GetRecordsByVehicleTypeId(string altTable, uint vehicleTypeId, CancellationToken cancellationToken)
    {
      var result = await _altService.GetRecords(altTable, vehicleTypeId, null, cancellationToken);
      return Ok(result);
    }

    [HttpGet]
    [Route("/api/altParents/{altTable}/parent/{altParentId}")]
    public async Task<IActionResult> GetRecordsByParentId(string altTable, uint altParentId, CancellationToken cancellationToken)
    {
      var result = await _altService.GetRecords(altTable, null, altParentId, cancellationToken);
      return Ok(result);
    }

    [HttpGet]
    [Route("/api/altParents/{altTable}")]
    public async Task<IActionResult> GetRecordsByParentType(string altTable, CancellationToken cancellationToken)
    {
      var result = await _altService.GetRecords(altTable, null, null, cancellationToken);
      return Ok(result);
    }

    [HttpGet]
    [Route("/api/alt/autofix")]
    public async Task<IActionResult> AutoFix(CancellationToken cancellationToken)
    {
      var result = await _altService.AutoFix(cancellationToken);
      return Ok(result);
    }

    [HttpDelete]
    [Route("{altId}")]
    public async Task<IActionResult> RemoveAlt(uint altId, CancellationToken cancellationToken)
    {
      var result = await _altService.Remove(altId, cancellationToken);

      return Ok(result);
    }

    [HttpGet]
    [Route("nextPending/{altTable}")]
    public async Task<IActionResult> NextPending(string altTable)
    {
      return Ok(await _altService.NextPending(altTable, null));
    }
    [HttpGet]
    [Route("nextPending/{altTable}/vehicleTypeId/{vehicleTypeId}")]
    public async Task<IActionResult> NextPending(string altTable, uint vehicleTypeId)
    {
      return Ok(await _altService.NextPending(altTable, vehicleTypeId));
    }


    [HttpGet]
    [Route("{altTable}/altRealId/{altRealId}")]
    public async Task<IActionResult> FetchAlts(string altTable, uint altRealId, CancellationToken cancellationToken)
    {
      var result = await _altService.Search(new AltSearchDTO() { Filters = { AltTable = altTable, AltRealId = altRealId }}, cancellationToken);

      return Ok(result);
    }

    [HttpGet]
    [Route("{altTable}/vehicleType/{vehicleTypeId}/pending")]
    public async Task<IActionResult> PendingList(string altTable, uint vehicleTypeId, CancellationToken cancellationToken)
    {
      var search = new AltSearchDTO() { Filters = { 
          AltTable = altTable ,
          VehicleTypeId = vehicleTypeId,
          StatusId = (int) StatusEnum.Pending
        } };

      return Ok(await _altService.Search(search, cancellationToken));
    }

    [HttpPost]
    [Route("{altTable}/{id}")]
    public async Task<IActionResult> PatchAlt(string altTable, uint id, [FromBody] PatchAltDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        if (altTable == "make")
        {
          bool response = await _mergeService.ChangeMakeName(id, dto.newValue, cancellationToken);
          return Ok(response);
        }
        else if (altTable == "model") {
          bool response = await _mergeService.ChangeModelName(id, dto.newValue, cancellationToken);
          return Ok(response);
        }
        else if (altTable == "deriv")
        {
          bool response = await _mergeService.ChangeDerivName(id, dto.newValue, cancellationToken);
          return Ok(response);
        }
        else if (altTable == "body_type")
        {
          var response = await _mergeService.ChangeBodyTypeName(id, dto.newValue, cancellationToken);
          return Ok(response);
        }
        else if (altTable == "fuel_type")
        {
          bool response = await _mergeService.ChangeFuelTypeName(id, dto.newValue, cancellationToken);
          return Ok(response);
        }
        else if (altTable == "transmission_type")
        {
          bool response = await _mergeService.ChangeTransmissionTypeName(id, dto.newValue, cancellationToken);
          return Ok(response);
        }
        else if (altTable == "colour")
        {
          bool response = await _mergeService.ChangeColourName(id, dto.newValue, cancellationToken);
          return Ok(response);
        }

        return NotFound();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpGet]
    [Route("{altTable}/{fromId}/merge/{toId}")]
    public async Task<IActionResult> MergeAlt(string altTable, uint fromId, uint toId, CancellationToken cancellationToken)
    {
      try
      {
        if (altTable == "make")
        {
          await _mergeService.MergeMakes(fromId, toId, cancellationToken);
          return Ok();
        }
        else if (altTable == "model") {
          await _mergeService.MergeModels(fromId, toId, cancellationToken);
          return Ok();
        }
        else if (altTable == "deriv")
        {
          await _mergeService.MergeDerivs(fromId, toId, cancellationToken);
          return Ok();
        }
        else if (altTable == "fuel_type")
        {
          await _mergeService.MergeFuelTypes(fromId, toId, cancellationToken);
          return Ok();
        }
        else if (altTable == "colour")
        {
          await _mergeService.MergeColours(fromId, toId, cancellationToken);
          return Ok();
        }
        else if (altTable == "transmission_type")
        {
          await _mergeService.MergeTransmissionTypes(fromId, toId, cancellationToken);
          return Ok();
        }
        else if (altTable == "body_type")
        {
          await _mergeService.MergeBodyTypes(fromId, toId, cancellationToken);
          return Ok();
        }

        return NotFound();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{typeId}/{id}/pending/approve")]
    public async Task<IActionResult> ApproveType(string typeId, uint id, CancellationToken cancellationToken)
    {
      try
      {
        if (typeId == "make")
        {
          var response = await _mergeService.ApproveMake(id, cancellationToken);
          return Ok(response);
        }
        else if (typeId == "model")
        {
          var response = await _mergeService.ApproveModel(id, cancellationToken);
          return Ok(response);
        }
        else if (typeId == "deriv")
        {
          var response = await _mergeService.ApproveDeriv(id, cancellationToken);
          return Ok(response);
        }
        else if (typeId == "fuel_type")
        {
          var response = await _mergeService.ApproveFuelType(id, cancellationToken);
          return Ok(response);
        }
        else if (typeId == "body_type")
        {
          var response = await _mergeService.ApproveBodyType(id, cancellationToken);
          return Ok(response);
        }
        else if (typeId == "transmission_type")
        {
          var response = await _mergeService.ApproveTransmissionType(id, cancellationToken);
          return Ok(response);
        }
        else if (typeId == "colour")
        {
          var response = await _mergeService.ApproveColour(id, cancellationToken);
          return Ok(response);
        }

        return NotFound();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpGet]
    [Route("{typeId}/{id}/pending/reject")]
    public async Task<IActionResult> RejectType(string typeId, uint id, CancellationToken cancellationToken)
    {
      try
      {
        if (typeId == "make")
        {
          bool response = await _mergeService.RejectMake(id, cancellationToken);
          return Ok(response);
        }
        else if (typeId == "model")
        {
          bool response = await _mergeService.RejectModel(id, cancellationToken);
          return Ok(response);
        }
        else if (typeId == "deriv")
        {
          bool response = await _mergeService.RejectDeriv(id, cancellationToken);
          return Ok(response);
        }
        else if (typeId == "fuel_type")
        {
          bool response = await _mergeService.RejectFuelType(id, cancellationToken);
          return Ok(response);
        }
        else if (typeId == "body_type")
        {
          bool response = await _mergeService.RejectBodyType(id, cancellationToken);
          return Ok(response);
        }
        else if (typeId == "transmission_type")
        {
          bool response = await _mergeService.RejectTransmissionType(id, cancellationToken);
          return Ok(response);
        }
        else if (typeId == "colour")
        {
          bool response = await _mergeService.RejectColour(id, cancellationToken);
          return Ok(response);
        }

        return NotFound();

      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
