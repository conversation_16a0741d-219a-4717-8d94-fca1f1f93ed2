﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.DTO;

namespace Trading.API.Data.DTO.ExtLeads
{
  public class ExtLeadVehicleDTO : BaseModelEntityDTO
  {
    public Guid ExtLeadId { get; set; }
    public string Vehicle { get; set; }
    public uint? Mileage { get; set; }
    public uint? Year { get; set; }
    public string UniqueId { get; set; }
    public uint? RetailPrice { get; set; }
    public uint? TradePrice { get; set; }
    public uint? Margin { get; set; }
    public uint? SaleType { get; set; }

    public string Vrm { get; set; }

    public string VatStatus { get; set; }
    public short? Keys { get; set; }

    public string Colour { get; set; }

    public string Description { get; set; } 

    public string ServiceHistory { get; set; }

    public string V5Status { get; set; }
    public int? ReservePrice { get; set; }

    public bool Imported { get; set; }
  }

  public class ExtLeadVehicleSearchDTO : BaseSearchDTO
  {
    public ExtLeadVehicleFiltersDTO Filters { get; set; } = new ExtLeadVehicleFiltersDTO() { };
  }

  public class ExtLeadVehicleFiltersDTO : BaseFilterGuid
  {
    public Guid? ExtLeadId { get; set; }
  }
}
