﻿using System;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICContainerWidgetStyleDTO : BaseModelEntityDTO
  {
    public Guid? ICContainerWidgetId { get; set; }
    public ICContainerWidgetDTO? ICContainerWidget { get; set; }
    public Guid? ICStyleId { get; set; }
    public ICStyleDTO? ICStyle { get; set; }
    public uint? Position { get; set; }
  }

  public class ICContainerWidgetStyleSearchDTO : BaseSearchDTO
  {
    public ICContainerWidgetStyleSearchFilters Filters { get; set; } = new ICContainerWidgetStyleSearchFilters();
  }

  public class ICContainerWidgetStyleSearchFilters : BaseFilter
  {
    public Guid? ICContainerWidgetId { get; set; }
    public Guid? ICStyleId { get; set; }
  }

  public class ICContainerWidgetStyleCreateDTO
  {
    public Guid? ICContainerWidgetId { get; set; }
  }
}
