﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums.Valuation;

namespace Trading.API.Data.DTO.Search.Valuation
{
  public class ValuationSearchDTO : BaseSearchDTO
  {
    public ValuationFilters Filters { get; set; } = new ValuationFilters();
  }

  public class ValuationFilters : BaseFilterInt
  {
    public Guid? ValuationProfileId { get; set; }
  }
}
