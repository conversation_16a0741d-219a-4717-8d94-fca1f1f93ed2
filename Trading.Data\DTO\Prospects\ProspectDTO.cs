﻿using System;
using System.Collections.Generic;
using Trading.API.Data.Enums.Prospects;

namespace Trading.API.Data.DTO.Prospects
{
  public class ProspectDTO : BaseModelEntityDTO
  {
    public Guid? BrokerageId { get; set; }

    public Guid ProspectContactId { get; set; }

    public ContactDTO ProspectContact { get; set; }

    public uint ProspectStateId { get; set; } = 1;

    public bool ManuallyAdded { get; set; }
    public bool FromBidder { get; set; }
    public bool FromWatcher { get; set; }
    public bool FromViewer { get; set; }
    public bool FromAlert { get; set; }
    public bool FromEnquiry { get; set; }
    public Guid? AssignedTo { get; set; }

    //public List<ProspectHistoryDTO> ProspectHistories { get; set; }
    public List<AdvertNoteDTO> AdvertNotes { get; set; }
  }
}
