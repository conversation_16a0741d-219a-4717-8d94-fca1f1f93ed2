﻿@using Trading.API.Views.XeroTokenRenewed
@model dynamic


@{
}

<style>

    body {
        font-family: Arial;
    }
</style>

<h1>Xero Token Refreshed</h1>

<p>Details stored in the database shown below (for debug purposes)</p>

<table border="1">
    <tr>
        <td nowrap>Access Token</td><td>@Model.AccessToken</td>
     </tr>

    <tr>
        <td nowrap>Id Token</td><td>@Model.IdToken</td>
    </tr>
    <tr>
        <td nowrap>Refresh Token</td><td>@Model.RefreshToken</td>
    </tr>
    <tr>
        <td nowrap>Expires At UTC</td><td>@Model.ExpiresAtUtc</td>
    </tr>
</table>
