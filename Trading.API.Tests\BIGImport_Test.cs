﻿//using Microsoft.Extensions.Options;
//using Moq;
//using System.Collections.Generic;
//using System.Linq;
//using System.Threading;
//using System.Threading.Tasks;
//using Trading.API.Data.DTO;
//using Trading.API.Tests.ClassData;
//using Trading.API.Tests.Common;
//using Trading.Services;
//using Trading.Services.Classes;
//using Trading.Services.Custom;
//using Trading.Services.ExternalDTO;
//using Trading.Services.ExternalDTO.BIG;
//using Trading.Services.Imports;
//using Trading.Services.Interfaces;
//using Trading.Services.Interfaces.Custom;
//using Trading.Services.Interfaces.Imports;
//using Xunit;

//namespace Trading.API.Tests
//{

//  [Collection("DatabaseCollection")]
//  public class BIGImport_Test : TestBase
//  {
//    public DatabaseFixture _fixture;

//    public BIGImport_Test(DatabaseFixture fixture)
//    {
//      _fixture = fixture;
//    }

//    private IBIGImportService GetService()
//    {
//      IAppraisalService appraisalService = new AppraisalService(_context, _mapper, _common.FileStorageService);

//      IVehicleCheckService checkService = new Mock<IVehicleCheckService>().Object;

//      var config = new Mock<IOptionsSnapshot<CapHPIDTO>>();
//      config.SetupAllProperties();
//      config.SetupGet(p => p.Value).Returns(new CapHPIDTO { });
//      IVRMLookupService vrmLookupService = new CapHpiVehicleLookupService(_context, _mapper, _common.LookupService, config.Object, checkService);

//      IYTService youtubeService = new YTService(_context, new Mock<IOptionsSnapshot<YoutubeDTO>>().Object);
//      IVehicleMediaService mediaService = new VehicleMediaService(_context, _mapper, _common.FileStorageService, _common.MessageService, _common.UserService, youtubeService);
//      IVehicleService vehicleService = new VehicleService(_context, null, _mapper, _common.FileStorageService, mediaService, 
//        _common.LookupService, _common.Configuration
//        , _common.DVLAService, appraisalService, _common.UserService, youtubeService, _common.MessageService, new List<IVRMLookupService> { vrmLookupService }, checkService);

//      IImportLoggingService importLogging = new ImportLoggingService(_context, _mapper);

//      var kipperConfig = new Mock<IOptionsSnapshot<KipperDTO>>();
//      kipperConfig.SetupAllProperties();
//      kipperConfig.SetupGet(p => p.Value).Returns(new KipperDTO { });
//      IBIGImportService service = new BIGImportService(_context, _common.Mapper, vehicleService, 
//        _common.FileStorageService, GetAdvertService(), mediaService, appraisalService, importLogging, new List<IVRMLookupService> { vrmLookupService }, kipperConfig.Object);

//      return service;
//    }

//    private IAdvertService GetAdvertService()
//    {
//      var dpDTO = new Mock<IOptionsSnapshot<DefaultPlatformDTO>>();
//      dpDTO.SetupAllProperties();
//      dpDTO.SetupGet(p => p.Value).Returns(new DefaultPlatformDTO { PlatformId = 1 });
//      IBidService bidService = new BidService(_common.DealService, _common.DbConnection, _context, _mapper, 
//        _common.MessageService, _common.InMailService, _common.NegotiationService, _common.EmailService, dpDTO.Object);
//      IAdvertService adService = new AdvertService(_context, bidService, _mapper, _common.ContactService
//        , _common.CustomerService, _common.LookupService, _common.UserService, _common.MessageService, 
//        _common.DbConnection, _common.ContactActionService, null, _common.CustomerInternalInfoService);

//      return adService;
//    }


//    [Theory]
//    [ClassData(typeof(BIGImportTestClassData))]
//    public async Task ImportVehicle(BIGImportDTO data)
//    {
//      var service = GetService();
//      var advertId = await service.ImportBIGVehicle(data, CancellationToken.None);

//      // load up the advert and test various properties exist 
//      var advertService = GetAdvertService();
//      var advertSearchResult = await advertService.Search(new AdvertSearchDTO { 
//        Component = "AdvertView", 
//        Filters = new AdvertSearchFiltersDTO { Id = advertId } 
//      }, CancellationToken.None);

//      var advert = advertSearchResult.Advert;

//      Assert.NotNull(advert);
//      Assert.NotNull(advert.Vehicle);

//      if (data.VehicleImages.Count > 0)
//      {
//        Assert.NotNull(advert.Vehicle.VehicleMedia);
//        Assert.True(advert.Vehicle.VehicleMedia.Count() ==  data.VehicleImages.Count);
//      }

//      if (data.ServiceHistoryItems.Count > 0)
//      {
//        Assert.NotNull(advert.Vehicle.ServiceHistories);
//        Assert.True(advert.Vehicle.ServiceHistories.Count() == data.ServiceHistoryItems.Count);
//      }

//      if (data.AppraisalItems.Count > 0)
//      {
//        Assert.NotNull(advert.Vehicle.Appraisals);

//        var appraisalItemCount = advert.Vehicle.Appraisals.SelectMany(x => x.AppraisalItems).Count();
//        Assert.True(appraisalItemCount == data.AppraisalItems.Count);
//      }

//      // check advert is published and data is correct
//      Assert.True(advert.AdvertStatus == Data.Enums.AdvertStatusEnum.Active
//        && advert.SoldStatus == Data.Enums.SoldStatusEnum.Active);
//    }
//  }
//}
