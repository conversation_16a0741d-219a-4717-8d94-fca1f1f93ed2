﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Trading.API.Data.Migrations
{
    /// <inheritdoc />
    public partial class ICOutcomeContainerGroup : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "ICContainerGroupId",
                table: "ICVehicleOutcome",
                type: "binary(16)",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "IX_ICVehicleOutcome_ICContainerGroupId",
                table: "ICVehicleOutcome",
                column: "ICContainerGroupId");

            migrationBuilder.AddForeignKey(
                name: "FK_ICVehicleOutcome_ICContainerGroup_ICContainerGroupId",
                table: "ICVehicleOutcome",
                column: "ICContainerGroupId",
                principalTable: "ICContainerGroup",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ICVehicleOutcome_ICContainerGroup_ICContainerGroupId",
                table: "ICVehicleOutcome");

            migrationBuilder.DropIndex(
                name: "IX_ICVehicleOutcome_ICContainerGroupId",
                table: "ICVehicleOutcome");

            migrationBuilder.DropColumn(
                name: "ICContainerGroupId",
                table: "ICVehicleOutcome");
        }
    }
}
