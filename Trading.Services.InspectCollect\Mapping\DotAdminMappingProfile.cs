using AutoMapper;
using Trading.API.Data.DTO.DotAdmin;
using Trading.API.Data;
using Trading.API.Data.Models.InspectCollect.VehicleData; // Adjust namespace as needed for ICVehicle

namespace Trading.Services.InspectCollect.Mappings;

/// <summary>
/// AutoMapper profile for mapping between ICVehicle and dotAdmin DTOs
/// </summary>
public class DotAdminMappingProfile : Profile
{
  public DotAdminMappingProfile()
  {
    CreateMap<ICVehicle, DotAdminCreateVehicleRequest>()
      .ForMember(dest => dest.MotorVehicleRegistration, opt => opt.MapFrom(src => src.VRM))
      .ForMember(dest => dest.MotorVehicleVin, opt => opt.MapFrom(src => src.VIN))
      .ForMember(dest => dest.MotorVehicleMileage, opt => opt.MapFrom(src => src.Odometer))
      .ForMember(dest => dest.MotorVehicleManufacturer, opt => opt.MapFrom(src => src.MakeName))
      .ForMember(dest => dest.MotorVehicleModel, opt => opt.MapFrom(src => src.ModelName))
      .ForMember(dest => dest.MotorVehicleVariant, opt => opt.MapFrom(src => src.DerivName))
      .ForMember(dest => dest.MotorVehicleColour, opt => opt.MapFrom(src => src.Colour))
      .ForMember(dest => dest.MotorVehicleBodyStyle, opt => opt.MapFrom(src => src.BodyTypeName))
      .ForMember(dest => dest.MotorVehicleDoorCount, opt => opt.MapFrom(src => src.Doors))
      .ForMember(dest => dest.MotorVehicleExactCc, opt => opt.MapFrom(src => src.EngineCC))
      .ForMember(dest => dest.MotorVehicleFuelType, opt => opt.MapFrom(src => src.FuelTypeName))
      .ForMember(dest => dest.MotorVehicleYearOfManufacture, opt => opt.MapFrom(src => src.YearOfManufacture))
      .ForMember(dest => dest.MotorVehicleGearboxType, opt => opt.MapFrom(src => src.TransmissionTypeName))
      .ForMember(dest => dest.MotorVehicleBhp, opt => opt.MapFrom(src => src.BHP))
      .ForMember(dest => dest.MotorVehicleCo2, opt => opt.MapFrom(src => src.CO2))

      // Set default values for required fields
      .ForMember(dest => dest.Save, opt => opt.MapFrom(src => "1"))
      .ForMember(dest => dest.Lookup, opt => opt.MapFrom(src => true))

      // These will be set by the service layer
      .ForMember(dest => dest.VendorId, opt => opt.Ignore())
      .ForMember(dest => dest.LogisticsLocationId, opt => opt.Ignore())
      .ForMember(dest => dest.MotorVehicleTypeId, opt => opt.Ignore())
      .ForMember(dest => dest.MotorVehicleClassificationId, opt => opt.Ignore())

      // Optional fields that may not be available in ICVehicle
      .ForMember(dest => dest.VendorHold, opt => opt.Ignore())
      .ForMember(dest => dest.MotorVehicleTitle, opt => opt.Ignore())
      .ForMember(dest => dest.MotorVehicleMotExpires, opt => opt.Ignore())
      .ForMember(dest => dest.MotorVehicleReserve, opt => opt.Ignore())
      .ForMember(dest => dest.MotorVehicleStandInValue, opt => opt.Ignore())
      .ForMember(dest => dest.MotorVehicleComments, opt => opt.Ignore());

    // Additional mappings for other entities if needed
    CreateMap<DotAdminVehicle, VehicleSummaryDto>()
      .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.IdAsInt))
      .ForMember(dest => dest.Registration, opt => opt.MapFrom(src => src.Registration))
      .ForMember(dest => dest.TypeId, opt => opt.MapFrom(src => src.TypeId));
  }
}

/// <summary>
/// DTO for vehicle summary information
/// </summary>
public class VehicleSummaryDto
{
  public int Id { get; set; }
  public string Registration { get; set; }
  public int TypeId { get; set; }
}