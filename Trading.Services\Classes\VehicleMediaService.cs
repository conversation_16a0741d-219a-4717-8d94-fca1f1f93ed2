﻿using AutoMapper;
using Google.Apis.Auth.OAuth2;
using Google.Apis.Auth.OAuth2.Responses;
using Google.Apis.Services;
using Google.Apis.Upload;
using Google.Apis.YouTube.v3;
using Google.Apis.YouTube.v3.Data;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Azure;
using Microsoft.Extensions.Options;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Vehicle;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.API.Data.Models.MechanicalFaults;
using Trading.Services.Classes.LambdaFunctions;
using Trading.Services.Extensions;
using Trading.Services.ExternalDTO;
using Trading.Services.Helpers;
using Trading.Services.Interfaces;
using Trading.Services.Interfaces.LambdaFunctions;
using static Trading.Services.DTOMappingProfiles.MappingProfile;

namespace Trading.Services.Classes
{
  public class VehicleMediaService : IVehicleMediaService
  {
    private readonly TradingContext _tradingContext;
    private readonly IMapper _mapper;
    private readonly IFileStorageService _fileStorageService;
    private readonly IMessageService _messageService;
    private readonly IUserService _userService;
    private readonly IYTService _ytService;
    private readonly ILambdaFunctionsService _lambdaFunctionsService;

    public VehicleMediaService(TradingContext tradingContext
      , IMapper mapper
      , IFileStorageService fileStorageService
      , IMessageService messageService
      , IUserService userService
      , IYTService ytService
      , ILambdaFunctionsService lambdaFunctionsService)
    {
      _tradingContext = tradingContext;
      _mapper = mapper;
      _fileStorageService = fileStorageService;
      _messageService = messageService;
      _userService = userService;
      _ytService = ytService;
      _lambdaFunctionsService = lambdaFunctionsService;
    }

    private struct ECodes
    {
      public static ExceptionData NoVehicleMedia = new ExceptionData("VM1", "Could not find specified Vehicle Media record");
    }

    public async Task<uint> UpdateVehicleImageCount(Guid vehicleId, CancellationToken cancellationToken)
    {
      var x = await _tradingContext.VehicleMedia
       .Where(x => x.StatusId == (uint)StatusEnum.Active && x.VehicleId == vehicleId && x.MediaTypeId == (uint)MediaTypeEnum.Image)
       .AsNoTracking()
       .CountAsync();

      var v = await _tradingContext.Vehicles.Where(x => x.Id == vehicleId).FirstOrDefaultAsync(cancellationToken);

      v.ImageCount = (uint)x;

      await _tradingContext.SaveChangesAsync(cancellationToken);

      return (uint)x;
    }

    public async Task<bool> SetVehicleMediaStatus(Guid id, StatusEnum statusId, CancellationToken cancellationToken)
    {
      var vm = await _tradingContext.VehicleMedia
        .Where(x => x.Id == id)
        .Include(x => x.Vehicle)
        .FirstOrDefaultAsync(cancellationToken);

      if (vm == null)
      {
        throw new Exception(ExceptionHelper.NotFound(ECodes.NoVehicleMedia));
      }

      vm.StatusId = (uint)statusId;

      // if this is the primary vehicle image then assign the next image to be the primary (if we have one)
      if (vm.Vehicle.PrimaryImageId == vm.Id)
      {
        var nextImage = await _tradingContext.VehicleMedia
          .Where(x => x.VehicleId == vm.VehicleId
            && x.MediaTypeId == vm.MediaTypeId
            && x.StatusId == (int)StatusEnum.Active
            && x.Id != vm.Vehicle.PrimaryImageId
            )
          .OrderBy(x => x.Sequence)
          .AsNoTracking()
          .FirstOrDefaultAsync(cancellationToken);

        vm.Vehicle.PrimaryImageId = nextImage == null ? null : nextImage.Id;
      }

      await _tradingContext.SaveChangesAsync(cancellationToken);

      // If it's a video, delete that from Youtube also
      //if (vm.MediaTypeId == (int)MediaTypeEnum.Video)
      //{
      //    await _ytService.DeleteVideo(vm.ExternalId);
      //}

      return true;
    }

    public async Task<bool> VehicleMediaExists(Guid id)
    {
      return _tradingContext.VehicleMedia.Any(e => e.Id == id);
    }

    public async Task<bool> UpdateSequences(Guid id, IEnumerable<VehicleMediaDTO> dto)
    {
      foreach (var item in dto)
      {
        var vehicleMedia = await _tradingContext.VehicleMedia.Where(x => x.Vehicle.Id == id && x.Id == item.Id).FirstOrDefaultAsync();

        if (vehicleMedia != null)
        {
          vehicleMedia.Sequence = item.Sequence;
        }
      }

      await _tradingContext.SaveChangesAsync();

      if (dto.Count() > 0)
      {
        var vehicle = await _tradingContext.Vehicles.Where(x => x.Id == id).FirstOrDefaultAsync(); ;

        if (vehicle != null)
        {
          vehicle.PrimaryImageId = (Guid)dto.First().Id;
          await _tradingContext.SaveChangesAsync();
        }
      }

      return true;
    }

    public async Task<IEnumerable<VehicleMediaDTO>> UploadMediaFromURLs(Guid customerId, VehicleMediaUploadDTO uploadDTO, CancellationToken cancellationToken)
    {
      var now = DateTime.Now;
      List<VehicleMedia> vehicleMedias = new List<VehicleMedia>();
      var vehicleId = uploadDTO.VehicleId;
      var vehicle = await _tradingContext.Vehicles
          .Include(x => x.VehicleMedia)
          .Where(x => x.Id == vehicleId)
          .FirstOrDefaultAsync();

      if (vehicle == null)
      {
        throw new ApplicationException(string.Format("Could not find vehicle with id: {0}", vehicleId));
      }

      if (customerId != vehicle.CustomerId && _userService.IsAdminOrGreater())
      {
        customerId = vehicle.CustomerId.Value;
      }

      // get media category (condition report, v5 etc.) if category code is specified 
      uint? categoryId = null;
      if (!string.IsNullOrEmpty(uploadDTO.MediaCategoryCode))
      {
        var attribVal = await _tradingContext.Attribvals.FirstOrDefaultAsync(x => x.AttribvalCode == uploadDTO.MediaCategoryCode);
        if (attribVal != null)
        {
          categoryId = attribVal.Id;
        }
      }

      bool useFirstAsPrimary = !uploadDTO.MediaURLContent.Any(x => x.IsPrimary);
      int sequence = vehicle.VehicleMedia?.Count() ?? 0 + 1;

      foreach (var urlContent in uploadDTO.MediaURLContent)
      {
        // Check file size and resize if needed
        var processedImageUrl = await ProcessImageIfNeeded(urlContent.Url, cancellationToken);

        // Create entities but don't save yet
        var media = new Media()
        {
          StatusId = (uint)StatusEnum.Active,
          Added = now,
          Updated = now,
          MediaURL = processedImageUrl // Use processed URL
        };

        var vehicleMedia = new VehicleMedia()
        {
          Added = now,
          Updated = now,
          VehicleId = vehicleId,
          Media = media,
          MediaTypeId = (uint)urlContent.MediaType,
          StatusId = (uint)StatusEnum.Active,
          ExternalId = urlContent.Tag,
          MediaCategoryId = categoryId,
          IsSpinImage = urlContent.IsSpinImage,
          IsInternal = urlContent.IsInternal,
          Sequence = Convert.ToSByte(sequence++)
        };

        vehicle.VehicleMedia.Add(vehicleMedia);
        vehicleMedias.Add(vehicleMedia);

        // Save to get the ID
        await _tradingContext.SaveChangesAsync(cancellationToken);

        // Upload to S3 BEFORE ImageKit might access it
        string key = URLHelper.VehicleMediaKey(
            new VehicleMediaURLDTO()
            {
              CustomerId = customerId,
              VehicleId = vehicleId,
              VehicleMediaId = vehicleMedia.Id
            }
        );

        await _fileStorageService.UploadFileToStorage(key, processedImageUrl, cancellationToken);

        // Now handle primary image logic
        if (useFirstAsPrimary && string.IsNullOrEmpty(uploadDTO.MediaCategoryCode) &&
            (vehicle.PrimaryImageId == Guid.Empty || !vehicle.PrimaryImageId.HasValue))
        {
          vehicle.PrimaryImageId = vehicleMedia.Id;
          await _tradingContext.SaveChangesAsync(cancellationToken);
        }

        if (!useFirstAsPrimary && urlContent.IsPrimary)
        {
          vehicle.PrimaryImageId = vehicleMedia.Id;
          await _tradingContext.SaveChangesAsync(cancellationToken);
        }
      }

      var x = await UpdateVehicleImageCount(vehicleId, cancellationToken);
      return _mapper.Map<IEnumerable<VehicleMedia>, IEnumerable<VehicleMediaDTO>>(vehicleMedias);
    }

    public async Task<IEnumerable<VehicleMediaDTO>> UploadMediaFromURLsWithLogoSwap(
            Guid customerId,
            VehicleMediaUploadDTO uploadDTO,
            int pixelLogoHeight,
            CancellationToken cancellationToken)
    {
      var now = DateTime.Now;
      List<VehicleMedia> vehicleMedias = new List<VehicleMedia>();
      var vehicleId = uploadDTO.VehicleId;
      var vehicle = await _tradingContext.Vehicles
          .Include(x => x.VehicleMedia)
          .Where(x => x.Id == vehicleId)
          .FirstOrDefaultAsync();

      if (vehicle == null)
      {
        throw new ApplicationException($"Could not find vehicle with id: {vehicleId}");
      }

      if (customerId != vehicle.CustomerId && _userService.IsAdminOrGreater())
      {
        customerId = vehicle.CustomerId.Value;
      }

      // get media category (condition report, v5 etc.) if category code is specified 
      uint? categoryId = null;
      if (!string.IsNullOrEmpty(uploadDTO.MediaCategoryCode))
      {
        var attribVal = await _tradingContext.Attribvals.FirstOrDefaultAsync(x => x.AttribvalCode == uploadDTO.MediaCategoryCode);
        if (attribVal != null)
        {
          categoryId = attribVal.Id;
        }
      }

      bool useFirstAsPrimary = !uploadDTO.MediaURLContent.Any(x => x.IsPrimary);
      int sequence = vehicle.VehicleMedia?.Count() ?? 0 + 1;

      // First, create all VehicleMedia entities and save them to get the actual IDs
      foreach (var urlContent in uploadDTO.MediaURLContent)
      {
        var media = new Media()
        {
          StatusId = (uint)StatusEnum.Active,
          Added = now,
          Updated = now,
          MediaURL = urlContent.Url // Initially use original URL
        };

        var vehicleMedia = new VehicleMedia()
        {
          Added = now,
          Updated = now,
          VehicleId = vehicleId,
          Media = media,
          MediaTypeId = (uint)urlContent.MediaType,
          StatusId = (uint)StatusEnum.Active,
          ExternalId = urlContent.Tag,
          MediaCategoryId = categoryId,
          IsSpinImage = urlContent.IsSpinImage,
          IsInternal = urlContent.IsInternal,
          Sequence = Convert.ToSByte(sequence++)
        };

        vehicle.VehicleMedia.Add(vehicleMedia);
        vehicleMedias.Add(vehicleMedia);
      }

      // Save all entities to get the actual IDs
      await _tradingContext.SaveChangesAsync(cancellationToken);

      // Now prepare image processing requests with the actual VehicleMedia IDs
      var imageRequests = vehicleMedias.Select(vm => new ImageProcessingRequest
      {
        Url = vm.Media.MediaURL,
        VehicleMediaId = vm.Id, // Use the actual database-generated ID
        Tag = vm.ExternalId
      }).ToList();

      // Try logo processing
      Dictionary<Guid, ProcessedImageResult> processedImages = null;
      bool logoProcessingSucceeded = false;

      try
      {
        var results = await _lambdaFunctionsService.ProcessImagesWithLogoSwap(
            imageRequests,
            customerId,
            vehicleId,
            pixelLogoHeight,
            cancellationToken);

        processedImages = results.ToDictionary(r => r.VehicleMediaId, r => r);
        logoProcessingSucceeded = true;
        //_logger.LogInformation($"Successfully processed {results.Count} images with logo swap via Lambda");
      }
      catch (Exception ex)
      {
        // Logo processing failed, we'll fall back to original processing for all images
        //_logger.LogWarning($"Logo processing failed, falling back to original processing: {ex.Message}");
        logoProcessingSucceeded = false;
        processedImages = null;
      }

      // Process each VehicleMedia entity
      for (int i = 0; i < vehicleMedias.Count; i++)
      {
        var vehicleMedia = vehicleMedias[i];
        var urlContent = uploadDTO.MediaURLContent.ElementAt(i);

        string mediaUrl;
        bool skipS3Upload = false;

        if (logoProcessingSucceeded && processedImages?.ContainsKey(vehicleMedia.Id) == true)
        {
          // Use the processed image from Lambda
          var processedImage = processedImages[vehicleMedia.Id];
          mediaUrl = processedImage.S3Url;
          skipS3Upload = true; // Lambda already uploaded it
                               //_logger.LogDebug($"Using Lambda-processed image for VehicleMedia ID {vehicleMedia.Id}");
        }
        else
        {
          // Fallback to original processing method
          mediaUrl = await ProcessImageIfNeeded(urlContent.Url, cancellationToken);
          skipS3Upload = false;
          //_logger.LogDebug($"Using original processing for VehicleMedia ID {vehicleMedia.Id}");
        }

        // Update the media URL if it changed
        if (vehicleMedia.Media.MediaURL != mediaUrl)
        {
          vehicleMedia.Media.MediaURL = mediaUrl;
          vehicleMedia.Media.Updated = now;
        }

        // Only upload to S3 if Lambda didn't already do it
        if (!skipS3Upload)
        {
          string key = URLHelper.VehicleMediaKey(new VehicleMediaURLDTO()
          {
            CustomerId = customerId,
            VehicleId = vehicleId,
            VehicleMediaId = vehicleMedia.Id
          });

          await _fileStorageService.UploadFileToStorage(key, mediaUrl, cancellationToken);
        }

        // Handle primary image logic
        if (useFirstAsPrimary && string.IsNullOrEmpty(uploadDTO.MediaCategoryCode) &&
            (vehicle.PrimaryImageId == Guid.Empty || !vehicle.PrimaryImageId.HasValue))
        {
          vehicle.PrimaryImageId = vehicleMedia.Id;
          useFirstAsPrimary = false; // Only set the first one as primary
        }

        if (!useFirstAsPrimary && urlContent.IsPrimary)
        {
          vehicle.PrimaryImageId = vehicleMedia.Id;
        }
      }

      // Save any updates made during processing
      await _tradingContext.SaveChangesAsync(cancellationToken);

      var imageCount = await UpdateVehicleImageCount(vehicleId, cancellationToken);
      return _mapper.Map<IEnumerable<VehicleMedia>, IEnumerable<VehicleMediaDTO>>(vehicleMedias);
    }

    // Helper method to get file size from URL
    private async Task<long> GetFileSizeFromUrl(string url, CancellationToken cancellationToken)
    {
      try
      {
        using var httpClient = new HttpClient();
        using var response = await httpClient.SendAsync(new HttpRequestMessage(HttpMethod.Head, url), cancellationToken);
        response.EnsureSuccessStatusCode();

        if (response.Content.Headers.ContentLength.HasValue)
        {
          return response.Content.Headers.ContentLength.Value;
        }

        // Fallback: download first few KB to estimate
        using var partialResponse = await httpClient.GetAsync(url, HttpCompletionOption.ResponseHeadersRead, cancellationToken);
        return partialResponse.Content.Headers.ContentLength ?? 0;
      }
      catch (Exception ex)
      {
        // Log error and return 0 to skip resizing
        Console.WriteLine($"Error getting file size for {url}: {ex.Message}");
        return 0;
      }
    }

    // Helper method to process image if it's too large
    private async Task<string> ProcessImageIfNeeded(string originalUrl, CancellationToken cancellationToken)
    {
      try
      {
        var fileSize = await GetFileSizeFromUrl(originalUrl, cancellationToken);

        // If file is larger than 1MB, resize it
        if (fileSize > 1000000)
        {
          Console.WriteLine($"Image {originalUrl} is {fileSize} bytes, resizing...");
          return await ResizeImage(originalUrl, cancellationToken);
        }

        return originalUrl;
      }
      catch (Exception ex)
      {
        Console.WriteLine($"Error processing image {originalUrl}: {ex.Message}");
        return originalUrl; // Return original if processing fails
      }
    }

    // Helper method to resize image
    private async Task<string> ResizeImage(string imageUrl, CancellationToken cancellationToken)
    {
      try
      {
        using var httpClient = new HttpClient();
        var imageBytes = await httpClient.GetByteArrayAsync(imageUrl, cancellationToken);

        // Using System.Drawing or ImageSharp for resizing
        // This example assumes you have ImageSharp installed: Install-Package SixLabors.ImageSharp
        using var image = SixLabors.ImageSharp.Image.Load(imageBytes);

        // Calculate new dimensions (max width 1920px, maintain aspect ratio)
        var maxWidth = 1920;
        var maxHeight = 1080;

        if (image.Width > maxWidth || image.Height > maxHeight)
        {
          var ratioX = (double)maxWidth / image.Width;
          var ratioY = (double)maxHeight / image.Height;
          var ratio = Math.Min(ratioX, ratioY);

          var newWidth = (int)(image.Width * ratio);
          var newHeight = (int)(image.Height * ratio);

          image.Mutate(x => x.Resize(newWidth, newHeight));
        }

        // Save to temporary location or memory stream
        using var resizedStream = new MemoryStream();
        await image.SaveAsJpegAsync(resizedStream, new SixLabors.ImageSharp.Formats.Jpeg.JpegEncoder
        {
          Quality = 85 // Reduce quality to decrease file size
        }, cancellationToken);

        // Upload resized image to temporary S3 location and return URL
        var tempKey = $"temp/resized_{Guid.NewGuid()}.jpg";
        await _fileStorageService.UploadStreamToStorage(tempKey, resizedStream, cancellationToken);

        // Return the temporary S3 URL
        return $"https://tradesales-media-dev.s3.eu-west-2.amazonaws.com/{tempKey}";
      }
      catch (Exception ex)
      {
        Console.WriteLine($"Error resizing image {imageUrl}: {ex.Message}");
        return imageUrl; // Return original if resizing fails
      }
    }

    public async Task<string> UploadVideo(IFormFile file, CancellationToken cancellationToken)
    {
      UserCredential credential;

      using (var stream = new FileStream("../Trading.API/client_secrets.json", FileMode.Open, FileAccess.Read))
      {
        credential = await GoogleWebAuthorizationBroker.AuthorizeAsync(
            GoogleClientSecrets.FromStream(stream).Secrets,
            // This OAuth 2.0 access scope allows an application to upload files to the
            // authenticated user's YouTube channel, but doesn't allow other types of access.
            new[] { YouTubeService.Scope.YoutubeUpload },
            "user",
            CancellationToken.None
        );
      }

      var youtubeService = new YouTubeService(new BaseClientService.Initializer()
      {
        HttpClientInitializer = credential,
        ApplicationName = "Trading.API"
      });

      var video = new Video();
      video.Snippet = new VideoSnippet();
      video.Snippet.Title = "Default Video Title";
      video.Snippet.Description = "Default Video Description";
      video.Snippet.Tags = new string[] { "tag1", "tag2" };
      video.Snippet.CategoryId = "22"; // See https://developers.google.com/youtube/v3/docs/videoCategories/list
      video.Status = new VideoStatus();
      video.Status.PrivacyStatus = "unlisted"; // or "private" or "public"
      var filePath = @"D:\Work\Trading Platform\Test Video\TestVideo.mp4"; // Replace with path to actual movie file.

      using (var fileStream = new FileStream(filePath, FileMode.Open))
      {
        var videosInsertRequest = youtubeService.Videos.Insert(video, "snippet,status", fileStream, "video/*");
        videosInsertRequest.ProgressChanged += videosInsertRequest_ProgressChanged;
        videosInsertRequest.ResponseReceived += videosInsertRequest_ResponseReceived;

        await videosInsertRequest.UploadAsync();
      }

      // set the walkaround link in the advert record 


      // return the link to the video 
      return "";
    }

    public async Task<VehicleMediaSearchResultDTO> Search(VehicleMediaSearchDTO searchDTO, CancellationToken ct)
    {
      VehicleMediaSearchResultDTO response = new VehicleMediaSearchResultDTO();

      if (searchDTO.Component == "MakeImagesLocal")
      {
        var z = await _tradingContext.VehicleMedia
          .Include(x => x.Media)
          .Include(x => x.Vehicle)
          .Where(x => x.VehicleId == searchDTO.Filters.VehicleId)
          .Where(x => x.StatusId == searchDTO.Filters.StatusId)
          .Where(x => x.MediaTypeId == (int)searchDTO.Filters.MediaType)
          .AsNoTracking()
          .ToListAsync();

        response.VehicleMedias = z;
      }

      return response;
    }

    public async Task<bool> MakeImagesLocal(Guid vehicleId)
    {
      var contactId = _userService.GetContactId();

      VehicleMediaSearchDTO searchDTO = new VehicleMediaSearchDTO()
      {
        Component = "MakeImagesLocal",
        Filters = {
          VehicleId = vehicleId ,
          StatusId = (int) StatusEnum.Pending,
          MediaType = MediaTypeEnum.Image
        }
      };

      var vehicle = await _tradingContext.Vehicles.FirstOrDefaultAsync(x => x.Id == vehicleId);

      var response = await Search(searchDTO, new CancellationToken());
      var vehicleMedias = response.VehicleMedias;

      List<ImageConversionDTO> convertedList = new List<ImageConversionDTO>();

      foreach (var vmedia in vehicleMedias)
      {
        string key = URLHelper.VehicleMediaKey(
          new VehicleMediaURLDTO()
          {
            CustomerId = vmedia.Vehicle.CustomerId.Value,
            VehicleId = vmedia.VehicleId.Value,
            VehicleMediaId = vmedia.Id
          }
        );

        string url = URLHelper.VehicleMediaUrl(
          new VehicleMediaURLDTO()
          {
            CustomerId = vmedia.Vehicle.CustomerId.Value,
            VehicleId = vmedia.VehicleId.Value,
            VehicleMediaId = vmedia.Id
          }
        );

        await _fileStorageService.UploadFileToStorage(key, vmedia.Media.MediaURL, new CancellationToken());

        //Console.WriteLine("UPLOADING " + vmedia.Media.MediaURL + " to " + key + " for vehicle " + vehicleId);

        // Consider using dapper
        JsonPatchDocument<Media> mpatch = new JsonPatchDocument<Media>();
        mpatch.Replace(x => x.StatusId, (uint)StatusEnum.Active);
        mpatch.Replace(x => x.Filename, key);
        await PatchMedia(vmedia.Media.Id, mpatch, new CancellationToken());

        // Consider using dapper
        JsonPatchDocument<VehicleMedia> vmpatch = new JsonPatchDocument<VehicleMedia>();
        vmpatch.Replace(x => x.StatusId, (uint)StatusEnum.Active);
        await PatchVehicleMedia(vmedia.Id, vmpatch, new CancellationToken());

        if (!vehicle.PrimaryImageId.HasValue)
        {
          vehicle.PrimaryImageId = vmedia.Id;
          await _tradingContext.SaveChangesAsync();
        }

        convertedList.Add(new ImageConversionDTO() { VehicleMediaId = vmedia.Id, Url = url, StatusId = StatusEnum.Active });

        if (contactId.HasValue)
        {
          await _messageService.SendContactMessage(contactId.Value, MessageAreaEnum.Adverts, MessageTypeEnum.ImportAdvertImageProgress, convertedList);
        }
      }

      return true;
    }



    public async Task<IEnumerable<VehicleMediaDTO>> UploadMediaFromURLsWithLogoSwapBatched(
            Guid customerId,
            VehicleMediaUploadDTO uploadDTO,
            int pixelLogoHeight,
            CancellationToken cancellationToken)
    {
      var now = DateTime.Now;
      List<VehicleMedia> vehicleMedias = new List<VehicleMedia>();
      var vehicleId = uploadDTO.VehicleId;
      var vehicle = await _tradingContext.Vehicles
          .Include(x => x.VehicleMedia)
          .Where(x => x.Id == vehicleId)
          .FirstOrDefaultAsync(cancellationToken);

      if (vehicle == null)
      {
        throw new ApplicationException($"Could not find vehicle with id: {vehicleId}");
      }

      if (customerId != vehicle.CustomerId && _userService.IsAdminOrGreater())
      {
        customerId = vehicle.CustomerId.Value;
      }

      // get media category (condition report, v5 etc.) if category code is specified
      uint? categoryId = null;
      if (!string.IsNullOrEmpty(uploadDTO.MediaCategoryCode))
      {
        var attribVal = await _tradingContext.Attribvals.FirstOrDefaultAsync(x => x.AttribvalCode == uploadDTO.MediaCategoryCode, cancellationToken);
        if (attribVal != null)
        {
          categoryId = attribVal.Id;
        }
      }

      bool useFirstAsPrimary = !uploadDTO.MediaURLContent.Any(x => x.IsPrimary);
      int sequence = vehicle.VehicleMedia?.Count() ?? 0 + 1;

      // First, create all VehicleMedia entities and save them to get the actual IDs
      foreach (var urlContent in uploadDTO.MediaURLContent)
      {
        var media = new Media()
        {
          StatusId = (uint)StatusEnum.Active,
          Added = now,
          Updated = now,
          MediaURL = urlContent.Url // Initially use original URL
        };

        var vehicleMedia = new VehicleMedia()
        {
          Added = now,
          Updated = now,
          VehicleId = vehicleId,
          Media = media,
          MediaTypeId = (uint)urlContent.MediaType,
          StatusId = (uint)StatusEnum.Active,
          ExternalId = urlContent.Tag,
          MediaCategoryId = categoryId,
          IsSpinImage = urlContent.IsSpinImage,
          IsInternal = urlContent.IsInternal,
          Sequence = Convert.ToSByte(sequence++)
        };

        vehicle.VehicleMedia.Add(vehicleMedia);
        vehicleMedias.Add(vehicleMedia);
      }

      // Save all entities to get the actual IDs
      await _tradingContext.SaveChangesAsync(cancellationToken);

      // Process images in batches of 2, but run batches in parallel
      var batches = vehicleMedias
          .Select((vm, index) => new { vm, index })
          .GroupBy(x => x.index / 2)
          .Select(g => g.Select(x => x.vm).ToList())
          .ToList();

      // Control parallelism - allow up to X concurrent lambda invocations
      using var semaphore = new SemaphoreSlim(11, 20);

      var batchTasks = batches.Select(async batch =>
      {
        await semaphore.WaitAsync(cancellationToken);
        try
        {
          // Prepare image processing requests for this batch
          var imageRequests = batch.Select(vm => new ImageProcessingRequest
          {
            Url = vm.Media.MediaURL,
            VehicleMediaId = vm.Id,
            Tag = vm.ExternalId
          }).ToList();

          // Process this batch with lambda
          var results = await _lambdaFunctionsService.ProcessImagesWithLogoSwap(
              imageRequests,
              customerId,
              vehicleId,
              pixelLogoHeight,
              cancellationToken);

          // Update the media URLs for this batch
          var processedImages = results.ToDictionary(r => r.VehicleMediaId, r => r);

          foreach (var vehicleMedia in batch)
          {
            if (processedImages.ContainsKey(vehicleMedia.Id))
            {
              var processedImage = processedImages[vehicleMedia.Id];
              vehicleMedia.Media.MediaURL = processedImage.S3Url;
              vehicleMedia.Media.Updated = now;
            }
            else
            {
              // Fall back to original processing if lambda failed for this image
              // Use the actual MediaId instead of a random GUID
              await ProcessFallbackImage(vehicleMedia, uploadDTO, customerId, vehicleId, now, cancellationToken);
            }
          }
        }
        catch (Exception ex)
        {
          // If lambda processing fails for this batch, fall back to original processing
          foreach (var vehicleMedia in batch)
          {
            // Use the actual MediaId instead of a random GUID
            await ProcessFallbackImage(vehicleMedia, uploadDTO, customerId, vehicleId, now, cancellationToken);
          }
        }
        finally
        {
          semaphore.Release();
        }
      });

      // Wait for all batches to complete
      await Task.WhenAll(batchTasks);

      // Handle primary image logic after all processing is complete
      bool primaryImageSet = false;
      for (int i = 0; i < vehicleMedias.Count; i++)
      {
        var vehicleMedia = vehicleMedias[i];
        var urlContent = uploadDTO.MediaURLContent.ElementAt(i);

        // Handle primary image logic
        if (useFirstAsPrimary && string.IsNullOrEmpty(uploadDTO.MediaCategoryCode) &&
            (vehicle.PrimaryImageId == Guid.Empty || !vehicle.PrimaryImageId.HasValue) && !primaryImageSet)
        {
          vehicle.PrimaryImageId = vehicleMedia.Id;
          primaryImageSet = true; // Only set the first one as primary
        }

        if (!useFirstAsPrimary && urlContent.IsPrimary)
        {
          vehicle.PrimaryImageId = vehicleMedia.Id;
        }
      }

      // Save all the updated URLs and primary image
      await _tradingContext.SaveChangesAsync(cancellationToken);

      // Update vehicle image count
      var imageCount = await UpdateVehicleImageCount(vehicleId, cancellationToken);

      // Convert to DTOs
      var vehicleMediaDTOs = vehicleMedias.Select(vm => new VehicleMediaDTO
      {
        Id = vm.Id,
        VehicleId = vm.VehicleId,
        MediaId = vm.MediaId,
        MediaTypeId = vm.MediaTypeId,
        StatusId = vm.StatusId,
        ExternalId = vm.ExternalId,
        MediaCategoryId = vm.MediaCategoryId,
        IsSpinImage = vm.IsSpinImage,
        IsInternal = vm.IsInternal,
        Sequence = vm.Sequence,
        Added = vm.Added,
        Updated = vm.Updated,
        Media = new MediaDTO
        {
          Id = vm.Media.Id,
          MediaURL = vm.Media.MediaURL,
          StatusId = vm.Media.StatusId,
          Added = vm.Media.Added,
          Updated = vm.Media.Updated
        }
      });

      return vehicleMediaDTOs;
    }

    // Helper method to handle fallback image processing
    private async Task ProcessFallbackImage(
        VehicleMedia vehicleMedia,
        VehicleMediaUploadDTO uploadDTO,
        Guid customerId,
        Guid vehicleId,
        DateTime now,
        CancellationToken cancellationToken)
    {
      var urlContent = uploadDTO.MediaURLContent.FirstOrDefault(x => x.Tag == vehicleMedia.ExternalId);
      if (urlContent != null && !urlContent.IsInternal)
      {
        // Use the actual VehicleMediaId to match Lambda function S3 key construction
        var imageName = $"customer/{customerId}/vehicle/{vehicleId}/media/{vehicleMedia.Id}.jpeg";
        vehicleMedia.Media.MediaURL = await _fileStorageService.UploadFileFromUrlMultiThreaded(urlContent.Url, imageName, cancellationToken);
        vehicleMedia.Media.Updated = now;
      }
    }

    public async Task<IEnumerable<VehicleMediaDTO>> UploadVehicleMedia(Guid customerId, VehicleMediaUploadDTO uploadDTO, IFormFileCollection files, CancellationToken cancellationToken)
    {
      List<VehicleMedia> vehicleMedias = new List<VehicleMedia>();

      var vehicleId = uploadDTO.VehicleId;

      var vehicle = await _tradingContext.Vehicles.FirstOrDefaultAsync(x => x.Id == vehicleId);
      if (vehicle == null)
      {
        throw new ApplicationException(string.Format("Could not find vehicle with id: {0}", vehicleId));
      }

      if (customerId != vehicle.CustomerId && _userService.IsAdminOrGreater())
      {
        customerId = vehicle.CustomerId.Value;
      }

      // get media category (condition report, v5 etc.) if category code is specified 
      uint? categoryId = null;
      if (!string.IsNullOrEmpty(uploadDTO.MediaCategoryCode))
      {
        var attribVal = await _tradingContext.Attribvals.FirstOrDefaultAsync(x => x.AttribvalCode == uploadDTO.MediaCategoryCode);
        if (attribVal != null)
        {
          categoryId = attribVal.Id;
        }
      }

      var cnt = 0;

      foreach (var file in files)
      {
        // create media item 
        var media = new Media()
        {
          StatusId = (uint)StatusEnum.Active,
          Filename = file.FileName,
          Updated = DateTime.Now,
          Added = DateTime.Now,
        };

        // create vehicle media item 
        var vehicleMedia = new VehicleMedia()
        {
          VehicleId = vehicleId,
          Media = media,
          MediaTypeId = (uint)uploadDTO.MediaType[cnt],
          StatusId = (uint)StatusEnum.Active,
          MediaCategoryId = categoryId,
          Sequence = Convert.ToSByte(99), // Add it to the end
          Added = DateTime.Now,
          Updated = DateTime.Now,
        };
        vehicle.VehicleMedia.Add(vehicleMedia);
        vehicleMedias.Add(vehicleMedia);

        // save changes
        await _tradingContext.SaveChangesAsync(cancellationToken);

        // save video to youtube
        if (uploadDTO.MediaType[cnt] == (int)MediaTypeEnum.Video)
        {
          // if we have an existing walkaround video for this vehicle, delete it
          // only one video per vehicle allowed, hence FirstOrDefault

          var existingMedia = await _tradingContext.VehicleMedia
            .Where(x => x.VehicleId == vehicle.Id && x.MediaTypeId == (int)MediaTypeEnum.Video && !string.IsNullOrEmpty(x.ExternalId))
            .Where(x => x.StatusId == (int)StatusEnum.Active)
            .FirstOrDefaultAsync(cancellationToken);

          if (existingMedia != null)
          {
            try
            {
              await _ytService.DeleteVideo(existingMedia.ExternalId);
              _tradingContext.Remove(existingMedia);
            }
            catch
            {
            }
          }

          var dto = new VideoUploadDTO()
          {
            Title = vehicle.Vrm,
            File = file
          };

          var videoId = await _ytService.UploadVideo(dto);

          // store the videoId against the vehicleMedia entity
          vehicleMedia.ExternalId = videoId;

          await _tradingContext.SaveChangesAsync(cancellationToken);
        }
        else
        {
          // save images to s3

          string key = URLHelper.VehicleMediaKey(
            new VehicleMediaURLDTO() { CustomerId = customerId, VehicleId = vehicleId, VehicleMediaId = vehicleMedia.Id }
          );

          // if the vehicle has no primary image, use the first image in this list
          // the media category code will be null when uploading images 
          if (string.IsNullOrEmpty(uploadDTO.MediaCategoryCode) && (vehicle.PrimaryImageId == Guid.Empty || !vehicle.PrimaryImageId.HasValue))
          {
            vehicle.PrimaryImageId = vehicleMedia.Id;
            await _tradingContext.SaveChangesAsync(cancellationToken);
          }

          await _fileStorageService.UploadFileToStorage(key, file, cancellationToken);
        }

        cnt++;
      }

      var x = await UpdateVehicleImageCount(vehicleId, cancellationToken);

      return _mapper.Map<IEnumerable<VehicleMedia>, IEnumerable<VehicleMediaDTO>>(vehicleMedias);
    }
    public async Task<VehicleMediaDTO> PatchVehicleMedia(Guid vehicleMediaId, JsonPatchDocument<VehicleMedia> patch, CancellationToken cancellationToken)
    {
      var vehicleMedia = await Get(vehicleMediaId, null, cancellationToken);

      if (vehicleMedia != null)
      {
        patch.Replace(c => c.Updated, DateTime.Now);
        patch.FilterPatch();
        patch.ApplyTo(vehicleMedia);
        await _tradingContext.SaveChangesAsync(cancellationToken);
        return _mapper.Map<VehicleMedia, VehicleMediaDTO>(vehicleMedia);
      }

      return null;
    }
    public async Task<MediaDTO> PatchMedia(Guid mediaId, JsonPatchDocument<Media> patch, CancellationToken cancellationToken)
    {
      var media = await GetMedia(mediaId, null, cancellationToken);

      if (media != null)
      {
        patch.Replace(c => c.Updated, DateTime.Now);
        patch.FilterPatch();
        patch.ApplyTo(media);
        await _tradingContext.SaveChangesAsync(cancellationToken);
        return _mapper.Map<Media, MediaDTO>(media);
      }

      return null;
    }


    public async Task<VehicleMedia> Get(Guid vehicleMediaId, VehicleMediaSearchDTO dto, CancellationToken ct)
    {
      return await _tradingContext.VehicleMedia.Where(x => x.Id == vehicleMediaId).FirstOrDefaultAsync(ct);
    }

    public async Task<Media> GetMedia(Guid mediaId, MediaSearchDTO dto, CancellationToken ct)
    {
      return await _tradingContext.Media.Where(x => x.Id == mediaId).FirstOrDefaultAsync(ct);
    }

    public async Task<IEnumerable<VehicleMediaDTO>> GetVehicleMedia(Guid vehicleId, MediaCategoryEnum? mediaCategory, CancellationToken cancellationToken)
    {
      var media = _tradingContext.VehicleMedia.Include(x => x.Vehicle)
        .Where(x => x.VehicleId == vehicleId && x.StatusId == (int)StatusEnum.Active);

      if (mediaCategory.HasValue)
      {
        media = media.Where(x => x.MediaCategoryId == (int)mediaCategory.Value);
      }
      else
      {
        media = media.Where(x => !x.MediaCategoryId.HasValue);
      }

      var medias = await media
        .AsNoTracking()
        .ToListAsync(cancellationToken);

      var dtos = _mapper.Map<IEnumerable<VehicleMedia>, IEnumerable<VehicleMediaDTO>>(medias);

      return dtos;
    }

    public async Task<IEnumerable<VehicleMediaDTO>> GetVehicleMediaByAdvert(Guid advertId, MediaCategoryEnum? mediaCategory, CancellationToken cancellationToken)
    {
      var vehicleId = await _tradingContext.Adverts
        .AsNoTracking()
        .Where(x => x.Id == advertId)
        .Select(x => x.VehicleId)
        .FirstOrDefaultAsync(cancellationToken);

      if (vehicleId.HasValue)
      {
        return await GetVehicleMedia(vehicleId.Value, mediaCategory, cancellationToken);
      }

      return null;
    }

    private void videosInsertRequest_ResponseReceived(Video obj)
    {
      var test = obj;
    }

    private void videosInsertRequest_ProgressChanged(IUploadProgress obj)
    {
      var test = obj;
    }

    public async Task<bool> SwapOriginalForEnhancedImage(EnhancedImageDataDTO dto)
    {
      var ok = await _fileStorageService.SwapOriginalForEnhancedImage(dto.EnhancedImageURL, CancellationToken.None);

      if (ok)
      {
        // update the media record 
        var media = await _tradingContext.VehicleMedia
          .FirstOrDefaultAsync(x => x.MediaId == dto.MediaId);
        if (media != null)
        {
          media.IsEnhanced = true;
          await _tradingContext.SaveChangesAsync();

          return true;
        }
      }

      return false;
    }

  }

  internal class ImageConversionDTO
  {
    public Guid VehicleMediaId { get; set; }
    public string Url { get; set; }
    public StatusEnum StatusId { get; set; }
  }
}