﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.Services.Interfaces;

namespace Trading.API.Tests.Common.Fakes
{
  public class FakeFileStorageService : IFileStorageService
  {
    public Task<string> CheckFileExists(string key)
    {
      throw new NotImplementedException();
    }

    public Task<Stream> ConvertBase64ToStream(string base64String)
    {
      throw new NotImplementedException();
    }

    public Task CopyFileToNewLocation(string sourceKey, string destKey)
    {
      return Task.CompletedTask;
    }

    public Task RemoveFileFromStorage(string key, CancellationToken cancellationToken)
    {
      return Task.CompletedTask;
    }

    public Task SwapOriginalForEnhancedImage(string enhancedUrl, CancellationToken cancellationToken)
    {
      throw new NotImplementedException();
    }

    public Task<string> UploadFileFromUrlMultiThreaded(string url, string fileName, CancellationToken cancellationToken = default)
    {
      return (Task<string>)Task.CompletedTask;
    }

    public Task UploadFileToStorage(string key, string url, CancellationToken cancellationToken)
    {
      return Task.CompletedTask;
    }

    public Task UploadFileToStorage(string key, IFormFile formFile, CancellationToken cancellationToken)
    {
      return Task.CompletedTask;
    }

    public Task UploadStreamToStorage(string key, Stream stream, CancellationToken cancellationToken)
    {
      return Task.CompletedTask;
    }

    Task<bool> IFileStorageService.SwapOriginalForEnhancedImage(string enhancedUrl, CancellationToken cancellationToken)
    {
      throw new NotImplementedException();
    }
  }
}
