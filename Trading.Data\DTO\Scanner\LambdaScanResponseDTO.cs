﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Models;
using Trading.API.Data.Models.DTO;

namespace Trading.API.Data.DTO
{
  public class LambdaScanEndedResponseDTO
  {
    public uint scan_queue_id { get; set; } // HTMl/JSON response we had to parse
    public string raw_response { get; set; } // HTMl/JSON response we had to parse
    public bool? save_sample { get; set; }

    // Gets deserialized later
    public List<LambdaScanStageResponseDTO>? sample_data { get; set; }
    public List<LambdaScanSampleErrorsDTO>? errors { get; set; }
  }
  public class LambdaScanStageResponseDTO
  {
    public uint stage_id { get; set; }
    public List<LambdaScanSampleDataDTO> data { get; set; }
    public List<LambdaScanSampleErrorsDTO> errors { get; set; }
  }

  public class LambdaScanSampleDataDTO
  {
    public uint scan_field_id { get; set; }
    public string scan_value { get; set; }
    public string scan_comment { get; set; }
    public List<string> errors { get; set; }
  }

  public class LambdaScanSampleErrorsDTO
  {
    public uint scan_field_id { get; set; }
    public List<string> errors { get; set; }
  }
}