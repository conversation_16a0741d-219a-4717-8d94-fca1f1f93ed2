﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.Services.Interfaces;

/*

namespace Trading.API.Controllers
{

  [Route("api/[controller]")]
  [ApiController]
  public class SmartFleetController : ControllerBase
  {
    private readonly ISmartFleetService _smartFleetService;

    public SmartFleetController(ISmartFleetService smartFleetService)
    {
      this._smartFleetService = smartFleetService;
    }

    [HttpPost]
    [Route("importVehicle")]
    public async Task<IActionResult> ImportVehicle([FromBody]string vrm, CancellationToken cancellationToken)
    {
      try
      {
        var vehicle = await _smartFleetService.ImportVehicle(vrm, cancellationToken);
        return Ok(vehicle);
      } 
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("getVehicle/{vrm}")]
    public async Task<IActionResult> GetVehicle(string vrm, CancellationToken cancellationToken)
    {
      try
      {
        var response = await _smartFleetService.GetVehicleData(vrm, cancellationToken);
        return Ok(response);
      } 
      catch (Exception ex)
      {
        return BadRequest(ex);
      }

    }
  }
}

*/
