﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.UInspections
{
  public class UInspectQuestionOptionDTO : BaseModelEntityDTO
  {
    public Guid UInspectQuestionId { get; set; }
    public UInspectQuestionDTO UInspectQuestion { get; set; }

    public uint NextSectionId { get; set; }

    public string OptionLabel { get; set; } // i.e. full service, partial service etc.

    public string OptionValue { get; set; } // i.e. full service, partial service etc.
    public uint Sequence { get; set; } // Display Order
    public string MappedToValue { get; set; } // Display Order
  }
}
