using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.Services.Helpers;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/[controller]")]
  [ApiController]
  public class VehicleLookupController : ControllerBase
  {
    private readonly IEnumerable<IVRMLookupService> _vrmLookupServices;
    private readonly IVehicleService _vehicleService;

    public VehicleLookupController(IEnumerable<IVRMLookupService> vrmLookupServices, IVehicleService vehicleService) {

      _vrmLookupServices = vrmLookupServices;
      _vehicleService = vehicleService;
    }


    /// <summary>
    /// Performs a vehicle lookup
    /// </summary>
    /// <param name="vrm">The Vehicle Registration Mark (VRM), also known as the Number Plate, e.g AB12CTO</param>
    [HttpGet("{vrm}")]
    [ProducesResponseType(typeof(VehicleLookupInfoDTO), StatusCodes.Status200OK)]
    public async Task<IActionResult> VRMLookup(string vrm) 
    {
      var service = _vrmLookupServices.GetVRMLookupService(vrm);

      if (service != null)
      {
        VRMLookupDataDTO lookupDTO = new VRMLookupDataDTO { vrm = vrm };
        var response = await service.GetVehicleData(lookupDTO, CancellationToken.None);
        return Ok(response);
      }
      
      return NotFound("VRM lookup service provider not found");
    }

    [HttpGet("{vrm}/{odometer}")]
    [ProducesResponseType(typeof(VehicleLookupInfoDTO), StatusCodes.Status200OK)]
    public async Task<IActionResult> VRMLookupWithMileage(string vrm, uint? odometer)
    {
      var service = _vrmLookupServices.GetVRMLookupService(vrm);

      if (service != null)
      {
        VRMLookupDataDTO lookupDTO = new VRMLookupDataDTO { vrm = vrm, odometer = odometer };
        var response = await service.GetVehicleData(lookupDTO, CancellationToken.None);
        return Ok(response);
      }

      return NotFound("VRM lookup service provider not found");
    }


    [HttpGet]
    [ResponseCache(Duration = 600)]
    [Route("dvla/{vrm}")]
    public async Task<IActionResult> GetDVLAData(string vrm, CancellationToken cancellationToken)
    {
      try
      {
        var data = await _vehicleService.GetOrCreateDVLAData(vrm, cancellationToken);
        return Ok(data);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
