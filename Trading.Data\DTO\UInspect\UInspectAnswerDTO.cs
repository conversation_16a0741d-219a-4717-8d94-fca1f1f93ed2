﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.UInspections
{
  public class UInspectAnswerDTO : BaseModelEntityIntDTO
  {
    public Guid? UInspectId { get; set; }
    public Guid? UInspectQuestionId { get; set; }
    public Guid? AnswerOptionId { get; set; }
    public UInspectQuestionOptionDTO? AnswerOption { get; set; }
    public string Question { get; set; }
    public string Answer { get; set; }
    public bool AnswerBool { get; set; }
    public int AnswerInt { get; set; }
    public DateTime AnswerDate { get; set; }

    public string IPAddress { get; set; }
    public float Longitude { get; set; }
    public float Latitude { get; set; }
  }
}
