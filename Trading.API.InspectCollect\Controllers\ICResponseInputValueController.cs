using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/response-input-value")]
  [ApiController]
  [AllowAnonymous]
  public class ICResponseInputValueController : ControllerBase
  {
    private readonly ICResponseInputValueInterface _icResponseInputValueService;

    public ICResponseInputValueController(ICResponseInputValueInterface serviceInterface)
    {
      _icResponseInputValueService = serviceInterface;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icResponseInputValueService.Get(id, null, cancellationToken);
      return Ok(res);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icResponseInputValueService.Delete(id);
      return Ok(res);
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Post([FromBody]ICResponseInputValueCreateDTO dto, CancellationToken cancellationToken)
    {
      var res = await _icResponseInputValueService.Create(dto);
      return Ok(res);
    }

    [HttpGet]
    [Route("search")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICResponseInputValueSearchDTO>(query);
      var res = await _icResponseInputValueService.Search(dto, cancellationToken);
      return Ok(res);
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<ICResponseInputValue> dto)
    {
      var response = await _icResponseInputValueService.Patch(id, dto);
      return Ok(response);
    }
  }
}