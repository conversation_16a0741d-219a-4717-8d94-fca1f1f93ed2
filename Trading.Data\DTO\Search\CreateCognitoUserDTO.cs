﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO
{
  public class CreateCognitoUserDTO
  {

    public string Email { get; set; }
    public string Company { get; set; }
    public string Phone { get; set; }
    public string ContactName { get; set; }
    public string TempPassword { get; set; }

    // Values that get returned 
    public bool? UserExists { get; set; }

    public string UserId;

    public Guid? ContactId { get; set; }
    public Guid? CustomerId { get; set; }
  }
}