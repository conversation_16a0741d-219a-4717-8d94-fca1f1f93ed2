﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/[controller]")]
  [ApiController]
  [Authorize]
  public class CustomerOrderController : ControllerBase
  {
    private readonly ICustomerOrderService _customerOrderService;

    public CustomerOrderController(ICustomerOrderService customerOrderService)
    {
      this._customerOrderService = customerOrderService;
    }

    // get all orders
    [HttpGet]
    [Route("/api/customer/{customerId}/customerOrders")]
    public async Task<IActionResult> Get(CancellationToken cancellationToken)
    {
      try
      {
        var customerId = this.User.CustomerId();
        var orders = await _customerOrderService.GetCustomerOrders(customerId.Value, cancellationToken);

        return Ok(orders);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    // get single order
    [HttpGet]
    [Route("/api/customer/{customerId}/customerOrder/{orderId}")]
    public async Task<IActionResult> GetCustomerOrder(Guid customerId, Guid orderId, CancellationToken cancellationToken)
    {
      try
      {
        // DB - Code below is wrong
        //var customerId = this.User.CustomerId();
        // var orders = await _customerOrderService.GetCustomerOrders(customerId.Value, cancellationToken);
        // return Ok(orders);

        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
