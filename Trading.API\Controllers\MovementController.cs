using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using Trading.Services.Interfaces.Movements;

namespace Trading.API.Controllers
{
  [Route("api/[controller]")]
  [ApiController]
  public class MovementController : ControllerBase
  {
    private readonly IMovementService _movementService;
    public MovementController(IMovementService movementService)
    {
      _movementService = movementService;
    }

  }
}
