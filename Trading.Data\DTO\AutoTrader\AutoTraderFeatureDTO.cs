﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.AutoTrader;
public class AutoTraderFeatureDTO
{
  public uint Id { get; set; }
  public Guid? ICVehicleId { get; set; }
  public uint AutoTraderFeatureListId { get; set; }
  public string Name { get; set; }
  public string GenericName { get; set; }
  public string Type { get; set; }// "Standard" or "Optional"

  public string Category { get; set; }
  public decimal BasicPrice { get; set; }
  public decimal VatPrice { get; set; }
  public bool FactoryFitted { get; set; }
  public string Finish { get; set; }
  public string GenericFinish { get; set; }

  [DefaultValue(false)]
  public bool ResponseSelected { get; set; } // This is used to indicate if the feature was selected in a response, similar to ICResponseInputFeatureDTO
}
