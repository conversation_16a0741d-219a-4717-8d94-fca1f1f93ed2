﻿using Microsoft.EntityFrameworkCore;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.API.Tests.Common.Params;

namespace Trading.API.Tests.Common.Helpers
{
  public class VehicleHelper
  {
    public static async Task<Vehicle> CreateTestVehicle(TradingContext context, LookupFactory lookupFactory, Guid customerId, CreateVehicleParams vehicleParams)
    {
      var existing = await context.Vehicles.FirstOrDefaultAsync(x => x.Vrm == vehicleParams.Vrm);
      if (existing != null)
      {
        return existing;
      }

      var vehT = VehicleTypeEnum.Car;

      var make = await lookupFactory.GetLookup<Make>(new LookupDTO { TableName = "make", LookupValue = vehicleParams.Make, VehicleType = vehT });
      var model = await lookupFactory.GetLookup<Model>(new LookupDTO { TableName = "model", LookupValue = vehicleParams.Model, ParentId = make.Id, VehicleType = vehT });
      var deriv = await lookupFactory.GetLookup<Deriv>(new LookupDTO { TableName = "deriv", LookupValue = vehicleParams.Deriv, ParentId = model.Id, VehicleType = vehT });
      var fuel = await lookupFactory.GetLookup<FuelType>(new LookupDTO { TableName = "fuel_type", LookupValue = vehicleParams.Fuel, VehicleType = vehT });
      var trans = await lookupFactory.GetLookup<TransmissionType>(new LookupDTO { TableName = "transmission_type", LookupValue = vehicleParams.Transmission, VehicleType = vehT });
      var plate = await lookupFactory.GetLookup<Plate>(new LookupDTO { TableName = "plate", LookupValue = vehicleParams.Plate, VehicleType = vehT });

      var colour = new VehicleColour { ColourName = vehicleParams.Colour, StatusId = 1 };
      AddEntity(context, colour);

      var vehicle = new Vehicle
      {
        Vrm = vehicleParams.Vrm,
        VehicleColour = colour,
        VehicleColourId = colour.Id,
        Make = make,
        MakeId = make.Id,
        Model = model,
        ModelId = model.Id,
        Deriv = deriv,
        DerivId = deriv.Id,
        FuelType = fuel,
        FuelTypeId = fuel.Id,
        TransmissionType = trans,
        TransmissionTypeId = trans.Id,
        CustomerId = customerId,
        VehicleTypeId = 1,
        PlateId = plate.Id,
        StatusId = 1
      };

      AddEntity(context, vehicle);

      return vehicle;
    }

    private static void AddEntity(TradingContext context, object entity)
    {
      context.Add(entity);
      context.SaveChanges();
    }
  }
}
