﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.Models.InventData;

[Table("InventAuction")]
public class InventAuction : BaseModelEntity
{
  public Guid? InventUserId { get; set; }
  public virtual InventUser InventUser { get; set; }

  public long AuctionId { get; set; }

  public string Title { get; set; }

  public DateTime? DateTime { get; set; }

  public DateTime? EndDateTime { get; set; }

  /// <summary>
  /// Auction status: 1 = Upcoming, 2 = In-Progress, 3 = Paused, 4 = Finished, 5 = Closed
  /// </summary>
  public int ActivityStatus { get; set; }

  public bool AllowStandOn { get; set; }

  public int? CurrentLotId { get; set; }

  public string Information { get; set; }

  public int AuctionLocationId { get; set; }

  public string AuctionLocationTitle { get; set; }

  public string AuctionTypeTitle { get; set; }


  public virtual ICollection<InventAuctionLot> Lots { get; set; }
}

