﻿using AutoMapper;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.EntityFrameworkCore;
using System;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.Extensions;
using Trading.API.Data.Enums;
using Trading.Services.InspectCollect.Interfaces;
using System.Web;

namespace Trading.Services.InspectCollect.Classes
{
  public class ICValidationService : ICValidationInterface
  {
    private readonly TradingContext _context;
    private readonly IMapper _mapper;

    public ICValidationService(TradingContext context, IMapper mapper)
    {
      _context = context;
      _mapper = mapper;
    }

    public async Task<ICValidationDTO> Create(ICValidationCreateDTO dto)
    {
      var io = _mapper.Map<ICValidation>(dto);

      io.StatusId = (uint)StatusEnum.Active;
      io.Added = DateTime.Now;
      io.Updated = DateTime.Now;

      _context.ICValidations.Add(io);

      await _context.SaveChangesAsync();

      return _mapper.Map<ICValidationDTO>(io);
    }

    public async Task<bool> Delete(Guid id)
    {
      var patch = new JsonPatchDocument<ICValidation>();
      patch.Add(x => x.StatusId, (uint)StatusEnum.Deleted);
      await this.Patch(id, patch);

      return true;
    }

    public async Task<ValidatedResultDTO<ICValidationDTO>> Get(Guid guid, ICValidationSearchDTO searchDTO, CancellationToken cancellationToken)
    {
      if (searchDTO == null) { searchDTO = new ICValidationSearchDTO(); }

      searchDTO.Filters.Id = guid;
      var inputOptions = await Search(searchDTO, cancellationToken);

      return new ValidatedResultDTO<ICValidationDTO>()
      {
        IsValid = inputOptions.TotalItems > 0,
        DTO = inputOptions.Results.FirstOrDefault()
      };
    }

    public async Task<ICValidationDTO> Patch(Guid id, JsonPatchDocument<ICValidation> patch)
    {
      var io = await _context.ICValidations.Where(x => x.Id == id).FirstOrDefaultAsync();

      if (io == null) { return null; }

      patch.FilterPatch();
      patch.ApplyTo(io);
      io.Updated = DateTime.Now;

      io.ValidationCode = HttpUtility.HtmlDecode(io.ValidationCode);

      await _context.SaveChangesAsync();

      return _mapper.Map<ICValidationDTO>(io);
    }

    public async Task<SearchResultDTO<ICValidationDTO>> Search(ICValidationSearchDTO searchDTO, CancellationToken cancellationToken)
    {
      var preQuery = _context.ICValidations.AsQueryable();

      if (searchDTO.Filters != null)
      {
        if (searchDTO.Filters.Id != null)
        {
          preQuery = preQuery.Where(x => x.Id == searchDTO.Filters.Id);
        }
        if (searchDTO.Filters.ICContainerGroupId != null)
        {
          preQuery = preQuery.Where(x => x.ICContainerGroupId == searchDTO.Filters.ICContainerGroupId);
        }
      }

      var query = preQuery;

      if (searchDTO.Limit.HasValue)
      {
        query = query.Take(searchDTO.Limit.Value);
        query = query.Skip(searchDTO.Offset.Value);
      }

      var items = await query
        .AsNoTracking()
        .ToListAsync(cancellationToken);

      return new SearchResultDTO<ICValidationDTO>
      {
        TotalItems = (searchDTO.Limit.HasValue) ? await preQuery.CountAsync(cancellationToken) : items.Count(),
        Results = _mapper.Map<IEnumerable<ICValidation>, IEnumerable<ICValidationDTO>>(items)
      };
    }
  }
}
