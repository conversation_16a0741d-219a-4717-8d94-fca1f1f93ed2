﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO
{
  public class CustomerAttribSearchDTO : BaseSearchDTO
  {
    public CustomerAttribFilters Filters { get; set; } = new CustomerAttribFilters() { };
  }

  public class CustomerAttribFilters : BaseFilterInt
  {
    public Guid? CustomerId;
    public uint? AttribId;
  }
}