﻿using AutoMapper;
using System.Linq;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using System;
using Trading.API.Data.Enums;
using Trading.Services.Helpers;
using Trading.API.Data.DTO.Export;
using Trading.API.Data.Models.Comms;
using Trading.API.Data.DTO.Comms;
using Trading.API.Data.DTO.Imports;
using Trading.API.Data.Models.Imports;
using Trading.API.Data.DTO.Documents;
using Trading.API.Data.Models.MechanicalFaults;
using Trading.API.Data.DTO.MechanicalFaults;
using Trading.API.Data.DTO.WhosWhoNS;
using Trading.API.Data.Models.WhosWhoNS;
using Trading.API.Data.Models.VOIP;
using Trading.API.Data.DTO.VOIP;
using Trading.API.Data.Models.ContactModels;
using Trading.API.Data.DTO.ExtLeads;
using Trading.API.Data.Models.ExtLeads;
using System.Web;
using Trading.API.Data.Models.AutoTrader;
using Trading.API.Data.DTO.AutoTrader;
using Trading.API.Data.Models.InspectCollect.VehicleData;
using Trading.API.Data.DTO.InspectCollect.VehicleData;

namespace Trading.Services.DTOMappingProfiles
{
  public class MappingProfile : Profile
  {


    public class AppraisalMediaURLDTO {
      public Guid CustomerId { get; set; }
      public Guid VehicleId { get; set; }
      public Guid AppraisalId { get; set; }
      public Guid AppraisalItemId { get; set; }
      public Guid AppraisalMediaId { get; set; }
    }
    public class VehicleMediaURLDTO
    {
      public Guid CustomerId { get; set; }
      public Guid VehicleId { get; set; }
      public Guid VehicleMediaId { get; set; }
      public uint? Height { get; set; }
    }
    public class CustomerMediaURLDTO
    {
      public Guid CustomerId { get; set; }
      public CustomerMediaCategoryEnum MediaCategory { get; set; }
      public CustomerMediaSubCategoryEnum MediaSubCategory { get; set; }
      public Guid CustomerMediaId { get; set; }
    }

    public MappingProfile()
    {
      // Add as many of these lines as you need to map your objects
      CreateMap<Address, AddressDTO>().ReverseMap().PreserveReferences();
      CreateMap<Alt, AltDTO>().ReverseMap().PreserveReferences();
      // CreateMap<JsonPatchDocument<AddressDTO>, JsonPatchDocument<Address>>();
      //CreateMap<Operation<AddressDTO>, Operation<Address>>();

      CreateMap<AdminTask, AdminTaskDTO>().ReverseMap().PreserveReferences();
      CreateMap<AdminTask, AdminTaskDTO>()
        .ForMember(x => x.Overdue, y => y.MapFrom(z => z.SleepUntil < DateTime.Now));

      CreateMap<Adview, AdviewDTO>().ReverseMap().PreserveReferences();
      CreateMap<Attrib, AttribDTO>()
        .ForMember(m => m.AttribType, opt => opt.MapFrom(t => (AttribTypeEnum)t.AttribType))
        .ReverseMap().PreserveReferences();

      CreateMap<Attribval, AttribvalDTO>()
        .ReverseMap().PreserveReferences();

      CreateMap<Bid, BidDTO>().ReverseMap().PreserveReferences();
      CreateMap<Bid, BidSummaryDTO>().PreserveReferences();
      CreateMap<Bid, BidDTO_Public>().PreserveReferences();

      CreateMap<BodyType, BodyTypeDTO>().ReverseMap().PreserveReferences();
      
      CreateMap<BodyPartGroup, BodyPartGroupDTO>();
      CreateMap<BodyPartGroupDTO, BodyPartGroup>();

      CreateMap<BodyPart, BodyPartDTO>();
      CreateMap<BodyPartDTO, BodyPart>();

      CreateMap<CustomerGroup, CustomerGroupDTO>().ReverseMap().PreserveReferences();

      CreateMap<Contact, ContactDTO>().ReverseMap().PreserveReferences();
      CreateMap<Contact, ContactDTO_Public>().ReverseMap().PreserveReferences();
      CreateMap<Contact, MailContactDTO>().ReverseMap().PreserveReferences();
      CreateMap<ContactInternalInfo, ContactInternalInfoDTO>().ReverseMap().PreserveReferences();
      
      CreateMap<ContactAction, ContactActionDTO>().ReverseMap().PreserveReferences();
      
      CreateMap<ContactAction, ContactActionDTO>()
        .ForMember(x => x.ActionName, y => y.MapFrom(z => MappingHelper.GetContactActionName(z.ContactActionType)))
        .PreserveReferences();

      CreateMap<CountryProduct, CountryProductDTO>().ReverseMap().PreserveReferences();
      CreateMap<ContactRole, ContactRoleDTO>().ReverseMap().PreserveReferences();
      CreateMap<ContactAttrib, ContactAttribDTO>().ReverseMap().PreserveReferences();
      CreateMap<Country, CountryDTO>().ReverseMap().PreserveReferences();
      CreateMap<Currency, CurrencyDTO>().ReverseMap().PreserveReferences();
      CreateMap<CustomerAttrib, CustomerAttribDTO>().ReverseMap().PreserveReferences();

      CreateMap<Customer, CustomerDTO>().ReverseMap().PreserveReferences();
      CreateMap<Customer, CustomerDTO_Public>().ReverseMap().PreserveReferences();
      CreateMap<Customer, CustomerAdminDTO>()
        .ForMember(x => x.VehiclesBought, y => y.MapFrom(z => z.Orders.Count(c => c.OrderType == CustomerOrderTypeEnum.Bought)))
        .ForMember(x => x.VehiclesSold, y => y.MapFrom(z => z.Orders.Count(c => c.OrderType == CustomerOrderTypeEnum.Sold)))
        .ForMember(x => x.UserCount, y => y.MapFrom(z => z.Contacts.Count))
        .PreserveReferences()
        .AfterMap((src, dst) => {
          if (dst.PrimaryContact != null)
          {
            dst.PrimaryContact.Customer = null;
          }
        });

      CreateMap<CustomerInternalInfo, CustomerInternalInfoDTO>().PreserveReferences();

      CreateMap<CustomerOrderDTO, CustomerOrder>().PreserveReferences();
      CreateMap<CustomerOrder, CustomerOrderDTO>()
        .ForMember(x => x.ImageURL, y => y.MapFrom(z => z.Deal != null ? URLHelper.PrimaryImageUrl(z.Deal.Advert.CustomerId, z.Deal.Advert.VehicleId.Value, z.Deal.Advert.Vehicle.PrimaryImageId) : ""))
        .ForMember(x => x.OrderTypeDesc, y => y.MapFrom(z => z.OrderType.ToString()))
        .ForMember(x => x.Description, y => y.MapFrom(z => 
              (z.OrderType == CustomerOrderTypeEnum.Bought ? "Purchased " : "Sold ") + (z.Deal != null ? "Vehicle " + z.Deal.Advert.Vehicle.Vrm : "Item")
            )
        )
        .ForMember(x => x.VRM, y => y.MapFrom(z => z.Deal != null ? z.Deal.Advert.Vehicle.Vrm : ""))
        .ForMember(x => x.InvoiceURL, y => y.MapFrom(z => z.Bill != null ? URLHelper.InvoiceURL(z.CustomerId.ToString(), z.Bill.InvoiceReference) : null))
        .ForMember(x => x.InvoiceReference, y => y.MapFrom(z => z.Bill != null ? z.Bill.InvoiceReference : null))
        .ForMember(x => x.InvoiceNumber, y => y.MapFrom(z => z.Bill != null ? z.Bill.InvoiceNumber : null))
        .ForMember(x => x.PaidDate, y => y.MapFrom(z => z.Bill != null ? z.Bill.PaidDate : null))
        .PreserveReferences();

      CreateMap<Bill, BillDTO>()
        .ForMember(x => x.InvoiceURL, y => y.MapFrom(z => URLHelper.InvoiceURL(z.CustomerId.ToString(), z.InvoiceReference)))
        .ForMember(x => x.PaymentMethodId, y => y.MapFrom(x => x.Customer.CustomerInternalInfo.StripePaymentMethodId))
        .ForMember(x => x.PaymentCustomerId, y => y.MapFrom(x => x.Customer.CustomerInternalInfo.StripeCustomerId))
        ;

      CreateMap<CustomerNote, CustomerNoteDTO>().ReverseMap().PreserveReferences();
      CreateMap<CustomerNote, CustomerNoteDTO>();

      CreateMap<Damage, DamageDTO>().ReverseMap().PreserveReferences();

      CreateMap<DamageDTO, Damage>();

      CreateMap<DamageSeverity, DamageSeverityDTO>();
      CreateMap<DamageSeverityDTO, DamageSeverity>();

      CreateMap<DamageDetail, DamageDetailDTO>();
      CreateMap<DamageDetailDTO, DamageDetail>();

      CreateMap<Deal, DealDTO>()
        .ReverseMap().PreserveReferences();

      CreateMap<Deal, DealDTO>()
        .ForMember(x => x.BuyerAssignee, y => y.MapFrom(z => z.BuyerContact.Customer.CustomerInternalInfo.AssignedTo))
        .ForMember(x => x.SellerAssignee, y => y.MapFrom(z => z.SellerContact.Customer.CustomerInternalInfo.AssignedTo));

      CreateMap<Deriv, DerivDTO>().ReverseMap().PreserveReferences();

      CreateMap<DVLAData, DVLAVehicleDTO>().ReverseMap().PreserveReferences();

      CreateMap<FuelType, FuelTypeDTO>().ReverseMap().PreserveReferences();
      CreateMap<InMail, InMailDTO>().ReverseMap().PreserveReferences();

      CreateMap<InMail, InMailDTO>();

      CreateMap<Location, LocationDTO>()
        //.ForMember(x => x.Platform, y => y.MapFrom(z => z.Platform.PlatformName))
        .ReverseMap().PreserveReferences();

      CreateMap<Make, MakeDTO>().ReverseMap().PreserveReferences();
      CreateMap<OrderLine, OrderLineDTO>().ReverseMap().PreserveReferences();
      CreateMap<Invite, InviteDTO>().ReverseMap().PreserveReferences();
      CreateMap<Model, ModelDTO>().ReverseMap().PreserveReferences();

      CreateMap<Offer, OfferDTO>().ReverseMap().PreserveReferences();


      CreateMap<Plate, PlateDTO>().ReverseMap().PreserveReferences();
      CreateMap<Platform, PlatformDTO>().ReverseMap().PreserveReferences();
      CreateMap<Product, ProductDTO>().ReverseMap().PreserveReferences();
      CreateMap<Role, RoleDTO>().ReverseMap().PreserveReferences();

      CreateMap<Site, SiteDTO>().ReverseMap().PreserveReferences();
      CreateMap<SaleType, SaleTypeDTO>().ReverseMap().PreserveReferences();
      

      CreateMap<SphAll, SphAllDTO>()
        .ForMember(x => x.Advert, y => y.MapFrom(y => y.SphLink.Advert));

      CreateMap<Search, SearchDTO>().ReverseMap().PreserveReferences();
      CreateMap<Search, SearchDTO>()
        .ForMember(x => x.SavedSearchCount, y => y.MapFrom(z => z.SavedSearches != null ? z.SavedSearches.Count : 0))
        .PreserveReferences();

      CreateMap<SavedSearch, SavedSearchDTO>().ReverseMap().PreserveReferences();

      // CreateMap<JsonPatchDocument<SavedSearchDTO>, JsonPatchDocument<SavedSearch>>();
      //CreateMap<Operation<SavedSearchDTO>, Operation<SavedSearch>>();

      CreateMap<Status, StatusDTO>().ReverseMap().PreserveReferences();
      CreateMap<Tax, TaxDTO>().ReverseMap().PreserveReferences();
      CreateMap<TransmissionType, TransmissionTypeDTO>().ReverseMap().PreserveReferences();
      CreateMap<Watchlist, WatchlistDTO>().ReverseMap().PreserveReferences();
      CreateMap<Watchlist, WatchlistDTO_Public>().ReverseMap().PreserveReferences();
      CreateMap<VehicleColour, VehicleColourDTO>().ReverseMap().PreserveReferences();


      CreateMap<VehicleType, VehicleTypeDTO>().ReverseMap().PreserveReferences();

      CreateMap<Appraisal, AppraisalDTO>()
        .ForMember(x => x.VehicleType, y => y.MapFrom(z => z.Vehicle.VehicleTypeId.HasValue ? (VehicleTypeEnum)z.Vehicle.VehicleTypeId : VehicleTypeEnum.Car))
        ;
      CreateMap<AppraisalDTO, Appraisal>();
      CreateMap<AppraisalItem, AppraisalItemDTO>();

      CreateMap<BodyType, AltMaintListDTO>().ForMember(x => x.Value, y => y.MapFrom(y => y.BodyTypeName)); 
      CreateMap<VehicleColour, AltMaintListDTO>().ForMember(x => x.Value, y => y.MapFrom(y => y.ColourName)); 
      CreateMap<TransmissionType, AltMaintListDTO>().ForMember(x => x.Value, y => y.MapFrom(y => y.TransmissionTypeName)); 
      CreateMap<FuelType, AltMaintListDTO>().ForMember(x => x.Value, y => y.MapFrom(y => y.FuelTypeName)); 
      CreateMap<Make, AltMaintListDTO>().ForMember(x => x.Value, y => y.MapFrom(y => y.MakeName)); 
      CreateMap<Model, AltMaintListDTO>().ForMember(x => x.Value, y => y.MapFrom(y => y.ModelName)); 
      CreateMap<Deriv, AltMaintListDTO>().ForMember(x => x.Value, y => y.MapFrom(y => y.DerivName)); 
      
      CreateMap<AppraisalItemDTO, AppraisalItem>()
        .ForMember(x => x.BodyPart, y => y.Ignore());
        ;

      CreateMap<AppraisalMedia, AppraisalMediaDTO>()
        .ForMember(x => x.MediaURL, y => y.MapFrom(GetAppraisalMediaURL()))
        .ReverseMap().PreserveReferences();

      CreateMap<CustomerMedia, CustomerMediaDTO>()
        .ForMember(x => x.MediaURL, y => y.MapFrom(GetCustomerMediaURL()))
        .ReverseMap().PreserveReferences();

      CreateMap<CustomerMediaDTO, CustomerMedia>().PreserveReferences();

      CreateMap<MediaType, MediaTypeDTO>().ReverseMap().PreserveReferences();

      CreateMap<Vehicle, VehicleDTO>()
        .ForMember(x => x.ConditionReports, y => y.MapFrom(z => z.VehicleMedia
          .Where(vm => vm.MediaCategory != null && vm.MediaCategory.AttribvalCode == "condition_report")))
        .ForMember(x => x.V5Media, y => y.MapFrom(z => z.VehicleMedia
          .Where(vm => vm.MediaCategory != null && vm.MediaCategory.AttribvalCode == "v5")))
        .ForMember(x => x.ServiceBookMedia, y => y.MapFrom(z => z.VehicleMedia
          .Where(vm => vm.MediaCategory != null && vm.MediaCategory.AttribvalCode == "service_book")))
        .ForMember(x => x.Walkaround, y => y.MapFrom(z => z.VehicleMedia
          .FirstOrDefault(vm => vm.MediaCategory != null && vm.MediaCategory.AttribvalCode == "walkaround")))
        .ForMember(x => x.VehicleMedia, y => y.MapFrom(z => z.VehicleMedia != null ? z.VehicleMedia.Where(vm => vm.MediaCategory == null) : null))
        .ForMember(x => x.PrimaryImageURL, y => y.MapFrom(GetPrimaryImageURL()))
        .ForMember(x => x.OdometerSuffix, y => y.MapFrom(GetOdometerSuffix()))
        .ForMember(x => x.Colour, y => y.MapFrom(z => z.VehicleColour.ColourName))
        .ReverseMap()
          .ForMember(x => x.VehicleColour, y => y.Ignore())
          .ForMember(x => x.Colour, y => y.Ignore())
        .PreserveReferences();

      CreateMap<Vehicle, VehicleDTO_Public>()
        .ForMember(x => x.ConditionReports, y => y.MapFrom(z => z.VehicleMedia
          .Where(vm => vm.MediaCategory != null && vm.MediaCategory.AttribvalCode == "condition_report")))
        .ForMember(x => x.V5Media, y => y.MapFrom(z => z.VehicleMedia
          .Where(vm => vm.MediaCategory != null && vm.MediaCategory.AttribvalCode == "v5")))
        .ForMember(x => x.ServiceBookMedia, y => y.MapFrom(z => z.VehicleMedia
          .Where(vm => vm.MediaCategory != null && vm.MediaCategory.AttribvalCode == "service_book")))
        .ForMember(x => x.Walkaround, y => y.MapFrom(z => z.VehicleMedia
          .FirstOrDefault(vm => vm.MediaCategory != null && vm.MediaCategory.AttribvalCode == "walkaround")))
        .ForMember(x => x.VehicleMedia, y => y.MapFrom(z => z.VehicleMedia.Where(vm => vm.MediaCategory == null)))
        .ForMember(x => x.PrimaryImageURL, y => y.MapFrom(GetPrimaryImageURL()))
        .ForMember(x => x.OdometerSuffix, y => y.MapFrom(GetOdometerSuffix()))
        .ForMember(x => x.Colour, y => y.MapFrom(z => z.VehicleColour.ColourName))
        .ReverseMap()
          .ForMember(x => x.VehicleColour, y => y.Ignore())
          .ForMember(x => x.Colour, y => y.Ignore())
        .PreserveReferences();

      CreateMap<VehicleDTO_Public, VehicleDTO>().ReverseMap().PreserveReferences();

      CreateMap<AdvertDTO, AdvertDTO_Public>()
        .ForMember(x => x.ReserveMet, y => y.MapFrom(z => z.ReservePrice >= 0 && z.CurrentPrice > 0 && z.CurrentPrice >= (z.ReservePrice ?? 0)))
//      Use FE: HelpersService.DecodeHTML
//      .ForMember(x => x.Headline, y => y.MapFrom(z => HttpUtility.HtmlDecode(z.Headline)))
//      .ForMember(x => x.Description, y => y.MapFrom(z => HttpUtility.HtmlDecode(z.Description)))
        .ReverseMap()
        .PreserveReferences();
      
      CreateMap<SaleDTO_Public, SaleDTO>().ReverseMap().PreserveReferences();
      CreateMap<AddressDTO_Public, AddressDTO>().ReverseMap().PreserveReferences();
      CreateMap<WatchlistDTO_Public, WatchlistDTO>().ReverseMap().PreserveReferences();

      CreateMap<VehicleTyreInfo, VehicleTyreInfoDTO>().PreserveReferences().ReverseMap();


      // CreateMap<JsonPatchDocument<VehicleDTO>, JsonPatchDocument<Vehicle>>();

      CreateMap<Advert, AdvertDTO_Public>()
        .ForMember(x => x.ReserveMet, y => y.MapFrom(z => z.ReservePrice >= 0 && z.CurrentPrice > 0 && z.CurrentPrice >= (z.ReservePrice ?? 0)))
        .ForMember(x => x.UnderwriteReservePrice, y => y.MapFrom(z => (z.Sale.SaleTypeId == (int)SaleTypeEnum.Underwrite) ? (z.ReservePrice ?? 0) : 0))
        .ForMember(x => x.CountyPlaceId, y => y.MapFrom(z => z.Address != null ? z.Address.CountyPlaceId : null))
        .ForMember(x => x.CountyLat, y => y.MapFrom(z => z.Address != null ? z.Address.CountyLat : null))
        .ForMember(x => x.CountyLng, y => y.MapFrom(z => z.Address != null ? z.Address.CountyLng : null))
//      Use FE: HelpersService.DecodeHTML
//      .ForMember(x => x.Headline, y => y.MapFrom(z => HttpUtility.HtmlDecode(z.Headline)))
//      .ForMember(x => x.Description, y => y.MapFrom(z => HttpUtility.HtmlDecode(z.Description)))
        ;

      CreateMap<Sale, SaleDTO_Public>();
      CreateMap<Sale, SaleDTO>().PreserveReferences().ReverseMap();
      CreateMap<Sale, BroadcastSaleUpdateDTO>().PreserveReferences().ReverseMap();
      CreateMap<Address, AddressDTO_Public>();

      CreateMap<SphAdvert, SphAdvertDTO_Public>()
        .ForPath(x => x.Advert, y => y.MapFrom(z => z.SphLink.Advert))
        .ForPath(x => x.Advert.Vehicle.Make.MakeName, y => y.MapFrom(z => z.makeName))
        .ForPath(x => x.Advert.Vehicle.Model.ModelName, y => y.MapFrom(z => z.modelName))
        .ForPath(x => x.Advert.Vehicle.Deriv.DerivName, y => y.MapFrom(z => z.derivName))
        .ForPath(x => x.Advert.Vehicle.FuelType.FuelTypeName, y => y.MapFrom(z => z.fuelTypeName))
        .ForPath(x => x.Advert.Vehicle.Plate.PlateName, y => y.MapFrom(z => z.plateName))
        .ForPath(x => x.Advert.Vehicle.TransmissionType.TransmissionTypeName, y => y.MapFrom(z => z.transmissionTypeName))
        .ForPath(x => x.Advert.Vehicle.BodyType.BodyTypeName, y => y.MapFrom(z => z.bodyTypeName))
        .ForPath(x => x.Advert.Vehicle.VehicleType.VehicleTypeName, y => y.MapFrom(z => z.vehicleTypeName))
        .ForPath(x => x.Advert.Sale.SaleName, y => y.MapFrom(z => z.saleName))
        .ForPath(x => x.Advert.Vehicle.Address.AddressName, y => y.MapFrom(z => z.vehicleAddressName))
        .ForPath(x => x.Advert.Vehicle.VATStatus.AttribvalName, y => y.MapFrom(z => z.vatStatusName))
        .ForPath(x => x.Advert.Vehicle.V5Status.AttribvalName, y => y.MapFrom(z => z.v5StatusName))
        .ForPath(x => x.Advert.Vehicle.Colour, y => y.MapFrom(z => z.vehicleColourName))
        .PreserveReferences()
      ;

      CreateMap<SphAdvert, SphAdvertDTO>()
        .ForPath(x => x.Advert, y => y.MapFrom(z => z.SphLink.Advert))
        .ForPath(x => x.Advert.Vehicle.Make.MakeName, y => y.MapFrom(z => z.makeName))
        .ForPath(x => x.Advert.Vehicle.Model.ModelName, y => y.MapFrom(z => z.modelName))
        .ForPath(x => x.Advert.Vehicle.Deriv.DerivName, y => y.MapFrom(z => z.derivName))
        .ForPath(x => x.Advert.Vehicle.FuelType.FuelTypeName, y => y.MapFrom(z => z.fuelTypeName))
        .ForPath(x => x.Advert.Vehicle.Plate.PlateName, y => y.MapFrom(z => z.plateName))
        .ForPath(x => x.Advert.Vehicle.TransmissionType.TransmissionTypeName, y => y.MapFrom(z => z.transmissionTypeName))
        .ForPath(x => x.Advert.Vehicle.BodyType.BodyTypeName, y => y.MapFrom(z => z.bodyTypeName))
        .ForPath(x => x.Advert.Vehicle.VehicleType.VehicleTypeName, y => y.MapFrom(z => z.vehicleTypeName))
        .ForPath(x => x.Advert.Sale.SaleName, y => y.MapFrom(z => z.saleName))
        .ForPath(x => x.Advert.Vehicle.Address.AddressName, y => y.MapFrom(z => z.vehicleAddressName))
        .ForPath(x => x.Advert.Vehicle.VATStatus.AttribvalName, y => y.MapFrom(z => z.vatStatusName))
        .ForPath(x => x.Advert.Vehicle.V5Status.AttribvalName, y => y.MapFrom(z => z.v5StatusName))
        .ForPath(x => x.Advert.Vehicle.Colour, y => y.MapFrom(z => z.vehicleColourName))
        .ForPath(x => x.Customer.CustomerName, y => y.MapFrom(z => z.customerName))
        .PreserveReferences()
      ;

      CreateMap<SphUnlotted, SphAdvertDTO>()
        .ForPath(x => x.Advert, y => y.MapFrom(z => z.SphLink.Advert))
        .ForPath(x => x.Advert.Vehicle.Make.MakeName, y => y.MapFrom(z => z.makeName))
        .ForPath(x => x.Advert.Vehicle.Model.ModelName, y => y.MapFrom(z => z.modelName))
        .ForPath(x => x.Advert.Vehicle.Deriv.DerivName, y => y.MapFrom(z => z.derivName))
        .ForPath(x => x.Advert.Vehicle.FuelType.FuelTypeName, y => y.MapFrom(z => z.fuelTypeName))
        .ForPath(x => x.Advert.Vehicle.Plate.PlateName, y => y.MapFrom(z => z.plateName))
        .ForPath(x => x.Advert.Vehicle.TransmissionType.TransmissionTypeName, y => y.MapFrom(z => z.transmissionTypeName))
        .ForPath(x => x.Advert.Vehicle.BodyType.BodyTypeName, y => y.MapFrom(z => z.bodyTypeName))
        .ForPath(x => x.Advert.Vehicle.VehicleType.VehicleTypeName, y => y.MapFrom(z => z.vehicleTypeName))
        .ForPath(x => x.Advert.Vehicle.Address.AddressName, y => y.MapFrom(z => z.vehicleAddressName))
        .ForPath(x => x.Advert.Vehicle.VATStatus.AttribvalName, y => y.MapFrom(z => z.vatStatusName))
        .ForPath(x => x.Advert.Vehicle.V5Status.AttribvalName, y => y.MapFrom(z => z.v5StatusName))
        .ForPath(x => x.Advert.Vehicle.Colour, y => y.MapFrom(z => z.vehicleColourName))
        .ForPath(x => x.Customer.CustomerName, y => y.MapFrom(z => z.customerName))
        ;


      CreateMap<VehicleMedia, VehicleMediaDTO>()
        .ForMember(x => x.MediaURL, y => y.MapFrom(GetVehicleMediaURL()))
        .ReverseMap().PreserveReferences();

      CreateMap<VehicleMediaDTO, VehicleMediaDTO>()
        .ForMember(x => x.MediaURL, y => y.MapFrom(GetVehicleMediaDTOURL()));


      CreateMap<Media, MediaDTO>().ReverseMap().PreserveReferences();
      CreateMap<VehicleAttrib, VehicleAttribDTO>().ReverseMap().PreserveReferences();
      CreateMap<MOTHistory, MOTHistoryDTO>().ReverseMap().PreserveReferences();
      CreateMap<MOTItem, MOTItemDTO>().ReverseMap().PreserveReferences();

      CreateMap<ServiceHistory, ServiceHistoryDTO>().ReverseMap().PreserveReferences();


      CreateMap<RateCard, RateCardDTO>().ReverseMap().PreserveReferences();
      CreateMap<ProductDiscount, ProductDiscountDTO>().ReverseMap().PreserveReferences();
      CreateMap<CustomerDiscount, CustomerDiscountDTO>().ReverseMap().PreserveReferences();

      CreateMap<PriceRange, RangeDTO>()
        .ForMember(x => x.Min, y => y.MapFrom(z => z.PriceMin))
        .ForMember(x => x.Max, y => y.MapFrom(z => z.PriceMax));

      CreateMap<MileageRange, RangeDTO>()
        .ForMember(x => x.Min, y => y.MapFrom(z => z.MileageMin))
        .ForMember(x => x.Max, y => y.MapFrom(z => z.MileageMax));

      CreateMap<CapacityRange, RangeDTO>()
        .ForMember(x => x.Min, y => y.MapFrom(z => z.CapacityMin))
        .ForMember(x => x.Max, y => y.MapFrom(z => z.CapacityMax));

      CreateMap<PurchaseDealDTO, PurchaseDealDTO>()
        .ForMember(x => x.PrimaryImageURL, y => y.MapFrom(z => URLHelper.PrimaryImageUrl(z.CustomerId, z.VehicleId, z.PrimaryImageId)));


      CreateMap<VehicleLookupInfo, VehicleLookupInfoDTO>().ReverseMap();

      CreateMap<CustomerInternalInfo, CustomerInternalInfoDTO>().PreserveReferences().ReverseMap();

      CreateMap<Advert, AdvertExportDTO>()
        .ForMember(x => x.Deal, y => y.MapFrom(z => z.Deals.OrderByDescending(x => x.Updated).FirstOrDefault()))
        .ForMember(x => x.AdvertisedDate, y => y.MapFrom(z => z.AvailableDate ?? z.Added))
        .PreserveReferences();

      CreateMap<Deal, DealAdvertDTO>()
        .ForMember(x => x.VehiclePrimaryImageURL, y => y.MapFrom(z => URLHelper.PrimaryImageUrl(z.Advert.CustomerId, z.Advert.Vehicle.Id, z.Advert.Vehicle.PrimaryImageId)));

      CreateMap<Vehicle, VehicleBasicDTO>()
        .ForMember(x => x.MakeName, y => y.MapFrom(z => z.Make.MakeName)) 
        .ForMember(x => x.ModelName, y => y.MapFrom(z => z.Model.ModelName))
        .ForMember(x => x.DerivName, y => y.MapFrom(z => z.Deriv.DerivName))
        .ForMember(x => x.CustomerName, y => y.MapFrom(z => z.Customer.CustomerName))
        .ForMember(x => x.PrimaryImageURL, y => y.MapFrom(z => z.CustomerId.HasValue ? URLHelper.PrimaryImageUrl(z.CustomerId.Value, z.Id, z.PrimaryImageId) : null))
        ;

      CreateMap<VehicleCheck, VehicleCheckDTO>()
        .ReverseMap()
        .PreserveReferences();

      CreateMap<VehicleCheckProvider, VehicleCheckProviderDTO>()
        .ReverseMap()
        .PreserveReferences();

      CreateMap<Negotiation, NegotiationDTO>().ReverseMap().PreserveReferences();
      CreateMap<NegotiationNote, NegotiationNoteDTO>().ReverseMap().PreserveReferences();

      CreateMap<SaleAttendee, SaleAttendeeDTO>().ReverseMap().PreserveReferences();

      CreateMap<SaleProfile, SaleProfileDTO>().ReverseMap().PreserveReferences();
      CreateMap<SaleSearchProfile, SaleSearchProfileDTO>().ReverseMap().PreserveReferences();


      CreateMap<EventQueue, EventQueueDTO>().ReverseMap().PreserveReferences();

      CreateMap<ImportLog, ImportLogDTO>().ReverseMap().PreserveReferences();
      CreateMap<CommsEvent, CommsEventDTO>().ReverseMap().PreserveReferences();
      CreateMap<CommsTemplate, CommsTemplateDTO>().ReverseMap().PreserveReferences();
      CreateMap<CommsHistory, CommsHistoryDTO>().ReverseMap().PreserveReferences();
      CreateMap<CommsTemplateExclude, CommsTemplateExcludeDTO>().ReverseMap().PreserveReferences();
      CreateMap<CommsTemplateResponseDTO, CommsPreviewTemplateResponseDTO>().ReverseMap().PreserveReferences();

      CreateMap<Document, DocumentDTO>().ReverseMap().PreserveReferences();

      CreateMap<WhosWho, WhosWhoDTO>().ReverseMap().PreserveReferences();
      CreateMap<CallRecord, CallRecordDTO>().ReverseMap().PreserveReferences();

      CreateMap<TermsTemplate, TermsTemplateDTO>().ReverseMap().PreserveReferences();

      CreateMap<Trading.API.Data.Models.ExtLeads.ExtLead, ExtLeadDTO>().ReverseMap().PreserveReferences();
      CreateMap<ExtLeadVehicle, ExtLeadVehicleDTO>().ReverseMap().PreserveReferences();

      CreateMap<AutoTraderVehicle, AutoTraderVehicleDTO>()
        .ReverseMap()
        .PreserveReferences();

      CreateMap<AutoTraderValuation, AutoTraderValuationDTO>()
        .ReverseMap()
        .PreserveReferences();

      CreateMap<AutoTraderVehicleMetricData, AutoTraderVehicleMetricDataDTO>()
        .ReverseMap()
        .PreserveReferences();

      CreateMap<AutoTraderFeature, AutoTraderFeatureDTO>()
        .ReverseMap()
        .PreserveReferences();

      CreateMap<AutoTraderFeatureList, AutoTraderFeatureListDTO>()
        .ReverseMap()
        .PreserveReferences();

      CreateMap<ICCapData, ICCapDataDTO>()
        .ReverseMap()
        .PreserveReferences();

      CreateMap<ICCapProvenance, ICCapProvenanceDTO>()
        .ReverseMap()
        .PreserveReferences();

      CreateMap<ICCapValuation, ICCapValuationDTO>()
        .ReverseMap()
        .PreserveReferences();
    }

    private static System.Linq.Expressions.Expression<Func<Vehicle, string>> GetOdometerSuffix()
    {
      return z => z.OdometerUnit == (int)OdometerUnitEnum.Kilometres ? "km" : "mls";
    }
    private static System.Linq.Expressions.Expression<Func<AppraisalMedia, string>> GetAppraisalMediaURL()
    {
      return z => z.CustomerRef != null && z.CustomerRef.Contains("inspection")
          ? URLHelper.ProxiedAppraisalMediaUrl(z.CustomerRef)
          : URLHelper.AppraisalMediaUrl(new AppraisalMediaURLDTO()
          {
            CustomerId = z.AppraisalItem.Appraisal.Vehicle.CustomerId.Value,
            VehicleId = z.AppraisalItem.Appraisal.Vehicle.Id,
            AppraisalId = z.AppraisalItem.Appraisal.Id,
            AppraisalItemId = z.AppraisalItem.Id,
            AppraisalMediaId = z.Id
          });
    }

    private static System.Linq.Expressions.Expression<Func<CustomerMedia, string>> GetCustomerMediaURL()
    {
      return z => URLHelper.CustomerMediaUrl(new CustomerMediaURLDTO()
      {
        CustomerId = z.CustomerId,
        MediaSubCategory = z.MediaSubCategory,
        MediaCategory = z.MediaCategory,  
        CustomerMediaId = z.Id
      });
    }

    private static System.Linq.Expressions.Expression<Func<AppraisalMediaDTO, string>> GetAppraisalMediaDTOURL()
    {
      return z => URLHelper.AppraisalMediaUrl(new AppraisalMediaURLDTO()
      {
        CustomerId = z.AppraisalItem.Appraisal.Vehicle.CustomerId.Value,
        VehicleId = z.AppraisalItem.Appraisal.Vehicle.Id.Value,
        AppraisalId = z.AppraisalItem.Appraisal.Id.Value,
        AppraisalItemId = z.AppraisalItem.Id.Value,
        AppraisalMediaId = z.Id.Value
      });
    }

    private static System.Linq.Expressions.Expression<Func<VehicleMedia, string>> GetVehicleMediaURL()
    {
      return z => !string.IsNullOrEmpty(z.ExternalId) && z.MediaTypeId == (uint)MediaTypeEnum.Video ? URLHelper.VideoMediaUrl(z.ExternalId) : URLHelper.VehicleMediaUrl(new VehicleMediaURLDTO() {
        CustomerId = z.Vehicle.CustomerId.Value,
        VehicleId = z.Vehicle.Id,
        VehicleMediaId = z.Id
      });
    }

    private static System.Linq.Expressions.Expression<Func<VehicleMediaDTO, string>> GetVehicleMediaDTOURL()
    {
      return
        z =>
          (!string.IsNullOrEmpty(z.MediaURL) ?  // If MediaURL is already set
            z.MediaURL : 
            (
              !string.IsNullOrEmpty(z.ExternalId) && z.MediaTypeId == (uint)MediaTypeEnum.Video ?
                URLHelper.VideoMediaUrl(z.ExternalId) :
                URLHelper.VehicleMediaUrl(new VehicleMediaURLDTO() {
                    CustomerId = z.Vehicle.CustomerId.Value,
                    VehicleId = z.Vehicle.Id.Value,
                    VehicleMediaId = z.Id.Value
                })
            )
      );
    }
    private static System.Linq.Expressions.Expression<Func<Vehicle, string>> GetPrimaryImageURL()
    {
      return z => z.PrimaryImageId.HasValue ? URLHelper.VehicleMediaUrl(new VehicleMediaURLDTO() { 
        CustomerId = z.CustomerId.Value, 
        VehicleId = z.Id, 
        VehicleMediaId = z.PrimaryImageId.Value }) : null;
    }
  }
}


