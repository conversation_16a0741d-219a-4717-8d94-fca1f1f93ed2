using System;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Xunit;
using Trading.API.Data.DTO.DotAdmin;
using Trading.API.Data.Models.InspectCollect.VehicleData;
using Trading.Services.InspectCollect.Classes.DotAdmin;
using Trading.Services.ExternalDTO.Configs;
using Trading.Services.InspectCollect.Interfaces;
using Trading.API.Data;

namespace Trading.Services.InspectCollect.Tests.DotAdmin
{
  public class DotAdminServiceTests : IDisposable
  {
    private readonly Mock<IDotAdminClient> _clientMock;
    private readonly Mock<IOptionsSnapshot<DotAdminDTO>> _optionsMock;
    private readonly Mock<ILogger<DotAdminService>> _loggerMock;
    private readonly Mock<IMapper> _mapperMock;
    private readonly TradingContext _tradingContext;
    private readonly DotAdminDTO _config;
    private readonly DotAdminService _service;

    public DotAdminServiceTests()
    {
      _clientMock = new Mock<IDotAdminClient>();
      _optionsMock = new Mock<IOptionsSnapshot<DotAdminDTO>>();
      _loggerMock = new Mock<ILogger<DotAdminService>>();
      _mapperMock = new Mock<IMapper>();

      // Setup in-memory database
      var options = new DbContextOptionsBuilder<TradingContext>()
        .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
        .Options;
      _tradingContext = new TradingContext(options);

      _config = new DotAdminDTO
      {
        BaseUrl = "https://test.dotadmin.net",
        Username = "<EMAIL>",
        Password = "testpassword",
        DefaultCustomerId = 123,
        DefaultLocationId = 456
      };

      _optionsMock.Setup(x => x.Value).Returns(_config);

      _service = new DotAdminService(
        _clientMock.Object,
        _optionsMock.Object,
        _loggerMock.Object,
        _tradingContext,
        _mapperMock.Object);
    }



    [Fact]
    public async Task CreateVehicleFromICVehicleAsync_WithValidICVehicle_ReturnsVehicle()
    {
      // Arrange
      var icVehicleId = Guid.NewGuid();
      var icResponseId = Guid.NewGuid();
      var locationId = 789;
      var customerId = 123;

      var icVehicle = new ICVehicle
      {
        Id = icVehicleId,
        VRM = "AB12 CDE",
        VIN = "1234567890",
        MakeName = "Ford",
        ModelName = "Focus"
      };

      await _tradingContext.ICVehicles.AddAsync(icVehicle);
      await _tradingContext.SaveChangesAsync();

      var mappedRequest = new DotAdminCreateVehicleRequest
      {
        MotorVehicleRegistration = "AB12 CDE",
        MotorVehicleVin = "1234567890",
        VendorId = _config.DefaultCustomerId,
        LogisticsLocationId = locationId,
        Lookup = true
      };

      _mapperMock
        .Setup(x => x.Map<DotAdminCreateVehicleRequest>(It.IsAny<ICVehicle>()))
        .Returns(mappedRequest);

      var expectedVehicle = new DotAdminVehicle
      {
        Id = "12345",
        Registration = "AB12 CDE",
        TypeId = 1,
        Flags = new DotAdminVehicleFlags(),
        LossCategory = false
      };

      var createResponse = new DotAdminCreateVehicleResponse
      {
        Success = true,
        Vehicle = expectedVehicle
      };

      _clientMock
        .Setup(x => x.CreateVehicleAsync(It.IsAny<DotAdminCreateVehicleRequest>(), It.IsAny<CancellationToken>()))
        .ReturnsAsync(createResponse);

      // Act
      var result = await _service.CreateVehicleFromICVehicleAsync(icResponseId, icVehicleId, locationId, customerId);

      // Assert
      Assert.NotNull(result);
      Assert.Equal("12345", result.Id);
      Assert.Equal("AB12 CDE", result.Registration);

      _clientMock.Verify(x => x.CreateVehicleAsync(
        It.Is<DotAdminCreateVehicleRequest>(req =>
          req.MotorVehicleRegistration == "AB12 CDE" &&
          req.MotorVehicleVin == "1234567890" &&
          req.VendorId == _config.DefaultCustomerId &&
          req.LogisticsLocationId == locationId &&
          req.Lookup == true),
        It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task CreateVehicleFromICVehicleAsync_WithNonExistentICVehicle_ThrowsArgumentException()
    {
      // Arrange
      var icVehicleId = Guid.NewGuid();
      var icResponseId = Guid.NewGuid();
      var locationId = 789;
      var customerId = 123;

      // Act & Assert
      await Assert.ThrowsAsync<ArgumentException>(
        () => _service.CreateVehicleFromICVehicleAsync(icResponseId, icVehicleId, locationId, customerId));
    }

    [Fact]
    public async Task CreateVehicleFromICVehicleAsync_WithICVehicleWithoutVRM_ThrowsInvalidOperationException()
    {
      // Arrange
      var icVehicleId = Guid.NewGuid();
      var icResponseId = Guid.NewGuid();
      var locationId = 789;
      var customerId = 123;

      var icVehicle = new ICVehicle
      {
        Id = icVehicleId,
        VRM = "", // Empty VRM
        VIN = "1234567890"
      };

      await _tradingContext.ICVehicles.AddAsync(icVehicle);
      await _tradingContext.SaveChangesAsync();

      // Act & Assert
      await Assert.ThrowsAsync<InvalidOperationException>(
        () => _service.CreateVehicleFromICVehicleAsync(icResponseId, icVehicleId, locationId, customerId));
    }

    [Fact]
    public async Task CreateVehicleAsync_WithValidParameters_ReturnsVehicle()
    {
      // Arrange
      var registration = "AB12 CDE";
      var vin = "1234567890";
      var customerId = 123;
      var locationId = 456;

      var expectedVehicle = new DotAdminVehicle
      {
        Id = "12345",
        Registration = registration,
        TypeId = 1,
        Flags = new DotAdminVehicleFlags(),
        LossCategory = false
      };

      var createResponse = new DotAdminCreateVehicleResponse
      {
        Success = true,
        Vehicle = expectedVehicle
      };

      _clientMock
        .Setup(x => x.CreateVehicleAsync(It.IsAny<DotAdminCreateVehicleRequest>(), It.IsAny<CancellationToken>()))
        .ReturnsAsync(createResponse);

      // Act
      var result = await _service.CreateVehicleAsync(registration, vin, customerId, locationId);

      // Assert
      Assert.NotNull(result);
      Assert.Equal("12345", result.Id);
      Assert.Equal(registration, result.Registration);
    }

    [Fact]
    public async Task CreateVehicleAsync_WithEmptyRegistration_ThrowsArgumentException()
    {
      // Act & Assert
      await Assert.ThrowsAsync<ArgumentException>(
        () => _service.CreateVehicleAsync("", "vin", 123, 456));
    }



    public void Dispose()
    {
      _tradingContext?.Dispose();
    }
  }
}
