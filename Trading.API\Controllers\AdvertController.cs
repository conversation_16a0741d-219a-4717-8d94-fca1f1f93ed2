using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Common;
using Trading.API.Data.DTO;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.API.Remarq.Controllers.Extensions;
using Trading.Services;
using Trading.Services.Extensions;
using Trading.Services.ExternalDTO;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/listing")] // not using [Controller] in route since Ad<PERSON>locker will block it (contains /ad)
  [ApiController]
  [Authorize]
  public class AdvertController : ControllerBase
  {
    private readonly IAdvertService _advertService;
    private readonly IMapper _mapper;
    private readonly INegotiationService _negotiationService;
    private readonly IBidService _bidService;

    public AdvertController(IAdvertService advertService, IMapper mapper,
      INegotiationService negotiationService, IBidService bidService)
    {
      _advertService = advertService;
      _mapper = mapper;
      this._negotiationService = negotiationService;
      this._bidService = bidService;
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> CreateAdvert([FromBody] CreateAdvertDTO advertDTO, CancellationToken cancellationToken)
    {
      try
      {
        var dto = await _advertService.CreateAdvert(advertDTO, cancellationToken);
        return Ok(dto);

      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("setLotNums")]
    public async Task<IActionResult> SetLotNums([FromBody] IEnumerable<AdvertSetLotNumDTO> setLotNums, CancellationToken cancellationToken)
    {
      try
      {
        foreach (var setLotNum in setLotNums)
        {
          var patch = new JsonPatchDocument<Advert>();
          patch.Add(x => x.LotNo, setLotNum.LotNo);
          patch.Add(x => x.LotSeq, setLotNum.LotSeq);
          await _advertService.Patch(setLotNum.AdvertId, setLotNum.CustomerId, patch, cancellationToken);
        }
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpGet("/api/listings")]
    public async Task<IActionResult> GetAdminAdvertView([FromQuery] string query, CancellationToken cancellationToken)
    {
      if (!this.User.IsAdmin() && !this.User.IsPowerUser())
      {
        return Unauthorized();
      }

      var dto = JsonConvert.DeserializeObject<AdvertSearchDTO>(query);
      dto.IsAdmin = true;

      var response = await _advertService.Search(dto, cancellationToken);

      return Ok(response);
    }


    // GET: api/listing
    //[OutputCache(Duration = 600, PolicyName = "OutputCacheWithAuthPolicy")] // incicate policy name
    [HttpGet]
    public async Task<IActionResult> GetAdverts([FromQuery] string? query, CancellationToken cancellationToken)
    {
      AdvertSearchDTO dto = new AdvertSearchDTO();

      if (query != null)
      {
        dto = JsonConvert.DeserializeObject<AdvertSearchDTO>(query);
      }

      var response = await _advertService.Search(dto, cancellationToken);

      return Ok(response);
    }

    [HttpGet("vehicle-info")]
    [OutputCache(Duration = 600, PolicyName = "ConditionalCacheWithAuthPolicy")] // incicate policy name
    public async Task<IActionResult> GetAdvertViewVehicleInfo([FromQuery] string? query, CancellationToken cancellationToken)
    {
      AdvertSearchDTO dto = new AdvertSearchDTO();

      if (query != null)
      {
        dto = JsonConvert.DeserializeObject<AdvertSearchDTO>(query);
      }

      var response = await _advertService.Search(dto, cancellationToken);

      return Ok(response);
    }

    [HttpGet]
    [Route("/api/customer/{customerId}/sellerCounts")]
    public async Task<IActionResult> SellerCounts(Guid customerId, [FromQuery] string query, CancellationToken cancellationToken)
    {
      AdvertSearchDTO dto = new AdvertSearchDTO();

      if (query != null)
      {
        dto = JsonConvert.DeserializeObject<AdvertSearchDTO>(query);
      }

      if (customerId == User.CustomerId() || User.IsAdmin())
      {
        dto.Filters.CustomerId = customerId;
        dto.Filters.ContactId = User.ContactId(); // TODO: Needs passing in
        var response = await _advertService.SellerCounts(dto, cancellationToken);

        return Ok(response);
      }

      return NotFound();
    }

    [HttpPost]
    [Route("{advertId}/changeBidIncrement")]
    public async Task<IActionResult> Search(Guid advertId, [FromBody] ChangeBidIncrementDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        var response = await _advertService.ChangeBidIncrement(advertId, dto.customerId, dto.currentBidIncrement, dto.increase, cancellationToken);
        return Ok(response);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }



    [HttpGet("/sale/{id}/listings")]
    [ResponseCache(Duration = 2)] // Prevents it being hammered more than once a second
    public async Task<IActionResult> GetSaleAdverts(Guid id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      AdvertSearchDTO dto = new AdvertSearchDTO();

      dto.Filters.SaleId = id;

      if (query != null)
      {
        dto = JsonConvert.DeserializeObject<AdvertSearchDTO>(query);
      }

      try
      {
        var advert = await _advertService.Search(dto, cancellationToken);
        return Ok(advert);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }

    }


    [HttpGet("{id}")]
    [AllowAnonymous]
    public async Task<IActionResult> GetAdvert(Guid id, [FromQuery] string? query, CancellationToken cancellationToken)
    {
      AdvertSearchDTO dto = new AdvertSearchDTO();

      if (query != null)
      {
        dto = JsonConvert.DeserializeObject<AdvertSearchDTO>(query);
      }


      var isApiKey = HttpContext.IsAPIKeyRequest();

      dto.Component = dto.Component ?? (isApiKey ? "isAPIKey" : "AdvertView");
      dto.CurrentContactId = User.ContactId();
      dto.CurrentCustomerId = User.CustomerId();
      dto.Filters.CustomerId = dto.CurrentCustomerId;

      try
      {
        var advertSearchDTO = await _advertService.GetAdvert(id, dto, cancellationToken);
        return Ok(new AdvertSearchResultDTO() { Advert = advertSearchDTO.DTO });
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet("{id}/view-data")]
    [ResponseCache(Duration = 2)] // Prevents it being hammered more than once a second
    public async Task<IActionResult> GetAdvertViewData(Guid id, [FromQuery] string? query, CancellationToken cancellationToken)
    {
      AdvertSearchDTO dto = new AdvertSearchDTO();

      if (query != null)
      {
        dto = JsonConvert.DeserializeObject<AdvertSearchDTO>(query);
      }

      dto.Component = "AdvertView";
      dto.CurrentContactId = User.ContactId();
      dto.CurrentCustomerId = User.CustomerId();
      dto.Filters.CustomerId = dto.CurrentCustomerId;

      try
      {
        var advertSearchDTO = await _advertService.GetAdvertViewData(id, dto, cancellationToken);
        return Ok(advertSearchDTO);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    /*
    */

    [HttpGet]
    [Route("{advertId}/publish")]
    public async Task<IActionResult> PublishAdvert(Guid advertId, CancellationToken cancellationToken)
    {
      try
      {
        var contactId = User.ContactId();
        var dto = await _advertService.PublishAdvert(contactId.Value, advertId, cancellationToken);
        return Ok(dto);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet("/api/contact/{contactId}/recentBids")]
    [ResponseCache(Duration = 30)]
    public async Task<IActionResult> GetRecentBidActivity(Guid contactId, [FromQuery] int? limit, CancellationToken cancellationToken)
    {
      limit = limit ?? 4;

      if (limit > 100) { limit = 100; }

      try
      {
        if (contactId == User.ContactId() || User.IsAdmin())
        {
          var response = await _bidService.GetRecentBids(contactId, limit.Value, cancellationToken);
          return Ok(response);
        }
        return BadRequest();
      }
      catch (Exception ex) { return BadRequest(ex); }
    }

    [HttpGet("/api/contact/{contactId}/advertsOfInterest")]
    [AllowAnonymous]
    public async Task<IActionResult> GetAdvertsOfInterest(Guid contactId, CancellationToken cancellationToken)
    {
      try
      {
        var response = await _advertService.GetAdvertsOfInterest(contactId, cancellationToken);
        return Ok(response);
      }
      catch (Exception ex) { return BadRequest(ex); }
    }

    [HttpGet("/api/customer/{customerId}/listings")]
    public async Task<IActionResult> AdvertSearch(Guid customerId, [FromQuery] string query, CancellationToken cancellationToken)
    {
      if (customerId == User.CustomerId() || User.IsAdmin())
      {
        var dto = JsonConvert.DeserializeObject<AdvertSearchDTO>(query);

        dto.Filters.CustomerId = customerId;

        return Ok(await _advertService.Search(dto, cancellationToken));
      }

      return Forbid();
    }


    [HttpPatch]
    [Route("{advertId}")]
    public async Task<IActionResult> PatchAdvert(Guid advertId, [FromBody] JsonPatchDocument<Advert> patch, CancellationToken cancellationToken)
    {

      try
      {
        var customerId = User.CustomerId();

        var advert = await _advertService.Patch(advertId, customerId.Value, patch, cancellationToken);
        return Ok(advert);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("/api/customer/{customerId}/listing/{advertId}/startPrice")]
    public async Task<IActionResult> SetStartPrice(Guid customerId, Guid advertId, [FromBody] JsonPatchDocument<Advert> patch, CancellationToken cancellationToken)
    {

      try
      {
        var advert = await _advertService.SetStartPrice(advertId, customerId, patch, cancellationToken);
        return Ok(advert);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpGet]
    [Route("{advertId}/relist")]
    public async Task<IActionResult> Relist(Guid advertId, CancellationToken cancellationToken)
    {
      try
      {
        var customerId = User.CustomerId();
        var contactId = User.ContactId();

        var id = await _advertService.Relist(advertId, customerId.Value, contactId.Value, false, cancellationToken);
        return Ok(id);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("relistByVehicle/{vehicleId}")]
    [ApiExplorerSettings(IgnoreApi = true)] // won't show in swagger doc (it's for internal use only currently)
    public async Task<IActionResult> RelistByVehicle(Guid vehicleId, CancellationToken cancellationToken)
    {
      try
      {
        var customerId = User.CustomerId();
        var contactId = User.ContactId();

        await _advertService.RelistByVehicle(vehicleId, customerId.Value, contactId.Value, true, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("republishByVehicle/{vehicleId}")]
    [ApiExplorerSettings(IgnoreApi = true)] // won't show in swagger doc (it's for internal use only currently)
    public async Task<IActionResult> RepublishByVehicle(Guid vehicleId, CancellationToken cancellationToken)
    {
      try
      {
        var customerId = User.CustomerId();
        var contactId = User.ContactId();

        await _advertService.RelistByVehicle(vehicleId, customerId.Value, contactId.Value, true, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{advertId}/cancelListing/{reasonId}")]
    public async Task<IActionResult> EndSale(Guid advertId, uint reasonId, CancellationToken cancellationToken)
    {
      try
      {
        var customerId = User.CustomerId();
        var contactId = User.ContactId();

        await _advertService.CancelAdvert(advertId, reasonId, customerId.Value, contactId.Value, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }



    [HttpGet]
    [Route("cancelListingReasons")]
    public async Task<IActionResult> GetEndListingReasons(CancellationToken cancellationToken)
    {
      try
      {
        var reasons = await _advertService.GetListingEndReasons(cancellationToken);
        return Ok(reasons);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("soldStatus/{soldStatus}/exportToCSV")]
    public async Task<IActionResult> ExportToCSV(SoldStatusEnum soldStatus, CancellationToken cancellationToken)
    {
      try
      {
        var customerId = User.CustomerId().Value;
        var bytes = await _advertService.ExportToCSV(customerId, soldStatus, cancellationToken);

        return new FileContentResult(bytes, "text/csv");
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpDelete]
    [Route("{advertId}")]
    public async Task<IActionResult> Delete(Guid advertId, CancellationToken cancellationToken)
    {
      try
      {
        await _advertService.DeleteAdvert(advertId, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("createTestNegotiations/{advertId}")]
    [ApiExplorerSettings(IgnoreApi = true)] // won't show in swagger doc (it's for internal use only currently)
    public async Task<IActionResult> CreateTestNegotiations(Guid advertId, CancellationToken cancellationToken)
    {
      try
      {
        await _negotiationService.CreateNegotiations(advertId, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpGet]
    [AllowAnonymous]
    [Route("{advertId}/lambdaInfo")]
    public async Task<IActionResult> LambdaLocationInfo(Guid advertId)
    {
      var info = await _advertService.LambdaInfo(advertId);
      return Ok(info);
    }
  }
}
