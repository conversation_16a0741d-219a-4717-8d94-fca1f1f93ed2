﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Common;
using Trading.API.Data.DTO.LeadCRM;
using Trading.API.Data.Models.LeadCRM;
using Trading.Services;
using Trading.Services.Extensions;
using Trading.Services.LeadCRM.Interfaces;

namespace Trading.API.LeadCRM.Controllers
{
  [Route("api/lead-document")]
  [ApiController]
  [Authorize]
  public class LeadDocumentController : ControllerBase
  {
    private readonly ILeadDocumentService _leadDocumentService;

    public LeadDocumentController(ILeadDocumentService leadDocumentService)
    {
      this._leadDocumentService = leadDocumentService;
    }


    [HttpPost]
    [Route("/api/lead/{leadId}/documents")]
    [RequestFormLimits(ValueLengthLimit = int.MaxValue, MultipartBodyLengthLimit = int.MaxValue)]
    [DisableRequestSizeLimit]
    public async Task<IActionResult> UploadAppraisalMedia(IFormCollection data, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<LeadDocumentUploadDTO>(data["leadDocumentUploadDTO"]);

      // todo: check that both MediaUpload and DocumentUpload are not set before returning bad request
      if (dto.MediaUpload == null)
      {
        return BadRequest("MediaUpload property is required");
      }

      dto.MediaUpload.FormFiles = data.Files;

      var medias = await _leadDocumentService.UploadLeadDocuments(dto, cancellationToken);
      return Ok(medias);
    }


    //[ResponseCache(Duration = 60)]
    [HttpGet]
    [Route("/api/lead-documents")]
    public async Task<IActionResult> Search([FromQuery] string? query, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = new LeadDocumentSearchDTO();
        
        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<LeadDocumentSearchDTO>(query);
        }

        var result = await _leadDocumentService.Search(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create([FromBody] LeadDocumentDTO dto, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadDocumentService.Create(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpPost]
    [Route("create-from-appraisal-media")]
    [AllowAnonymous] 
    public async Task<IActionResult> CreateFromAppraisalMedia([FromBody] CreateAppraisalDocDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        var result = await _leadDocumentService.CreateFromAppraisalMedia(dto, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<IActionResult> Get(
      Guid leadDocumentId, 
      [FromQuery] string query, 
      CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = JsonConvert.DeserializeObject<LeadDocumentSearchDTO>(query);
        var result = await _leadDocumentService.Get(leadDocumentId,dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<LeadDocument> patch, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadDocumentService.Patch(id, patch, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<IActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadDocumentService.Delete(id, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
