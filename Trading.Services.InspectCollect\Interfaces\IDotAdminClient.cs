using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO.DotAdmin;

namespace Trading.Services.InspectCollect.Interfaces;

/// <summary>
/// Interface for dotAdmin HTTP client operations
/// </summary>
public interface IDotAdminClient : IDisposable
{
  /// <summary>
  /// Check if the client is currently authenticated with a valid token
  /// </summary>
  bool IsAuthenticated { get; }

  /// <summary>
  /// Get the current authentication token
  /// </summary>
  string? CurrentToken { get; }

  /// <summary>
  /// Get the current authentication response containing customer/location data
  /// </summary>
  DotAdminAuthResponse? CurrentAuth { get; }

  /// <summary>
  /// Authenticate with the dotAdmin API
  /// </summary>
  Task<DotAdminAuthResponse> AuthenticateAsync(
    string username,
    string password,
    int? customerId = null,
    int? locationId = null,
    CancellationToken cancellationToken = default);

  /// <summary>
  /// Select a customer and location for the current session
  /// </summary>
  Task<DotAdminAuthResponse> SelectCustomerLocationAsync(
    int customerId,
    int locationId,
    CancellationToken cancellationToken = default);

  /// <summary>
  /// Create a new vehicle in dotAdmin
  /// </summary>
  Task<DotAdminCreateVehicleResponse> CreateVehicleAsync(
    DotAdminCreateVehicleRequest request,
    CancellationToken cancellationToken = default);

  /// <summary>
  /// Make a GET request to the dotAdmin API
  /// </summary>
  Task<T> GetAsync<T>(string endpoint, CancellationToken cancellationToken = default);

  /// <summary>
  /// Make a POST request to the dotAdmin API
  /// </summary>
  Task<T> PostAsync<T>(string endpoint, object? data = null, CancellationToken cancellationToken = default);

}
