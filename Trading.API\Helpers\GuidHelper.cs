﻿using Force.Crc32;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.Services.Helpers;

namespace Trading.API.Helpers
{
  public static class GuidHelper
  {
    public static Guid Int2Guid(int value)
    {
      byte[] bytes = new byte[16];
      BitConverter.GetBytes(value).CopyTo(bytes, 0);
      return new Guid(bytes);
    }

    public static int Guid2Int(Guid value)
    {
      byte[] b = value.ToByteArray();
      int bint = BitConverter.ToInt32(b, 0);
      return bint;
    }

    public static uint Guid2Crc(Guid value)
    {
      return Crc32Algorithm.Compute(Encoding.ASCII.GetBytes(value.ToString()));
    }
  }
}
