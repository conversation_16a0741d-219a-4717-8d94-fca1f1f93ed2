﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Search.Valuation;
using Trading.API.Data.DTO.Valuation;
using Trading.Services.Extensions;
using Trading.Services.Valuations.Interfaces;

namespace Trading.API.Valuations.Controllers
{
  [Route("api/valuation-quote")]
  [ApiController]
  [Authorize]
  public class ValuationQuoteController : ControllerBase
  {
    private readonly IValuationQuoteService _valuationQuoteService;

    public ValuationQuoteController(IValuationQuoteService valuationQuoteService) {
      this._valuationQuoteService = valuationQuoteService;
    }


    [HttpPost]
    [Route("initialize-quote")]
    [AllowAnonymous] // the service filter will prevent execution if no API key or user is not authenticated
    public async Task<IActionResult> CreateValuationQuote([FromBody] ValuationRequestDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        // test version is admin only 
        if (dto.TestValuation)
        {
          return BadRequest("Test mode cannot be used for valuation quotes");
        }

        var result = await _valuationQuoteService.InitializeQuote(dto, cancellationToken);
        var res = new ObjectResult(result);
        res.StatusCode = (int)result.HTTPStatus;

        return res;
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("get-quote")]
    [AllowAnonymous]
    public async Task<ValidatedResultDTO<ValuationResultDTO>> GetValuationQuote([FromBody] QuoteRequestDTO dto, CancellationToken cancellationToken)
    {
      var fullRequest = new ValuationRequestDTO
      {
        VRM = dto.VRM.ToUpper(),
        Mileage = dto.Mileage,
        Name = dto.Name,
        Email = dto.Email,
        Mobile = dto.Mobile,
        Phone = dto.Phone,
        AggregateType = Data.Enums.Valuation.ValuationAggregateTypeEnum.Average,
        CreateTestQuote = false,
        TestValuation = false,
        QuoteType = Data.Enums.Valuation.ValuationQuoteTypeEnum.Estimate
      };

      var res = await _valuationQuoteService.InitializeQuote(fullRequest, cancellationToken);
      var result = new ValidatedResultDTO<ValuationResultDTO>
      {
        IsValid = res.IsValid,
        HTTPStatus = res.HTTPStatus,
        Message = res.Message,
        DTO = new ValuationResultDTO
        {
          ContactId = res.DTO.ContactId,
          ContactNote = res.DTO.ContactNote,
          Expires = res.DTO.Expires,
          ExternalRef = res.DTO.ExternalRef,
          LeadVehicleId = res.DTO.LeadVehicle?.Id,
          GradeValues = res.DTO.GradeValues,
          Log = res.DTO.Log,
          QuoteType = res.DTO.QuoteType,
          ResultValue = res.DTO.ResultValue,
          Steps = res.DTO.Steps,
          VehicleInfo = res.DTO.VehicleInfo,
          ContactEmail = res.DTO.ContactEmail,
          ContactName = res.DTO.ContactName,
          ContactPhone = res.DTO.ContactPhone,
        }
      };

      return result;
    }

    [HttpPost]
    [Route("manual-quote")]
    public async Task<IActionResult> CreateManualQuote([FromBody]ManualQuoteRequestDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        dto.ContactId = this.User.ContactId();
        var result = await _valuationQuoteService.CreateManualQuote(dto, cancellationToken);
        var res = new ObjectResult(result);
        res.StatusCode = (int)result.HTTPStatus;

        return res;
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/valuation-quotes")]
    [AllowAnonymous]
    public async Task<IActionResult> SearchValuationQuotes([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = JsonConvert.DeserializeObject<ValuationQuoteSearchDTO>(query);

        var result = await _valuationQuoteService.SearchValuationQuotes(dto, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

  }
}
