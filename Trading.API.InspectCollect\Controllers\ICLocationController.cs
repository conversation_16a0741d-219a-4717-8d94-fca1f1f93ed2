﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect.Location;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.Extensions;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/location")]
  [ApiController]
  [Authorize(Policy = "RequireInspectCollect")]
  public class ICLocationController : ControllerBase
  {
    private readonly ICLocationInterface _locationService;

    public ICLocationController(ICLocationInterface locationService)
    {
      _locationService = locationService;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(Guid id, CancellationToken cancellationToken)
    {
      var location = await _locationService.Get(id, cancellationToken);
      return Ok(location);
    }

    [HttpPost]
    public async Task<ActionResult> Create([FromBody] CreateICLocationDTO dto)
    {
      var location = await _locationService.Create(dto);
      return Ok(location);
    }

    [HttpPut]
    [Route("{id}")]
    public async Task<ActionResult> Update(Guid id, [FromBody] UpdateICLocationDTO dto)
    {
      var location = await _locationService.Update(id, dto);
      return Ok(location);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(Guid id)
    {
      var result = await _locationService.Delete(id);
      return Ok(result);
    }

    [HttpGet]
    [Route("/api/inspect-collect/locations")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICLocationSearchDTO>(query);

      // Set defaults if not provided
      if (dto.Filters == null)
      {
        dto.Filters = new ICLocationSearchFilters();
      }

      if (dto.Limit <= 0)
      {
        dto.Limit = 100;
      }

      // Check if user has admin access or belongs to the appropriate container group
      if (User.IsManager() || User.IsAdmin())
      {
        var result = await _locationService.Search(dto, cancellationToken);
        return Ok(result);
      }

      return Forbid();
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, [FromBody] JsonPatchDocument<ICLocation> patchDocument)
    {
      var location = await _locationService.Patch(id, patchDocument);
      return Ok(location);
    }

    // User Location Management Endpoints
    [HttpGet]
    [Route("user/{userId}")]
    public async Task<ActionResult> GetUserLocations(Guid userId, CancellationToken cancellationToken)
    {
      // Check permission - users can only see their own locations unless admin/manager
      if (!User.IsAdmin() && !User.IsManager() && User.InspectCollectUserId() != userId.ToString())
      {
        return Forbid();
      }

      var userLocations = await _locationService.GetUserLocations(userId, cancellationToken);
      return Ok(userLocations);
    }

    [HttpPost]
    [Route("assign")]
    public async Task<ActionResult> AssignUserToLocation([FromBody] CreateICUserLocationDTO dto)
    {
      // Check permission - users can only assign themselves unless admin/manager
      if (!User.IsAdmin() && !User.IsManager() && User.InspectCollectUserId() != dto.ICUserId.ToString())
      {
        return Forbid();
      }

      var userLocation = await _locationService.AssignUserToLocation(dto);
      return Ok(userLocation);
    }

    [HttpDelete]
    [Route("user/{userId}/location/{locationId}")]
    public async Task<ActionResult> RemoveUserFromLocation(Guid userId, Guid locationId)
    {
      // Check permission - users can only remove themselves unless admin/manager
      if (!User.IsAdmin() && !User.IsManager() && User.InspectCollectUserId() != userId.ToString())
      {
        return Forbid();
      }

      var result = await _locationService.RemoveUserFromLocation(userId, locationId);
      return Ok(result);
    }

    [HttpPost]
    [Route("set-default")]
    public async Task<ActionResult> SetDefaultLocation([FromBody] SetDefaultLocationDTO dto)
    {
      // Check permission - users can only set their own default unless admin/manager
      if (!User.IsAdmin() && !User.IsManager() && User.InspectCollectUserId() != dto.ICUserId.ToString())
      {
        return Forbid();
      }

      var user = await _locationService.SetDefaultLocation(dto);
      return Ok(user);
    }
  }
}