﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.Search.LeadCRM;
using Trading.API.Data.Models.LeadCRM;
using Trading.Services.Extensions;
using Trading.Services.LeadCRM.Interfaces;

namespace Trading.API.LeadCRM.Controllers
{
  [Route("api/crm-attrib")]
  [ApiController]
  [Authorize]
  public class CRMAttribController : ControllerBase
  {
    private readonly ICRMAttribService _crmAttribService;

    public CRMAttribController(ICRMAttribService crmAttribService)
    {
      this._crmAttribService = crmAttribService;
    }

    //[ResponseCache(Duration = 60)]
    [HttpGet]
    [Route("/api/crm-attribs")]
    public async Task<IActionResult> Search([FromQuery] string? query, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = new CRMAttribSearchDTO();
        
        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<CRMAttribSearchDTO>(query);
        }

        var result = await _crmAttribService.Search(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create([FromBody] CRMAttribDTO dto, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _crmAttribService.Create(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<IActionResult> Get(
      uint attribId, 
      [FromQuery] string? query, 
      CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = JsonConvert.DeserializeObject<CRMAttribSearchDTO>(query);
        var result = await _crmAttribService.Get(attribId,cancellationToken, dto);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("{attribId}")]
    public async Task<IActionResult> Patch(uint attribId, JsonPatchDocument<CRMAttrib> patch, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _crmAttribService.Patch(attribId, patch, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
