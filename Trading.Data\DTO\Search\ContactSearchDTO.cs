﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO
{
  public class ContactSearchDTO : BaseSearchDTO
  {
    public ContactFilters Filters { get; set; } = new ContactFilters() { };
  }
  public class ContactFilters : BaseFilterGuid
  {
    public Guid? CustomerId { get; set; }
    public string Email { get; set; }
    public string HasRole { get; set; }
    public bool? Available { get; set; }

    public string NameSearch { get; set; }
  }
}
