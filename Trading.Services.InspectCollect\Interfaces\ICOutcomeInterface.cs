using Microsoft.AspNetCore.JsonPatch;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.InspectCollect.VehicleData;
using Trading.API.Data.Models.InspectCollect.VehicleData;

namespace Trading.Services.InspectCollect.Interfaces
{
  public interface ICOutcomeInterface
  {
    Task<ICOutcomeDTO> Create(ICOutcomeCreateDTO dto);

    Task<ValidatedResultDTO<ICOutcomeDTO>> Get(uint id, ICOutcomeSearchDTO search, CancellationToken cancellationToken);
    Task<bool> Delete(uint id);

    Task<ICOutcomeDTO> Patch(uint id, JsonPatchDocument<ICOutcome> patch);

    Task<SearchResultDTO<ICOutcomeDTO>> Search(ICOutcomeSearchDTO search, CancellationToken cancellationToken);
  }
}
