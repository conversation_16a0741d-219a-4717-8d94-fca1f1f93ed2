{"region": "eu-west-2", "configuration": "Debug", "framework": "net5.0", "self-contained": false, "application": "TradingAPI", "environment": "TradingAPI-dev", "enable-xray": false, "enhanced-health-type": "basic", "additional-options": "", "solution-stack": "64bit Amazon Linux 2 v2.1.1 running .NET Core", "environment-type": "SingleInstance", "cname": "tradingapi-dev", "instance-type": "t3a.Media", "key-pair": "Beanstalk", "instance-profile": "aws-elasticbeanstalk-ec2-role", "service-role": "aws-elasticbeanstalk-service-role", "proxy-server": "none"}