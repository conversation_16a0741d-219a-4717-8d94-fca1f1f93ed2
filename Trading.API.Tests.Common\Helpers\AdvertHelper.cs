﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Moq;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.API.Tests.Common.Fakes;
using Trading.API.Tests.Common.Params;
using Trading.Services;
using Trading.Services.Classes;
using Trading.Services.ExternalDTO;
using Trading.Services.Interfaces;
using Xunit;
namespace Trading.API.Tests.Common.Helpers
{
  public class AdvertHelper
  {
    public static async Task<AdvertDTO> CreateTestAdvert(TradingContext context, IMapper mapper, CommonServices common, LookupFactory lookupFactory, 
      Guid customerId, Guid contactId, string vrm, IBidService? bidService = null)
    {
      var vehicle = await VehicleHelper.CreateTestVehicle(context, lookupFactory, customerId, CreateVehicleParams.GetDefault(vrm));

      if (bidService == null)
      {
        var dpDTO = new Mock<IOptionsSnapshot<DefaultPlatformDTO>>();
        dpDTO.SetupAllProperties();
        dpDTO.SetupGet(p => p.Value).Returns(new DefaultPlatformDTO { PlatformId = 1 });

        bidService = new BidService(common.DealService, common.DbConnection, context, mapper, common.MessageService, 
          common.InMailService, common.NegotiationService, common.EmailService, dpDTO.Object, null, null, new FakeStaffNotificationService());
      }

      IAdvertService advertService = new AdvertService(context, bidService, mapper, common.ContactService, common.CustomerService
          , common.LookupService, 
        common.UserService, common.MessageService, common.DbConnection, common.ContactActionService, null, common.CustomerInternalInfoService, common.ServiceQueueService, common.VehicleCheckService);

      var advertDTO = await advertService.CreateAdvert(new CreateAdvertDTO { VehicleId = vehicle.Id, ContactId = contactId, CustomerId = customerId }, CancellationToken.None);
      return advertDTO;
    }

    public static async Task<Advert> SetAdvertProperties(TradingContext context, AdvertPropertiesDTO ap)
    {
      if (ap.startDate == null)
      {
        ap.startDate = DateTime.Now.Subtract(TimeSpan.FromMinutes(10));
      }
      if (ap.endDate == null)
      {
        ap.endDate = DateTime.Now.AddDays(7);
      }

      var sale = await context.Sales.FirstOrDefaultAsync(x => x.SaleTypeId == (uint)ap.saleType);
      Assert.NotNull(sale);

      var advert = await context.Adverts.FirstOrDefaultAsync(x => x.Id == ap.adId);

      advert.BuyItNowPrice = ap.buyItNowPrice;
      advert.StartPrice = ap.startPrice;
      advert.AcceptBids = ap.acceptBids;
      advert.BidIncrement = ap.bidIncrement;
      advert.SaleId = sale.Id;
      advert.AvailableDate = ap.startDate.Value;
      advert.EndDateTime = ap.endDate.Value;
      advert.StatusId = (uint)StatusEnum.Active;
      advert.AutoRejectBid = ap.autoRejectBid;
      advert.AutoAcceptBid = ap.autoAcceptBid;
      advert.ReservePrice = ap.reserve;
      advert.AdvertStatus = ap.advertStatus;
      advert.SoldStatus = ap.soldStatus;
      advert.CurrentPrice = ap.CurrentPrice;

      if (ap.vatStatusId.HasValue)
      {
        advert.Vehicle.VatStatusId = ap.vatStatusId.Value;
      }

      await context.SaveChangesAsync();

      return advert;
    }
  }
}
