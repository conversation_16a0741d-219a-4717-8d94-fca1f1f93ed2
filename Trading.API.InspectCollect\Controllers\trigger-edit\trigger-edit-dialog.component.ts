import {Component, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild} from '@angular/core';
import {ModalDirective} from "ng-uikit-pro-standard";
import {
  FormControl,
  FormGroup,
  Validators
} from "@angular/forms";
import {
  TriggerService,
  LocalStorageService
} from "../../../../../../services";
import {
  AdminEventService,
} from "../../../../../../services/admin";
import {ICAdminEventTypeEnum} from "../../../../../../enums";
import {
  ICContainerGroupDTO, ICTriggerDTO,
  User
} from "../../../../../../interfaces";
import {filter, Subject, Subscription} from "rxjs";
import {HelpersService} from '../../../../../../global/services';
import {StatusEnum} from '../../../../../../global/enums';
import {compare} from "fast-json-patch";
import {UserService} from "../../../../../../services/user.service";
import {takeUntil} from "rxjs/operators";
import {ICTriggerTypeEnum} from "../../../../../../enums/ICTriggerTypeEnum";

@Component({
  selector: 'app-trigger-edit-dialog',
  templateUrl: './trigger-edit-dialog.component.html',
  styleUrls: ['./trigger-edit-dialog.component.scss'],
})

export class TriggerEditDialogComponent implements OnInit, OnDestroy {

  @ViewChild("addTriggerModal") addTriggerModal: ModalDirective;
  @ViewChild("editTriggerModal") editTriggerModal: ModalDirective;

  addTriggerForm = new FormGroup({
    name: new FormControl(null, [Validators.required]),
    icContainerGroupId: new FormControl(null),
  });

  editTriggerForm = new FormGroup({
    id: new FormControl(null),
    name: new FormControl(null, [Validators.required]),
    triggerType: new FormControl(null),
    wait: new FormControl(null),
    remoteCall: new FormControl(null),
    remoteTriggerName: new FormControl(null),
    payloadTemplate: new FormControl(null),
    statusId: new FormControl(null)
  });
  statusOptions: { label: string; value: number }[] = [];
  statusName: { [key: number]: string } = {};
  private eventSub: Subscription;
  private preEdit: {};
  private user: User | null = null;
  private icContainerGroupId: string;
  private destroy$ = new Subject<void>();
  triggerTypeOptions: any[] = [];

  constructor(
    private eventService: AdminEventService,
    private triggerService: TriggerService,
    private localStorage: LocalStorageService,
    private userService: UserService,
    private helperService: HelpersService
  ) {

    this.statusOptions = this.helperService.getLabelsAndValues(StatusEnum);

    this.statusOptions.forEach(x => {
      this.statusName[x.value] = x.label;
    });

    this.triggerTypeOptions = this.helperService.getLabelsAndValues(ICTriggerTypeEnum);
  }

  get af() {
    return this.addTriggerForm.value;
  }

  get ef() {
    return this.editTriggerForm.value;
  }

  ngOnDestroy() {
    this.eventSub.unsubscribe();
    this.destroy$.next();
    this.destroy$.complete();
  }

  ngOnInit(): void {

    this.userService.user$.pipe(
      filter(user => !!user),
      takeUntil(this.destroy$)
    ).subscribe(user => {
      this.user = user;
      this.icContainerGroupId = user?.effectiveICContainerGroupId;
    });

    this.eventSub = this.eventService.ICEvent.subscribe((data) => {

      if (data.eventType == ICAdminEventTypeEnum.TriggerAddInit) {
        this.startAddTrigger(this.icContainerGroupId).then();
      }

      if (data.eventType == ICAdminEventTypeEnum.TriggerEditInit) {
        this.startEditTrigger(data.object.icTriggerId).then();
      }
    });
  }

  async startAddTrigger(icContainerGroupId) {

    this.addTriggerForm.reset();
    this.addTriggerForm.patchValue({icContainerGroupId});
    this.addTriggerModal.show();

  }

  async startEditTrigger(triggerId: string) {

    this.editTriggerForm.reset();

    this.localStorage.getGlobalContainerGroupPromise().then((gcg) => {

      this.getTrigger(triggerId).then((response) => {

        const dto = response.dto;

        this.editTriggerForm.patchValue({
          id: triggerId,
          name: dto.name,
          triggerType: dto.triggerType,
          wait: dto.wait,
          remoteCall: dto.remoteCall,
          remoteTriggerName: dto.remoteTriggerName,
          payloadTemplate: dto.payloadTemplate,
          statusId: dto.statusId
        });

        this.preEdit = this.ef;

        this.editTriggerModal.show();
      });
    });

  }

  getTrigger(triggerId: string) {
    return this.triggerService.get(triggerId, {component: 'trigger-add-dialog'});
  }

  deleteTrigger() {

    const patch = compare({}, {statusId: StatusEnum.Deleted});

    this.triggerService.patch(this.ef.id, patch).then(() => {
      this.hideEditTrigger();
      this.eventService.ICEvent.emit({eventType: ICAdminEventTypeEnum.TriggerDeleted});
    });
  }

  saveAddTrigger() {

    this.triggerService.create({
      name: this.af.name,
      icContainerGroupId: this.af.icContainerGroupId,
    }).then((newTrigger) => {
      this.eventService.ICEvent.emit({eventType: ICAdminEventTypeEnum.TriggerSaved});
      this.startEditTrigger(newTrigger.id).then();
    });

    this.hideAddTrigger();
  }

  saveEditTrigger() {

    const patch = compare(this.preEdit, this.ef);

    this.triggerService.patch(this.ef.id, patch).then((newTrigger) => {

      this.hideEditTrigger();
      this.eventService.ICEvent.emit({eventType: ICAdminEventTypeEnum.TriggerSaved});
    });
  }

  hideAddTrigger() {
    this.addTriggerModal.hide();
  }

  hideEditTrigger() {
    this.editTriggerModal.hide();
  }
}

