﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Common;
using Trading.API.Data.DTO.LeadCRM;
using Trading.API.Data.DTO.Search.LeadCRM;
using Trading.API.Data.Models.LeadCRM;
using Trading.Services.Extensions;
using Trading.Services.LeadCRM.Interfaces;

namespace Trading.API.LeadCRM.Controllers
{
  [Route("api/lead-document-category")]
  [ApiController]
  [Authorize]
  public class LeadDocumentCategoryController : ControllerBase
  {
    private readonly ILeadDocumentCategoryService _LeadDocumentCategoryService;

    public LeadDocumentCategoryController(ILeadDocumentCategoryService LeadDocumentCategoryService)
    {
      this._LeadDocumentCategoryService = LeadDocumentCategoryService;
    }

    //[ResponseCache(Duration = 60)]
    [HttpGet]
    [Route("/api/lead-document-categories")]
    public async Task<IActionResult> Search([FromQuery] string? query, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = new LeadDocumentCategorySearchDTO();
        
        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<LeadDocumentCategorySearchDTO>(query);
        }

        var result = await _LeadDocumentCategoryService.Search(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create([FromBody] LeadDocumentCategoryDTO dto, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _LeadDocumentCategoryService.Create(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<IActionResult> Get(
      uint LeadDocumentCategoryId, 
      [FromQuery] string query, 
      CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = JsonConvert.DeserializeObject<LeadDocumentCategorySearchDTO>(query);
        var result = await _LeadDocumentCategoryService.Get(LeadDocumentCategoryId,dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(uint id, JsonPatchDocument<LeadDocumentCategory> patch, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _LeadDocumentCategoryService.Patch(id, patch, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<IActionResult> Delete(uint id, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _LeadDocumentCategoryService.Delete(id, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
