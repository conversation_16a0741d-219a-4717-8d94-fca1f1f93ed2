using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/container-widget")]
  [ApiController]
  [AllowAnonymous]
  public class ICContainerWidgetController : ControllerBase
  {
    private readonly ICContainerWidgetInterface _icContainerWidgetService;

    public ICContainerWidgetController(ICContainerWidgetInterface serviceInterface)
    {
      _icContainerWidgetService = serviceInterface;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(Guid id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICContainerWidgetSearchDTO>(query);

      dto.Filters.Id = id;

      var res = await _icContainerWidgetService.Get(id, dto, cancellationToken);
      return Ok(res);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icContainerWidgetService.Delete(id);
      return Ok(res);
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Post([FromBody] ICContainerWidgetCreateDTO dto, CancellationToken cancellationToken)
    {
      var res = await _icContainerWidgetService.Create(dto);
      return Ok(res);
    }

    [HttpGet]
    [Route("/api/inspect-collect/container-widgets")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICContainerWidgetSearchDTO>(query);
      var res = await _icContainerWidgetService.Search(dto, cancellationToken);
      return Ok(res);
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<ICContainerWidget> dto)
    {
      var response = await _icContainerWidgetService.Patch(id, dto);
      return Ok(response);
    }
  }
}