# This workflow will build a .NET project
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-net

name: Template - Build API

on:
  workflow_call:
    inputs:
      CONFIGURATION:
        required: true
        type: string
     
jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: 6.0.x

    - name: Restore dependencies
      run: dotnet restore ./Trading.API.sln

    - name: Build
      run: dotnet build --no-restore --configuration ${{ inputs.CONFIGURATION }} ./Trading.API.sln

    - name: List Files in Distribution Directory
      run: ls -R ./
 
# Require tests
#    - name: Test
#      run: dotnet test --no-build --verbosity normal ./Trading.API.sln
