﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Moq;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Tests.Common;
using Trading.API.Tests.Common.Helpers;
using Trading.API.Tests.Common.Params;
using Trading.Services;
using Trading.Services.Classes;
using Trading.Services.ExternalDTO;
using Trading.Services.Interfaces;
using Trading.Services.Interfaces.DVLA;
using Xunit;

namespace Trading.API.Tests
{
    [Collection("DatabaseCollection")]
  public class VehicleService_Test : TestBase
  {
    DatabaseFixture _fixture;

    public VehicleService_Test(DatabaseFixture fixture)
    {
      _fixture = fixture;
    }

    private IVehicleService GetService()
    {
      var ukVehicleDTO = new Mock<IOptionsSnapshot<UKVehicleDTO>>();
      ukVehicleDTO.SetupAllProperties();
      ukVehicleDTO.SetupGet(p => p.Value).Returns(new UKVehicleDTO { APIKey = "FakeKey" });
      IVRMLookupService vrmLookupService = new UKVehicleDataService(_context, ukVehicleDTO.Object, _mapper, _common.LookupService);

      ILookupService lookupService = new LookupService(_context, _common.DVLAService, _mapper);
      IAppraisalService appraisalService = new AppraisalService(_context, _mapper, _common.FileStorageService);

      IVehicleCheckService checkService = new Mock<IVehicleCheckService>().Object;

      IYTService youtubeService = new YTService(_context, new Mock<IOptionsSnapshot<YoutubeDTO>>().Object);
      IVehicleMediaService mediaService = new VehicleMediaService(_context, _mapper, _common.FileStorageService, _common.MessageService, _common.UserService, youtubeService, null);      IVehicleService vehicleService = new VehicleService(_context, null, _mapper, _common.FileStorageService, mediaService, lookupService, _common.Configuration
        , _common.DVLAService, appraisalService, _common.UserService, youtubeService, _common.MessageService, new List<IVRMLookupService> { vrmLookupService }, checkService);

      return vehicleService;
    }

    // not testing APIs
    //[Fact]
    //public async Task CreateVehicleFromLookup()
    //{
    //  var service = GetService();

    //  var vehicle = await service.CreateVehicleFromLookup(new CreateVehicleDTO { Vrm = "DG67CTO" }, CancellationToken.None);

    //  Assert.NotNull(vehicle);
      
    //  // assert vehicle is present in db 
    //  Assert.True(await _context.Vehicles.AnyAsync(x => x.Id == vehicle.Id));
    //}

    [Fact]
    public async Task GetVehicle()
    {
      var vrm = "DG67CTO";

      var service = GetService();

      var vehicle = await VehicleHelper.CreateTestVehicle(_context, _lookupFactory, _testBase.BaseCustomerIdGuid, CreateVehicleParams.GetDefault(vrm));
      var internalVehicle = await service.GetVehicle(vehicle.Id, CancellationToken.None);

      Assert.NotNull(internalVehicle);
    }

    [Fact]
    public async Task CheckVehicleFields()
    {
      var vrm = "DG67CTO";

      var service = GetService();

      var vehicle = await VehicleHelper.CreateTestVehicle(_context, _lookupFactory, _testBase.BaseCustomerIdGuid, CreateVehicleParams.GetDefault(vrm));
      var internalVehicle = await service.GetVehicle(vehicle.Id, CancellationToken.None);

      Assert.True(internalVehicle != null);
    }
  }
}
