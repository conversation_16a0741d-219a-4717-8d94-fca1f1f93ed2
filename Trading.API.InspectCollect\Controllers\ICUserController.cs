using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect;
using Trading.Services.Extensions;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/user")]
  [ApiController]
  [Authorize(Policy = "RequireInspectCollect")]
  public class ICUserController : ControllerBase
  {
    private readonly ICUserServiceInterface _serviceInterface;

    public ICUserController(ICUserServiceInterface serviceInterface)
    {
      _serviceInterface = serviceInterface;
    }


    //[HttpPost]
    //[Route("create-user-from-cognito/{icContainerGroupId}")]
    //[AllowAnonymous]
    //[ApiExplorerSettings(IgnoreApi = true)] // won't show in swagger doc 
    //public async Task<IActionResult> CreateUserFromCognito(Guid icContainerGroupId, CancellationToken cancellationToken)
    //{
    //  var email = User.Email();
    //  var contactName = User.ContactName();
    //  var userId = User.CognitoUserId(); // userName from Cognito claims is a unique Id (Guid)

    //  if (String.IsNullOrEmpty(email) || String.IsNullOrEmpty(contactName))
    //  {
    //    return Forbid();
    //  }

    //  var user = await _serviceInterface.AddUser(icContainerGroupId, contactName, email, userId);
    //  return Ok(user);
    //}

    [HttpPost]
    [Route("create-user-for-cognito")]

    [ApiExplorerSettings(IgnoreApi = true)] // won't show in swagger doc 
    public async Task<IActionResult> CreateUserForCognito([FromBody]CreateICUserDTO dto, CancellationToken cancellationToken)
    {
      var user = await _serviceInterface.AddUser(dto);
      return Ok(user);
    }

    [HttpGet]
    [Route("search")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICUserSearchDTO>(query);

      if (dto.Filters.ICContainerGroupId != User.ICContainerGroupId() && ! User.IsAdmin())
      {
        return Forbid();
      }

      var res = await _serviceInterface.Search(dto, cancellationToken);
      return Ok(res);
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(Guid id, CancellationToken cancellationToken)
    {
      var res = await _serviceInterface.GetUser(id);
      return Ok(res);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      var res = await _serviceInterface.DeleteUser(id);
      return Ok(res);
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<ICUserDTO> dto)
    {
      var response = await _serviceInterface.PatchUser(id, dto);
      return Ok(response);
    }

    [HttpPost("{id}/reset-password")]
    public async Task<IActionResult> ResetPassword(Guid id)
    {
      bool result = await _serviceInterface.ResetUserPassword(id);
      if (result)
      {
        return Ok(new { message = "Password reset successfully. A temporary password has been generated." });
      }

      return NotFound("User not found or unable to reset password");
    }

    [HttpPost("{id}/toggle-status")]
    public async Task<ActionResult<ICUserDTO>> ToggleUserStatus(Guid id)
    {
      var user = await _serviceInterface.ToggleUserStatus(id);

      if (user == null)
      {
        return NotFound();
      }

      return Ok(user);
    }

    [HttpPut("{id}/roles")]
    public async Task<ActionResult<ICUserDTO>> UpdateUserWithRoles(Guid id, [FromBody] ICUserDTO updateDto)
    {
      if (id != updateDto.Id)
        return BadRequest("ID mismatch");

      var updatedUser = await _serviceInterface.UpdateUserWithRoles(id, updateDto);

      if (updatedUser == null)
        return NotFound();

      return Ok(updatedUser);
    }
  }
}