﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data;

namespace Trading.API.Tests
{
  static internal class InMemoryDataContext
  {
    public static TradingContext GetContext(string dbName = null)
    {
      if (string.IsNullOrEmpty(dbName))
      {
        dbName = Guid.NewGuid().ToString();
      }
      
      var serviceProvider = new ServiceCollection()
        .AddEntityFrameworkInMemoryDatabase()
        .BuildServiceProvider();

      var options = new DbContextOptionsBuilder<TradingContext>()
        .UseInMemoryDatabase(dbName)
        .UseInternalServiceProvider(serviceProvider)
        .ConfigureWarnings(w => w.Ignore(InMemoryEventId.TransactionIgnoredWarning))
        .Options;

      return new TradingContext(options);
    }
  }
}
