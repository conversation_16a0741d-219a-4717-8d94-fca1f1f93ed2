﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Threading;
using Trading.API.Data.DTO;
using Trading.Services.Classes;
using Trading.Services.Interfaces;
using System;
using Trading.API.Data.DTO.Search;
using Trading.API.Data.DTO.VOIP;
using Newtonsoft.Json;

namespace Trading.API.Remarq.Controllers
{
  [Route("api/[controller]")]
  [ApiController]
  //[Authorize]
  public class VOIPController : ControllerBase
  {
    private readonly IVOIPService _voipService;

    public VOIPController(IVOIPService voipService)
    {
      _voipService = voipService;
    }

    [HttpGet]
    [Route("download")]
    [AllowAnonymous]
    public async Task<IActionResult> Download([FromQuery] string date, CancellationToken ct)
    {
      await _voipService.DownloadAndStoreProviderCallRecords(date, ct);
      return Ok();
    }

    [HttpGet]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var searchDTO = JsonConvert.DeserializeObject<SearchCallRecordsDTO>(query);
      var res = await _voipService.Search(searchDTO, cancellationToken);

      return Ok(res);
    }

    [HttpGet("stats")]
    public async Task<IActionResult> GetStats(CancellationToken cancellationToken)
    {
      var res = await _voipService.GetCallRecordStats(cancellationToken);
      return Ok(res);
    }

  }
}
