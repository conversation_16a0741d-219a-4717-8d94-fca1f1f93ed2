using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.Services.Interfaces;
using Trading.Services.Extensions;
using System.Threading;
using Trading.API.Helpers;
using Newtonsoft.Json;
using Trading.Services.ExternalDTO;
using Microsoft.AspNetCore.Http;
using System.IO;

namespace Trading.API.Controllers
{
  // TODO: refactor this class as it's using scaffolded structures as well as service-based

  [Route("api/vehicleMedia")]
  [ApiController]
  public class VehicleMediaController : ControllerBase
  {
    private readonly TradingContext _context;
    private readonly IVehicleMediaService _vehicleMediaService;

    public VehicleMediaController(TradingContext context, IVehicleMediaService vehicleMediaService)
    {
      _context = context;
      _vehicleMediaService = vehicleMediaService;
    }

    // GET: api/VehicleMedia
    [HttpGet]
    public async Task<ActionResult<IEnumerable<VehicleMedia>>> GetVehicleMedia()
    {
      return await _context.VehicleMedia.ToListAsync();
    }

    [HttpGet]
    [Route("/api/vehicle/{vehicleId}/makeImagesLocal")]
    public async Task<ActionResult<VehicleListDTO>> MakeImagesLocal(Guid vehicleId)
    {
      try
      {
        await _vehicleMediaService.MakeImagesLocal(vehicleId);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpGet("{vehicleId}/category/{mediaCategory}")]
    public async Task<ActionResult<VehicleMedia>> GetAllVehicleMedia(Guid vehicleId, MediaCategoryEnum mediaCategory, CancellationToken cancellationToken)
    {
      try
      {
        var media = await _vehicleMediaService.GetVehicleMedia(vehicleId, mediaCategory, cancellationToken);
        return Ok(media);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet("{vehicleId}")]
    public async Task<ActionResult<VehicleMedia>> GetAllVehicleImages(Guid vehicleId, CancellationToken cancellationToken)
    {
      try
      {
        var media = await _vehicleMediaService.GetVehicleMedia(vehicleId, null, cancellationToken);
        return Ok(media);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    // PUT: api/VehicleMedia/5
    // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
    [HttpPut("{id}")]
    public async Task<IActionResult> PutVehicleMedia(Guid id, VehicleMedia vehicleMedia)
    {
      if (id != vehicleMedia.Id)
      {
        return BadRequest();
      }

      _context.Entry(vehicleMedia).State = EntityState.Modified;

      try
      {
        await _context.SaveChangesAsync();
      }
      catch (DbUpdateConcurrencyException)
      {
        if (! await _vehicleMediaService.VehicleMediaExists(id))
        {
          return NotFound();
        }
        else
        {
          throw;
        }
      }

      return NoContent();
    }

    /*
    [HttpPost]
    public async Task<ActionResult<VehicleMedia>> PostVehicleMedia(VehicleMedia vehicleMedia)
    {

      _context.VehicleMedia.Add(vehicleMedia);
      await _context.SaveChangesAsync();

      return CreatedAtAction("GetVehicleMedia", new { id = vehicleMedia.Id }, vehicleMedia);
    }
    */

    [HttpPut("{vehicleId}/updateSequences")]
    public async Task<IActionResult> UpdateSequences(Guid vehicleId, [FromBody] IEnumerable<VehicleMediaDTO> dto)
    {
      var ok = await _vehicleMediaService.UpdateSequences(vehicleId, dto);

      return Ok();
    }

    [HttpPost]
    [Route("")]
    [RequestFormLimits(ValueLengthLimit = int.MaxValue, MultipartBodyLengthLimit = int.MaxValue)]
    [DisableRequestSizeLimit]
    public async Task<IActionResult> UploadVehicleMedia(IFormCollection data, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<VehicleMediaUploadDTO>(data["vehicleMediaUploadDTO"]);
      var customerId = User.CustomerId().Value;

      try
      {
        var medias = await _vehicleMediaService.UploadVehicleMedia(customerId, dto, data.Files, cancellationToken);
        return Ok(medias);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("new")]
    [RequestFormLimits(ValueLengthLimit = int.MaxValue, MultipartBodyLengthLimit = int.MaxValue)]
    [DisableRequestSizeLimit]
    public async Task<IActionResult> UploadVehicleMediaNew([FromBody] VehicleMediaUploadContainerDTO data, CancellationToken cancellationToken)
    {
      var dto = data.VehicleMediaUpload;
      var customerId = User.CustomerId().Value;

      try
      {
        var medias = await _vehicleMediaService.UploadVehicleMedia(customerId, dto, FormFileHelper.DataURLsToFormFiles(data.Files), cancellationToken);
        return Ok(medias);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    // DELETE: api/VehicleMedia/5
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteVehicleMedia(Guid id, CancellationToken cancellationToken)
    {
      var vm = await _context.VehicleMedia
        .Where(x => x.Id == id)
        .Include(x => x.Vehicle)
        .FirstOrDefaultAsync(cancellationToken);

      if (vm.Vehicle.CustomerId != this.User.CustomerId() && !User.IsAdmin())
      {
        return Unauthorized();
      }

      try
      {
        await _vehicleMediaService.SetVehicleMediaStatus(id, StatusEnum.Deleted, cancellationToken);
      }
      catch (Exception ex)
      {
        return ex.ParseError();
      }

      return Ok();
    }



    [HttpGet("/api/listing/{advertId}/vehicle-media/category/{mediaCategory}")]
    public async Task<ActionResult<VehicleMedia>> GetAllVehicleMediaByAdvert(Guid advertId, MediaCategoryEnum mediaCategory, CancellationToken cancellationToken)
    {
      var media = await _vehicleMediaService.GetVehicleMediaByAdvert(advertId, mediaCategory, cancellationToken);
      return Ok(media);
    }


  }
}
