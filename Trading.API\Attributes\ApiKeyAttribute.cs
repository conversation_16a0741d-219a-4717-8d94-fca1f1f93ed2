﻿//using Microsoft.AspNetCore.Mvc;
//using Microsoft.AspNetCore.Mvc.Filters;
//using System;
//using System.Threading.Tasks;
//using Microsoft.Extensions.Configuration;
//using Microsoft.Extensions.DependencyInjection;

//namespace Trading.API.Attributes
//{
//  [AttributeUsage(validOn: AttributeTargets.Class | AttributeTargets.Method)]
//  public class ApiKeyAttribute : Attribute, IAsyncActionFilter
//  {
//    private const string APIKEYNAME = "X-Api-Key";
//    public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
//    {
//      if (!context.HttpContext.Request.Headers.TryGetValue(APIKEYNAME, out var extractedApiKey))
//      {
//        context.Result = new ContentResult()
//        {
//          StatusCode = 401,
//          Content = "Api Key was not provided"
//        };
//        return;
//      }

//      var appSettings = context.HttpContext.RequestServices.GetRequiredService<IConfiguration>();

//      var apiKey = appSettings.GetValue<string>(APIKEYNAME);

//      if (string.IsNullOrEmpty(apiKey) || !apiKey.Equals(extractedApiKey))
//      {
//        context.Result = new ContentResult()
//        {
//          StatusCode = 401,
//          Content = "Api Key is not valid"
//        };
//        return;
//      }

//      await next();
//    }
//  }
//}
