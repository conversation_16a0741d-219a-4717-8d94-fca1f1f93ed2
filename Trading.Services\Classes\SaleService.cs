﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using Dapper;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.Services.Interfaces;
using Microsoft.AspNetCore.JsonPatch;
using Trading.API.Data.Enums;
using Trading.Services.Extensions;
using Trading.Services.ExternalDTO;
using System.Data;
using Microsoft.Extensions.Caching.Memory;

namespace Trading.Services.Classes;

public class SaleService : ISaleService
{
  private readonly TradingContext _tradingContext;
  private readonly IMapper _mapper;
  private readonly IMemoryCache _memCache;
  private readonly IAdvertService _advertService;
  private readonly IBidService _bidService;
  private readonly IMessageService _messageService;
  private readonly IDbConnection _db;

  private const string STATIC_SALES_CACHE_KEY = "static_sales_data";


  public SaleService(TradingContext tradingContext, IMapper mapper,
    IAdvertService advertService,
    IBidService bidService,
    IDbConnection db,
    IMessageService messageService,
    IMemoryCache memCache
  )
  {
    _tradingContext = tradingContext;
    _mapper = mapper;
    _advertService = advertService;
    _bidService = bidService;
    _messageService = messageService;
    _db = db;
    _memCache = memCache;
  }

  /** Static Sales Data **/

  // Replace the existing GetStaticSales method and add these new methods to your SaleService

  public async Task<IEnumerable<SaleDTO>> GetStaticSales(CancellationToken cancellationToken = default)
  {
    var cached = _memCache.TryGetValue(STATIC_SALES_CACHE_KEY, out IEnumerable<SaleDTO> salesDTO);

    if (!cached)
    {
      var saleTypes = new List<uint>
        {
            (uint)SaleTypeEnum.BuyNow,
            (uint)SaleTypeEnum.TimedAuction
        };

      var sales = await _tradingContext.Sales
          .Where(x => saleTypes.Contains(x.SaleTypeId))
          .AsNoTracking()
          .ToListAsync(cancellationToken);

      salesDTO = _mapper.Map<IEnumerable<Sale>, IEnumerable<SaleDTO>>(sales);

      // Cache for a long time since this is static data
      var cacheOptions = new MemoryCacheEntryOptions()
          .SetAbsoluteExpiration(TimeSpan.FromHours(24))
          .SetPriority(CacheItemPriority.High);

      _memCache.Set(STATIC_SALES_CACHE_KEY, salesDTO, cacheOptions);
    }

    return salesDTO;
  }

  // Get the BuyNow sale ID directly
  public async Task<Guid?> GetBuyNowSaleId(CancellationToken cancellationToken = default)
  {
    var staticSales = await GetStaticSales(cancellationToken);
    var buyNowSale = staticSales.FirstOrDefault(x => x.SaleTypeId == (uint)SaleTypeEnum.BuyNow);

    if (buyNowSale == null)
      throw new ApplicationException("BuyNow static sale not found");

    return buyNowSale.Id;
  }

  // Get the TimedAuction sale ID directly  
  public async Task<Guid?> GetTimedAuctionSaleId(CancellationToken cancellationToken = default)
  {
    var staticSales = await GetStaticSales(cancellationToken);
    var timedAuctionSale = staticSales.FirstOrDefault(x => x.SaleTypeId == (uint)SaleTypeEnum.TimedAuction);

    if (timedAuctionSale == null)
      throw new ApplicationException("TimedAuction static sale not found");

    return timedAuctionSale.Id;
  }

  // Generic method to get sale ID by type
  public async Task<Guid?> GetStaticSaleIdByType(SaleTypeEnum saleType, CancellationToken cancellationToken = default)
  {
    var staticSales = await GetStaticSales(cancellationToken);
    var sale = staticSales.FirstOrDefault(x => x.SaleTypeId == (uint)saleType);

    if (sale == null)
      throw new ApplicationException($"Static sale of type {saleType} not found");

    return sale.Id;
  }

  // Method to get a specific static sale DTO
  public async Task<SaleDTO> GetStaticSale(SaleTypeEnum saleType, CancellationToken cancellationToken = default)
  {
    var staticSales = await GetStaticSales(cancellationToken);
    var sale = staticSales.FirstOrDefault(x => x.SaleTypeId == (uint)saleType);

    if (sale == null)
      throw new ApplicationException($"Static sale of type {saleType} not found");

    return sale;
  }

  public void InvalidateStaticSalesCache()
  {
    _memCache.Remove(STATIC_SALES_CACHE_KEY);
  }

  /** End Static Sales Data **/

  public async Task<int> GetSaleLotCount(Guid saleId, CancellationToken cancellationToken)
  {
    var x = await _tradingContext.Adverts
      .Where(x => x.SaleId == saleId &&
                  (x.StatusId == (int)StatusEnum.Active || x.StatusId == (int)StatusEnum.Pending))
      .CountAsync(cancellationToken);

    return x;
  }

  public async Task<uint> SetSaleLotCount(Guid saleId, CancellationToken cancellationToken)
  {
    var x = await _tradingContext.Adverts
      .Where(x => x.SaleId == saleId &&
                  (x.StatusId == (int)StatusEnum.Active || x.StatusId == (int)StatusEnum.Pending))
      .CountAsync(cancellationToken);

    var salePatch = new JsonPatchDocument<Sale>();
    salePatch.Add(x => x.AdvertCount, (uint)x);

    await Patch(saleId, salePatch, cancellationToken);

    return (uint)x;
  }

  public async Task<bool> SetSaleLots(IEnumerable<SetSaleLotDTO> dto, CancellationToken cancellationToken)
  {
    foreach (var lot in dto)
    {
      var patch = new JsonPatchDocument<Advert>();

      if (lot.Unlot)
      {
        patch.Add(x => x.SaleId, null);
        patch.Add(x => x.LotNo, null);
      }
      else
      {
        patch.Add(x => x.SaleId, lot.SaleId);
      }

      await _advertService.Patch(lot.AdvertId, lot.CustomerId, patch, cancellationToken);
    }

    var saleIds = dto.Select(x => x.SaleId).Distinct();

    foreach (var saleId in saleIds)
    {
      var x = await SetSaleLotCount(saleId.Value, cancellationToken);
    }

    return true;
  }

  public async Task<IEnumerable<SaleDTO>> GetSalesByVehicleVRM(string vrm, CancellationToken cancellationToken)
  {
    var sales = await _tradingContext.Sales.Include(x => x.Adverts)
      .Where(x => x.Adverts.Any(y => y.Vehicle.Vrm == vrm))
      .AsNoTracking()
      .ToListAsync(cancellationToken);

    var salesDTOs = _mapper.Map<IEnumerable<Sale>, IEnumerable<SaleDTO>>(sales);
    return salesDTOs;
  }

  public async Task<SaleDTO> Create(SaleDTO dto, CancellationToken cancellationToken)
  {
    var sale = _mapper.Map<SaleDTO, Sale>(dto);

    _tradingContext.Sales.Add(sale);

    await _tradingContext.SaveChangesAsync(cancellationToken);

    var saleDTO = _mapper.Map<Sale, SaleDTO>(sale);
    return saleDTO;
  }

  public async Task<SaleDTO> Patch(Guid saleId, JsonPatchDocument<Sale> patch, CancellationToken cancellationToken)
  {
    var sale = await _tradingContext.Sales.Where(x => x.Id == saleId).FirstOrDefaultAsync(cancellationToken);

    if (sale != null)
    {
      patch.FilterPatch();
      patch.ApplyTo(sale);
      await _tradingContext.SaveChangesAsync(cancellationToken);
    }

    var saleDTO = _mapper.Map<Sale, SaleDTO>(sale);

    return saleDTO;
  }

  public async Task<Sale> Get(Guid saleId, BaseSearchDTO dto, CancellationToken ct)
  {
    return await _tradingContext.Sales.Where(x => x.Id == saleId).FirstOrDefaultAsync(ct);
  }

  public async Task<SaleSearchResultDTO> Search(SaleSearchDTO saleSearch, CancellationToken cancellationToken)
  {
    switch (saleSearch.Component)
    {
      case "AdminSaleSearch":

        var response = await AdminSaleSearch(saleSearch, cancellationToken);
        return response;

      case "AdminSaleDetails":

        var rt = await AdminSaleDetails(saleSearch, cancellationToken);
        return rt;

      case "AdminViewSaleLots":

        var rt1 = await AdminViewSaleLots(saleSearch, cancellationToken);
        return rt1;

      case "AdminSaleEdit":

        var rt2 = await AdminSaleEdit(saleSearch, cancellationToken);
        return rt2;

      case "AdminSaleAuctioneer":

        var rt3 = await AdminSaleAuctioneer(saleSearch, cancellationToken);
        return rt3;

      case "SaleView":

        var rt4 = await SaleView(saleSearch, cancellationToken);
        return rt4;

      case "Dashboard":

        var rt5 = await DashboardSales(saleSearch, cancellationToken);
        return rt5;

      case "HelloDatabase":

        var rt6 = await HelloDatabase(saleSearch, cancellationToken);
        return rt6;

      default:
        var defaultResponse = await DefaultSaleSearch(saleSearch, cancellationToken);
        return defaultResponse;
    }
  }

  public async Task<SaleSearchResultDTO> HelloDatabase(SaleSearchDTO dto, CancellationToken ct)
  {
    var sale = await _tradingContext.Sales.Where(x => x.StatusId == (int)StatusEnum.Active).AsNoTracking().FirstOrDefaultAsync(ct);

    if (sale != null)
    {
      return new SaleSearchResultDTO() { Sale = _mapper.Map<Sale, SaleDTO>(sale) };
    }

    return null;
  }

  public async Task<SaleSearchResultDTO> DefaultSaleSearch(SaleSearchDTO dto, CancellationToken ct)
  {
    var query = _tradingContext.Sales.AsQueryable();

    query = SaleQueryFilters(query, dto);

    var sales = await query.AsNoTracking().ToListAsync(ct);
    var salesDTO_Public = _mapper.Map<IEnumerable<Sale>, IEnumerable<SaleDTO_Public>>(sales);
    var salesDTOs = _mapper.Map<IEnumerable<SaleDTO_Public>, IEnumerable<SaleDTO>>(salesDTO_Public);

    return new SaleSearchResultDTO() { Sales = salesDTOs };
  }

  private async Task<SaleSearchResultDTO> SaleView(SaleSearchDTO dto, CancellationToken ct)
  {
    var sale = await _tradingContext.Sales.Where(x => x.Id == dto.Filters.Id).AsNoTracking().FirstOrDefaultAsync();

    var saleDTO = _mapper.Map<SaleDTO_Public, SaleDTO>(_mapper.Map<Sale, SaleDTO_Public>(sale));

    return new SaleSearchResultDTO() { Sale = saleDTO };
  }

  private async Task<SaleSearchResultDTO> DashboardSales(SaleSearchDTO dto, CancellationToken ct)
  {
    var cacheKey = dto.Component;

    var ok = _memCache.TryGetValue(cacheKey, out IEnumerable<SaleDTO> salesDTO);

    if (!ok || dto.InvalidateCache == true)
    {
      var sales = await _tradingContext.Sales
        .Include(x => x.Address)
        .Where(x => x.StatusId == (int)StatusEnum.Active && x.SaleTypeId == (int)SaleTypeEnum.ManagedSale)
        .Where(x => x.SaleEnd >= DateTime.Now)
        .AsNoTracking()
        .ToListAsync(ct);

      salesDTO =
        _mapper.Map<IEnumerable<SaleDTO_Public>, IEnumerable<SaleDTO>>
          (_mapper.Map<IEnumerable<Sale>, IEnumerable<SaleDTO_Public>>(sales));

      var cacheOptions = new MemoryCacheEntryOptions()
        .SetAbsoluteExpiration(TimeSpan.FromMinutes(1));

      _memCache.Set(cacheKey, salesDTO, cacheOptions);
    }

    return new SaleSearchResultDTO() { Sales = salesDTO };
  }

  public async Task<SaleSearchResultDTO> AdminSaleSearch(SaleSearchDTO dto, CancellationToken ct)
  {
    var query = _tradingContext.Sales.AsQueryable();

    query = query.Include(x => x.Address);
    query = query.Include(x => x.SaleType);
    query = SaleQueryFilters(query, dto);

    if (dto.Offset != null)
    {
      query = query.Skip(dto.Offset.Value).Take(dto.Limit.Value);
    }

    var sales = await query.AsNoTracking().ToListAsync(ct);
    var salesDTOs = _mapper.Map<IEnumerable<Sale>, IEnumerable<SaleDTO>>(sales);

    salesDTOs = salesDTOs.OrderByDescending(x => x.SaleStart).ToList();

    return new SaleSearchResultDTO() { Sales = salesDTOs };
  }


  public async Task<SaleSearchResultDTO> AdminSaleDetails(SaleSearchDTO dto, CancellationToken ct)
  {
    // this can only be called with an id
    if (dto.Filters == null || !dto.Filters.Id.HasValue)
    {
      throw new ApplicationException("Cannot get sale details with no ID specified");
    }

    var query = _tradingContext.Sales.AsQueryable();

    query = query.Include(x => x.Address);
    query = query.Include(x => x.SaleType);
    query = query.Include(x => x.Adverts).ThenInclude(x => x.Vehicle.Make)
        .Include(x => x.Adverts).ThenInclude(x => x.Vehicle.Model)
        .Include(x => x.Adverts).ThenInclude(x => x.Vehicle.LatestValuation)
        .Include(x => x.Adverts).ThenInclude(x => x.Vehicle.Appraisals)
        .Include(x => x.Adverts).ThenInclude(x => x.Negotiations).ThenInclude(x => x.NegotiationNotes)
      ;
    query = SaleQueryFilters(query, dto);

    var sale = await query.AsNoTracking().FirstOrDefaultAsync(ct);
    if (sale != null)
    {
      var saleDTO = _mapper.Map<Sale, SaleDTO>(sale);
      return new SaleSearchResultDTO() { Sale = saleDTO };
    }

    return null;
  }

  public async Task<SaleSearchResultDTO> AdminSaleAuctioneer(SaleSearchDTO dto, CancellationToken ct)
  {
    var query = _tradingContext.Sales.AsQueryable();

    query = query.Include(x => x.Address);
    query = query.Include(x => x.SaleType);
    query = SaleQueryFilters(query, dto);

    query = query.Where(x => x.StatusId == (int)StatusEnum.Active);

    if (dto.Offset != null)
    {
      query = query.Skip(dto.Offset.Value).Take(dto.Limit.Value);
    }

    var sales = await query.AsNoTracking().FirstOrDefaultAsync(ct);
    var salesDTO = _mapper.Map<Sale, SaleDTO>(sales);

    return new SaleSearchResultDTO() { Sale = salesDTO };
  }


  public async Task<SaleSearchResultDTO> AdminViewSaleLots(SaleSearchDTO dto, CancellationToken ct)
  {
    var query = _tradingContext.Sales.AsQueryable();

    query = query.Include(x => x.Address);
    query = query.Include(x => x.SaleType);
    query = query.Include(x => x.SaleProfile).ThenInclude(x => x.SaleSearchProfiles).ThenInclude(x => x.SavedSearch).ThenInclude(x => x.Search);
    query = SaleQueryFilters(query, dto);

    var sale = await query.AsNoTracking().FirstOrDefaultAsync(ct);
    var salesDTO = _mapper.Map<Sale, SaleDTO>(sale);

    return new SaleSearchResultDTO() { Sale = salesDTO };
  }

  public async Task<SaleSearchResultDTO> AdminSaleEdit(SaleSearchDTO dto, CancellationToken ct)
  {
    var query = _tradingContext.Sales.AsQueryable();

    query = query.Include(x => x.Address);
    query = query.Include(x => x.SaleType);
    query = SaleQueryFilters(query, dto);

    var sale = await query.AsNoTracking().FirstOrDefaultAsync(ct);
    var salesDTO = _mapper.Map<Sale, SaleDTO>(sale);

    return new SaleSearchResultDTO() { Sale = salesDTO };
  }


  public static IQueryable<Sale> SaleQueryFilters(IQueryable<Sale> query, SaleSearchDTO saleSearch)
  {
    if (saleSearch.Filters.SaleTypeId != null && saleSearch.Filters.SaleTypeId > 0)
    {
      query = query.Where(x => x.SaleTypeId == saleSearch.Filters.SaleTypeId);
    }

    if (saleSearch.Filters.IncludeSaleTypeIds != null && saleSearch.Filters.IncludeSaleTypeIds.Count > 0)
    {
      query = query.Where(x => saleSearch.Filters.IncludeSaleTypeIds.Contains(x.SaleTypeId));
    }

    if (saleSearch.Filters.Id != Guid.Empty && saleSearch.Filters.Id != null)
    {
      query = query.Where(x => x.Id == saleSearch.Filters.Id);
    }

    if (saleSearch.Filters.StatusId > 0)
    {
      query = query.Where(x => x.StatusId == saleSearch.Filters.StatusId);
    }

    if (saleSearch.Filters.IncludeStatusIds != null && saleSearch.Filters.IncludeStatusIds.Count > 0)
    {
      query = query.Where(x => saleSearch.Filters.IncludeStatusIds.Contains(x.StatusId));
    }

    if (saleSearch.Filters.SaleDateFrom != null)
    {
      query = query.Where(x => x.SaleStart >= saleSearch.Filters.SaleDateFrom);
    }

    if (saleSearch.Filters.SaleDateTo != null)
    {
      query = query.Where(x => x.SaleStart >= saleSearch.Filters.SaleDateTo);
    }

    if (saleSearch.Filters.SaleTypeId != null)
    {
      query = query.Where(x => x.SaleTypeId == saleSearch.Filters.SaleTypeId);
    }

    if (saleSearch.Filters.SaleName != null)
    {
      query = query.Where(x => x.SaleName.Contains(saleSearch.Filters.SaleName));
    }

    if (saleSearch.Filters.AddressId != null)
    {
      query = query.Where(x => x.AddressId == saleSearch.Filters.AddressId);
    }

    return query;
  }

  public static Sale GetFullSale(Sale sale)
  {
    // just return the object as-is
    return sale;
  }

  public static Sale GetBasicSale(Sale sale)
  {
    // create a new instance of the sale with non-relevant properties omitted
    return new Sale()
    {
      Id = sale.Id,
      Added = sale.Added,
      SaleStart = sale.SaleStart,
      Status = sale.Status,
      StatusId = sale.StatusId,
      Address = sale.Address,
      AddressId = sale.AddressId,
      SaleName = sale.SaleName
    };
  }

  public async Task<bool> SaleStart(Guid saleId, CancellationToken cancellationToken)
  {
    // Get Sale
    var sale = await Get(saleId, new BaseSearchDTO(), cancellationToken);

    if (sale == null)
    {
      throw new Exception("Sale not found");
    }
    else if (sale.LiveBiddingStatus != LiveBiddingStatusEnum.Paused)
    {
      throw new Exception("Live Bidding Status not paused");
    }
    else if (sale.StatusId != (int)StatusEnum.Active)
    {
      throw new Exception("Sale Status not Active");
    }


    if (sale.CurrentAdvertId == null)
    {
      // Find the first lot in the sale that hasn't been processed
      var ads = await _advertService.LiveSaleUpcomingLots(sale.Id, 1, cancellationToken);

      if (ads.Count() > 0)
      {
        var nextAdvert = ads.First();
        sale.CurrentAdvertId = nextAdvert.Id;
      }
    }

    sale.LiveBiddingStatus = LiveBiddingStatusEnum.Live;

    await _tradingContext.SaveChangesAsync(cancellationToken);

    await BroadcastSaleUpdate(saleId, cancellationToken);

    if (sale.CurrentAdvertId != null)
    {
      await _advertService.BroadcastBidderCurrentLot(sale.CurrentAdvertId.Value, cancellationToken);
    }

    return true;
  }

  public async Task<bool> BroadcastSaleUpdate(Guid saleId, CancellationToken cancellationToken)
  {
    // Fetch Sale Info

    var sale = await _tradingContext.Sales
      .Where(x => x.Id == saleId)
      .AsNoTracking()
      .FirstOrDefaultAsync(cancellationToken);

    var x = _mapper.Map<Sale, BroadcastSaleUpdateDTO>(sale);

    await _messageService.SendSaleMessage(saleId, MessageAreaEnum.Sales, MessageTypeEnum.SaleUpdate, x);

    return true;
  }

  public async Task<bool> SalePause(Guid saleId, CancellationToken cancellationToken)
  {
    var patch = new JsonPatchDocument<Sale>();
    patch.Add(x => x.LiveBiddingStatus, LiveBiddingStatusEnum.Paused);
    await Patch(saleId, patch, cancellationToken);

    await BroadcastSaleUpdate(saleId, cancellationToken);

    return true;
  }

  public async Task<bool> SaleEnd(Guid saleId, CancellationToken cancellationToken)
  {
    var patch = new JsonPatchDocument<Sale>();
    patch.Add(x => x.LiveBiddingStatus, LiveBiddingStatusEnum.Ended);
    await Patch(saleId, patch, cancellationToken);

    await BroadcastSaleUpdate(saleId, cancellationToken);

    return true;
  }

  public async Task<bool> ResetLotEndDates(Guid saleId, CancellationToken cancellationToken)
  {
    // For unsold vehicles reset the end dates to the end of the sale

    var sale = await _tradingContext.Sales
      .Include(x => x.Adverts.Where(x => x.AdvertStatus == AdvertStatusEnum.Active && x.SoldStatus == SoldStatusEnum.Active))
      .Where(x => x.Id == saleId)
      .Where(x => x.StatusId == (int)StatusEnum.Active || x.StatusId == (int)StatusEnum.Pending)
      .FirstOrDefaultAsync(cancellationToken);

    foreach (var advert in sale.Adverts)
    {
      advert.EndDateTime = sale.SaleEnd;
    }

    await _tradingContext.SaveChangesAsync(cancellationToken);

    return true;
  }


  public async Task<bool> GoToNextLot(Guid saleId, Guid currentAdvertId, bool skip,
    CancellationToken cancellationToken)
  {
    // If skip -- we're moving current lot to the end
    // Consider putting this in its own method
    if (skip)
    {
      var maxLotSeq = _tradingContext.Adverts
        .Where(x => x.SaleId == saleId && x.StatusId == (int)StatusEnum.Active)
        .Max(x => x.LotSeq);

      var sql = "UPDATE Advert SET LotSeq = @newLotSeq WHERE Advert.Id = @advertId";
      var updateParams = new { newLotSeq = maxLotSeq + 1, advertId = currentAdvertId };

      try
      {
        await _db.ExecuteAsync(sql, updateParams);
      }
      catch (Exception ex)
      {
      }
    }

    var nextLotId = await _advertService.GetNextLotId(saleId, currentAdvertId, cancellationToken);

    if (nextLotId != null)
    {
      try
      {
        await SetCurrentLot(saleId, nextLotId.Value, cancellationToken);
      }
      catch (Exception ex)
      {
      }
    }

    return true;
  }

  public async Task<uint?> CurrentLotSeq(Guid saleId, CancellationToken cancellationToken)
  {
    var lotSeq = await _tradingContext.Sales
      .Include(x => x.CurrentAdvert)
      .AsNoTracking()
      .Where(x => x.Id == saleId && x.CurrentAdvert != null)
      .Select(x => x.CurrentAdvert.LotSeq)
      .FirstOrDefaultAsync(cancellationToken);

    return lotSeq;
  }

  public async Task<bool> SetCurrentLot(Guid saleId, Guid advertId, CancellationToken cancellationToken)
  {
    // Validation here

    // Update the Sale Record
    var patch = new JsonPatchDocument<Sale>();
    patch.Add(x => x.CurrentAdvertId, advertId);
    await Patch(saleId, patch, cancellationToken);

    var lotSeq = _tradingContext.Adverts.Where(x => x.Id == advertId).AsNoTracking().Select(x => x.LotSeq)
      .FirstOrDefault();

    // Broadcast updates

    await BroadcastSaleUpdate(saleId, cancellationToken);
    await _advertService.BroadcastBidderCurrentLot(advertId, cancellationToken);
    await _advertService.BroadcastAuctioneerUpcomingLots(saleId, 999, cancellationToken);
    await _advertService.BroadcastAuctioneerPreviousLots(saleId, lotSeq.Value, 999, cancellationToken);

    return true;
  }

  public async Task<IEnumerable<SaleAttendeeDTO>> GetSaleAttendees(Guid saleId, CancellationToken cancellationToken)
  {
    var attendees = await GetAttendeeQuery()
      .Where(x => x.SaleId == saleId).ToListAsync(cancellationToken);

    var dtos = _mapper.Map<IEnumerable<SaleAttendee>, IEnumerable<SaleAttendeeDTO>>(attendees);

    return dtos;
  }

  private IQueryable<SaleAttendee> GetAttendeeQuery()
  {
    return _tradingContext.SaleAttendees
      .Include(x => x.Contact.PrimaryAddress)
      .Include(x => x.Contact.Customer)
      .AsNoTracking().AsQueryable();
  }

  public async Task<SaleAttendeeDTO> AddSaleAttendee(SaleAttendeeDTO dto, CancellationToken cancellationToken)
  {
    // ensure contact exists and is active
    var contact = await _tradingContext.Contacts
      .AsNoTracking()
      .FirstOrDefaultAsync(x => x.Id == dto.ContactId, cancellationToken);
    if (contact == null || contact.StatusId != (uint)StatusEnum.Active)
    {
      throw new ApplicationException("Contact either doesn't exist or is not active");
    }

    // ensure sale exists and is not ended 
    var sale = await _tradingContext.Sales
      .AsNoTracking()
      .FirstOrDefaultAsync(x => x.Id == dto.SaleId, cancellationToken);
    if (sale == null || sale.StatusId != (uint)StatusEnum.Active && sale.StatusId != (uint)StatusEnum.Pending)
    {
      throw new ApplicationException("Sale either doesn't exist or is not active/pending");
    }

    var entity = await GetAttendeeQuery()
      .FirstOrDefaultAsync(x => x.SaleId == dto.SaleId && x.ContactId == dto.ContactId, cancellationToken);

    if (entity == null)
    {
      _tradingContext.SaleAttendees.Add(new SaleAttendee
      {
        SaleId = dto.SaleId,
        ContactId = dto.ContactId,
        PaddleNumber = dto.PaddleNumber,
        StatusId = (uint)StatusEnum.Active,
        Added = DateTime.Now
      });

      await _tradingContext.SaveChangesAsync(cancellationToken);

      entity = await GetAttendeeQuery()
        .FirstOrDefaultAsync(x => x.SaleId == dto.SaleId && x.ContactId == dto.ContactId, cancellationToken);
    }

    return _mapper.Map<SaleAttendee, SaleAttendeeDTO>(entity);
  }

  public async Task RemoveAttendee(uint attendeeId, CancellationToken cancellationToken)
  {
    var attendee =
      await _tradingContext.SaleAttendees.FirstOrDefaultAsync(x => x.Id == attendeeId, cancellationToken);
    if (attendee != null)
    {
      _tradingContext.SaleAttendees.Remove(attendee);

      await _tradingContext.SaveChangesAsync(cancellationToken);
    }
  }

  public async Task<SaleSearchResultDTO> SalesLive(bool forceUpdate, CancellationToken cancellationToken)
  {
    return await Search(new SaleSearchDTO() { Component = "Dashboard", InvalidateCache = forceUpdate },
      cancellationToken);
  }
}