﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Trading.API.Data.Migrations
{
  /// <inheritdoc />
  public partial class OutcomeDisposalCode : Migration
  {
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
      migrationBuilder.AddColumn<string>(
          name: "DisposalCode",
          table: "ICVehicleOutcome",
          type: "varchar(12)",
          maxLength: 12,
          nullable: true)
          .Annotation("MySql:CharSet", "utf8mb4");

      migrationBuilder.AddColumn<string>(
          name: "OutcomeResponseRef",
          table: "ICVehicle",
          type: "varchar(64)",
          maxLength: 64,
          nullable: true)
          .Annotation("MySql:CharSet", "utf8mb4");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
      migrationBuilder.DropColumn(
          name: "DisposalCode",
          table: "ICVehicleOutcome");

      migrationBuilder.DropColumn(
          name: "OutcomeResponseRef",
          table: "ICVehicle");
    }
  }
}
