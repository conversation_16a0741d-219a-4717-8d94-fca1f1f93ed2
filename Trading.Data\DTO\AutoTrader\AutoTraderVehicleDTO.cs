﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.AutoTrader;
public class AutoTraderVehicleDTO
{
  public Guid? ICVehicleId { get; set; }

  public string Registration { get; set; }

  public string Vin { get; set; }

  public string Make { get; set; }

  public string Model { get; set; }

  public string Generation { get; set; }

  public string Derivative { get; set; }

  public string DerivativeId { get; set; }

  public string VehicleType { get; set; }

  public string Trim { get; set; }

  public string BodyType { get; set; }

  public string FuelType { get; set; }

  public string? CabType { get; set; }

  public string TransmissionType { get; set; }

  public string WheelbaseType { get; set; }

  public string Drivetrain { get; set; }

  public int Seats { get; set; }
  public int Doors { get; set; }
  public int EngineTorqueNM { get; set; }
  public int Co2EmissionGPKM { get; set; }
  public int TopSpeedMPH { get; set; }
  public double? ZeroToSixtyMPHSeconds { get; set; }
  public double? ZeroToOneHundredKMPHSeconds { get; set; }
  public double BadgeEngineSizeLitres { get; set; }
  public int EngineCapacityCC { get; set; }
  public int EnginePowerBHP { get; set; }
  public double? FuelCapacityLitres { get; set; }
  public string EmissionClass { get; set; }
  public int Owners { get; set; }
  public double? FuelEconomyNEDCExtraUrbanMPG { get; set; }
  public double? FuelEconomyNEDCUrbanMPG { get; set; }
  public double? FuelEconomyNEDCCombinedMPG { get; set; }
  public double? FuelEconomyWLTPLowMPG { get; set; }
  public double? FuelEconomyWLTPMediumMPG { get; set; }
  public double? FuelEconomyWLTPHighMPG { get; set; }
  public double? FuelEconomyWLTPExtraHighMPG { get; set; }
  public double? FuelEconomyWLTPCombinedMPG { get; set; }
  public double? BootSpaceSeatsUpLitres { get; set; }

  public string InsuranceGroup { get; set; }
  public DateTime? FirstRegistrationDate { get; set; }

  public string Colour { get; set; }
  public int LengthMM { get; set; }
  public int HeightMM { get; set; }
  public int WidthMM { get; set; }
  public int MinimumKerbWeightKG { get; set; }
  public int GrossVehicleWeightKG { get; set; }

  public string EngineNumber { get; set; }

  public string FuelDelivery { get; set; }
  public int Gears { get; set; }
  public bool StartStop { get; set; }
  public int EnginePowerPS { get; set; }
  public double EngineTorqueLBFT { get; set; }
  public double? BootSpaceSeatsDownLitres { get; set; }
  public int? BatteryRangeMiles { get; set; }
  public double? BatteryCapacityKWH { get; set; }
  public double? BatteryUsableCapacityKWH { get; set; }
  public int WheelbaseMM { get; set; }
  public int Axles { get; set; }

  public string CountryOfOrigin { get; set; }

  public string DriveType { get; set; }
}
