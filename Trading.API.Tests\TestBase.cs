﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Tests.Common.Helpers;
using Trading.API.Tests.Common;

namespace Trading.API.Tests
{
  public abstract class TestBase
  {
    protected ITestBase _testBase;
    protected TradingContext _context;
    protected IMapper _mapper;
    protected CommonServices _common;
    protected LookupFactory _lookupFactory;

    public TestBase() {
      _testBase = new CommonTestBase();

      _context = _testBase.Context;
      _mapper = _testBase.Mapper;
      _common = _testBase.Common;
      _lookupFactory = _testBase.LookupFactory;
    }
  }
}
