﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.DTO.Documents;
using Trading.API.Data.Models.LeadCRM;

namespace Trading.API.Data.DTO.LeadCRM
{
    public class LeadDocumentDTO: BaseModelEntityDTO
  {
    public Guid? LeadId { get; set; }

    public Lead Lead { get; set; }

    public DocumentDTO Document { get; set; }
    public Guid? DocumentId { get; set; }
    public LeadMediaDTO LeadMedia { get; set; }
    public Guid? LeadMediaId { get; set; }
    public uint LatestVersionNumber { get; set; } = 1;

    public uint LeadDocumentCategoryId { get; set; }
    public LeadDocumentCategoryDTO LeadDocumentCategory { get; set; }
  }

  public class LeadDocumentSearchDTO: BaseSearchDTO 
  {
    public LeadDocumentFilters Filters { get; set; } = new LeadDocumentFilters();
  }

  public class LeadDocumentFilters : BaseFilter
  {
  }
}
