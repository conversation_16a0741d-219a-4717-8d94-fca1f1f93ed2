﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICResponseInputDTO : BaseModelEntityDTO
  {
    public Guid? ICResponseId { get; set; }
    public Guid? ICInputId { get; set; }
    public Guid? ICVehicleId { get; set; }
    public List<ICResponseInputValueDTO> ICResponseInputValues { get; set; }
    public List<ICResponseInputFeatureDTO> ICResponseInputFeatures { get; set; }
    public List<ICResponseInputAssetDTO> ICResponseInputAssets { get; set; }

    public Guid? ICContainerId { get; set; }
  }


  public class ICResponseInputSearchDTO : BaseSearchDTO
  {
    public ICResponseInputSearchFilters Filters { get; set; } = new ICResponseInputSearchFilters();
  }

  public class ICResponseInputSearchFilters : BaseFilter
  {

  }

  public class ICResponseInputCreateDTO
  {
    public Guid? ICResponseId { get; set; }
    public Guid? ICInputId { get; set; }

  }
}
