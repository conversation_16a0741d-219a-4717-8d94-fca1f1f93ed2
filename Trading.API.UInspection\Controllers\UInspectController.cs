﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO.UInspect.StatData;
using Trading.API.Data.DTO.UInspections;
using Trading.Services.Interfaces;
using Trading.Services.UInspections.Interfaces;

namespace Trading.API.UInspection.Controllers
{
  [Route("api/uinspect")]
  [ApiController]
  public class UInspectController : ControllerBase
  {
    private readonly IUInspectService _uInspectService;

    public UInspectController(
      IUInspectService uInspectService
      )
    {
      _uInspectService = uInspectService;
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create(UInspectRequestDTO requestDTO, CancellationToken cancellationToken)
    {
      try
      {
        var result = await _uInspectService.Create(requestDTO, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/uInspects")]
    [ResponseCache(Duration = 5)]
    public async Task<IActionResult> GetInspections([FromQuery] string? query, CancellationToken cancellationToken)
    {
      var search = new UInspectSearchDTO();

      if (!String.IsNullOrEmpty(query))
      {
        search = JsonConvert.DeserializeObject<UInspectSearchDTO>(query);
      }

      var result = await _uInspectService.Search(search, cancellationToken);

      return Ok(result);
    }


    [HttpGet]
    [Route("{uInspectId}")]
    [ResponseCache(Duration = 5)]
    public async Task<IActionResult> GetInspection(Guid uInspectId, [FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var search = new UInspectSearchDTO();

        if (!String.IsNullOrEmpty(query))
        {
          search = JsonConvert.DeserializeObject<UInspectSearchDTO>(query);
        }

        switch (search.Component)
        {
          case "uinspect-journey": 

            var result = await _uInspectService.GetWithAnswers(uInspectId, cancellationToken, search);

            // If we're fetching it from the appraisal component, and we haven't opened it, set it top open
            if (result.DTO.Opened == null)
            {
              var ipAddress = HttpContext.Connection.RemoteIpAddress.ToString();
              await _uInspectService.SetOpened(uInspectId, ipAddress, cancellationToken);
            }
  
            if (result.IsValid)
            {
              return Ok(result);
            }

            break;

          case "inspection-view": 

            var result3 = await _uInspectService.GetWithAnswers(uInspectId, cancellationToken, search);

            if (result3.IsValid)
            {
              return Ok(result3);
            }

            break;

          default:

            var result2 = await _uInspectService.Get(uInspectId, cancellationToken, search);

            if (result2.IsValid)
            {
              return Ok(result2);
            }

            break;
        }

        return NotFound();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    //[ResponseCache(Duration = 60)] // uncomment for prod version
    [Route("/api/uinspect/stats")]
    public async Task<IActionResult> GetStatData([FromQuery]string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<UInspectStatRequestDTO>(query);

      var result = await _uInspectService.GetStatData(dto, cancellationToken);
      return Ok(result);
    }

    [HttpGet]
    //[ResponseCache(Duration = 60)] // uncomment for prod version
    [Route("/api/uinspect/funnel")]
    public async Task<IActionResult> GetFunnelData([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<UInspectStatRequestDTO>(query);

      var result = await _uInspectService.GetFunnelData(dto, cancellationToken);
      return Ok(result);
    }
  }
}
