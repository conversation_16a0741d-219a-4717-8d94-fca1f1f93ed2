﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.API.Helpers;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/attribval")]
  [ApiController]
  [Authorize]
  public class AttribValController : ControllerBase
  {
    private readonly IAttribvalService _attribvalService;

    public AttribValController(IAttribvalService attribvalService)
    {
      _attribvalService = attribvalService;
    }

    [HttpGet]
    [Route("/api/attribvals")]
    public async Task<IActionResult> Search([FromQuery] int? unique, [FromQuery] string? search, CancellationToken cancellationToken)
    {
      var searchDTO = new AttribvalSearchDTO() { };

      if (!String.IsNullOrEmpty(search))
      {
        searchDTO = JsonConvert.DeserializeObject<AttribvalSearchDTO>(search);
      }

      try
      {
        var attribvals = await _attribvalService.Search(searchDTO, cancellationToken);
        return Ok(attribvals);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/attrib/{attribId}/attribvals")]
    public async Task<IActionResult> Search(uint attribId, [FromQuery] int? unique, [FromQuery] string? search, CancellationToken cancellationToken)
    {
      var searchDTO = new AttribvalSearchDTO();

      searchDTO.Filters.AttribId = attribId;

      if (!String.IsNullOrEmpty(search))
      {
        searchDTO = JsonConvert.DeserializeObject<AttribvalSearchDTO>(search);
      }

      try
      {
        var attribvals = await _attribvalService.Search(searchDTO, cancellationToken);
        return Ok(attribvals);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{attribvalId}")]
    public async Task<IActionResult> Get(uint attribvalId, [FromQuery] int? unique, [FromQuery] string? search, CancellationToken cancellationToken)
    {
      var searchDTO = new AttribvalSearchDTO() { LockRecord = false };

      if (!String.IsNullOrEmpty(search))
      {
        searchDTO = JsonConvert.DeserializeObject<AttribvalSearchDTO>(search);

      }

      try
      {
        var attrib = await _attribvalService.Get(attribvalId, searchDTO, cancellationToken);

        return Ok(attrib);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create(AttribvalDTO dto, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        var response = await _attribvalService.Create(dto, cancellationToken);

        return Ok(response);
      }

      return Forbid();
    }

    [HttpDelete]
    [Route("{attribvalId}")]
    public async Task<IActionResult> Delete(uint attribvalId, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        var response = await _attribvalService.Delete(attribvalId, cancellationToken);

        return Ok(response);
      }

      return Forbid();
    }

    [HttpPatch]
    [Route("{attribvalId}")]
    public async Task<IActionResult> Patch(uint attribvalId, [FromBody] JsonPatchDocument<Attribval> patch, CancellationToken cancellationToken)
    {
      // Only edit addresses that are ours (or if we're admin)
      if (User.IsAdmin())
      {
        try
        {
          return Ok(await _attribvalService.Patch(attribvalId, patch, cancellationToken));
        }
        catch (Exception ex)
        {
          return ex.ParseError();
        }
      }

      return Forbid();
    }
  }
}
