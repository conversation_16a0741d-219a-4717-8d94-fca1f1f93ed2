﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO
{
  public class VehicleStatusCheckDTO
  {
    public Guid? id;
    public string vrm;
    public uint? MakeId;
    public string MakeName;
    public uint? ModelId;
    public string ModelName;
    public uint? DerivId;
    public string DerivName;
    public uint? VehicleTypeId;
    public string VehicleTypeName;
    public uint? PlateId;
    public string PlateName;
    public uint? TransmissionTypeId;
    public string TransmissionTypeName;
    public uint? BodyTypeId;
    public string BodyTypeName;
    public uint? FuelTypeId;
    public string FuelTypeName;
    public Guid? CustomerId;
    public string CustomerName;
    public Guid? AdvertId;
    public Guid? AdvertAddressId;
    public uint? AdvertStatus;
    public Guid? SaleId;
    public string SaleStatusName;
    public Guid? AddressId;
    public string AddressName;
    public uint? PlatformId;
    public string PlatformName;
    public uint? SaleTypeId;
    public string SaleTypeName;
    public uint? SphLinkId;
    public Guid? MotId;
  }
}
