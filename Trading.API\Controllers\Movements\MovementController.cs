﻿
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Movements;
using Trading.API.Data.DTO.Search.Movements;
using Trading.API.Data.Models.Movements;
using Trading.Services.Extensions;
using Trading.Services.Interfaces.Movements;

namespace Trading.API.Controllers.Movements
{
  [Route("api/movement")]
  [ApiController]
  [Authorize]
  public class MovementController : ControllerBase
  {
    private readonly IMovementService _movementService;

    public MovementController(IMovementService movementService)
    {
      this._movementService = movementService;
    }


    [HttpGet]
    [Route("{id}")]
    public async Task<IActionResult> Get(Guid id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = new MovementSearchDTO();

        if (! String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<MovementSearchDTO>(query);
        }

        var result = await _movementService.Get(id, dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/movements")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = JsonConvert.DeserializeObject<MovementSearchDTO>(query);

        var result = await _movementService.Search(dto, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create([FromBody] MovementDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        var result = await _movementService.Create(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpDelete]
    [Route("{movementId}")]
    public async Task<IActionResult> Delete(Guid movementId, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        var response = await _movementService.Delete(movementId, cancellationToken);

        return Ok(response);
      }

      return Forbid();
    }

    [HttpPatch]
    [Route("{movementId}")]
    public async Task<IActionResult> Patch(Guid movementId, [FromBody] JsonPatchDocument<Movement> patch, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        try
        {
          return Ok(await _movementService.Patch(movementId, patch, cancellationToken));
        }
        catch (Exception ex)
        {
          return BadRequest(ex);
        }
      }

      return Forbid();
    }
  }
}
