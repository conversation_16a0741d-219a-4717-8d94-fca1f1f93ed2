﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.DTO.LeadCRM;

namespace Trading.API.Data.DTO.Search.LeadCRM
{
  public class LeadAppraisalSearchDTO : BaseSearchDTO
  {
    public LeadAppraisalFilters Filters { get; set; } = new LeadAppraisalFilters();
  }

  public class LeadAppraisalFilters : BaseFilterGuid
  {
    public Guid? LeadId { get; set; }
  }

}
