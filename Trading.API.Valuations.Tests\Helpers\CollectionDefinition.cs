﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Tests.Common;

namespace Trading.API.Valuations.Tests.Helpers
{

  [CollectionDefinition("DatabaseCollection")]
  public class DatabaseCollection : ICollectionFixture<DatabaseFixture>
  {
    // This class has no code, and is never created. Its purpose is simply
    // to be the place to apply [CollectionDefinition] and all the
    // ICollectionFixture<> interfaces.
  }
}
