using System;
using Trading.API.Data.Models;

namespace Trading.API.Data.DTO
{
  public class SearchDTO : BaseModelEntityDTO
  {
    public Guid? ContactId { get; set; }

    public ContactDTO Contact { get; set; }

    public string SearchJSONValue { get; set; }

    public string Description { get; set; }

    public AdvertSearchDTO AdSearchDTO { get; set; }

    public int SavedSearchCount { get; set; }

  }
}