﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.InspectCollect.VehicleData;

public class ICCapDataDTO
{
  public Guid? ICVehicleId { get; set; }
  public string CapCode { get; set; }

  public uint? LatestICValuationId { get; set; }
  public virtual ICCapValuationDTO LatestICValuation { get; set; }

  public uint? LatestICProvenanceId { get; set; }

  public virtual ICCapProvenanceDTO LatestICProvenance { get; set; }
}
