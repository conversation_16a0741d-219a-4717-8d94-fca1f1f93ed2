﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Configurations>RemarqDevelopment;RemarqProd;RemarqLocal-DevDB;RemarqLocal-ProdDB;ICLocal-Dev;ICLocal-Prod;IC-Dev;IC-Prod</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="6.6.2" />
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Trading.Data\Trading.API.Data.csproj" />
    <ProjectReference Include="..\Trading.Services\Trading.Services.csproj" />
  </ItemGroup>

</Project>
