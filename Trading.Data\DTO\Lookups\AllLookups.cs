﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO
{
  public class AllLookupsDTO 
  {
    public IEnumerable<RangeDTO> PriceRanges { get; set; }
    public IEnumerable<RangeDTO> MileageRanges { get; set; }
    public IEnumerable<RangeDTO> CapacityRanges { get; set; }

    public IEnumerable<LookupIntStringDTO> Makes { get; set; }
    public IEnumerable<LookupIntStringDTO> FuelTypes { get; set; }
    public IEnumerable<LookupIntStringDTO> TransmissionTypes { get; set; }
    public IEnumerable<LookupIntStringDTO> VehicleColours { get; set; }
    public IEnumerable<LookupIntStringDTO> BodyTypes { get; set; }
    public IEnumerable<LookupIntStringDTO> Colours { get; set; }
  }
  public class LookupGuidStringDTO
  {
    public Guid Id { get; set; }
    public string LookupValue { get; set; }

  } 
  public class LookupIntStringDTO
  {
    public uint Id { get; set; }
    public string LookupValue { get; set; }

  } 
}
