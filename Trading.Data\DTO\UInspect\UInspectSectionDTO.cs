﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;
using Trading.API.Data.Enums.UInspections;

namespace Trading.API.Data.DTO.UInspections
{
  public class UInspectSectionDTO : BaseModelEntityIntDTO
  {
    public string PlaceholderURL { get; set; }

    public UInspectSectionTypeEnum SectionType { get; set; }

    public string Title { get; set; }

    public string Explanation { get; set; }

    public int DisplayOrder { get; set; }

    public short IsInterior { get; set; } // inside the vehicle

    public bool Disabled { get; set; } // some sections aren't relevant to all cars (i.e. Rear Seats)

    // if not null then a LeadDocument record (with the specified document category) will be created linked to the media of this section
    public uint? MapToDocumentCategoryId { get; set; } 

    public uint UInspectFormatId { get; set; }
    public UInspectFormatDTO UInspectFormat { get; set; }

    public uint MinMedia { get; set; }
    public uint MaxMedia { get; set; }
    public bool Stop { get; set; }
    public uint NextSectionId { get; set; }
    public MediaTypeEnum MediaType { get; set; }
    public string ContinueURL { get; set; }
    public string InternalLabel { get; set; }
    public List<UInspectQuestionDTO> Questions { get; set; }
    public List<UInspectMediaDTO> Medias { get; set; }


  }

  public class UInspectSectionWithAnswerDTO : UInspectSectionDTO { 
    public List<UInspectMediaDTO> Medias { get; set; }
    public UInspectSectionCompleteDTO SectionComplete { get; set; }
    public new List<UInspectQuestionWithAnswerDTO> Questions { get; set; }

  }
}
