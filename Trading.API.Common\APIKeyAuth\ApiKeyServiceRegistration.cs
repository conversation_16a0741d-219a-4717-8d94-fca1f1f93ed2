﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Trading.API.Common.Authorization;
using Trading.Services.Classes.APIKeyAuth;
using Trading.Services.Interfaces.APIKeyAuth;

namespace Trading.API.Common.APIKeyAuth;

public static class ApiKeyServiceRegistration
{
  public static IServiceCollection AddApiKeyAuthentication(this IServiceCollection services)
  {
    // Register API key service
    services.AddScoped<IApiKeyService, ApiKeyService>();

    // Add API key authentication
    services.AddAuthentication()
        .AddScheme<AuthenticationSchemeOptions, ApiKeyAuthenticationHandler>(
            "ApiKey", options => { });

    // Add authorization handler for scopes
    services.AddSingleton<IAuthorizationHandler, RequireScopeHandler>();

    services.AddSingleton<IAuthorizationHandler, JwtOrApiKeyHandler>();

    // Add API key policy
    services.AddAuthorization(options =>
    {
      options.AddPolicy("ApiKeyPolicy", policy =>
      {
        policy.AddAuthenticationSchemes("ApiKey");
        policy.RequireAuthenticatedUser();
      });

      // Add policy for specific scopes
      // Example: "ReadOnly", "Admin", "Project1", etc.
      options.AddPolicy("TradingPlatform", policy =>
      {
        policy.AddAuthenticationSchemes("ApiKey");
        policy.RequireAuthenticatedUser();
        policy.AddRequirements(new RequireScopePolicy("TradingPlatform"));
      });

      options.AddPolicy("InspectCollect", policy =>
      {
        policy.AddAuthenticationSchemes("ApiKey");
        policy.RequireAuthenticatedUser();
        policy.AddRequirements(new RequireScopePolicy("InspectCollect"));
      });

      // You can define additional policies for other scopes as needed
    });

    return services;
  }
}
