﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.Services.Interfaces;

namespace Trading.API.Tests.Common.Fakes
{
  public class FakeStaffNotificationService : IStaffNotificationService
  {
    public Task<bool> NotifyStaffInternalMessaging(string subject, string body)
    {
      Console.WriteLine(subject);
      return Task.FromResult(true);
    }

    public Task<bool> NotifyStaffSMS(string msg)
    {
      Console.WriteLine(msg);
      return Task.FromResult(true);
    }
  }
}
