﻿using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO.UInspections;
using Trading.API.Data.Models.UInspections;
using Trading.Services.UInspections.Interfaces;

namespace Trading.API.UInspection.Controllers
{
  [Route("api/uinspect/question-option")]
  [ApiController]
  public class UInspectQuestionOptionController : ControllerBase
  {
    private readonly IUInspectQuestionOptionService _uInspectQuestionOptionService;

    public UInspectQuestionOptionController(IUInspectQuestionOptionService uInspectQuestionOptionService)
    {
      this._uInspectQuestionOptionService = uInspectQuestionOptionService;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<IActionResult> Get(Guid id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = new UInspectQuestionOptionSearchDTO();
        
        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<UInspectQuestionOptionSearchDTO>(query);
        }

        var result = await _uInspectQuestionOptionService.Get(id, cancellationToken, dto);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/uinspect/question-options")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = new UInspectQuestionOptionSearchDTO();
        
        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<UInspectQuestionOptionSearchDTO>(query);
        }

        var result = await _uInspectQuestionOptionService.Search(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create([FromBody] UInspectQuestionOptionDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        var result = await _uInspectQuestionOptionService.Create(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<UInspectQuestionOption> patch, CancellationToken cancellationToken)
    {
      try
      {
        var result = await _uInspectQuestionOptionService.Patch(id, patch, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
