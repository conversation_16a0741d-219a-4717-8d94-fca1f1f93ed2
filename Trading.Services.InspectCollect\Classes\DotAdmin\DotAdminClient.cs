﻿using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Trading.API.Data.DTO.DotAdmin;
using Trading.Services.ExternalDTO.Configs;
using Trading.Services.InspectCollect.Interfaces;
using System.Net.Http.Headers;

namespace Trading.Services.InspectCollect.Classes.DotAdmin
{
  /// <summary>
  /// HTTP client for dotAdmin API operations
  /// </summary>
  public class DotAdminClient : IDotAdminClient, IDisposable
  {
    private readonly HttpClient _httpClient;
    private readonly DotAdminDTO _options;
    private readonly JsonSerializerOptions _jsonOptions;
    private readonly ILogger<DotAdminClient>? _logger;
    private DotAdminAuthResponse? _currentAuth;
    private bool _disposed = false;

    public DotAdminClient(HttpClient httpClient, IOptionsSnapshot<DotAdminDTO> options, ILogger<DotAdminClient>? logger = null)
    {
      _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
      _options = options.Value ?? throw new ArgumentNullException(nameof(options));
      _logger = logger;

      // Configure HttpClient
      _httpClient.BaseAddress = new Uri(_options.BaseUrl);
      _httpClient.Timeout = TimeSpan.FromSeconds(_options.TimeoutSeconds);
      _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

      // Configure JSON serialization
      _jsonOptions = new JsonSerializerOptions
      {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        PropertyNameCaseInsensitive = true,
        WriteIndented = false
      };

      _logger?.LogInformation("DotAdminClient configured with BaseAddress: {BaseAddress}", _httpClient.BaseAddress);
    }

    public bool IsAuthenticated => _currentAuth != null && !_currentAuth.IsExpiredOrExpiringSoon(_options.TokenRefreshBufferSeconds);
    public string? CurrentToken => _currentAuth?.Token;
    public DotAdminAuthResponse? CurrentAuth => _currentAuth;

    public async Task<DotAdminAuthResponse> AuthenticateAsync(
      string username,
      string password,
      int? customerId = null,
      int? locationId = null,
      CancellationToken cancellationToken = default)
    {
      _logger?.LogInformation("Authenticating with dotAdmin API for user: {Username}", username);

      var request = new DotAdminAuthRequest
      {
        Username = username,
        Password = password,
        CustomerId = customerId,
        LocationId = locationId
      };

      var response = await PostJsonAsync<DotAdminAuthResponse>("/login", request, cancellationToken);

      if (response.Success)
      {
        _currentAuth = response;
        _logger?.LogInformation("Successfully authenticated with dotAdmin API. Token expires at: {ExpirationTime}",
          response.ExpirationDateTime);
        return response;
      }

      _logger?.LogWarning("Authentication failed with dotAdmin API");
      throw new InvalidOperationException("Authentication failed with dotAdmin API");
    }

    public async Task<DotAdminAuthResponse> SelectCustomerLocationAsync(
      int customerId,
      int locationId,
      CancellationToken cancellationToken = default)
    {
      if (!IsAuthenticated)
        throw new InvalidOperationException("Must be authenticated before selecting customer and location");

      _logger?.LogInformation("Selecting customer {CustomerId} and location {LocationId}", customerId, locationId);

      var request = new DotAdminCustomerLocationRequest
      {
        CustomerId = customerId,
        LocationId = locationId
      };

      var response = await PostJsonAsync<DotAdminAuthResponse>("/loginselectcustomer", request, cancellationToken);

      if (response.Success)
      {
        _currentAuth = response;
        _logger?.LogInformation("Successfully selected customer and location. New token expires at: {ExpirationTime}",
          response.ExpirationDateTime);
        return response;
      }

      _logger?.LogWarning("Failed to select customer and location");
      throw new InvalidOperationException("Failed to select customer and location");
    }

    public async Task<DotAdminCreateVehicleResponse> CreateVehicleAsync(
      DotAdminCreateVehicleRequest request,
      CancellationToken cancellationToken = default)
    {
      _logger?.LogInformation("Creating vehicle in dotAdmin with registration: {Registration}", request.MotorVehicleRegistration);

      await EnsureAuthenticatedAsync(cancellationToken);

      var response = await PostJsonAsync<DotAdminCreateVehicleResponse>(
        "/admin/auction/motorvehicles/createmotorvehicle",
        request,
        cancellationToken);

      if (response.Success && response.Vehicle != null)
      {
        _logger?.LogInformation("Successfully created vehicle with ID: {VehicleId}", response.Vehicle.Id);
        return response;
      }

      _logger?.LogWarning("Failed to create vehicle in dotAdmin");
      throw new InvalidOperationException("Failed to create vehicle in dotAdmin");
    }

    public async Task<T> GetAsync<T>(string endpoint, CancellationToken cancellationToken = default)
    {
      await EnsureAuthenticatedAsync(cancellationToken);

      using var request = new HttpRequestMessage(HttpMethod.Get, endpoint);
      AddAuthHeaders(request);

      var response = await _httpClient.SendAsync(request, cancellationToken);
      return await ProcessJsonResponseAsync<T>(response, cancellationToken);
    }

    public async Task<T> PostAsync<T>(string endpoint, object? data = null, CancellationToken cancellationToken = default)
    {
      if (endpoint != "/login")
        await EnsureAuthenticatedAsync(cancellationToken);

      return await PostJsonAsync<T>(endpoint, data, cancellationToken);
    }

    // Core method that handles all JSON POST requests
    private async Task<T> PostJsonAsync<T>(string endpoint, object? data, CancellationToken cancellationToken)
    {
      using var request = new HttpRequestMessage(HttpMethod.Post, endpoint);

      // Create JSON content
      var json = data != null ? JsonSerializer.Serialize(data, _jsonOptions) : "{}";
      request.Content = new StringContent(json, Encoding.UTF8);
      request.Content.Headers.ContentType = new MediaTypeHeaderValue("application/json");

      // Add auth headers if not login
      if (endpoint != "/login")
        AddAuthHeaders(request);

      _logger?.LogDebug("POST {Endpoint}: {Json}", endpoint, json);

      var response = await _httpClient.SendAsync(request, cancellationToken);
      return await ProcessJsonResponseAsync<T>(response, cancellationToken);
    }

    // Process and validate responses
    private async Task<T> ProcessJsonResponseAsync<T>(HttpResponseMessage response, CancellationToken cancellationToken)
    {
      var content = await response.Content.ReadAsStringAsync(cancellationToken);

      // Check for HTML response (dual-purpose endpoint issue)
      if (content.TrimStart().StartsWith("<", StringComparison.OrdinalIgnoreCase))
      {
        throw new InvalidOperationException(
          $"Received HTML response instead of JSON. Status: {response.StatusCode}. " +
          "Content-Type header may not be set correctly.");
      }

      response.EnsureSuccessStatusCode();

      var result = JsonSerializer.Deserialize<T>(content, _jsonOptions) ??
                   throw new InvalidOperationException("Deserialized result is null");

      // Validate API responses
      ValidateApiResponse(result);
      return result;
    }

    private void AddAuthHeaders(HttpRequestMessage request)
    {
      if (_currentAuth?.Token != null)
        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", _currentAuth.Token);
    }

    private void ValidateApiResponse<T>(T result)
    {
      switch (result)
      {
        case DotAdminCreateVehicleResponse createResponse when !createResponse.Success:
          throw new InvalidOperationException("Vehicle creation failed in dotAdmin API");
        case DotAdminAuthResponse authResponse when !authResponse.Success:
          throw new InvalidOperationException("Authentication failed with dotAdmin API");
      }
    }

    private async Task EnsureAuthenticatedAsync(CancellationToken cancellationToken)
    {
      if (!IsAuthenticated)
      {
        if (string.IsNullOrEmpty(_options.Username) || string.IsNullOrEmpty(_options.Password))
          throw new InvalidOperationException("No valid authentication token and no credentials configured");

        await AuthenticateAsync(_options.Username, _options.Password,
          _options.DefaultCustomerId, _options.DefaultLocationId, cancellationToken);
      }
    }

    public void Dispose()
    {
      if (!_disposed)
      {
        _httpClient?.Dispose();
        _disposed = true;
      }
    }
  }
}