﻿using System;
using Trading.API.Data.DTO.LeadCRM;
using Trading.API.Data.Enums.Movements;

#nullable disable

namespace Trading.API.Data.DTO.Movements
{
  public partial class MovementDTO : BaseModelEntityDTO
  {
    public AdvertDTO Advert { get; set; }
    public Guid? AdvertId { get; set; }
    public LeadVehicleDTO LeadVehicle { get; set; }
    public Guid? LeadVehicleId { get; set; }

    public MovementProviderEnum Provider { get; set; }
    public MovementStatusEnum MovementStatus { get; set; }
    public uint PickupAddressId { get; set; }
    public MovementAddressDTO PickupAddress { get; set; }

    public uint DropoffAddressId { get; set; }
    public MovementAddressDTO DropoffAddress { get; set; }

    public decimal ProviderPrice { get; set; }
    public decimal PlatformPrice { get; set; }
    public ContactDTO BookedByContact { get; set; }
    public DateTime PickupEarliest { get; set; }
    public DateTime PickupLatest { get; set; }
    public DateTime DropoffEarliest { get; set; }
    public DateTime DropoffLatest { get; set; }

    public decimal DriverLat { get; set; }
    public decimal DriverLon { get; set; }

    public string DriverName { get; set; }

    public string DriverPhone1 { get; set; }

    public string DriverPhone2 { get; set; }
  }
}
