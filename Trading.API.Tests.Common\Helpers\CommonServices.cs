﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using MySqlConnector;
using System.Data;
using System.Security.Claims;
using Trading.API.Data;
using Trading.API.Data.DTO.UInspections;
using Trading.API.Data.Models.UInspections;
using Trading.API.Tests.Common.Fakes;
using Trading.Services;
using Trading.Services.Classes;
using Trading.Services.Classes.AI;
using Trading.Services.Classes.DVLA;
using Trading.Services.ExternalDTO;
using Trading.Services.Interfaces;
using Trading.Services.Interfaces.AI;
using Trading.Services.Interfaces.DVLA;
using Trading.Services.Interfaces.WhosWhoNS;
using Trading.Services.WhosWhoNS;
using Tweetinvi.Models.V2;

namespace Trading.API.Tests.Common.Helpers
{
    public class CommonServices
  {
    public TradingContext Context { get; }
    public IMapper Mapper { get; }
    public IUserService UserService { get; }
    public IXeroService XeroService { get; }
    public IFileStorageService FileStorageService { get; }

    public IConfiguration Configuration { get; }

    public IMessageService MessageService { get; }
    public IInMailService InMailService { get; }
    public ILookupService LookupService { get; }
    public IDVLAService DVLAService { get; }

    public IDbConnection DbConnection { get; }

    public IStripeService StripeService { get; }
    public IContactActionService ContactActionService { get; }
    public ICustomerOrderService CustomerOrderService { get; }

    public INegotiationService NegotiationService { get; }

    public IFileService FileService { get; }
    public IEmailService EmailService { get; }

    public IDealService DealService { get; }

    public IWatchlistService WatchlistService { get; }

    public IRoleService RoleService { get; }
    public IContactRoleService ContactRoleService { get; }

    public ICustomerService CustomerService { get; }
    public IContactService ContactService { get; }
    public ICustomerInternalInfoService CustomerInternalInfoService { get; }
    public IWhosWhoService WhosWhoService { get; }

    public IAIService AIService { get; }
    public IVehicleCheckService VehicleCheckService { get; }
    public IServiceQueueService ServiceQueueService { get; }
    public IStaffNotificationService StaffNotificationService { get; }

    public CommonServices(IMapper mapper, TradingContext context
      , IUserService userService
      , IXeroService xeroService
      , IFileStorageService fileStorageService
      , IConfiguration configuration
      , IMessageService messageService
      , IInMailService inMailService
      , ILookupService lookupService
      , IDVLAService dvlaService
      , IDbConnection dbConnection
      , IStripeService stripeService
      , IContactActionService contactActionService
      , ICustomerOrderService customerOrderService
      , INegotiationService negotiationService
      , IEmailService emailService
      , IFileService fileService
      , IDealService dealService
      , IWatchlistService watchlistService
      , IRoleService roleService
      , IContactRoleService contactRoleService
      , ICustomerService customerService
      , IContactService contactService
      , ICustomerInternalInfoService customerInternalInfoService
      , IWhosWhoService whosWhoService
      , IAIService aiService
      , IVehicleCheckService vehicleCheckService
      , IServiceQueueService serviceQueueService
      , IStaffNotificationService staffNotificationService
      )
    {
      Context = context;
      Mapper = mapper;
      UserService = userService;
      XeroService = xeroService;
      FileStorageService = fileStorageService;
      Configuration = configuration;
      MessageService = messageService;
      InMailService = inMailService;
      LookupService = lookupService;
      DVLAService = dvlaService;
      DbConnection = dbConnection;
      StripeService = stripeService;
      ContactActionService = contactActionService;
      CustomerOrderService = customerOrderService;
      NegotiationService = negotiationService;
      EmailService = emailService;
      FileService = fileService;
      DealService = dealService;
      WatchlistService = watchlistService;
      RoleService = roleService;
      ContactRoleService = contactRoleService;
      CustomerService = customerService;
      ContactService = contactService;
      CustomerInternalInfoService = customerInternalInfoService;
      WhosWhoService = whosWhoService;
      AIService = aiService;
      VehicleCheckService = vehicleCheckService;
      ServiceQueueService = serviceQueueService;
      StaffNotificationService = staffNotificationService;
    }

    public static CommonServices Create(TradingContext context)
    {
      var config = new MapperConfiguration(cfg =>
      {
        cfg.AddMaps(typeof(Services.DTOMappingProfiles.MappingProfile));
        cfg.AddMaps(typeof(Services.DTOMappingProfiles.AdvertMappingProfile));
      });
      var mapper = config.CreateMapper();

      var builder = new ConfigurationBuilder();
      builder.AddInMemoryCollection(new List<KeyValuePair<string, string>>
        {
            new KeyValuePair<string, string>("Gods:0", "\"pete\""),
            new KeyValuePair<string, string>("Gods:1", "\"dave\""),
        })
        .Build();

      var configuration = builder.Build();

      HttpContextAccessor httpContextAccessor = new HttpContextAccessor();
      httpContextAccessor.HttpContext = new HttpContextMoq.HttpContextMock();

      var claims = new List<Claim>()
      {
          new Claim(ClaimTypes.Name, "username"),
          new Claim(ClaimTypes.NameIdentifier, "userId"),
          new Claim("contactId", new Guid().ToString()),
      };
      var identity = new ClaimsIdentity(claims, "TestAuthType");
      var claimsPrincipal = new ClaimsPrincipal(identity);

      httpContextAccessor.HttpContext.User = claimsPrincipal;

      var httpClient = new Mock<HttpClient>().Object;
      var fileStorage = new Mock<IFileStorageService>().Object;

      var userService = new UserService(httpContextAccessor, context);
      IXeroService xeroService = new XeroService(new Mock<IOptionsSnapshot<XeroDTO>>().Object, context, httpClient, fileStorage, null);

      IMessageService messageService = new Mock<IMessageService>().Object;
      IInMailService inMailService = new Mock<IInMailService>().Object;

      var dvlaDTO = new Mock<IOptionsSnapshot<DVLADTO>>();
      dvlaDTO.SetupAllProperties();
      dvlaDTO.SetupGet(p => p.Value).Returns(new DVLADTO { MOTApiKey = "3kZ2Jlf7Z46kyImejl7RC9Nbt62kCebi6mBsszUQ" });
      var dvlaClient = new DVLAClient(httpClient, dvlaDTO.Object);

      IDVLAService dvlaService = new DVLAService(dvlaDTO.Object, context, mapper, dvlaClient);

      ILookupService lookupService = new LookupService(context, dvlaService, mapper);

      IDbConnection dbConnection = new MySqlConnection(DBStrings.dbConn);

      IContactActionService contactActionService = new ContactActionService(context, mapper, userService, dbConnection, null, null);

      var loggerMock = new Mock<ILogger<StripeService>>().Object;
      var stripeDTO = new Mock<IOptionsSnapshot<StripeDTO>>();
      stripeDTO.SetupAllProperties();
      stripeDTO.SetupGet(p => p.Value).Returns(new StripeDTO { SecretKey = "Fake Key" });

      IStripeService stripeService = new StripeService(loggerMock, stripeDTO.Object, context, xeroService, contactActionService, null, null, null, null, null, null);

      ICustomerOrderService customerOrderService = new CustomerOrderService(context, xeroService, mapper, fileStorage, dbConnection, stripeService);

      INegotiationService negotiationService = new NegotiationService(context, mapper, userService);

      IFileProvider provider = new NullFileProvider();
      IFileService fileService = new FileService(provider);
      IEmailService emailService = new FakeEmailService();

      ISphAllService sphAllService = new SphAllService(context, mapper);

      IWatchlistService watchlistService = new WatchlistService(context, mapper, contactActionService);

      IRoleService roleService = new RoleService(context, mapper);
      IContactRoleService contactRoleService = new ContactRoleService(roleService, context, mapper);

      ICommsTemplateService templateService = new Mock<ICommsTemplateService>().Object;

      ICustomerService customerService = new CustomerService(context, mapper, emailService, null, contactActionService, userService);
      ICustomerNoteService noteService = new CustomerNoteService(context, mapper, userService, null);
      ICustomerInternalInfoService customerInternalInfoService = new CustomerInternalInfoService(context, mapper, noteService, null);

      IWhosWhoService whosWhoService = new WhosWhoService(context, dbConnection, mapper);
      IContactService contactService = new ContactService(null, context, contactRoleService, roleService, configuration, customerService, mapper, contactActionService, templateService, customerInternalInfoService, whosWhoService);

      IStaffNotificationService staffNotificationService = new FakeStaffNotificationService();

      IDealService dealService = new DealService(context, mapper, sphAllService, dbConnection, customerOrderService, 
        contactActionService, staffNotificationService);

      IAIService aiService = new FakeAIService();
      IVehicleCheckService vehicleCheckService = new FakeVehicleCheckService();
      IServiceQueueService serviceQueueService = new ServiceQueueService(context, vehicleCheckService);

      return new CommonServices(mapper, context
        , userService
        , xeroService
        , fileStorage
        , configuration
        , messageService
        , inMailService
        , lookupService
        , dvlaService
        , dbConnection
        , stripeService
        , contactActionService
        , customerOrderService
        , negotiationService
        , emailService
        , fileService
        , dealService
        , watchlistService
        , roleService
        , contactRoleService
        , customerService
        , contactService
        , customerInternalInfoService
        , whosWhoService
        , aiService
        , vehicleCheckService
        , serviceQueueService
        , staffNotificationService
        );
    }
  }
}
