﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO
{
  public class NegotiationDTO : BaseModelEntityDTO
  {
    public Guid AdvertId { get; set; }
    
    public Guid ContactId { get; set; } // the person being contacted regarding the lot 
    public ContactDTO Contact { get; set; }

    public NegotiationStateEnum State { get; set; }
    public DateTime? SleepUntil { get; set; } // used to set list order

    public List<NegotiationNoteDTO> NegotiationNotes { get; set; }
  }
}
