﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.API.Data.Models.DTO;

namespace Trading.API.Data.DTO
{
  public class MediaFilters : BaseFilterGuid
  {
    public Guid? VehicleMediaId { get; set; }
  }

  public class MediaSearchDTO: BaseSearchDTO
  {
    public MediaFilters Filters { get; set; } = new MediaFilters() { };
  }

  public class MediaSearchResultDTO
  {
    public MediaDTO MediaDTO { get; set; }
    public IEnumerable<MediaDTO> MediaDTOs { get; set; }
    public Media Media { get; set; }
    public IEnumerable<Media> Medias { get; set; }
  }
}
