﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO
{
  public class ContactActionSearchDTO : BaseSearchDTO
  {
    public ContactActionSearchFiltersDTO Filters { get; set; } = new ContactActionSearchFiltersDTO() { };
    public bool IsAdmin { get; set; }
  }

  public class ContactActionSearchFiltersDTO : BaseFilterGuid
  {
    public Guid? CustomerId { get; set; }
    public Guid? ContactId { get; set; }
    public ContactActionEnum? ContactAction { get; set; } 
    public int? DaysAgo { get; set; }
    public Guid? AssignedTo { get; set; }
    public bool ExcludeAdmins { get; set; }
  }
}
