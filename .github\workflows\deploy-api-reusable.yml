name: Template - Build / Deploy API

on:
  workflow_call:
    inputs:
      SERVER:
        required: true
        type: string
      REMOTEUSER:
        required: true
        type: string
      REMOTEPASSWORD:
        type: string
      CONFIGURATION:
        required: true
        type: string
      RUNNERNAME:
        required: false
        type: string
      RUNTYPE:
        required: false
        type: string
    secrets:
      PRIVATEKEY:
        required: true
   
jobs:

  ChoosingRunner:
    runs-on: ubuntu-latest
    
    outputs:
      runner: ${{ steps.step1.outputs.runner }}
      
    steps:
      - name: Which runner to use
        id: step1
        run: echo "runner=${{ inputs.RUNNERNAME || 'ubuntu-latest' }}" >> $GITHUB_OUTPUT
         

  BuildingAPI:

    needs: [ChoosingRunner]
    runs-on: ${{ needs.ChoosingRunner.outputs.runner }}

    steps:
    
    - uses: actions/checkout@v3

    - name: Check Event Name
      run: |
        echo " RUN TYPE ${{ inputs.RUNTYPE }}";
        if [ "${{ inputs.RUNTYPE }}" == "push" ]; then
          echo "This workflow was triggered by a push event."
        elif [ "${{ inputs.RUNTYPE }}" == "workflow_dispatch" ]; then
          echo "This workflow was triggered by a workflow_dispatch event."
        fi
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: 8.0.x

    - name: Restore dependencies
      run: dotnet restore ./Trading.API.sln

    - name: Building Project
      run: dotnet build --no-restore --configuration ${{ inputs.CONFIGURATION }} ./Trading.API.sln

    - name: List Files in Distribution Directory
      run: ls -R ./Trading.API
        
    - name: Deploying Files with Private Key
      if: ${{ inputs.RUNTYPE == 'workflow_dispatch' }}
      uses: SamKirkland/web-deploy@v1
      with:
        target-server: ${{ inputs.SERVER }}
        remote-user: ${{ inputs.REMOTEUSER }}
        private-ssh-key: ${{ secrets.PRIVATEKEY }}
        source-path: ./Trading.API/bin/Release
        destination-path: /var/www/html/trading/trading.api/Trading.API/Trading.API/bin
   
