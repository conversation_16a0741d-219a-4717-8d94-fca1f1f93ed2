﻿@using Trading.API.Views.Xero.XeroTokenFound
@model dynamic


@{
}

<style>

    body { font-family: Arial; }

</style>

<h1>Xero Token</h1>
<p>The Xero Access/Refresh Token information has been found in the database and is current. (see below) </p>

<a href="/xeroAuth?forceRefresh=1">Force a Xero Login ?</a>

&nbsp;

<a href="/bookingpage.html">Go to Booking Page</a>

<br/><br/>

<table border="1">
    <tr>
        <td nowrap>Access Token</td>
        <td>@Model.AccessToken</td>
    </tr>

    <tr>
        <td nowrap>Id Token</td>
        <td>@Model.IdToken</td>
    </tr>
    <tr>
        <td nowrap>Refresh Token</td>
        <td>@Model.RefreshToken</td>
    </tr>
    <tr>
        <td nowrap>Expires At UTC</td>
        <td>@Model.ExpiresAtUtc (UTC, not BST)</td>
    </tr>
</table>
