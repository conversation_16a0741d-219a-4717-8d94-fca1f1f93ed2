﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO.MechanicalFaults;
using Trading.API.Data.DTO.Search.MechanicalFaults;
using Trading.API.Data.Models.MechanicalFaults;
using Trading.Services.Interfaces.MechanicalFaults;

namespace Trading.API.Remarq.Controllers.MechanicalFaults
{
  [Route("api/vehicle-fault")]
  [ApiController]
  [Authorize]
  public class VehicleFaultController : ControllerBase
  {
    private readonly IVehicleFaultService _faultService;

    public VehicleFaultController(IVehicleFaultService faultService)
    {
      _faultService = faultService;
    }

    [HttpGet]
    [AllowAnonymous]
    [Route("items")]
    public async Task<IActionResult> GetFaultItems([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<FaultItemSearchDTO>(query);

      var items = await _faultService.SearchFaultItems(dto, cancellationToken);
      return Ok(items);
    }

    [HttpGet]
    [Route("checks")]
    public async Task<IActionResult> GetVehicleFaultCheck([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<VehicleFaultCheckSearchDTO>(query);

      var items = await _faultService.SearchVehicleFaultCheck(dto, cancellationToken);
      return Ok(items);
    }

    [HttpGet]
    [Route("statuses")]
    public async Task<IActionResult> GetFaultStatuses([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<FaultStatusSearchDTO>(query);

      var items = await _faultService.SearchFaultStatuses(dto, cancellationToken);
      return Ok(items);
    }

    [HttpPut]
    [Route("{vehicleFaultCheckId}/fault-item/{faultItemId}")]
    public async Task<IActionResult> SetVehicleFaultCheckItem(Guid vehicleFaultCheckId, uint faultItemId, [FromBody] VehicleFaultCheckItemDTO dto)
    {
      dto.VehicleFaultCheckId = vehicleFaultCheckId;
      dto.FaultItemId = faultItemId;
      var response = await _faultService.SetVehicleFaultCheckItem(vehicleFaultCheckId, faultItemId, dto);
      return Ok(response);
    }

    [HttpPost]
    [Route("/api/vehicle/{vehicleId}/fault-check")]
    public async Task<IActionResult> CreateVehicleFaultCheck(Guid vehicleId, [FromBody] VehicleFaultCheckDTO dto)
    {
      dto.VehicleId = vehicleId;
      var response = await _faultService.CreateVehicleFaultCheck(dto, new CancellationToken());
      return Ok(response);
    }

    [HttpGet]
    [Route("/api/vehicle/{vehicleId}/fault-check/latest")]
    public async Task<IActionResult> GetLatestFaultCheck(Guid vehicleId, [FromQuery] string query)
    {
      VehicleFaultCheckSearchDTO dto = new VehicleFaultCheckSearchDTO();

      if (!string.IsNullOrWhiteSpace(query))
      {
        dto = JsonConvert.DeserializeObject<VehicleFaultCheckSearchDTO>(query);

      }
      var response = await _faultService.GetLatestVehicleFaultCheck(vehicleId, dto, new CancellationToken());
      return Ok(response);
    }

    [HttpGet]
    [AllowAnonymous]
    [Route("check/{vehicleFaultCheckId}")]
    public async Task<IActionResult> GetFaultCheck(Guid vehicleFaultCheckId, [FromQuery] string query)
    {
      VehicleFaultCheckSearchDTO dto = new VehicleFaultCheckSearchDTO();

      if (!string.IsNullOrWhiteSpace(query))
      {
        dto = JsonConvert.DeserializeObject<VehicleFaultCheckSearchDTO>(query);
      }

      dto.Filters.Id = vehicleFaultCheckId;

      var response = await _faultService.GetVehicleFaultCheck(vehicleFaultCheckId, dto, new CancellationToken());
      return Ok(response);
    }

    [HttpPatch]
    [Route("check/{id}")]
    public async Task<IActionResult> UpdateVehicleFaultCheck(Guid id, JsonPatchDocument<VehicleFaultCheck> patch)
    {
      var response = await _faultService.PatchVehicleFaultCheck(id, patch);
      return Ok(response);
    }

    [HttpGet]
    [Route("categories")]
    public async Task<IActionResult> SearchCategories([FromQuery] string query)
    {
      var dto = JsonConvert.DeserializeObject<FaultCategorySearchDTO>(query);
      var response = await _faultService.SearchFaultCategories(dto, new CancellationToken());
      return Ok(response);
    }

    [HttpPatch]
    [Route("category/{id}")]
    public async Task<IActionResult> PatchCategory(uint id, JsonPatchDocument<FaultCategory> dto)
    {
      if (!User.IsInRole("ADMIN")) { return NotFound(); }

      var response = await _faultService.PatchFaultCategory(id, dto, new CancellationToken());
      return Ok(response);
    }

    [HttpPost]
    [Route("category")]
    public async Task<IActionResult> CreateCategory([FromBody] FaultCategoryDTO dto)
    {
      if (!User.IsInRole("ADMIN")) { return NotFound(); }

      var response = await _faultService.CreateFaultCategory(dto, new CancellationToken());
      return Ok(response);
    }

    [HttpDelete]
    [Route("category/{id}")]
    public async Task<IActionResult> DeleteCategory(uint id)
    {
      if (!User.IsInRole("ADMIN")) { return NotFound(); }

      var response = await _faultService.DeleteFaultCategory(id, new CancellationToken());
      return Ok(response);
    }

    [HttpGet]
    [Route("check-types")]
    public async Task<IActionResult> SearchCheckTypes([FromQuery] string query)
    {
      var dto = JsonConvert.DeserializeObject<FaultCheckTypeSearchDTO>(query);
      var response = await _faultService.SearchFaultCheckTypes(dto, new CancellationToken());
      return Ok(response);
    }

    [HttpPatch]
    [Route("check-type/{id}")]
    public async Task<IActionResult> PatchCheckType(uint id, JsonPatchDocument<FaultCheckType> dto)
    {
      if (!User.IsInRole("ADMIN")) { return NotFound(); }

      var response = await _faultService.PatchFaultCheckType(id, dto, new CancellationToken());
      return Ok(response);
    }

    [HttpPost]
    [Route("check-type")]
    public async Task<IActionResult> CreateCheckType([FromBody] FaultCheckTypeDTO dto)
    {
      if (!User.IsInRole("ADMIN")) { return NotFound(); }

      var response = await _faultService.CreateFaultCheckType(dto, new CancellationToken());
      return Ok(response);
    }

    [HttpDelete]
    [Route("check-type/{id}")]
    public async Task<IActionResult> DeleteCheckType(uint id)
    {
      if (!User.IsInRole("ADMIN")) { return NotFound(); }

      var response = await _faultService.DeleteFaultCheckType(id, new CancellationToken());
      return Ok(response);
    }

    [HttpGet]
    [Route("check-type/{id}/categories")]
    public async Task<IActionResult> SearchCheckTypeCategories([FromQuery] string query)
    {
      var dto = JsonConvert.DeserializeObject<FaultCheckTypeSearchDTO>(query);
      var response = await _faultService.SearchFaultCheckTypes(dto, new CancellationToken());
      return Ok(response);
    }

    [HttpGet]
    [Route("check-type/{id}")]
    public async Task<IActionResult> GetCheckType(uint id, [FromQuery] string query)
    {
      var dto = JsonConvert.DeserializeObject<FaultCheckTypeSearchDTO>(query);
      var response = await _faultService.GetFaultCheckType(id, dto, new CancellationToken());
      return Ok(response);
    }

    [HttpPatch]
    [Route("check-type-category/{checkTypeCategoryId}")]
    public async Task<IActionResult> PatchCheckTypeCategory(uint checkTypeCategoryId, JsonPatchDocument<FaultCheckTypeCategory> dto)
    {
      if (!User.IsInRole("ADMIN")) { return NotFound(); }

      var response = await _faultService.PatchFaultCheckTypeCategory(checkTypeCategoryId, dto, new CancellationToken());
      return Ok(response);
    }

    [HttpPost]
    [Route("check-type/{checkTypeId}/category")]
    public async Task<IActionResult> CreateCheckTypeCategory(uint checkTypeId, [FromBody] FaultCheckTypeCategoryDTO dto)
    {
      if (!User.IsInRole("ADMIN")) { return NotFound(); }

      dto.FaultCheckTypeId = checkTypeId;

      var response = await _faultService.CreateFaultCheckTypeCategory(dto, new CancellationToken());
      return Ok(response);
    }

    [HttpDelete]
    [Route("check-type/{checkTypeId}/category/{checkTypeCategoryId}")]
    public async Task<IActionResult> DeleteCheckTypeCategory(uint checkTypeId, uint checkTypeCategoryId)
    {
      if (!User.IsInRole("ADMIN")) { return NotFound(); }

      var response = await _faultService.DeleteFaultCheckTypeCategory(checkTypeCategoryId, new CancellationToken());
      return Ok(response);
    }

    [HttpPatch]
    [Route("status/{faultStatusId}")]
    public async Task<IActionResult> PatchFaultStatus(uint faultStatusId, JsonPatchDocument<FaultStatus> dto)
    {
      if (!User.IsInRole("ADMIN")) { return NotFound(); }

      var response = await _faultService.PatchFaultStatus(faultStatusId, dto, new CancellationToken());
      return Ok(response);
    }

    [HttpPost]
    [Route("status")]
    public async Task<IActionResult> CreateFaultStatus([FromBody] FaultStatusDTO dto)
    {
      if (!User.IsInRole("ADMIN")) { return NotFound(); }

      var response = await _faultService.CreateFaultStatus(dto, new CancellationToken());
      return Ok(response);
    }

    [HttpDelete]
    [Route("status/{faultStatusId}")]
    public async Task<IActionResult> DeleteFaultStatus(uint faultStatusId)
    {
      if (!User.IsInRole("ADMIN")) { return NotFound(); }

      var response = await _faultService.DeleteFaultStatus(faultStatusId, new CancellationToken());
      return Ok(response);
    }

    [HttpPatch]
    [Route("item/{faultItemId}")]
    public async Task<IActionResult> PatchFaultItem(uint faultItemId, JsonPatchDocument<FaultItem> dto)
    {
      if (!User.IsInRole("ADMIN")) { return NotFound(); }

      var response = await _faultService.PatchFaultItem(faultItemId, dto, new CancellationToken());
      return Ok(response);
    }

    [HttpPost]
    [Route("item")]
    public async Task<IActionResult> CreateFaultItem([FromBody] FaultItemDTO dto)
    {
      if (!User.IsInRole("ADMIN")) { return NotFound(); }

      var response = await _faultService.CreateFaultItem(dto, new CancellationToken());
      return Ok(response);
    }

    [HttpDelete]
    [Route("item/{faultItemId}")]
    public async Task<IActionResult> DeleteFaultItem(uint faultItemId)
    {
      if (!User.IsInRole("ADMIN")) { return NotFound(); }

      var response = await _faultService.DeleteFaultItem(faultItemId, new CancellationToken());
      return Ok(response);
    }
  }
}
