﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums.InspectCollect;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICContainerWidgetDTO : BaseModelEntityDTO
  {
    public Guid? ICContainerId { get; set; }
    public ICContainerDTO ICContainer { get; set; }
    public Guid? ICWidgetId { get; set; }

    public ICWidgetDTO ICWidget { get; set; }
    public uint? Position { get; set; }

    [MaxLength(50)]
    public string Name { get; set; }

    [MaxLength(50)]
    public string Label { get; set; }
    public string Content { get; set; }
    public List<ICContainerWidgetInputDTO> ICContainerWidgetInputs { get; set; }
    public List<ICContainerWidgetLinkDTO> ICContainerWidgetLinks { get; set; }
    public List<ICContainerWidgetAssetDTO> ICContainerWidgetAssets { get; set; }
    public List<ICContainerWidgetStyleDTO> ICContainerWidgetStyles { get; set; }
    public bool? Collapsible { get; set; }
    public uint? FlexGrow { get; set; }
    public ICAlignmentEnum? Alignment { get; set; }
    public ICAlignmentEnum? VAlignment { get; set; }
    public ICStickySectionEnum? Sticky { get; set; }
    public bool? RequiresValidContainer { get; set; }
    public Guid? ICTriggerId { get; set; }
  }

  public class ICContainerWidgetSearchDTO : BaseSearchDTO
  {
    public ICContainerWidgetSearchFilters Filters { get; set; } = new ICContainerWidgetSearchFilters();
  }

  public class ICContainerWidgetSearchFilters : BaseFilter
  {
    public string Name { get; set; }
  }

  public class ICContainerWidgetCreateDTO
  {
    public Guid ICContainerId { get; set; }
    public Guid ICWidgetId { get; set; }
    public string Name { get; set; }
    public string Label { get; set; }
    public string Content { get; set; }
    public uint? Position { get; set; }
    public bool? Collapsible { get; set; }

  }
}
