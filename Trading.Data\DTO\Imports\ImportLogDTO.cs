﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.Imports
{
  public class ImportLogDTO : BaseModelEntityIntDTO
  {
    public string Identifier { get; set; } // for vehicle imports this would be the VRM
    public bool Imported { get; set; } // false if import failed 

    public string ErrorText { get; set; }

    public Guid ImportId { get; set; }
  }
}
