using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.API.Helpers;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/customerAttrib")]
  [ApiController]
  [Authorize]
  public class CustomerAttribController : ControllerBase
  {
    public ICustomerAttribService _customerAttribService;
    public IMapper _mapper;

    public CustomerAttribController(
      ICustomerAttribService customerAttribService,
      IMapper mapper)
    {
      _customerAttribService = customerAttribService;
      _mapper = mapper;
    }

    [HttpGet]
    [Route("/api/customerAttribs")]
    [ResponseCache(Duration = 300)]
    public async Task<IActionResult> Search(CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        var response = await _customerAttribService.Search(new CustomerAttribSearchDTO(), cancellationToken);

        return Ok(response);
      }

      return Forbid();
    }

    [HttpGet]
    [Route("{customerAttribId}")]
    public async Task<IActionResult> GetProduct(uint customerAttribId, [FromQuery] string search, CancellationToken cancellationToken)
    {
      var searchDTO = new CustomerAttribSearchDTO() { };

      if (!String.IsNullOrEmpty(search))
      {
        searchDTO = JsonSerializer.Deserialize<CustomerAttribSearchDTO>(search);
      }

      var response = await _customerAttribService.Get(customerAttribId, searchDTO, cancellationToken);

      if (response != null)
      {
        return Ok(response);
      }

      return NotFound();
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create(CustomerAttribDTO dto, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        var response = await _customerAttribService.Create(dto, cancellationToken);

        return Ok(response);
      }

      return Forbid();
    }

    [HttpPatch]
    [Route("{customerAttribId}")]
    public async Task<IActionResult> Patch(uint customerAttribId, [FromBody] JsonPatchDocument<CustomerAttrib> patch, CancellationToken cancellationToken)
    {
      // Only edit addresses that are ours (or if we're admin)
      if (User.IsAdmin())
      {
        try
        {
          return Ok(await _customerAttribService.Patch(customerAttribId, patch, cancellationToken));
        }
        catch (Exception ex)
        {
          return ex.ParseError();
        }
      }

      return Forbid();
    }

    [HttpPut]
    [Route("customer/{customerId}/attrib/{attribId}")]
    public async Task<IActionResult> SetCustomerAttribId(Guid customerId, uint attribId, CustomerAttribDTO dto, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        // Get Customer Attrib
        var exists = await _customerAttribService.Search(new CustomerAttribSearchDTO()
        {
          Filters = {
            CustomerId = customerId,
            AttribId = attribId,
          },
          Component = "SetCustomerAttrib"
        }, cancellationToken);

        if (exists.Count() == 0)
        {
          var customerAttrib = await _customerAttribService.Create(new CustomerAttribDTO()
          {
            CustomerId = customerId,
            AttribId = attribId,
            AttribvalId = dto.AttribvalId,
            StatusId = (int) StatusEnum.Active
          }, cancellationToken);
        }
        else
        {
          var currentRecord = exists.First();
          var patch = new JsonPatchDocument<CustomerAttrib>();
          patch.Add(c => c.AttribvalId, dto.AttribvalId);
          var customerAttrib = await _customerAttribService.Patch((uint) currentRecord.Id, patch, cancellationToken);
        }

        return Ok();
      }

      return Forbid();
    }
  }
}
