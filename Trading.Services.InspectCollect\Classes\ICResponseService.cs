﻿using AutoMapper;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.EntityFrameworkCore;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.InspectCollect.Interfaces;
using Trading.API.Data.Enums;
using Trading.API.Data.Enums.InspectCollect;
using System;
using Trading.Services.Extensions;
using Trading.API.Data.Models.InspectCollect.VehicleData;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect.VehicleLookup;
using Newtonsoft.Json.Serialization;
using Trading.Services.InspectCollect.Classes.VehicleLookup;
using Trading.API.Data.DTO.DotAdmin;
using Amazon.JSII.JsonModel.Spec;

namespace Trading.Services.InspectCollect.Classes
{
  public class ICResponseService : ICResponseInterface
  {
    private readonly TradingContext _context;
    private readonly IMapper _mapper;
    private readonly ICVehicleDataInterface _vehicleDataService;
    private readonly ICResponseDataInterface _responseDataInterface;
    private readonly IDotAdminService _dotAdminService;
    private readonly ICVehicleInterface _vehicleService;

    public ICResponseService(TradingContext context, IMapper mapper,
      ICVehicleDataInterface vehicleDataService, ICResponseDataInterface responseDataInterface,
      IDotAdminService dotAdminService, ICVehicleInterface vehicleService)
    {
      _context = context;
      _mapper = mapper;
      _vehicleDataService = vehicleDataService;
      _responseDataInterface = responseDataInterface;
      _dotAdminService = dotAdminService;
      _vehicleService = vehicleService;
    }

    public async Task<ICResponseDTO> Create(ICResponseCreateDTO dto)
    {
      return await _responseDataInterface.Create(dto);
    }

    public async Task<bool> Delete(Guid id)
    {
      return await _responseDataInterface.Delete(id);
    }

    public async Task<ValidatedResultDTO<ICResponseDTO>> Get(Guid guid, ICResponseSearchDTO searchDTO, CancellationToken ct)
    {
      if (searchDTO == null) { searchDTO = new ICResponseSearchDTO(); }

      searchDTO.Filters.Id = guid;
      var responses = await Search(searchDTO, ct);

      return new ValidatedResultDTO<ICResponseDTO>()
      {
        IsValid = responses.TotalItems > 0,
        DTO = responses.Results.FirstOrDefault()
      };
    }

    public async Task<string> Trigger(Guid id, ICTriggerRequestDTO triggerRequest, CancellationToken ct)
    {
      string response = "";

      switch (triggerRequest.ICTriggerType)
      {
        case ICTriggerTypeEnum.VRMLookup:
          response = await VRMLookup(id, triggerRequest, ct);
          break;

        case ICTriggerTypeEnum.ValuationLookup:
          response = await ValuationLookup(id, triggerRequest, ct);
          break;

        case ICTriggerTypeEnum.FeatureValuation:
          response = await FeatureValuationLookup(id, triggerRequest, ct);
          break;

        case ICTriggerTypeEnum.SetOutcome:
          if (triggerRequest.Payload == null)
          {
            throw new ArgumentException("Payload cannot be null for SetOutcome trigger");
          }

          response = await SetOutcome(id, triggerRequest, ct);
          break;
      }

      return response;
    }

    private async Task<string> SetOutcome(Guid icResponseId, ICTriggerRequestDTO triggerRequest, CancellationToken ct)
    {
      var payload = JsonConvert.DeserializeObject<ICTriggerPayloadSetOutcome>(triggerRequest.Payload);

      payload.ICContainerGroupId = triggerRequest.ICContainerGroupId.Value;
      payload.IsGod = triggerRequest.IsGod.Value;
      payload.IsAdmin = triggerRequest.IsAdmin.Value;

      // ensure the icVehicle doesn't already have an outcome set
      var existingResponse = await _context.ICResponses
        .AsNoTracking()
        .AnyAsync(x => x.Id == icResponseId && x.ICVehicle.ICOutcomeId.HasValue && (x.ICContainerGroupId == triggerRequest.ICContainerGroupId || triggerRequest.IsGod == true), ct);

      if (existingResponse)
      {
        throw new InvalidOperationException($"ICVehicle with ID {payload.ICVehicleId} already has an outcome set.");
      }

      // get the outcome 
      var outcome = await _context.ICOutcomes
        .AsNoTracking()
        .FirstOrDefaultAsync(x => x.Id == payload.ICOutcomeId, ct);

      if (outcome == null)
      {
        throw new ArgumentException($"ICOutcome with ID {payload.ICOutcomeId} not found");
      }

      if (outcome.DisposalCode == ICConstants.ICDisposalCodeCity)
      {
        var auctionResponse = await PushToAuction(icResponseId, payload, ct);
        var responseResult = JsonConvert.DeserializeObject<ICPushToAuctionResult>(auctionResponse);

        // patch the icVehicle with the outcome details 
        var patch = new JsonPatchDocument<ICVehicle>();
        patch.Add(x => x.Outcome, outcome);
        patch.Add(x => x.OutcomeResponseRef, responseResult.Data.Id);
        patch.Add(x => x.OutcomeSetDate, DateTime.Now);

        await _vehicleService.Patch(payload.ICVehicleId, patch, ct, null);

        return auctionResponse;
      }

      return new string(' ', 0); // Return empty string as default 
    }

    private async Task<string> PushToAuction(Guid icResponseId, ICTriggerPayloadSetOutcome payload, CancellationToken ct)
    {
      var outcome = await _context.ICOutcomes
        .Where(x => x.Id == payload.ICOutcomeId)
        .Select(x => new { x.ExternalLocationRef, x.ICContainerGroup.InventId, x.ICContainerGroupId })
        .FirstOrDefaultAsync();

      if (outcome == null || outcome.ICContainerGroupId != payload.ICContainerGroupId && payload.IsGod != true)
      {
        throw new Exception("No such outcome for this customer");
      }

      var res = await _dotAdminService.CreateVehicleFromICVehicleAsync(icResponseId, payload.ICVehicleId, Int32.Parse(outcome.ExternalLocationRef), outcome.InventId.Value, ct);

      ICPushToAuctionResult result = new ICPushToAuctionResult
      {
        Success = res != null,
        ErrorMessage = res == null ? "Failed to push vehicle to auction" : null,
        Data = res != null ? new DotAdminVehicle
        {
          Registration = res.Registration,
          Id = res.Id,
          Flags = res.Flags,
          LossCategory = res.LossCategory,
          TypeId = res.TypeId
        } : null
      };

      var settings = new JsonSerializerSettings
      {
        ContractResolver = new CamelCasePropertyNamesContractResolver(),
      };

      if (!result.Success)
      {
        // Return error response
        var errorResponse = new { success = false, error = result.ErrorMessage };
        return JsonConvert.SerializeObject(errorResponse, settings);
      }

      var response = JsonConvert.SerializeObject(result, settings);

      return response;
    }

    private async Task<string> VRMLookup(Guid icResponseId, ICTriggerRequestDTO triggerRequest, CancellationToken ct)
    {
      var payload = JsonConvert.DeserializeObject<ICTriggerPayloadVehicleLookup>(triggerRequest.Payload);

      payload.VRM = payload.VRM.Replace(" ", "");

      var vrmLookupResult = await _vehicleDataService.GetLookupDataInternal(new ICVehicleEnquiryDTO()
      {
        ResponseId = icResponseId,
        VRM = payload.VRM.ToUpper(),
        ClearVehicleIfNotFound = payload.ClearVehicleIfNotFound,
        ICContainerGroupId = triggerRequest.ICContainerGroupId.Value,
      }, ct);

      var settings = new JsonSerializerSettings
      {
        ContractResolver = new CamelCasePropertyNamesContractResolver(),
      };

      if (!vrmLookupResult.Success)
      {
        // Return error response
        var errorResponse = new { success = false, error = vrmLookupResult.ErrorMessage };
        return JsonConvert.SerializeObject(errorResponse, settings);
      }

      var response = JsonConvert.SerializeObject(vrmLookupResult, settings);

      return response;
    }

    private async Task<string> ValuationLookup(Guid icResponseId, ICTriggerRequestDTO triggerRequest, CancellationToken ct)
    {
      var payload = JsonConvert.DeserializeObject<ICTriggerPayloadVehicleLookup>(triggerRequest.Payload);

      payload.VRM = payload.VRM.Replace(" ", "");

      var vrmLookupResponse = await _vehicleDataService.GetValuationDataInternal(new ICVehicleEnquiryDTO()
      {
        ResponseId = icResponseId,
        VRM = payload.VRM.ToUpper(),
        Odometer = (uint)payload.Odometer.Value
      }, ct);

      var settings = new JsonSerializerSettings
      {
        ContractResolver = new CamelCasePropertyNamesContractResolver(),
      };

      var response = JsonConvert.SerializeObject(vrmLookupResponse, settings);

      return response;
    }

    private async Task<string> FeatureValuationLookup(Guid icResponseId, ICTriggerRequestDTO triggerRequest, CancellationToken ct)
    {
      var payload = JsonConvert.DeserializeObject<ICTriggerPayloadVehicleLookup>(triggerRequest.Payload);

      if (!string.IsNullOrEmpty(payload.VRM))
      {
        payload.VRM = payload.VRM.Replace(" ", "");
      }

      var vrmLookupResponse = await _vehicleDataService.GetFeatureValuationInternal(new ICVehicleEnquiryDTO()
      {
        ResponseId = icResponseId
      }, ct);

      var settings = new JsonSerializerSettings
      {
        ContractResolver = new CamelCasePropertyNamesContractResolver(),
      };

      var response = JsonConvert.SerializeObject(vrmLookupResponse, settings);

      return response;
    }

    public async Task<ICResponseDTO> Patch(Guid id, JsonPatchDocument<ICResponse> patch)
    {
      return await _responseDataInterface.Patch(id, patch);
    }

    public async Task<SearchResultDTO<ICResponseDTO>> Search(ICResponseSearchDTO searchDTO, CancellationToken ct)
    {
      var preQuery = _context.ICResponses.AsQueryable();

      if (searchDTO.Filters != null)
      {
        if (searchDTO.Filters.Id != null)
        {
          preQuery = preQuery.Where(x => x.Id == searchDTO.Filters.Id);
        }
        if (searchDTO.Filters.ICLayoutId != null && searchDTO.Filters.ICLayoutId != Guid.Empty)
        {
          preQuery = preQuery.Where(x => x.ICLayoutId == searchDTO.Filters.ICLayoutId);
        }
        if (searchDTO.Filters.ICContainerGroupId != null && searchDTO.Filters.ICContainerGroupId != Guid.Empty)
        {
          preQuery = preQuery.Where(x => x.ICContainerGroupId == searchDTO.Filters.ICContainerGroupId);
        }
        if (searchDTO.Filters.ICResponseStatus != null && searchDTO.Filters.ICResponseStatus != 0)
        {
          preQuery = preQuery.Where(x => x.ICResponseStatus == searchDTO.Filters.ICResponseStatus);
        }
        if (searchDTO.Filters.Name != null)
        {
          preQuery = preQuery.Where(x => x.Name.Contains(searchDTO.Filters.Name));
        }
        if (searchDTO.Filters.Email != null)
        {
          preQuery = preQuery.Where(x => x.Email.Contains(searchDTO.Filters.Email));
        }
        if (searchDTO.Filters.VRM != null)
        {
          preQuery = preQuery.Where(x => x.VRM.Contains(searchDTO.Filters.VRM));
        }
        if (!String.IsNullOrEmpty(searchDTO.Filters.FromDate))
        {
          var ok = DateTime.TryParse(searchDTO.Filters.FromDate, out var fromDate);
          if (ok)
          {
            preQuery = preQuery.Where(x => x.Added >= fromDate);
          }
        }
        if (searchDTO.Filters.FromUpdatedDate.HasValue)
        {
          preQuery = preQuery.Where(x => x.Updated >= searchDTO.Filters.FromUpdatedDate);
        }
        if (searchDTO.Filters.ToDate != null)
        {
          var ok = DateTime.TryParse(searchDTO.Filters.ToDate, out var toDate);
          if (ok)
          {
            toDate = toDate.AddDays(1);
            preQuery = preQuery.Where(x => x.Added <= toDate);
          }
        }

        if (searchDTO.Filters.ForUserId.HasValue)
        {
          preQuery = preQuery.Where(x => x.ICUserId == searchDTO.Filters.ForUserId.Value);
        }
      }

      var query = preQuery;

      if (searchDTO.Component == "response-view")
      {
        query = query.Include(x => x.ICResponseInputs).ThenInclude(x => x.ICResponseInputValues).ThenInclude(x => x.ICInputOption);
        query = query.Include(x => x.ICResponseInputs).ThenInclude(x => x.ICResponseInputAssets);
        query = query.Include(x => x.ICUser);
        query = query.Include(x => x.ICResponseConditionItems).ThenInclude(x => x.Media);
        query = query.Include(x => x.ICVehicle.CapData.LatestICProvenance);
        query = query.Include(x => x.ICVehicle.CapData.LatestICValuation);
        query = query.Include(x => x.ICVehicle.AutoTraderData.LatestFeatureList.Features.Where(x => !x.FactoryFitted));
        query = query.Include(x => x.ICVehicle.AutoTraderData.LatestValuation);
        query = query.Include(x => x.ICVehicle.AutoTraderData.LatestVehicleMetrics);
        query = query.Include(x => x.ICLocation);
        query = query.AsSplitQuery();
      }

      if (searchDTO.Component == "dashboard-view")
      {
        query = query.Include(x => x.ICResponseInputs).ThenInclude(x => x.ICResponseInputValues).ThenInclude(x => x.ICInputOption);
        query = query.Include(x => x.ICResponseInputs).ThenInclude(x => x.ICResponseInputAssets);
        query = query.Include(x => x.ICVehicle.CapData.LatestICProvenance);
        query = query.Include(x => x.ICVehicle.CapData.LatestICValuation);
        query = query.Include(x => x.ICVehicle.AutoTraderData.LatestFeatureList.Features);
        query = query.Include(x => x.ICVehicle.AutoTraderData.LatestValuation);
        query = query.Include(x => x.ICLocation);
        query = query.AsSplitQuery();
      }

      if (searchDTO.Component == "response-list")
      {
        query = query.Include(x => x.ICUser);
        query = query.Include(x => x.ICVehicle);
        query = query.Include(x => x.ICLocation);
        query = query.Include(x => x.ICResponseInputs).ThenInclude(x => x.ICResponseInputAssets);
      }

      if (searchDTO.Component == "appraisal-state.service")
      {
        query = query.Include(x => x.ICLayout);
        query = query.Include(x => x.ICResponseInputs).ThenInclude(x => x.ICResponseInputValues);
        query = query.Include(x => x.ICResponseInputs).ThenInclude(x => x.ICResponseInputAssets);
        query = query.Include(x => x.ICVehicle.CapData.LatestICProvenance);
        query = query.Include(x => x.ICVehicle.CapData.LatestICValuation);
        query = query.Include(x => x.ICVehicle.AutoTraderData.LatestFeatureList.Features);
        query = query.Include(x => x.ICVehicle.AutoTraderData.LatestValuation);
        query = query.AsSplitQuery();
      }

      if (searchDTO.Component == "spark-summary")
      {
      }

      if (searchDTO.Component == "spark-detail")
      {
        query = query.Include(x => x.ICLayout);
        query = query.Include(x => x.ICResponseInputs).ThenInclude(x => x.ICResponseInputValues);
        query = query.Include(x => x.ICResponseInputs).ThenInclude(x => x.ICResponseInputAssets);
        query = query.Include(x => x.ICVehicle.CapData.LatestICProvenance);
        query = query.Include(x => x.ICVehicle.CapData.LatestICValuation);
        query = query.Include(x => x.ICVehicle.AutoTraderData.LatestFeatureList.Features);
        query = query.Include(x => x.ICVehicle.AutoTraderData.LatestValuation);
        query = query.Include(x => x.ICResponseConditionItems).ThenInclude(x => x.Media);
        query = query.Include(x => x.ICResponseConditionItems).ThenInclude(x => x.BodyPart);
        query = query.Include(x => x.ICResponseConditionItems).ThenInclude(x => x.Damage);
        query = query.Include(x => x.ICResponseConditionItems).ThenInclude(x => x.DamageDetail);
        query = query.Include(x => x.ICResponseConditionItems).ThenInclude(x => x.DamageSeverity);
        query = query.AsSplitQuery();

      }

      query = query.OrderByDescending(x => x.Added);

      if (searchDTO.Limit.HasValue)
      {
        query = query.Take(searchDTO.Limit.Value);
        query = query.Skip(searchDTO.Offset.Value);
      }

      var items = await query
        .AsNoTracking()
        .AsSplitQuery()
        .ToListAsync(ct);

      return new SearchResultDTO<ICResponseDTO>
      {
        TotalItems = (searchDTO.Limit.HasValue) ? await preQuery.CountAsync(ct) : items.Count(),
        Results = _mapper.Map<IEnumerable<ICResponse>, IEnumerable<ICResponseDTO>>(items)
      };
    }

    public async Task<List<ICResponseStatDataDTO>> GetResponseStatistics(ICResponseStatRequestDTO requestDTO, CancellationToken cancellationToken)
    {
      return await _responseDataInterface.GetResponseStatistics(requestDTO, cancellationToken);
    }
  }
}
