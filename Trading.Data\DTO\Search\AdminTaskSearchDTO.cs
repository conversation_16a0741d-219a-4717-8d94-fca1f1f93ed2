﻿using System;

namespace Trading.API.Data.DTO.Search
{
  public class AdminTaskSearchDTO : BaseSearchDTO
  {
    public AdminTaskFiltersDTO Filters { get; set; } = new AdminTaskFiltersDTO() { };
    public string SortBy { get; set; }
    public bool? SortDescending { get; set; }
  }

  public class AdminTaskFiltersDTO: BaseFilterGuid
  {
    public Guid? TasksForUserId { get; set; }

    public Guid? CustomerId { get; set; }
    public Guid? FromContactId { get; set; }
    public Guid? ForContactId { get; set; }
    public bool Overdue { get; set; }
  }
}
