﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.Payment
{
  public class PaymentResultDTO
  {
    // Unique identifier for the PaymentIntent
    public string Id { get; set; }

    public bool Success { get; set; }
    public string Message { get; set; }
    
    // Amount charged, in the smallest currency unit
    public long Amount { get; set; }

    // Currency of the payment (e.g., "usd", "gbp")
    public string Currency { get; set; }

    // Payment method ID used for the payment (only return last four chars)
    public string PaymentMethod { get; set; }

    // Timestamp of when the PaymentIntent was created
    public DateTime Created { get; set; }
    public string ErrorType { get; set; }
    public string ErrorCode { get; set; }
    public string Status { get; set; }
    public bool RequiresAction { get; set; }
  }

}
