using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.API.Helpers;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/model")]
  [ApiController]
  public class ModelController : ControllerBase
  {
    private readonly IModelService _modelService;
    private readonly IMergeService _mergeService;

    public ModelController(IModelService modelService, IMergeService mergeService)
    {
      this._modelService = modelService;
      this._mergeService = mergeService;
    }

    [HttpPatch]
    [Route("{modelId}")]
    public async Task<IActionResult> Patch(uint modelId, [FromBody] JsonPatchDocument<Model> patch, CancellationToken cancellationToken)
    {
      // Only edit addresses that are ours (or if we're admin)
      if (User.IsAdmin())
      {
        try
        {
          return Ok(await _modelService.Patch(modelId, patch, cancellationToken));
        }
        catch (Exception ex)
        {
          return ex.ParseError();
        }
      }

      return Forbid();
    }

    [HttpGet]
    [Route("/api/make/{makeId}/models")]
    [ResponseCache(Duration = 600)]
    public async Task<IActionResult> Search(uint makeId, [FromQuery] int? unique, [FromQuery] string? search, CancellationToken cancellationToken)
    {
      var searchDTO = new ModelSearchDTO() { };

      if (!String.IsNullOrEmpty(search))
      {
        searchDTO = JsonSerializer.Deserialize<ModelSearchDTO>(search);
      }

      searchDTO.Filters.makeId = makeId;

      var response = await _modelService.Search(searchDTO, cancellationToken);

      return Ok(response);
    }

   
  }
}
