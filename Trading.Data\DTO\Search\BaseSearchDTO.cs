﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;


namespace Trading.API.Data.DTO
{
  public class OrderByDTO
  {
    public string Column { get; set; }
    public bool? Descending { get; set; }

  }
  public class BaseSearchDTO
  {
    public string Component { get; set; }
    public List<string> Includes { get; set; }
    public List<OrderByDTO> Order { get; set; }
    public List<string> Columns { get; set; }
    public int? Offset { get; set; }
    public int? Limit { get; set; }
    public bool CountOnly { get; set; } = false;
    public bool LockRecord { get; set; } = false;
    public Guid? CurrentCustomerId { get; set; }
    public Guid? CurrentContactId { get; set; }
    public bool? InvalidateCache { get; set; } = false;
    public bool? UseCache { get; set; } = false;
  }

  public class BaseFilter
  {
    public Guid? Id { get; set; }
    public DateTime? Updated { get; set; }
    public DateTime? Added { get; set; }
    public uint? StatusId { get; set; }

  }

  public class BaseFilterInt : BaseFilter
  {
    public uint? Id { get; set; }
  }
  public class BaseFilterGuid : BaseFilter
  {
    public Guid? Id { get; set; }
  }

}
