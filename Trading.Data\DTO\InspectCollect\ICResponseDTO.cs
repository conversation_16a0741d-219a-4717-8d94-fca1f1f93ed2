﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.DTO.InspectCollect.Location;
using Trading.API.Data.DTO.InspectCollect.ResponseConditionItems;
using Trading.API.Data.DTO.InspectCollect.VehicleData;
using Trading.API.Data.Enums.InspectCollect;
using Trading.API.Data.Models.InspectCollect.ResponseConditionItems;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICResponseDTO : BaseModelEntityDTO
  {
    public Guid? ICLayoutId { get; set; }
    public ICLayoutDTO ICLayout { get; set; }
    public Guid? ICContainerGroupId { get; set; }

    public Guid? ICUserId { get; set; }
    public ICUserDTO ICUser { get; set; }

    public Guid? ICLocationId { get; set; }
    public ICLocationDTO ICLocation { get; set; }

    public Guid? ICVehicleId { get; set; }
    public ICVehicleDTO ICVehicle { get; set; }

    public string Name { get; set; }
    public string Email { get; set; }
    public string VRM { get; set; }
    public List<ICResponseInputDTO> ICResponseInputs { get; set; }
    public string ExternalId { get; set; }
    public DateTime? ResponseStarted { get; set; }
    public DateTime? ResponseEnded { get; set; }
    public string ResponseGeoIP { get; set; }
    public ICResponseStatusEnum ICResponseStatus { get; set; }
    public Guid? ICContainerId { get; set; }
    public float? Latitude { get; set; }
    public float? Longitude { get; set; }
    public List<ICResponseConditionItemDTO> ICResponseConditionItems { get; set; }

  }


  public class ICResponseSearchDTO : BaseSearchDTO
  {
    public ICResponseSearchFilters Filters { get; set; } = new ICResponseSearchFilters();
  }

  public class ICCombinedResponseContainers
  {
    public SearchResultDTO<ICContainerDTO> Containers { get; set; }
    public ValidatedResultDTO<ICResponseDTO> Response { get; set; }
  }


  public class ICResponseSearchFilters : BaseFilterGuid
  {
    public string Name { get; set; }
    public Guid? ICContainerGroupId { get; set; }
    public Guid? ICLayoutId { get; set; }
    public ICResponseStatusEnum ICResponseStatus { get; set; }
    public string FromDate { get; set; }
    public string ToDate { get; set; }
    public string VRM { get; set; }
    public string Email { get; set; }
    public Guid? ForUserId { get; set; }
    public DateTime? FromUpdatedDate { get; set; }
  }

  public class ICResponseCreateDTO
  {
    public Guid? ICLayoutId { get; set; }
    public Guid? ICContainerGroupId { get; set; }
    public Guid? ICUserId { get; set; }
    public Guid? ICLocationId { get; set; }
    public string Name { get; set; }
    public string Email { get; set; }
    public string VRM { get; set; }
    public string ExternalId { get; set; }
    public ICResponseStatusEnum ICResponseStatus { get; set; }
    public string PrimaryImageURL { get; set; }
  }
  public class ICResponseUpdateDTO
  {
    public List<ICResponseInputDTO> ICResponseInputs { get; set; }
    public string ResponseGeoIP { get; set; }
    public float? Latitude { get; set; }
    public float? Longitude { get; set; }
  }
}
