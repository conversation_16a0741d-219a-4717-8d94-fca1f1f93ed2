﻿using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;

namespace Trading.API.Remarq.Controllers.Extensions
{
  public static class ContextExtensions
  {
    private static Dictionary<string, bool> processes;
    private static JsonSerializerSettings camelCaseFormatter;

    static ContextExtensions()
    {
      camelCaseFormatter = new JsonSerializerSettings();
      camelCaseFormatter.ContractResolver = new CamelCasePropertyNamesContractResolver();

      processes = new Dictionary<string, bool>();
    }

    public static bool IsAPIKeyRequest(this HttpContext context)
    {
      return context.Request.Headers.ContainsKey("x-api-key");
    }

    public static async void KeepConnectionAlive(this HttpResponse response, CancellationToken cancellationToken, [CallerMemberName] string processName = "unknown")
    {
      if (processes.ContainsKey(processName))
      {
        if (processes[processName])
        {
          // remove the completed process 
          processes.Remove(processName);
        }
        else
        {
          throw new ApplicationException("Process name is already in use");
        }
      }

      processes.Add(processName, false);

      if (!response.HasStarted)
        response.Headers.Add("Content-Type", "text/event-stream");

      var taskDelay = 30 * 1000;// allow for a 30 secs emit
      while (!cancellationToken.IsCancellationRequested && (processes.ContainsKey(processName) && !processes[processName]))
      {
        try
        {
          await response.WriteAsync($": Running Task... {DateTime.Now}\r\r");
          await response.Body.FlushAsync();
          await Task.Delay(taskDelay, cancellationToken);
        }
        catch (Exception ex)
        {
          //Log.Logger.Error($"Error streaming data to client {ex.Message}");
        }
      }

      processes.Remove(processName);
    }

    public static async Task CompleteProcess(this HttpResponse response, [CallerMemberName] string processName = "unknown")
    {
      if (!processes.ContainsKey(processName))
      {
        return;
      }

      processes[processName] = true;

      await response.WriteAsync($"data: {JsonConvert.SerializeObject($"process {processName} completed", camelCaseFormatter)}\n\n");
      await response.Body.FlushAsync();
      response.Body.Close();
    }

    public static async Task CompleteProcess<T>(this HttpResponse response, T result, [CallerMemberName] string processName = "unknown") where T : class
    {
      if (!processes.ContainsKey(processName))
      {
        return;
      }

      processes[processName] = true;

      await response.WriteAsync($"data: {JsonConvert.SerializeObject(result, camelCaseFormatter)}\n\n");
      await response.Body.FlushAsync();
      response.Body.Close();
    }

    public static async Task CompleteProcessWithError(this HttpResponse response, Exception ex, [CallerMemberName] string processName = "unknown")
    {
      if (!processes.ContainsKey(processName))
      {
        return;
      }

      processes[processName] = true;

      var excep = ex;
      var msg = "";
      while (excep != null)
      {
        msg += excep.Message + "\n\n";
        excep = excep.InnerException;
      }

      msg += "\n\nTRACE: " + ex.StackTrace;

      await response.WriteAsync($"error: {Newtonsoft.Json.JsonConvert.SerializeObject(msg, camelCaseFormatter)}\n\n");
      await response.Body.FlushAsync();
    }
  }
}
