using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.API.Helpers;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/deriv")]
  [ApiController]
  public class DerivController : ControllerBase
  {
    private readonly IDerivService _derivService;
    private readonly IMergeService _mergeService;

    public DerivController(IDerivService derivService, IMergeService mergeService)
    {
      this._derivService = derivService;
      this._mergeService = mergeService;
    }

    [HttpGet]
    [Route("/api/model/{modelId}/derivs")]
    [ResponseCache(Duration = 600)]

    public async Task<IActionResult> ModelDerivs(uint modelId, [FromQuery] int? unique, [FromQuery] string? query, CancellationToken cancellationToken)
    {
      var searchDTO = new DerivSearchDTO() { };

      if (!String.IsNullOrEmpty(query))
      {
        searchDTO = JsonSerializer.Deserialize<DerivSearchDTO>(query);
      }

      searchDTO.Filters.modelId = modelId;

      var response = await _derivService.Search(searchDTO, cancellationToken);

      return Ok(response);
    }

 
    [HttpPatch]
    [Route("{derivId}")]
    public async Task<IActionResult> Patch(uint derivId, [FromBody] JsonPatchDocument<Deriv> patch, CancellationToken cancellationToken)
    {
      // Only edit addresses that are ours (or if we're admin)
      if (User.IsAdmin())
      {
        try
        {
          return Ok(await _derivService.Patch(derivId, patch, cancellationToken));
        }
        catch (Exception ex)
        {
          return ex.ParseError();
        }
      }

      return Forbid();
    }
  }
}
