﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.Models.InspectCollect;

[Table("ICAutoTraderSetting")]
public class ICAutoTraderSettings : BaseModelEntity
{
  public Guid ICContainerGroupId { get; set; }
  public virtual ICContainerGroup ICContainerGroup { get; set; }

  [MaxLength(128)]
  public string DealerName { get; set; }

  [MaxLength(12)]
  public string DealerId { get; set; }
  public bool HasExtendedMetricsLicense { get; set; }

}
