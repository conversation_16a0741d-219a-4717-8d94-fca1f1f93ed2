﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums.InspectCollect;

namespace Trading.API.Data.Models.InspectCollect
{
  [Table("ICValidation")]

  public class ICValidation : BaseModelEntity
  {
    [MaxLength(100)]
    public string Name { get; set; }

    [ForeignKey("ICContainerGroup")]
    public Guid ICContainerGroupId { get; set; }
    public ICContainerGroup ICContainerGroup { get; set; }
    public string ValidationCode { get; set; }
  }
}