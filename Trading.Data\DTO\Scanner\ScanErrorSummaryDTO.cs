﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Models;
using Trading.API.Data.Models.DTO;

namespace Trading.API.Data.DTO
{
  public class ScanErrorSummaryDTO
  {
    public string ScanServiceId { get; set; }
    public string ScanServiceName { get; set; }
    public string ScanStyleId { get; set; }
    public string ScanStyleName { get; set; }
    public uint CustomerCount { get; set; }
    public uint ErrorCount { get; set; }
    public DateTime LastError { get; set; }
  }
  public class ScanErrorDetailDTO
  {
    public uint? ScanStyleId { get; set; }
    public uint? ScanFieldId { get; set; }
    public uint? ScanStageId { get; set; }
    public uint? ScanCustomerId { get; set; }
    public string CompanyName { get; set; }
    public string Reason { get; set; }
    public uint ErrorCount { get; set; }
    public DateTime LastError { get; set; }

    public string Url { get; set; }
  }
}