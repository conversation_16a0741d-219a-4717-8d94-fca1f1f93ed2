﻿using System;
using System.Collections.Generic;
using Trading.API.Data.Models.DTO;

namespace Trading.Services.ExternalDTO
{
  // use this when creating a vehicle via the IVrmLookupService
  public class CreateVehicleDTO
  {
    public Guid? CustomerId { get; set; }
    public Guid ContactId { get; set; }
    public Guid? AddressId { get; set; }

    public string Vrm { get; set; }
    public string Vin { get; set; }
    public int? Odometer { get; set; }

    public string CustomerRef { get; set; }

    public bool LogBook { get; set; }

    public ScanVehicleDTO ScanVehicle { get; set; }
  }

  public class ImportScanVehicleDTO
  {
    public Guid? ScanVehicleGuid { get; set; }
    public List<uint> ScanVehicleImageIds;
  }
}
