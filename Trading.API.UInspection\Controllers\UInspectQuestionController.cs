﻿using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO.UInspections;
using Trading.API.Data.Models.UInspections;
using Trading.Services.UInspections.Interfaces;

namespace Trading.API.UInspection.Controllers
{
  [Route("api/uinspect/question")]
  [ApiController]
  public class UInspectQuestionController : ControllerBase
  {
    private readonly IUInspectQuestionService _uInspectQuestionService;

    public UInspectQuestionController(IUInspectQuestionService uInspectQuestionService)
    {
      this._uInspectQuestionService = uInspectQuestionService;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<IActionResult> Get(Guid id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = new UInspectQuestionSearchDTO();
        
        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<UInspectQuestionSearchDTO>(query);
        }

        var result = await _uInspectQuestionService.Get(id, cancellationToken, dto);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/uinspect/questions")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = new UInspectQuestionSearchDTO();
        
        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<UInspectQuestionSearchDTO>(query);
        }

        var result = await _uInspectQuestionService.Search(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create([FromBody] UInspectQuestionDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        var result = await _uInspectQuestionService.Create(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<UInspectQuestion> patch, CancellationToken cancellationToken)
    {
      try
      {
        var result = await _uInspectQuestionService.Patch(id, patch, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
