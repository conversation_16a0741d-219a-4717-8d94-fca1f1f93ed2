﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.LeadCRM
{
  public class LeadBaseModelDTO : BaseModelEntityDTO
  {
    public Guid? CreatedByContactId { get; set; }

    public ContactDTO CreatedByContact { get; set; }

    public Guid? UpdatedByContactId { get; set; }

    public ContactDTO UpdatedByContact { get; set; }
  }
}
