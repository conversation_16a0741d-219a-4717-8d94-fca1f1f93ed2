using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Search;
using Trading.Services;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/contact-action")]
  [ApiController]
  [Authorize]
  public class ContactActionController : ControllerBase
  {
    private readonly IContactActionService _contactActionService;

    public ContactActionController(IContactActionService contactActionService)
    {
      this._contactActionService = contactActionService;
    }
    [HttpGet]
    [Authorize(Roles = "ADMIN")]
    [Route("/api/contact/{contactId}/actions")]
    public async Task<IActionResult> GetContactActions(Guid contactId, CancellationToken cancellationToken)
    {
      try
      {
        var dto = new Data.DTO.ContactActionSearchDTO();
        var thisContactId = User.ContactId();

        if (!User.IsInRole("GOD") || thisContactId != contactId)
        {
          throw new Exception("You do not have permission to view this info");
        }

        dto.Filters.ContactId = contactId;

        var actions = await _contactActionService.Search(dto, cancellationToken);
        return Ok(actions);
      }
      catch (Exception ex)
      {
        return BadRequest(ex.Message);
      }
    }

    [HttpGet]
    [Authorize(Roles = "ADMIN, POWER_USER")]
    [Route("/api/contact-actions")]
    public async Task<IActionResult> Search(string query, CancellationToken cancellationToken)
    {
      try
      {
        if (String.IsNullOrEmpty(query))
        {
          return BadRequest("No query supplied");
        }

        var dto = JsonConvert.DeserializeObject<ContactActionSearchDTO>(query);
        var actions = await _contactActionService.Search(dto, cancellationToken);

        return Ok(actions);
      }
      catch (Exception ex)
      {
        return BadRequest(ex.Message);
      }
    }

    [HttpGet]
    [Authorize(Roles = "ADMIN")]
    [Route("/api/customer/{customerId}/contact-actions")]
    public async Task<IActionResult> GetCustomerActions(Guid customerId, [FromQuery] string query, CancellationToken cancellationToken)
    {
      if (!User.IsInRole("ADMIN") && !User.IsPowerUser() && User.CustomerId() != customerId)
      {
        throw new Exception("You do not have permission to view this info");
      }

      try
      {
        var dto =  JsonConvert.DeserializeObject<ContactActionSearchDTO>(query);

        dto.Filters.CustomerId = customerId;

        // get actions for all contacts of the specified customer
        var actions = await _contactActionService.Search(dto, cancellationToken);
        return Ok(actions);
      }
      catch (Exception ex)
      {
        return BadRequest(ex.Message);
      }
    }


    [HttpGet]
    [HttpPost]
    [AllowAnonymous]
    [Route("/api/contact/login-failed")]
    public async Task<IActionResult> LoginFailed([FromBody] LoginFailedDTO dto, CancellationToken cancellationToken)
    {
      var response = await _contactActionService.LoginFailed(dto, cancellationToken);

      return Ok(response);
    }

    [HttpGet()]
    [Authorize(Roles = "GOD")]
    public async Task<IActionResult> SearchContactActions([FromQuery]string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = JsonConvert.DeserializeObject<ContactActionSearchDTO>(query);

        var actions = await _contactActionService.Search(dto, cancellationToken);
        return Ok(actions);
      }
      catch (Exception ex)
      {
        return BadRequest(ex.Message);
      }
    }

    [HttpGet("login-summary")]
    [Authorize(Roles = "ADMIN, POWER_USER")]
    public async Task<IActionResult> GetTopLogins([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<WhosWhoAdminSearchDTO>(query);

      var res = await _contactActionService.GetLoginSummary(dto, cancellationToken);
      return Ok(res);
    }

  }
}
