﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Threading;
using System.Threading.Tasks;
using Trading.Services.ExternalDTO.Auth;
using Trading.Services.Interfaces;

namespace Trading.API.Remarq.Controllers
{
  [Route("api/[controller]")]
  [ApiController]
  public class AuthController : ControllerBase
  {
    private readonly IAuthService _authService;
    private readonly AuthConfigDTO _config;

    public AuthController(IAuthService authService, IOptionsSnapshot<AuthConfigDTO> config)
    {
      _authService = authService;
      _config = config.Value;
    }

    [HttpPost]
    public async Task<IActionResult> GetAuthResponse([FromBody] AuthRequestDTO request, CancellationToken cancellationToken)
    {
      // todo: put this in config (so it can be different for each deployment)
      if (request.SecretKey != _config.SecretKey)
      {
        return Forbid();
      }

      var response = await _authService.GetAuthResponse(request.UserEmail, cancellationToken);
      return Ok(response);
    }
  }
}
