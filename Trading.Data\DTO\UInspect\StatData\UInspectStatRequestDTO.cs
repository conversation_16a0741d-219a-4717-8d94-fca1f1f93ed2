﻿using System;
using Trading.API.Data.Enums.UInspect;

namespace Trading.API.Data.DTO.UInspect.StatData
{
  public class UInspectStatRequestDTO
  {
    public UInspectStatRangeEnum StatRange { get; set; }
    public UInspectStatGroupingEnum StatGrouping { get; set; }
    public UInspectChartTypeEnum ChartType { get; set; }

    // only used when statRange is custom, otherwise date range is calculated in the service method
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
  }
}
