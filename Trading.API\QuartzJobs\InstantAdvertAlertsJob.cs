﻿using Quartz;
using System.Threading;
using System.Threading.Tasks;
using Trading.Services.Interfaces;

namespace Trading.API.Remarq.QuartzJobs
{
  public class InstantAdvertAlertsJob : IJob
  {
    private readonly IAdvertSearchService _searchService;
    public InstantAdvertAlertsJob(IAdvertSearchService searchService)
    {
      _searchService = searchService;
    }

    public async Task Execute(IJobExecutionContext context)
    {
      await _searchService.ProcessImmediateAlerts(new CancellationToken());
    }
  }
}
