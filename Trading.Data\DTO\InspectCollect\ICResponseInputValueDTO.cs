﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICResponseInputFeatureDTO : BaseModelEntityIntDTO
  {
    public Guid? ICResponseInputId { get; set; }
    public Guid? ICVehicleId { get; set; }
    public uint FeatureId { get; set; }
    public bool ResponseSelected { get; set; }
  }
}
