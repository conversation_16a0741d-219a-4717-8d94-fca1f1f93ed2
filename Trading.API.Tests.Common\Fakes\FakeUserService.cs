﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Trading.Services.Interfaces;

namespace Trading.API.Tests.Common.Fakes
{
  internal class FakeUserService : IUserService
  {
    public Task<bool> EndImpersonate(Guid impersonator, CancellationToken ct)
    {
      throw new NotImplementedException();
    }

    public Guid? GetContactId()
    {
      throw new NotImplementedException();
    }

    public Guid? GetCustomerId()
    {
      throw new NotImplementedException();
    }

    public Task<bool> ImpersonateUser(Guid impersonator, string contactEmail, CancellationToken ct)
    {
      throw new NotImplementedException();
    }

    public bool IsAdmin()
    {
      throw new NotImplementedException();
    }

    public bool IsAdminOrGreater()
    {
      throw new NotImplementedException();
    }

    public bool IsGod()
    {
      throw new NotImplementedException();
    }
  }
}
