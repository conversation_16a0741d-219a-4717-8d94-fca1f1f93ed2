using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/[controller]")]
  [ApiController]
  public class HelloController : ControllerBase
  {
    private readonly ISaleService _saleService;

    public HelloController(ISaleService saleService)
    {
      _saleService = saleService;
    }
    
    [HttpGet]
    public async Task<ActionResult> Hello()
    {
      return Ok(true);
    }
    
    // NOTE: This is used by the Route53 HealthCheck, and should return "pong"
    [HttpGet]
    [Route("/api/ping")]
    public async Task<ActionResult> Ping()
    {
      return Ok("pong");
    }
    
    // NOTE: This is used by the Route53 HealthCheck, and should return a single saleID
    [HttpGet]
    [Route("/api/database")]
    public async Task<ActionResult> Database()
    {
      var result = await _saleService.Search(new SaleSearchDTO() { Component = "HelloDatabase" }, new CancellationToken());
      var sale = result.Sale;
      
      return Ok(sale.Id);
    }
  }
}
