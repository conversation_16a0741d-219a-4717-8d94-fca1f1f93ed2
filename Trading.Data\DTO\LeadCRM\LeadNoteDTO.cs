﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.LeadCRM
{
  public class LeadNoteDTO: BaseModelEntityDTO
  {
    public Guid LeadId { get; set; }
    public LeadDTO Lead { get; set; }

    public string Note { get; set; }

    public bool? ReminderSet { get; set; }
    public DateTime? Reminder { get; set; }

    public Guid CreatedByContactId { get; set; }

    public ContactDTO CreatedByContact { get; set; }
    public Guid? CreatedForContactId { get; set; }

    public ContactDTO CreatedForContact { get; set; }

    public int? LeadActionId { get; set; }
    public LeadActionDTO LeadAction { get; set; }
  }
}
