using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.Extensions;
using Trading.Services.InspectCollect.Classes;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/validations")]
  [ApiController]
  [Authorize(Policy = "RequireInspectCollect")]
  [AllowAnonymous]
  public class ICValidationController : ControllerBase
  {
    private readonly ICValidationInterface _icValidationService;

    public ICValidationController(ICValidationInterface icValidationService)
    {
      _icValidationService = icValidationService;
    }

    [HttpGet]
    [Route("search")]
    [Route("/api/inspect-collect/validations")]
    public async Task<ActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {

      var searchDTO = JsonConvert.DeserializeObject<ICValidationSearchDTO>(query) ?? new ICValidationSearchDTO();

      var icContainerGroupId = User.ICContainerGroupId();

      if (icContainerGroupId != searchDTO.Filters.ICContainerGroupId && !User.IsAdmin())
      {
        return Forbid();
      }

      var ok = await _icValidationService.Search(searchDTO, cancellationToken);

      return Ok(ok);
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Create([FromBody] ICValidationCreateDTO dto)
    {
      if (dto.ICContainerGroupId != User.ICContainerGroupId() && !User.IsAdmin())
      {
        return Forbid();
      }

      var ok = await _icValidationService.Create(dto);

      return Ok(ok);
    }

    [HttpPatch]
    [Route("{validationId}")]
    public async Task<ActionResult> Patch(Guid validationId, JsonPatchDocument<ICValidation> patch)
    {
      var ok = await _icValidationService.Patch(validationId, patch);

      return Ok(ok);
    }

    [HttpDelete]
    [Route("{validationId}")]
    public async Task<ActionResult> Delete(Guid validationId)
    {
      var ok = await _icValidationService.Delete(validationId);

      return Ok(ok);
    }

    [HttpGet]
    [Route("{validationId}")]
    public async Task<ActionResult> Get(Guid validationId, [FromQuery] string query, CancellationToken ct)
    {
      ICValidationSearchDTO searchDTO = new ICValidationSearchDTO();

      if (!String.IsNullOrEmpty(query))
      {
        searchDTO = JsonConvert.DeserializeObject<ICValidationSearchDTO>(query);
      }

      var ok = await _icValidationService.Get(validationId, searchDTO, ct);

      return Ok(ok);
    }
  }
}