﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;
using Trading.API.Data.Models.LeadCRM;

namespace Trading.API.Data.DTO.LeadCRM
{
  public class LeadMediaDTO: BaseModelEntityDTO
  {
    public Guid? LeadId { get; set; }

    public Lead Lead { get; set; }

    public MediaTypeEnum MediaType { get; set; }

    public string MediaURL { get; set; }
  }

  public class LeadMediaSearchDTO: BaseSearchDTO 
  {
    public LeadMediaFilters Filters { get; set; } = new LeadMediaFilters();
  }

  public class LeadMediaFilters : BaseFilter
  {
    public Guid? LeadId { get; set; }
  }
}
