﻿using AutoMapper;
using Microsoft.Extensions.Logging;
using Moq;
using Trading.API.Data.DTO.AutoTrader;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.DTO.InspectCollect.VehicleLookup;
using Trading.Services.InspectCollect.Classes.VehicleLookup;
using Trading.Services.InspectCollect.Interfaces;
using Trading.Services.InspectCollect.Tests.Helpers;
using Trading.Services.Interfaces.AutoTrader;

namespace Trading.Services.InspectCollect.Tests
{
  public class ICVehicleDataServiceTests : TestBase
  {
    private readonly Mock<ICVehicleLookupInterface> _mockLookupService;
    private readonly Mock<IAutoTraderService> _mockAutoTraderService;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<ICVehicleInterface> _mockVehicleService;
    private readonly Mock<ILogger<ICVehicleDataService>> _mockLogger;
    private readonly Mock<IAutoTraderClient> _mockAutoTraderClient;
    private readonly Mock<ICResponseDataInterface> _mockResponseService;
    private readonly Mock<ICAutoTraderSettingsInterface> _mockAutoTraderSettings; 
    private readonly ICVehicleDataService _service;

    public ICVehicleDataServiceTests()
    {
      _mockLookupService = MockHelpers.CreateMockLookupService();
      _mockAutoTraderService = MockHelpers.CreateMockAutoTraderService();
      _mockMapper = MockHelpers.CreateMockMapper();
      _mockVehicleService = MockHelpers.CreateMockVehicleService();
      _mockLogger = MockHelpers.CreateMockLogger();
      _mockAutoTraderClient = MockHelpers.CreateMockAutoTraderClient();
      _mockResponseService = MockHelpers.CreateMockResponseService();
      _mockAutoTraderSettings = MockHelpers.CreateMockAutoTraderSettings();

      _service = new ICVehicleDataService(
          _mockLookupService.Object,
          _mockAutoTraderService.Object,
          _mockMapper.Object,
          _mockVehicleService.Object,
          _mockLogger.Object,
          _mockAutoTraderClient.Object,
          _mockResponseService.Object,
          _mockAutoTraderSettings.Object
      );
    }

    #region Validation Tests

    [Fact]
    public async Task GetLookupDataInternal_WithNullEnquiry_ReturnsFailure()
    {
      // Arrange
      ICVehicleEnquiryDTO enquiry = null;

      // Act
      var result = await _service.GetLookupDataInternal(enquiry, CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Equal("Vehicle enquiry cannot be null", result.ErrorMessage);
      Assert.Null(result.Data);
    }

    [Fact]
    public async Task GetLookupDataInternal_WithEmptyVRMAndVIN_ReturnsFailure()
    {
      // Arrange
      var enquiry = TestDataHelpers.CreateInvalidEnquiry();

      // Act
      var result = await _service.GetLookupDataInternal(enquiry, CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Equal("Either VRM or VIN must be provided", result.ErrorMessage);
      Assert.Null(result.Data);
    }

    [Fact]
    public async Task GetLookupDataInternal_WithWhitespaceVRMAndVIN_ReturnsFailure()
    {
      // Arrange
      var enquiry = TestDataHelpers.CreateEnquiryWithWhitespace();

      // Act
      var result = await _service.GetLookupDataInternal(enquiry, CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Equal("Either VRM or VIN must be provided", result.ErrorMessage);
      Assert.Null(result.Data);
    }

    [Fact]
    public async Task GetLookupDataInternal_WithValidVRM_ReturnsSuccess()
    {
      // Arrange
      var enquiry = TestDataHelpers.CreateValidEnquiryWithVRM();

      // Act
      var result = await _service.GetLookupDataInternal(enquiry, CancellationToken.None);

      // Assert
      Assert.True(result.Success);
      Assert.NotNull(result.Data);
      Assert.Null(result.ErrorMessage);
    }

    [Fact]
    public async Task GetLookupDataInternal_WithValidVIN_ReturnsSuccess()
    {
      // Arrange
      var enquiry = TestDataHelpers.CreateValidEnquiryWithVIN();

      // Act
      var result = await _service.GetLookupDataInternal(enquiry, CancellationToken.None);

      // Assert
      Assert.True(result.Success);
      Assert.NotNull(result.Data);
      Assert.Null(result.ErrorMessage);
    }

    #endregion

    #region Exception Handling Tests

    [Fact]
    public async Task GetLookupDataInternal_WhenLookupServiceThrowsArgumentNullException_ReturnsFailure()
    {
      // Arrange
      var enquiry = TestDataHelpers.CreateValidEnquiryWithVRM();
      _mockLookupService.Setup(x => x.GetICVehicleLookupData(It.IsAny<ICVehicleEnquiryDTO>(), It.IsAny<CancellationToken>()))
          .ThrowsAsync(new ArgumentNullException("test parameter"));

      // Act
      var result = await _service.GetLookupDataInternal(enquiry, CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Contains("Invalid request:", result.ErrorMessage);
      Assert.Contains("test parameter", result.ErrorMessage);
    }

    [Fact]
    public async Task GetLookupDataInternal_WhenLookupServiceThrowsArgumentException_ReturnsFailure()
    {
      // Arrange
      var enquiry = TestDataHelpers.CreateValidEnquiryWithVRM();
      _mockLookupService.Setup(x => x.GetICVehicleLookupData(It.IsAny<ICVehicleEnquiryDTO>(), It.IsAny<CancellationToken>()))
          .ThrowsAsync(new ArgumentException("Invalid argument"));

      // Act
      var result = await _service.GetLookupDataInternal(enquiry, CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Contains("Invalid request:", result.ErrorMessage);
      Assert.Contains("Invalid argument", result.ErrorMessage);
    }

    [Fact]
    public async Task GetLookupDataInternal_WhenLookupServiceThrowsGenericException_ReturnsFailure()
    {
      // Arrange
      var enquiry = TestDataHelpers.CreateValidEnquiryWithVRM();
      _mockLookupService.Setup(x => x.GetICVehicleLookupData(It.IsAny<ICVehicleEnquiryDTO>(), It.IsAny<CancellationToken>()))
          .ThrowsAsync(new InvalidOperationException("Service unavailable"));

      // Act
      var result = await _service.GetLookupDataInternal(enquiry, CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Contains("Failed to getlookupdatainternal for VRM:", result.ErrorMessage);
      Assert.Contains("Service unavailable", result.ErrorMessage);
    }

    #endregion

    #region GetLookupDataInternal Tests

    [Fact]
    public async Task GetLookupDataInternal_WhenLookupServiceReturnsFailure_ReturnsFailure()
    {
      // Arrange
      var enquiry = TestDataHelpers.CreateValidEnquiryWithVRM();
      _mockLookupService.Setup(x => x.GetICVehicleLookupData(It.IsAny<ICVehicleEnquiryDTO>(), It.IsAny<CancellationToken>()))
          .ReturnsAsync(ICVehicleLookupResult.CreateFailure("Lookup service error"));

      // Act
      var result = await _service.GetLookupDataInternal(enquiry, CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Equal("Lookup service error", result.ErrorMessage);
    }

    [Fact]
    public async Task GetLookupDataInternal_WhenSuccessful_CallsVehicleServiceAndReturnsSuccess()
    {
      // Arrange
      var enquiry = TestDataHelpers.CreateValidEnquiryWithVRM();
      var lookupData = MockHelpers.CreateSampleLookupData();
      var vehicleDto = MockHelpers.CreateSampleVehicleDTO();

      _mockLookupService.Setup(x => x.GetICVehicleLookupData(It.IsAny<ICVehicleEnquiryDTO>(), It.IsAny<CancellationToken>()))
          .ReturnsAsync(ICVehicleLookupResult.CreateSuccess(lookupData));

      _mockVehicleService.Setup(x => x.GetOrCreateICVehicleData(It.IsAny<ICVehicleEnquiryDTO>(), It.IsAny<ICVehicleLookupDataDTO>(), It.IsAny<CancellationToken>()))
          .ReturnsAsync(vehicleDto);

      // Act
      var result = await _service.GetLookupDataInternal(enquiry, CancellationToken.None);

      // Assert
      Assert.True(result.Success);
      Assert.NotNull(result.Data);
      Assert.Equal(vehicleDto.Id.Value, result.Data.ICVehicleId);
      _mockVehicleService.Verify(x => x.GetOrCreateICVehicleData(enquiry, lookupData, It.IsAny<CancellationToken>()), Times.Once);
    }

    #endregion

    #region GetValuationDataInternal Tests

    [Fact]
    public async Task GetValuationDataInternal_WithNullEnquiry_ReturnsFailure()
    {
      // Arrange
      ICVehicleEnquiryDTO enquiry = null;

      // Act
      var result = await _service.GetValuationDataInternal(enquiry, CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Equal("Vehicle enquiry cannot be null", result.ErrorMessage);
    }

    [Fact]
    public async Task GetValuationDataInternal_WithInvalidEnquiry_ReturnsFailure()
    {
      // Arrange
      var enquiry = TestDataHelpers.CreateInvalidEnquiry();

      // Act
      var result = await _service.GetValuationDataInternal(enquiry, CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Equal("Either VRM or VIN must be provided", result.ErrorMessage);
    }

    [Fact]
    public async Task GetValuationDataInternal_WithValidEnquiry_ReturnsSuccess()
    {
      // Arrange
      var enquiry = TestDataHelpers.CreateValidEnquiryWithVRM();

      // Act
      var result = await _service.GetValuationDataInternal(enquiry, CancellationToken.None);

      // Assert
      Assert.True(result.Success);
      Assert.NotNull(result.Data);
      Assert.NotNull(result.Data.ICVehicle);
    }

    #endregion

    #region GetFeatureValuationInternal Tests

    [Fact]
    public async Task GetFeatureValuationInternal_WhenResponseNotFound_ReturnsFailure()
    {
      // Arrange
      var enquiry = TestDataHelpers.CreateValidEnquiryWithVRM();
      _mockResponseService.Setup(x => x.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
          .ReturnsAsync((ICResponseDTO)null);

      // Act
      var result = await _service.GetFeatureValuationInternal(enquiry, CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Equal("No ICVehicle data found for the provided ResponseId", result.ErrorMessage);
    }

    [Fact]
    public async Task GetFeatureValuationInternal_WhenVehicleHasNoVRMOrVIN_ReturnsFailure()
    {
      // Arrange
      var enquiry = TestDataHelpers.CreateValidEnquiryWithVRM();
      var response = MockHelpers.CreateSampleResponseDTO();
      response.ICVehicle.VRM = null;
      response.ICVehicle.VIN = null;

      _mockResponseService.Setup(x => x.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
          .ReturnsAsync(response);

      // Act
      var result = await _service.GetFeatureValuationInternal(enquiry, CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Equal("Incomplete feature data - missing derivative ID or first registration date", result.ErrorMessage);
    }

    #endregion

    #region AutoTrader Integration Tests

    [Fact]
    public async Task GetAutoTraderValuationAsync_WithNullVRM_ReturnsFailure()
    {
      // Act
      var result = await _service.GetAutoTraderValuationAsync(null, 50000, CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Equal("VRM cannot be null or empty", result.ErrorMessage);
    }

    [Fact]
    public async Task GetAutoTraderValuationAsync_WithEmptyVRM_ReturnsFailure()
    {
      // Act
      var result = await _service.GetAutoTraderValuationAsync("", 50000, CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Equal("VRM cannot be null or empty", result.ErrorMessage);
    }

    [Fact]
    public async Task GetAutoTraderValuationAsync_WithWhitespaceVRM_ReturnsFailure()
    {
      // Act
      var result = await _service.GetAutoTraderValuationAsync("   ", 50000, CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Equal("VRM cannot be null or empty", result.ErrorMessage);
    }

    [Fact]
    public async Task GetAutoTraderValuationAsync_WithNegativeOdometer_ReturnsFailure()
    {
      // Act
      var result = await _service.GetAutoTraderValuationAsync("TEST123", -1, CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Equal("Odometer reading must be non-negative", result.ErrorMessage);
    }

    [Fact]
    public async Task GetAutoTraderValuationAsync_WhenAuthenticationFails_ReturnsFailure()
    {
      // Arrange
      _mockAutoTraderClient.Setup(x => x.AuthenticateAsync(It.IsAny<CancellationToken>()))
          .ThrowsAsync(new UnauthorizedAccessException("Authentication failed"));

      // Act
      var result = await _service.GetAutoTraderValuationAsync("TEST123", 50000, CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Equal("AutoTrader authentication failed", result.ErrorMessage);
    }

    [Fact]
    public async Task GetAutoTraderValuationAsync_WithValidParameters_ReturnsSuccess()
    {
      // Act
      var result = await _service.GetAutoTraderValuationAsync("TEST123", 50000, CancellationToken.None);

      // Assert
      Assert.True(result.Success);
      Assert.NotNull(result.Data);
    }

    #endregion

    #region CAP Data Tests

    [Fact]
    public async Task GetCapDataAsync_WithNullEnquiry_ReturnsFailure()
    {
      // Act
      var result = await _service.GetCapDataAsync(null, CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Equal("Vehicle enquiry cannot be null", result.ErrorMessage);
    }

    [Fact]
    public async Task GetCapDataAsync_WithInvalidEnquiry_ReturnsFailure()
    {
      // Arrange
      var enquiry = TestDataHelpers.CreateInvalidEnquiry();

      // Act
      var result = await _service.GetCapDataAsync(enquiry, CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Equal("Either VRM or VIN must be provided", result.ErrorMessage);
    }

    [Fact]
    public async Task GetCapDataAsync_WhenLookupServiceFails_ReturnsFailure()
    {
      // Arrange
      var enquiry = TestDataHelpers.CreateValidEnquiryWithVRM();
      _mockLookupService.Setup(x => x.GetICVehicleValuationData(It.IsAny<ICVehicleEnquiryDTO>(), It.IsAny<CancellationToken>()))
          .ReturnsAsync(ICCapDataResult.CreateFailure("CAP service error"));

      // Act
      var result = await _service.GetCapDataAsync(enquiry, CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Equal("CAP service error", result.ErrorMessage);
    }

    [Fact]
    public async Task GetCapDataAsync_WithValidEnquiry_ReturnsSuccess()
    {
      // Arrange
      var enquiry = TestDataHelpers.CreateValidEnquiryWithVRM();

      // Act
      var result = await _service.GetCapDataAsync(enquiry, CancellationToken.None);

      // Assert
      Assert.True(result.Success);
      Assert.NotNull(result.Data);
    }

    #endregion

    #region AutoTrader Features Tests

    [Fact]
    public async Task GetAutoTraderFeaturesAsync_WithNullVRM_ReturnsFailure()
    {
      // Act
      var result = await _service.GetAutoTraderFeaturesAsync(null, CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Equal("VRM cannot be null or empty", result.ErrorMessage);
    }

    [Fact]
    public async Task GetAutoTraderFeaturesAsync_WithEmptyVRM_ReturnsFailure()
    {
      // Act
      var result = await _service.GetAutoTraderFeaturesAsync("", CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Equal("VRM cannot be null or empty", result.ErrorMessage);
    }

    [Fact]
    public async Task GetAutoTraderFeaturesAsync_WhenAuthenticationFails_ReturnsFailure()
    {
      // Arrange
      _mockAutoTraderClient.Setup(x => x.AuthenticateAsync(It.IsAny<CancellationToken>()))
          .ThrowsAsync(new UnauthorizedAccessException("Authentication failed"));

      // Act
      var result = await _service.GetAutoTraderFeaturesAsync("TEST123", CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Equal("AutoTrader authentication failed", result.ErrorMessage);
    }

    [Fact]
    public async Task GetAutoTraderFeaturesAsync_WithValidVRM_ReturnsSuccess()
    {
      // Act
      var result = await _service.GetAutoTraderFeaturesAsync("TEST123", CancellationToken.None);

      // Assert
      Assert.True(result.Success);
      Assert.NotNull(result.Data);
    }

    #endregion

    #region AutoTrader Metrics Tests

    [Fact]
    public async Task GetAutoTraderMetricsAsync_WithNullVRM_ReturnsFailure()
    {
      // Act
      var result = await _service.GetAutoTraderMetricsAsync(null, 50000, CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Equal("VRM cannot be null or empty", result.ErrorMessage);
    }

    [Fact]
    public async Task GetAutoTraderMetricsAsync_WithEmptyVRM_ReturnsFailure()
    {
      // Act
      var result = await _service.GetAutoTraderMetricsAsync("", 50000, CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Equal("VRM cannot be null or empty", result.ErrorMessage);
    }

    [Fact]
    public async Task GetAutoTraderMetricsAsync_WithNegativeOdometer_ReturnsFailure()
    {
      // Act
      var result = await _service.GetAutoTraderMetricsAsync("TEST123", -1, CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Equal("Odometer reading must be non-negative", result.ErrorMessage);
    }

    [Fact]
    public async Task GetAutoTraderMetricsAsync_WhenAuthenticationFails_ReturnsFailure()
    {
      // Arrange
      _mockAutoTraderClient.Setup(x => x.AuthenticateAsync(It.IsAny<CancellationToken>()))
          .ThrowsAsync(new UnauthorizedAccessException("Authentication failed"));

      // Act
      var result = await _service.GetAutoTraderMetricsAsync("TEST123", 50000, CancellationToken.None);

      // Assert
      Assert.False(result.Success);
      Assert.Equal("AutoTrader authentication failed", result.ErrorMessage);
    }

    [Fact]
    public async Task GetAutoTraderMetricsAsync_WithValidParameters_ReturnsSuccess()
    {
      // Act
      var result = await _service.GetAutoTraderMetricsAsync("TEST123", 50000, CancellationToken.None);

      // Assert
      Assert.True(result.Success);
      Assert.NotNull(result.Data);
    }

    #endregion

    #region Integration Tests

    [Fact]
    public async Task GetValuationDataInternal_WhenCapDataFails_ThrowsException()
    {
      // Arrange
      var enquiry = TestDataHelpers.CreateValidEnquiryWithVRM();
      _mockLookupService.Setup(x => x.GetICVehicleValuationData(It.IsAny<ICVehicleEnquiryDTO>(), It.IsAny<CancellationToken>()))
          .ReturnsAsync(ICCapDataResult.CreateFailure("CAP service unavailable"));

      // Act & Assert
      var result = await _service.GetValuationDataInternal(enquiry, CancellationToken.None);

      // The service should handle this gracefully and return a failure result
      Assert.False(result.Success);
      Assert.Contains("Failed to getvaluationdatainternal for VR", result.ErrorMessage);
    }

    [Fact]
    public async Task GetValuationDataInternal_WithCompleteData_ProcessesSuccessfully()
    {
      // Arrange
      var enquiry = TestDataHelpers.CreateValidEnquiryWithVRM();

      // Act
      var result = await _service.GetValuationDataInternal(enquiry, CancellationToken.None);

      // Assert
      Assert.True(result.Success);
      Assert.NotNull(result.Data);
      Assert.NotNull(result.Data.ICVehicle);

      // Verify that all services were called
      _mockLookupService.Verify(x => x.GetICVehicleValuationData(It.IsAny<ICVehicleEnquiryDTO>(), It.IsAny<CancellationToken>()), Times.Once);
      _mockAutoTraderClient.Verify(x => x.AuthenticateAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    #endregion
  }
}
