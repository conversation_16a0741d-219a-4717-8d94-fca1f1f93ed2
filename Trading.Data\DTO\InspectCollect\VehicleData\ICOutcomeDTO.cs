﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.DTO.AutoTrader;
using Trading.API.Data.Enums.InspectCollect;

namespace Trading.API.Data.DTO.InspectCollect.VehicleData;

public class ICOutcomeDTO : BaseModelEntityIntDTO
{
  public Guid? ICContainerGroupId { get; set; }
  public ICContainerGroupDTO ICContainerGroup { get; set; }
  public string Outcome { get; set; }
  public string Colour { get; set; }
  public string ExternalLocationRef { get; set; }
  public ICOutcomeTypeEnum OutcomeType { get; set; }
}

public class ICOutcomeSearchDTO : BaseSearchDTO
{
  public ICOutcomeSearchFilters Filters { get; set; } = new ICOutcomeSearchFilters();
}

public class ICOutcomeSearchFilters : BaseFilterInt
{
  public string Outcome { get; set; }
  public Guid? ICContainerGroupId { get; set; }
  public ICOutcomeTypeEnum? OutcomeType { get; set; }
  public string Colour { get; set; }
}

public class ICOutcomeCreateDTO
{
  public Guid ICContainerGroupId { get; set; }
  public string Outcome { get; set; }
  public string Colour { get; set; }
  public string ExternalLocationRef { get; set; }
  public ICOutcomeTypeEnum OutcomeType { get; set; }
}
