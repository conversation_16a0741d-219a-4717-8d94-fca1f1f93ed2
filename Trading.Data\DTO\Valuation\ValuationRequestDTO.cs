﻿using System;
using Trading.API.Data.Enums.Valuation;

namespace Trading.API.Data.DTO.Valuation
{
  /// <summary>
  /// Fetches a Valuation
  /// </summary>
  public class ValuationRequestDTO
  {
    public string VRM { get; set; }

    public bool TestValuation { get; set; }

    public string CapId { get; set; }

    public uint? Mileage { get; set; }

    public ValuationAggregateTypeEnum AggregateType { get; set; }

    // external appraisal request fields 
    public Guid? LeadVehicleId { get; set; }
    public string ExternalAppraisalCode { get; set; }

    public string Name { get; set; }
    public string Email { get; set; }
    public string Mobile { get; set; }
    public string Phone { get; set; }

    public ValuationQuoteTypeEnum QuoteType { get; set; } = ValuationQuoteTypeEnum.Estimate;

    public bool CreateTestQuote { get; set; }
  }
}
