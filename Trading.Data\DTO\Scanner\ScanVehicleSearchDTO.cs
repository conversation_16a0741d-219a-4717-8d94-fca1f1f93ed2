﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.API.Data.Models.DTO;

namespace Trading.API.Data.DTO
{
  public class ScanVehicleFiltersDTO : BaseFilterGuid
  {
    public Guid? VehicleId { get; set; }
    public string Vrm { get; set; }
    public uint? MakeId { get; set; }
    public List<uint> MakeIds { get; set; } = new List<uint>();
    public uint? ModelId { get; set; }
    public List<uint> ModelIds { get; set; } = new List<uint>();
    public uint? DerivId { get; set; }
    public List<uint> DerivIds { get; set; } = new List<uint>();
    public uint? PlateId { get; set; }
    public List<uint> PlateIds { get; set; } = new List<uint>();
    public uint? FuelTypeId { get; set; }
    public List<uint> FuelTypeIds { get; set; } = new List<uint>();
    public uint? ScanCustomerId { get; set; }
    public Guid? CustomerId { get; set; }
    public bool HaveVRM { get; set; }
  }

  public class ScanVehicleSearchDTO: BaseSearchDTO
  {
    public ScanVehicleFiltersDTO Filters { get; set; } = new ScanVehicleFiltersDTO() { };
    public bool? CountOnly { get; set; } = false;
    public string GroupBy { get; set; }
    public string GroupLabel { get; set; }
    public int? PageNumber { get; set; }
    public int? ResultsPerPage { get; set; }
  }

  public class ScanVehicleSearchResultDTO
  {
    public int? Status { get; set; }
    public int? TotalResults { get; set; }
    public int? PageNumber { get; set; }
    public int? ResultsPerPage { get; set; }
    public IEnumerable<ScanVehicleSearchResultItemDTO> Results { get; set; }


  }
  public class ScanVehicleFilterResultDTO
  {
    public int? Status { get; set; }
    public IEnumerable<ScanVehicleFilterResultItem> FilterItemResults { get; set; }

  }
  public class ScanVehicleFilterResultItem
  {
    public int? groupId { get; set; }
    public string groupLabel { get; set; }
    public int? groupCount { get; set; }
  }

  public class ScanVehicleSearchResultItemDTO
  {
    public uint Id { get; set; }
    public uint ScanCustomerId { get; set; }
    public string Vrm { get; set; }
    public Guid ScanVehicleGuid { get; set; }
    public string Title { get; set; }
    public string PrimaryImageUrl { get; set; }
    public string CompanyName { get; set; }
    public string MakeName { get; set; }
    public string ModelName { get; set; }
    public string DerivName { get; set; }
    public string FuelTypeName { get; set; }
    public string TransmissionTypeName { get; set; }
    public string BodyTypeName { get; set; }
    public string PlateName { get; set; }
    public string CO2 { get; set; }
    public uint Doors { get; set; }
    public uint Odometer { get; set; }
    public uint ImageCount { get; set; }
    public uint EngineCC { get; set; }
    public uint BatteryKwH { get; set; }
    public uint Price { get; set; }
    public string Colour { get; set; }
    public string UniqueId { get; set; }
    public DateTime? DateRegistered { get; set; }
    public List<ScanImageDTO> ScanImages { get; set; }
  }
}
