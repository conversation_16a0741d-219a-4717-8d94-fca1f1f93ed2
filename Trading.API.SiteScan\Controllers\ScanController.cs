using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.API.Data.Models.DTO;
using Trading.Services.SiteScan.Interfaces;

namespace Trading.API.SiteScan.Controllers
{
  [Route("api/scan")]
  [ApiController]
  public class ScanController : ControllerBase
  {
    public IMapper _mapper;
    public IScanningService _scanService;
    public IScanQueueService _scanQueueService;
    public IScanBatchService _scanBatchService;
    public IScanSampleService _scanSampleService;
    public IScanErrorService _scanErrorService;
    public IScanVehicleService _scanVehicleService;

    public ScanController(
      IScanningService scanService,
      IScanQueueService scanQueueService,
      IScanBatchService scanBatchService,
      IScanSampleService scanSampleService,
      IScanVehicleService scanVehicleService,
      IScanErrorService scanErrorService,
      IMapper mapper)
    {
      _scanService = scanService;
      _scanQueueService = scanQueueService;
      _scanBatchService = scanBatchService;
      _scanSampleService = scanSampleService;
      _scanErrorService = scanErrorService;
      _mapper = mapper;
    }

    [HttpGet]
    [Route("{id}/started")]
    public async Task<ActionResult> ScanStarted(uint id, CancellationToken ct)
    {
      await _scanService.ScanStarted(id, ct);

      return Ok();
    }

    [HttpPost]
    [Route("{scanQueueId}/ended")]
    public async Task<ActionResult> ScanEnded(uint scanQueueId, CancellationToken ct)
    {
      string json;
      using (StreamReader reader = new StreamReader(Request.Body))
      {
        json = await reader.ReadToEndAsync();
      }

      LambdaScanEndedResponseDTO data = null;

      var dynamic = JsonConvert.DeserializeObject<dynamic>(json);

      try
      {
        data = JsonConvert.DeserializeObject<LambdaScanEndedResponseDTO>(json);
      }
      catch (Exception ex)
      {

      }

      await _scanService.ScanEnded(scanQueueId, ct);

      var ok = await _scanErrorService.SaveScanErrors(scanQueueId, data, ct);

      if (data.save_sample != null && data.save_sample == true && data.sample_data != null)
      {
        var doubleQuoteJson = "";
        
        var scanCache = new ScanCache();

        foreach (var stageResponse in data.sample_data)
        {
          var stageId = stageResponse.stage_id;

          await _scanSampleService.SaveScanSample(scanQueueId, stageId, stageResponse, data.raw_response, ct);
        }
      }

      return Ok();
    }

    /* This gets the most recent preview code that's been requested for a customer (even if its already been run) */
    [HttpGet]
    [Route("scanCustomer/{scanCustomerId}/previewLambdaBody")]
    public async Task<IActionResult> GetPreviewCode(uint scanCustomerId, CancellationToken ct)
    {
      var z = await _scanBatchService.GetCustomerPreviewLambdaPayload(scanCustomerId, ct);
      return Ok(z);
    }

    /* This gets the next unprocessed scan queue item that's been requested for a customer */
    [HttpGet]
    [Route("scanCustomer/{scanCustomerId}/nextLambdaBody")]
    public async Task<IActionResult> GetNextCustomerQueueCode(uint scanCustomerId, CancellationToken ct)
    {
      var z = await _scanBatchService.GetCustomerQueueLambdaPayload(scanCustomerId, ct);
      return Ok(z);
    }

    [HttpGet]
    [Route("requestPreview")]
    public async Task<uint> RequestPreview([FromQuery] string query, CancellationToken ct)
    {
      ScanPreviewRequestDTO dto = new ScanPreviewRequestDTO();
      if (query != null)
      {
        dto = JsonConvert.DeserializeObject<ScanPreviewRequestDTO>(query);
      }

      var scanQueueId = await _scanService.RequestPreview(dto, ct);

      return scanQueueId;
    }

    [HttpGet]
    [Route("queue/{queueId}/viewCache")]
    public async Task<IActionResult> ViewCache(uint scanQueueId, CancellationToken ct)
    {
      var html = await _scanService.ScanPageCache(scanQueueId, ct);

      return Ok(html);
    }

    [HttpGet]
    [Route("stats")]
    public async Task<IActionResult> Stats(CancellationToken ct)
    {
      ScanStatsDTO stats = await _scanService.Stats(ct);
      return Ok(stats);
    }

    [HttpGet]
    [AllowAnonymous]
    [Route("updateStats")]
    public async Task<IActionResult> UpdateStats(CancellationToken ct)
    {
      var ok = await _scanService.UpdateStats(ct);
      return Ok();
    }


    [HttpGet]
    [Route("scanCustomer/{scanCustomerId}/scheduleScan")]
    public async Task<IActionResult> ScheduleScan(uint scanCustomerId, CancellationToken ct)
    {
      var x = await _scanQueueService.QueueCustomer(scanCustomerId, ct);
      var y = await _scanBatchService.BatchScanQueueItems(false, null, null);
      var z = await _scanBatchService.PublishBatches();
      return Ok();
    }

    [HttpGet]
    [Route("batchScans")]
    public async Task<IActionResult> BatchScans(CancellationToken ct)
    {
      uint batchedItems = 99;

      while (batchedItems > 0)
      {
        batchedItems = await _scanBatchService.BatchScanQueueItems(true, null, null);
      }

      return Ok();
    }


    [HttpGet]
    [Route("scheduleScans")]
    public async Task<IActionResult> ScheduleScans(CancellationToken cancellationToken)
    {
      var x = await _scanService.ScheduleScans(cancellationToken);

      return Ok(x);
    }

    [HttpGet]
    [Route("scanWorker")]
    public async Task<IActionResult> ScanWorker(CancellationToken cancellationToken)
    {
      var response = await _scanService.ScanWorker(cancellationToken);

      return Ok(response);
    }

    /* ---- SCAN SAMPLES ----- */

    [HttpGet]
    [Route("samples")]
    public async Task<IActionResult> GetScanSamples([FromQuery] string query, CancellationToken cancellationToken)
    {
      ScanSampleSearchDTO dto = new ScanSampleSearchDTO();
      if (query != null)
      {
        dto = JsonConvert.DeserializeObject<ScanSampleSearchDTO>(query);
      }

      var response = await _scanSampleService.Search(dto, cancellationToken);

      return Ok(response);
    }

    /* ---- SCAN QUEUES ----- */

    [HttpGet]
    [Route("queues")]
    public async Task<IActionResult> GetScanQueues([FromQuery] string query, CancellationToken cancellationToken)
    {
      ScanQueueSearchDTO dto = new ScanQueueSearchDTO();
      if (query != null)
      {
        dto = JsonConvert.DeserializeObject<ScanQueueSearchDTO>(query);
      }

      var response = await _scanQueueService.Search(dto, cancellationToken);

      return Ok(response);
    }

    /* ---- SCAN FIELDS ----- */

    [HttpGet]
    [Route("fields")]
    public async Task<IActionResult> GetScanFields([FromQuery] string query, CancellationToken cancellationToken)
    {
      ScanFieldSearchDTO dto = new ScanFieldSearchDTO();
      if (query != null)
      {
        dto = JsonConvert.DeserializeObject<ScanFieldSearchDTO>(query);
      }

      var response = await _scanService.SearchScanFields(dto, cancellationToken);

      return Ok(response);
    }

    [HttpPost]
    [Route("field")]
    public async Task<IActionResult> AddScanFields([FromBody] ScanFieldDTO dto, CancellationToken cancellationToken)
    {
      var x = await _scanService.AddScanFields(dto, cancellationToken);

      return Ok(x);
    }

    [HttpPatch]
    [Route("field/{scanFieldId}")]
    public async Task<IActionResult> PatchScanFields(uint scanFieldId, [FromBody] JsonPatchDocument<ScanField> patch, CancellationToken ct)
    {
      var x = await _scanService.PatchScanField(scanFieldId, patch, ct);

      return Ok(x);
    }

    /* ------- SCAN SERVICES -------- */

    [HttpGet]
    [Route("services")]
    public async Task<IActionResult> GetScanServices([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = new ScanServiceSearchDTO();
      if (query != null) { dto = JsonConvert.DeserializeObject<ScanServiceSearchDTO>(query); }

      var response = await _scanService.SearchScanServices(dto, cancellationToken);

      return Ok(response);
    }

    [HttpPost]
    [Route("service")]
    public async Task<IActionResult> AddScanService([FromBody] ScanServiceDTO dto, CancellationToken cancellationToken)
    {
      var x = await _scanService.AddScanService(dto, cancellationToken);

      return Ok(x);
    }

    [HttpPatch]
    [Route("service/{scanServiceId}")]
    public async Task<IActionResult> PatchScanService(uint scanServiceId, [FromBody] JsonPatchDocument<ScanService> patch, CancellationToken ct)
    {
      var x = await _scanService.PatchScanService(scanServiceId, patch, ct);

      return Ok(x);
    }

    /* ------ SCAN CUSTOMERS --------- */

    [HttpGet]
    [Route("customers")]
    public async Task<IActionResult> GetScanCustomers([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = new ScanCustomerSearchDTO();
      if (query != null) { dto = JsonConvert.DeserializeObject<ScanCustomerSearchDTO>(query); }

      var response = await _scanService.SearchScanCustomers(dto, cancellationToken);

      return Ok(response);
    }

    [HttpGet]
    [Route("customer/{scanCustomerId}")]
    public async Task<IActionResult> GetScanCustomer(uint scanCustomerId, [FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = new ScanCustomerSearchDTO();

      if (query != null) { dto = JsonConvert.DeserializeObject<ScanCustomerSearchDTO>(query); }

      dto.NoTracking = true;

      var response = await _scanService.GetScanCustomer(scanCustomerId, dto, cancellationToken);

      var x = _mapper.Map<ScanCustomer, ScanCustomerDTO>(response);

      return Ok(x);
    }

    [HttpPost]
    [Route("customer")]
    public async Task<IActionResult> AddScanCustomer([FromBody] ScanCustomerDTO dto, CancellationToken cancellationToken)
    {
      var x = await _scanService.AddScanCustomer(dto, cancellationToken);

      return Ok(x);
    }
    [HttpPatch]
    [Route("customer/{scanCustomerId}")]
    public async Task<IActionResult> PatchScanCustomer(uint scanCustomerId, [FromBody] JsonPatchDocument<ScanCustomer> patch, CancellationToken ct)
    {
      var x = await _scanService.PatchScanCustomer(scanCustomerId, patch, ct);

      return Ok(x);
    }
    [HttpDelete]
    [Route("customer/{scanCustomerId}")]
    public async Task<IActionResult> DeleteScanCustomer(uint scanCustomerId, CancellationToken ct)
    {
      var x = await _scanService.DeleteScanCustomer(scanCustomerId, ct);

      return Ok(x);
    }

    /* -------- SCAN STYLES --------- */

    [HttpGet]
    [Route("style/{scanStyleId}")]
    public async Task<IActionResult> GetScanStyle(uint scanStyleId, [FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = new ScanStyleSearchDTO();
      if (query != null) { dto = JsonConvert.DeserializeObject<ScanStyleSearchDTO>(query); }

      dto.Filters.Id = scanStyleId;

      var response = await _scanService.GetScanStyle(scanStyleId, dto, cancellationToken);

      if (response != null)
      {
        return Ok(_mapper.Map<ScanStyle, ScanStyleDTO>(response));
      }
      return NotFound();
    }

    [HttpGet]
    [Route("styles")]
    public async Task<IActionResult> GetScanStyles([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = new ScanStyleSearchDTO();
      if (query != null) { dto = JsonConvert.DeserializeObject<ScanStyleSearchDTO>(query); }

      var response = await _scanService.SearchScanStyles(dto, cancellationToken);

      return Ok(response);
    }

    [HttpPost]
    [Route("style")]
    public async Task<IActionResult> AddScanStyle([FromBody] ScanStyleDTO dto, CancellationToken cancellationToken)
    {
      var x = await _scanService.AddScanStyle(dto, cancellationToken);

      return Ok(x);
    }
    [HttpPatch]
    [Route("style/{scanStyleId}")]
    public async Task<IActionResult> PatchScanStyle(uint scanStyleId, [FromBody] JsonPatchDocument<ScanStyle> patch, CancellationToken ct)
    {
      var x = await _scanService.PatchScanStyle(scanStyleId, patch, ct);

      return Ok(x);
    }
    [HttpDelete]
    [Route("style/{scanStyleId}")]
    public async Task<IActionResult> DeleteScanStyle(uint scanStyleId, CancellationToken ct)
    {
      var x = await _scanService.DeleteScanStyle(scanStyleId, ct);

      return Ok(x);
    }

    /* -------- SCAN STAGES --------- */

    [HttpGet]
    [Route("stages")]
    public async Task<IActionResult> GetScanStages([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ScanStageSearchDTO>(query);

      var response = await _scanService.SearchScanStages(dto, cancellationToken);

      return Ok(response);
    }

    [HttpGet]
    [Route("stage/{scanStageId}")]
    public async Task<IActionResult> GetScanStage(uint scanStageId, [FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ScanStageSearchDTO>(query);

      var response = await _scanService.GetScanStage(scanStageId, dto, cancellationToken);

      return Ok(response);
    }

    [HttpPost]
    [Route("stage")]
    public async Task<IActionResult> AddScanStage([FromBody] ScanStageDTO dto, CancellationToken cancellationToken)
    {
      var x = await _scanService.AddScanStage(dto, cancellationToken);

      return Ok(x);
    }
    [HttpPatch]
    [Route("stage/{scanStageId}")]
    public async Task<IActionResult> PatchScanStage(uint scanStageId, [FromBody] JsonPatchDocument<ScanStage> patch, CancellationToken ct)
    {
      var x = await _scanService.PatchScanStage(scanStageId, patch, ct);

      return Ok(x);
    }
    [HttpDelete]
    [Route("stage/{scanStageId}")]
    public async Task<IActionResult> DeleteScanStage(uint scanStageId, CancellationToken ct)
    {
      var x = await _scanService.DeleteScanStage(scanStageId, ct);

      return Ok(x);
    }

    /* ------ SCAN CONFIG -------- */

    [HttpPost]
    [Route("stage/{scanStageId}/config")]
    public async Task<IActionResult> SetScanConfig(uint scanStageId, ScanConfigDTO dto, CancellationToken cancellationToken)
    {
      var x = await _scanService.SetScanConfig(scanStageId, dto, cancellationToken);

      return Ok(x);
    }

    /* ------ SCAN VEHICLES -------- */

    [HttpPost]
    [Route("vehicle")]
    public async Task<ActionResult> Vehicle([FromBody] SetScanVehicleDTO data, CancellationToken ct)
    {
      var returnVal = await _scanService.SetScanVehicle(data, ct);

      return Ok(returnVal);
    }

    /* ------ SCAN VEHICLE IAMGES -------- */

    [HttpPost]
    [Route("vehicle/{scanVehicleId}/images")]
    public async Task<ActionResult> VehicleImages(uint scanVehicleId, [FromBody] SetScanVehicleImagesDTO data, CancellationToken ct)
    {
      var returnVal = await _scanService.SetScanVehicleImages(scanVehicleId, data, ct);

      return Ok(returnVal);
    }

    /* SCAN ERRORS */

    [HttpGet]
    [Route("errorSummary")]
    public async Task<ActionResult> ScanErrorSummary(CancellationToken ct)
    {
      var result = await _scanErrorService.ScanErrorSummary(ct);

      return Ok(result);
    }

    [HttpGet]
    [Route("errorDetail")]
    public async Task<ActionResult> ScanErrorDetail([FromQuery] string query, CancellationToken ct)
    {
      var dto = JsonConvert.DeserializeObject<ScanErrorSearchDTO>(query);

      var result = await _scanErrorService.ScanErrorDetail(dto, ct);

      return Ok(result);
    }

    [HttpPost]
    [Route("clearErrors")]
    public async Task<ActionResult> ClearErrors([FromBody] ClearScanErrorDTO dto, CancellationToken ct)
    {
      var result = await _scanErrorService.ClearErrors(dto, ct);

      return Ok(result);
    }
 

  }
}
