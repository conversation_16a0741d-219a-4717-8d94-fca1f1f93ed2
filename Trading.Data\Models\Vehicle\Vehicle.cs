﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Trading.API.Data.Enums;
using Trading.API.Data.Models.InventData;
using Trading.API.Data.Models.MechanicalFaults;

#nullable disable

namespace Trading.API.Data.Models
{
  public partial class Vehicle : BaseModelEntity
  {
    public Vehicle()
    {
      Adverts = new HashSet<Advert>();
      Appraisals = new HashSet<Appraisal>();
      VehicleAttribs = new HashSet<VehicleAttrib>();
      VehicleChecks = new HashSet<VehicleCheck>();
      VehicleMedia = new HashSet<VehicleMedia>();
      MOTHistory = new HashSet<MOTHistory>();
      ServiceHistories = new HashSet<ServiceHistory>();
    }

    public Guid? CustomerId { get; set; }
    public Guid? ContactId { get; set; }
    public uint? VehicleTypeId { get; set; }

    [MaxLength(10)]
    public string Vrm { get; set; }

    [MaxLength(45)]
    public string Colour { get; set; }

    //public Guid VehicleMediaId { get; set; }
    public Guid? PrimaryImageId { get; set; }
    public uint? FuelTypeId { get; set; }
    public uint? BodyTypeId { get; set; }
    public uint? TransmissionTypeId { get; set; }
    public uint? PlateId { get; set; }
    [MaxLength(45)]
    public string CustomerRef { get; set; }

    public Guid? LatestProvenanceId { get; set; }

    public VehicleCheck LatestProvenance { get; set; }

    public Guid? LatestValuationId { get; set; }

    public VehicleCheck LatestValuation { get; set; }

    public Guid? LatestAIValuationId { get; set; }

    public VehicleCheck LatestAIValuation { get; set; }


    public DateTime? MotExpires { get; set; }
    public DateTime? DateOfReg { get; set; }
    public ushort? Doors { get; set; }
    public uint? Odometer { get; set; }

    public ushort Owners { get; set; }
    public ushort? EngineCc { get; set; }
    public uint? MakeId { get; set; }
    public uint? ModelId { get; set; }
    public uint? DerivId { get; set; }

    [MaxLength(30)]
    public string Vin { get; set; }
    //    public string Equipment { get; set; }

    public uint Kerbweight { get; set; }

    // public VATStatusEnum? VATStatus { get; set; }
    [ForeignKey("VatStatus")]
    public uint? VatStatusId { get; set; }

    [ForeignKey("VatStatusId")]
    public Attribval VatStatus { get; set; }

    [ForeignKey("V5StatusId")]
    public Attribval V5Status { get; set; }

    [ForeignKey("V5Status")]
    public uint? V5StatusId { get; set; }

    public bool Runner { get; set; }

    public short Grade { get; set; }

    public bool LogBook { get; set; }

    [ForeignKey("Address")]
    public Guid? AddressId { get; set; }

    [ForeignKey("AddressId")]
    public Address Address { get; set; }

    public bool ServiceHistory { get; set; }

    public DateTime? LastService { get; set; }

    public sbyte? TyreDepth_NSF { get; set; }
    public sbyte? TyreDepth_NSR { get; set; }
    public sbyte? TyreDepth_OSF { get; set; }
    public sbyte? TyreDepth_OSR { get; set; }
    public sbyte? TyreDepth_Spare { get; set; }

    public uint Co2 { get; set; }
    public uint? StandInValue { get; set; }
    public byte NoOfKeys { get; set; }

    [Column(TypeName = "decimal(5, 1)")]
    public uint BHP { get; set; }
    public uint? ImageCount { get; set; }

    // see OdometerUnitEnum for values
    public int OdometerUnit { get; set; }

    public ServiceHistoryTypeEnum ServiceHistoryType { get; set; }

    public virtual BodyType BodyType { get; set; }
    public virtual Contact Contact { get; set; }
    public virtual Customer Customer { get; set; }
    public virtual Deriv Deriv { get; set; }
    public virtual FuelType FuelType { get; set; }
    public virtual Make Make { get; set; }
    public virtual Model Model { get; set; }
    public virtual Plate Plate { get; set; }
    public virtual TransmissionType TransmissionType { get; set; }
    public virtual VehicleType VehicleType { get; set; }
    public virtual ICollection<Advert> Adverts { get; set; }
    public virtual ICollection<Appraisal> Appraisals { get; set; }
    public virtual ICollection<VehicleAttrib> VehicleAttribs { get; set; }
    public virtual ICollection<VehicleCheck> VehicleChecks { get; set; }
    public virtual ICollection<VehicleMedia> VehicleMedia { get; set; }
    public virtual ICollection<MOTHistory> MOTHistory { get; set; }
    public virtual ICollection<ServiceHistory> ServiceHistories { get; set; }

    // link to the nearest range block of mileage for searching (i.e. 1000, 5000, 10000, 20000, 30000, etc.)
    public uint? MileageRangeId { get; set; }
    public MileageRange MileageRange { get; set; }

    // link to the nearest range block of CC for searching (i.e. < 1000, 1000, 1500, 2000, 3000, > 3000 etc.)
    public uint? CapacityRangeId { get; set; }
    public CapacityRange CapacityRange { get; set; }

    public uint? VehicleColourId { get; set; }
    public VehicleColour VehicleColour { get; set; }

    public Guid? DVLADataId { get; set; }
    public DVLAData DVLAData { get; set; }

    [MaxLength(20)]
    public string CapCode { get; set; }

    [MaxLength(12)]
    public string CapId { get; set; }

    public bool ColourChanged { get; set; }

    [MaxLength(4)]
    public string Co2Rating { get; set; }
    public uint? YearOfManufacture { get; set; }

    public bool HasMOTIssues { get; set; }
    public Guid? LatestVehicleFaultCheckId { get; set; }
    public VehicleFaultCheck LatestVehicleFaultCheck { get; set; }

    public virtual ICollection<InventAuctionLot> InventAuctionLots { get; set; }
    public virtual ICollection<VehicleTyreInfo> Tyres { get; set; } = new HashSet<VehicleTyreInfo>();
  }
}
