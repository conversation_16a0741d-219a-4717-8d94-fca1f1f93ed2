﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.Totals
{
  public class CustomerAdvertisingTotalsDTO
  {
    public Guid CustomerId { get; set; }
    public string CustomerName { get; set; }
    public string CustomerEmail { get; set; }
    public string ContactName { get; set; }
    public string ContactPhone { get; set; }
    public int TotalAdverts { get; set; }
    public int LiveAdverts { get; set; }
    public DateTime? LastLoginDate { get; set; }
    public DateTime? LastAdvertDate { get; set; }
  }
}
