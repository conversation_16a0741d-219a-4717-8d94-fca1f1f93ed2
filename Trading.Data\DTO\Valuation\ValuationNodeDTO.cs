﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums.Valuation;

namespace Trading.API.Data.DTO.Valuation
{
  public class ValuationNodeDTO : BaseModelEntityDTO
  {
    public Guid? ValuationProfileId { get; set; }
    public ValuationLookupTypeEnum LookupType { get; set; }
    public string LookupName { get; set; }
    public string LookupId { get; set; }
    public uint? Pillar1 { get; set; }
    public string MakeName { get; set; }
    public uint? Pillar2 { get; set; }
    public string ModelName { get; set; }
    public uint? Pillar3 { get; set; }
    public string AgeName { get; set; }
    public uint? Pillar4 { get; set; }
    public string DerivName { get; set; }
    public uint? Pillar5 { get; set; }
    public double? AddValue { get; set; } // if > 0 then add this value to current amount 
    public double? Multiplier { get; set; }
    public bool? NoQuote { get; set; }
    public bool? CallForQuote { get; set; }
  }
}
