﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums.LeadCRM;

namespace Trading.API.Data.DTO.Search.LeadCRM
{
  public class CRMUserSearchDTO : BaseSearchDTO
  {
    public CRMUserFilters Filters { get; set; } = new CRMUserFilters() { };
  }

  public class CRMUserFilters : BaseFilterGuid
  {
    public CRMUserStatusEnum UserStatus { get; set; }
    public DateTime LastAssigned { get; set; }
  }
}
