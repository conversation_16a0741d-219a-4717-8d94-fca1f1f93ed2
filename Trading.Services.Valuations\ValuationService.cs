﻿using AutoMapper;
using Google.Apis.Util;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.EntityFrameworkCore;
using System.Threading;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.LeadCRM;
using Trading.API.Data.DTO.Search.LeadCRM;
using Trading.API.Data.DTO.Search.Valuation;
using Trading.API.Data.DTO.Valuation;
using Trading.API.Data.Enums;
using Trading.API.Data.Enums.Valuation;
using Trading.API.Data.Models;
using Trading.API.Data.Models.LeadCRM;
using Trading.API.Data.Models.Valuation;
using Trading.Services.Extensions;
using Trading.Services.ExternalDTO;
using Trading.Services.Helpers;
using Trading.Services.Interfaces;
using Trading.Services.LeadCRM.Interfaces;
using Trading.Services.Valuations.Interfaces;

namespace Trading.Services.Valuations
{
  public class ValuationService : IValuationService
  {
    private readonly TradingContext _context;
    private readonly IMapper _mapper;
    private readonly IEnumerable<IVRMLookupService> _vrmLookupServices;
    private readonly IVehicleService _vehicleService;
    private readonly IVehicleValueService _vehicleValueService;
    private readonly ILeadCRMService _leadCRMService;
    private readonly IVehicleCheckService _vehicleCheckService;
    List<string> ops = new List<string>();
    List<string> log = new List<string>();

    // list of coefficients (_multipliers) per lookup (pillars and nodes)
    Dictionary<string, ValuationNode> _multipliers = new Dictionary<string, ValuationNode>();

    public ValuationService(TradingContext context, IMapper mapper
      , IEnumerable<IVRMLookupService> vrmLookupServices
      , IVehicleService vehicleService
      , IVehicleValueService vehicleValueService
      , ILeadCRMService leadCRMService
      , IVehicleCheckService vehicleCheckService)
    {
      _context = context;
      _mapper = mapper;
      _vrmLookupServices = vrmLookupServices;
      _vehicleService = vehicleService;
      _vehicleValueService = vehicleValueService;
      _leadCRMService = leadCRMService;
      _vehicleCheckService = vehicleCheckService;
    }


    public async Task<ValidatedResultDTO<ValuationProfileDTO>> AddValuationProfile(ValuationProfileDTO dto, CancellationToken cancellationToken)
    {
      var result = new ValidatedResultDTO<ValuationProfileDTO>()
      {
        HTTPStatus = HTTPStatusEnum.OK,
        IsValid = true
      };

      var profile = _mapper.Map<ValuationProfile>(dto);

      profile.StatusId = (uint)StatusEnum.Active;
      profile.Added = DateTime.Now;
      profile.Updated = DateTime.Now;

      _context.ValuationProfiles.Add(profile);

      await _context.SaveChangesAsync(cancellationToken);

      result.DTO = _mapper.Map<ValuationProfileDTO>(profile);

      return result;
    }
    public async Task<bool> MakeProfilePrimary(Guid id, CancellationToken cancellationToken)
    {
      var profiles = await _context.ValuationProfiles.Where(x => x.IsDefault && x.Id != id).ToListAsync(cancellationToken);

      foreach (var profile in profiles)
      {
        profile.IsDefault = false;
      }

      var newProfile = await _context.ValuationProfiles.Where(x => x.Id == id).FirstOrDefaultAsync(cancellationToken);

      if (newProfile != null)
      {
        newProfile.IsDefault = true;
      }

      await _context.SaveChangesAsync(cancellationToken);

      return true;
    }

    public async Task<ValidatedResultDTO<ValuationPillarDTO>> AddPillarNode(ValuationPillarDTO pillarNode, CancellationToken cancellationToken)
    {
      var result = new ValidatedResultDTO<ValuationPillarDTO>()
      {
        HTTPStatus = HTTPStatusEnum.OK,
        IsValid = true
      };

      var exists = await _context.ValuationProfiles.AnyAsync(x => x.Id == pillarNode.ValuationProfileId, cancellationToken);
      if (!exists)
      {
        result.HTTPStatus = HTTPStatusEnum.NotFound;
        result.Message = "Valuation Profile not found";
        result.IsValid = false;
        return result;
      }

      var pillar = _mapper.Map<ValuationPillar>(pillarNode);

      pillar.StatusId = (uint)StatusEnum.Active;
      pillar.Added = DateTime.Now;

      _context.ValuationPillars.Add(pillar);

      await _context.SaveChangesAsync(cancellationToken);

      result.DTO = _mapper.Map<ValuationPillarDTO>(pillar);

      return result;
    }


    /*
    public async Task<ValidatedResultDTO<ValuationNodeDTO>> AddValuationNode(ValuationNodeDTO nodeDTO, CancellationToken cancellationToken)
    {
      var result = new ValidatedResultDTO<ValuationNodeDTO>() { IsValid = false };
      var pillar = await _context.ValuationPillars.FirstOrDefaultAsync(x => x.Id == nodeDTO.ValuationPillarId);

      if (pillar == null)
      {
        result.HTTPStatus = HTTPStatusEnum.NotFound;
        result.Message = "Valuation pillar not found";

        return result;
      }

      var node = _mapper.Map<ValuationNode>(nodeDTO);
      node.StatusId = (uint)StatusEnum.Active;
      node.Added = DateTime.Now;

      _context.ValuationNodes.Add(node);

      await _context.SaveChangesAsync();

      result.IsValid = true;
      result.HTTPStatus = HTTPStatusEnum.OK;
      result.DTO = _mapper.Map<ValuationNodeDTO>(node);

      return result;
    }
    */

    public async Task<ValidatedResultDTO<ResultFromValuationDTO>> CalculateValuation(ValuationRequestDTO request, CancellationToken cancellationToken)
    {
      var result = new ValidatedResultDTO<ResultFromValuationDTO>() { IsValid = false };

      if (string.IsNullOrEmpty(request.VRM))
      {
        result.Message = "VRM cannot be empty";
        result.HTTPStatus = HTTPStatusEnum.BadRequest;
        return result;
      }

      ops.Clear();
      log.Clear();
      _multipliers.Clear();

      ValuationProfile valuationProfile = await GetDefaultValuationProfile();

      // Get Vehicle Data based on VRM
      var lookup = _vrmLookupServices.GetVRMLookupService(request.VRM);
      VRMLookupDataDTO lookupDTO = new VRMLookupDataDTO { vrm = request.VRM, odometer = request.Mileage };

      var vehicleData = await lookup.GetVehicleData(lookupDTO, CancellationToken.None);

      if (vehicleData == null)
      {
        result.Message = "Vehicle not found";
        result.HTTPStatus = HTTPStatusEnum.NotFound;
        return result;
      }

      // If we have an external appraisalId we can get the vehicle from the crm 
      LeadVehicleDTO leadVehicleDTO = await GetLeadVehicle(request, cancellationToken);

      var vehicleDTO = leadVehicleDTO?.Vehicle;

      // no vehicle associated with the lead vehicle, so create it now with provenance data etc
      VehicleCheckDTO? valuationCheck = null;
      VehicleCheckDTO? provenanceCheck = null;

      if (vehicleDTO == null)
      {
        VehicleLookupInfoDTO lookupData = await _vehicleService.GetVehicleLookupInfoDTO(
          new CreateVehicleDTO { 
            ContactId = leadVehicleDTO.ContactId.Value, 
            Odometer = (int)request.Mileage, 
            Vrm = leadVehicleDTO.Vrm }
          , cancellationToken);

        vehicleDTO = await _vehicleService.CreateVehicleDTO(lookupData, cancellationToken);
        vehicleDTO = await _vehicleService.AddVehicle(vehicleDTO, cancellationToken);

        lookupDTO.vehicleId = vehicleDTO.Id.Value;
        var checks = await UpdateVehicleChecks(lookupDTO, cancellationToken);
        if (checks == null)
        {
          result.Message = "Could not create vehicle check records";
          result.HTTPStatus = HTTPStatusEnum.BadRequest;
          return result;
        }

        valuationCheck = checks.Value.valuation;
        provenanceCheck = checks.Value.provenance;

        // link the vehicle to the leadVehicle record 
        var patch = new JsonPatchDocument<LeadVehicle>();
        patch.Add(x => x.VehicleId, vehicleDTO.Id);
        await _leadCRMService.PatchLeadVehicle(leadVehicleDTO.Id.Value, patch, cancellationToken);
      } 
      else
      {
        lookupDTO.vehicleId = vehicleDTO.Id.Value;
        var checks = await UpdateVehicleChecks(lookupDTO, cancellationToken);
        if (checks == null)
        {
          result.Message = "Could not create vehicle check records";
          result.HTTPStatus = HTTPStatusEnum.BadRequest;
          return result;
        }

        valuationCheck = checks.Value.valuation;
        provenanceCheck = checks.Value.provenance;
      }

      if (valuationCheck == null)
      {
        // some vehicles have no valuation data, what do we do in this case??
        result.Message = "No valuation data found in VehicleCheck";
        result.HTTPStatus = HTTPStatusEnum.NotFound;
        return result;
      }

      // get the min and max price range for vehicles with the same capId
      // use all vehicleInfo to narrow down the retail values 
      // match on: age and miles at minimum 
      // age, miles, transmission, fuel type, colour

      var retailPrice = valuationCheck.PriceRetail; // request.BaseRetailPrice ?? 0

      // test valuations can have a retail price passed in with the request, in which case don't calculate it
      if (!request.TestValuation || retailPrice == 0)
      {
        // if we have no capId, the method will use make, model and deriv to get one
        var lookupRetail = await _vehicleValueService.GetRetailPriceAggregate(request, vehicleDTO, cancellationToken);
        ops.AddRange(lookupRetail.Ops);

        // ignore passed in retail price if any values in retailRange 
        if (lookupRetail.RetailPrice.HasValue)
        {
          retailPrice = (int)lookupRetail.RetailPrice;
        }
      }

      double valuation = valuationCheck.PriceClean ?? 0; //request.BaseCapPrice ?? 0; 

      double newValuation = valuation;

      log.Add($"Setting CAP to {CurrVal(valuation)}");

      // get age in months for age lookup
      DateTime registered;
      var monthDiff = 0;
      if (DateTime.TryParse(vehicleData.DateRegistered, out registered))
      {
        // valuation pillar lookup is a combination of modelId and age in months 
        monthDiff = GetMonthDifference(DateTime.Now, registered);
      }

      // standard nodes are fuel, color, transmission etc.
      var standardProcess = ProcessStandardNodes(valuationProfile, vehicleDTO, monthDiff, valuation, newValuation);

      if (standardProcess.noQuote || standardProcess.call)
      {
        result.DTO = new ResultFromValuationDTO();

        result.DTO.NoQuote = standardProcess.noQuote;
        result.DTO.CallRequired = standardProcess.call;

        if (result.DTO.NoQuote)
        {
          result.Message = "No quote is set for the vehicle - check valuation profile";
          return result;
        }
        if (result.DTO.CallRequired)
        {
          result.Message = "A call is required to value this vehicle";
          return result;
        }
      }

      // margin nodes are used to ensure the correct margin is allowed in the valuation (i.e. 5% or £500 etc.)
      newValuation = ProcessMarginNodes(valuationProfile, retailPrice, vehicleDTO, monthDiff, newValuation);

      log.Add($"Pre rounding margin valuation = {CurrVal(newValuation)}");
      newValuation = Math.Round(newValuation / 5.0) * 5;

      List<int> grades = ProcessGradeNodes(valuationProfile, retailPrice, vehicleDTO, monthDiff, ref newValuation);

      ops.Add($"Final valuation, up to £{newValuation}");

      result.IsValid = true;

      SetResult(request, result, vehicleData, newValuation, grades);

      result.HTTPStatus = HTTPStatusEnum.OK;
      return result;
    }

    private async Task<(VehicleCheckDTO valuation, VehicleCheckDTO provenance)?> UpdateVehicleChecks(VRMLookupDataDTO lookupDTO, CancellationToken cancellationToken)
    {
      var checks = await _vehicleCheckService.UpdateVehicleChecks(lookupDTO, cancellationToken);
      if (checks != null)
      {
        var provenanceCheck = _mapper.Map<VehicleCheckDTO>(checks.OrderByDescending(x => x.Added).FirstOrDefault(x => x.StatusId == (int)StatusEnum.Active
          && x.VehicleCheckType == VehicleCheckTypeEnum.Provenance));

        var valuationCheck = _mapper.Map<VehicleCheckDTO>(checks.OrderByDescending(x => x.Added).FirstOrDefault(x => x.StatusId == (int)StatusEnum.Active
          && x.VehicleCheckType == VehicleCheckTypeEnum.Valuation));

        return (valuationCheck, provenanceCheck);
      }

      return null;
    }

    private async Task<LeadVehicleDTO> GetLeadVehicle(ValuationRequestDTO request, CancellationToken cancellationToken)
    {
      var leadVehicles = await _leadCRMService.SearchLeadVehicles(
        new LeadVehicleSearchDTO
        {
          Component = "ValuationService.GetLeadVehicle",
          Filters = { ExternalAppraisalCode = request.ExternalAppraisalCode, LeadVehicleId = request.LeadVehicleId, VRM = request.VRM }
        }, cancellationToken);


      if (leadVehicles.TotalItems > 0)
      {
        return leadVehicles.Results.First();
      }

      return null;
    }

    private void SetResult(ValuationRequestDTO request, ValidatedResultDTO<ResultFromValuationDTO> result, VehicleLookupInfoDTO vehicleData, double newValuation, List<int> grades)
    {
      if (request.TestValuation)
      {
        result.DTO = new ResultFromValuationDTO
        {
          VehicleInfo = vehicleData,
          ResultValue = newValuation,
          Steps = ops,
          Log = log,
          GradeValues = grades
        };
      }
      else
      {
        result.DTO = new ResultFromValuationDTO
        {
          VehicleInfo = vehicleData,
          ResultValue = newValuation,
          Steps = null,
          Log = null,
          GradeValues = null
        };
      }

      result.DTO.QuoteType = request.QuoteType;
    }

    private async Task<ValuationProfile> GetDefaultValuationProfile()
    {
      return await _context.ValuationProfiles
        .Include(x => x.ValuationNodes)
        .Where(x => x.IsDefault == true)
        .AsNoTracking()
        .FirstOrDefaultAsync();
    }

    private List<int> ProcessGradeNodes(ValuationProfile valuationProfile, int? retailPrice, VehicleDTO vehicle, int monthDiff, ref double newValuation)
    {
      var grades = new List<int>();

      // process grade _multipliers
      for (var pillar = 4; pillar > 0; pillar--)
      {
        var nodes = valuationProfile.ValuationNodes;

        var gradeNodes = nodes
          .Where(x => x.Pillar1 == (pillar >= 1 ? vehicle.MakeId : null))
          .Where(x => x.Pillar2 == (pillar >= 2 ? vehicle.ModelId : null))
          .Where(x => x.Pillar3 == (pillar >= 3 ? monthDiff : null))
          .Where(x => x.Pillar4 == (pillar >= 4 ? vehicle.DerivId : null))
          .Where(x => x.Pillar5 == null)
          .Where(x => x.LookupType == ValuationLookupTypeEnum.VehicleGrade)
          .OrderBy(x => x.LookupName)
          .ToList();

        if (gradeNodes != null && gradeNodes.Any())
        {
          foreach (var node in gradeNodes)
          {
            log.Add($"Found grade node {node.Id}, matched on pillar {pillar}");
            int gradeValue = 0;

            if (node.Multiplier > 0)
            {
              gradeValue = (int)Math.Round((double)retailPrice / node.Multiplier);
              log.Add($"Set grade value {node.LookupName} to {gradeValue} using multiplier {node.Multiplier}");
            }
            else if (node.AddValue.HasValue)
            {
              gradeValue = (int)Math.Round((double)retailPrice - node.AddValue.Value);
              log.Add($"Set grade value {node.LookupName} to {gradeValue} using addition {node.AddValue.Value}");
            }

            grades.Add(gradeValue);
          }

          grades = grades.OrderBy(x => x).ToList();
          break;
        }
      }

      if (grades.Any())
      {
        newValuation = Math.Max(grades.Max(), newValuation);
      }

      return grades;
    }

    private double ProcessMarginNodes(ValuationProfile valuationProfile, int? retailPrice, VehicleDTO vehicle, int monthDiff, double newValuation)
    {
      log.Add($"Processing Margin nodes");

      // iterate all margin nodes (if any) and ensure final value is capped to margin from retail 
      // walk the tree backwards so we get the closest match based on all nodes 
      for (var pillar = 4; pillar > 0; pillar--)
      {
        var nodes = valuationProfile.ValuationNodes;

        var node = nodes
          .Where(x => x.Pillar1 == (pillar >= 1 ? vehicle.MakeId : null))
          .Where(x => x.Pillar2 == (pillar >= 2 ? vehicle.ModelId : null))
          .Where(x => x.Pillar3 == (pillar >= 3 ? monthDiff : null))
          .Where(x => x.Pillar4 == (pillar >= 4 ? vehicle.DerivId : null))
          .Where(x => x.Pillar5 == null)
          .Where(x => x.LookupType == ValuationLookupTypeEnum.Margin)
          .FirstOrDefault();

        if (node != null)
        {
          log.Add($"Found margin node {node.Id}, matched on pillar {pillar}");
          var storedValuation = newValuation;

          // use the multiplier/amount from this node to apply as the margin cap 
          if (retailPrice > 0)
          {
            if (node.Multiplier > 0 && node.Multiplier != 1)
            {
              // cap the valuation 
              newValuation = Math.Round(Math.Min(newValuation, (double)(retailPrice / node.Multiplier)));

              if (newValuation != storedValuation)
              {
                log.Add($"Valuation amount capped to {CurrVal(newValuation)} from {CurrVal(storedValuation)} using multiplier {node.Multiplier}");
              }
            }
            else if (node.AddValue.HasValue)
            {
              // cap the valuation 
              newValuation = Math.Round(Math.Min(newValuation, (double)(retailPrice - node.AddValue)));

              if (newValuation != storedValuation)
              {
                log.Add($"Valuation amount capped to {newValuation} using value {node.AddValue}");
              }
            }

            if (newValuation != storedValuation)
            {
              ops.Add($"Valuation amount capped from {CurrVal(storedValuation)} to {CurrVal(newValuation)}");
            }
          }
          else
          {
            log.Add($"Not capping valuation as retail price is £0");
          }

          break;
        }
      }

      return newValuation;
    }

    private (double value, bool call, bool noQuote) ProcessStandardNodes(ValuationProfile valuationProfile, VehicleDTO vehicle, int monthDiff, double valuation, double newValuation)
    {
      log.Add($"Processing standard nodes");

      for (var pillar = 0; pillar <= 4; pillar++)
      {
        var nodes = valuationProfile.ValuationNodes;

        log.Add($"Processing Stage {pillar}");

        nodes = nodes
          .Where(x => x.Pillar1 == (pillar >= 1 ? vehicle.MakeId : null))
          .Where(x => x.Pillar2 == (pillar >= 2 ? vehicle.ModelId : null))
          .Where(x => x.Pillar3 == (pillar >= 3 ? monthDiff : null))
          .Where(x => x.Pillar4 == (pillar >= 4 ? vehicle.DerivId : null))
          .Where(x => x.Pillar5 == null)
          .Where(x => x.LookupType != ValuationLookupTypeEnum.Margin && x.LookupType != ValuationLookupTypeEnum.VehicleGrade)
          .ToList();

        _multipliers["fuel"] = nodes.Where(x => x.LookupType == ValuationLookupTypeEnum.Fuel && x.LookupId == vehicle.FuelTypeId.ToString()).FirstOrDefault();
        _multipliers["colour"] = nodes.Where(x => x.LookupType == ValuationLookupTypeEnum.Colour && x.LookupId == vehicle.VehicleColourId.ToString()).FirstOrDefault();
        _multipliers["transmission"] = nodes.Where(x => x.LookupType == ValuationLookupTypeEnum.Transmission && x.LookupId == vehicle.TransmissionTypeId.ToString()).FirstOrDefault();
        _multipliers["doors"] = nodes.Where(x => x.LookupType == ValuationLookupTypeEnum.Doors && x.LookupId == vehicle.Doors.ToString()).FirstOrDefault();
        _multipliers["body"] = nodes.Where(x => x.LookupType == ValuationLookupTypeEnum.Body && x.LookupId == vehicle.BodyTypeId.ToString()).FirstOrDefault();
        _multipliers["serviceHistory"] = nodes.Where(x => x.LookupType == ValuationLookupTypeEnum.ServiceHistory && x.LookupId == vehicle.ServiceHistoryType.ToString()).FirstOrDefault();
        _multipliers["VATStatus"] = nodes.Where(x => x.LookupType == ValuationLookupTypeEnum.VATStatus && x.LookupId == vehicle.VatStatusId.ToString()).FirstOrDefault();
        _multipliers["keys"] = nodes.Where(x => x.LookupType == ValuationLookupTypeEnum.Keys && x.LookupId == vehicle.NoOfKeys.ToString()).FirstOrDefault();
        _multipliers["previousOwners"] = nodes.Where(x => x.LookupType == ValuationLookupTypeEnum.PreviousOwners && x.LookupId == vehicle.Owners.ToString()).FirstOrDefault();
        _multipliers["all"] = nodes.Where(x => x.LookupType == ValuationLookupTypeEnum.All).FirstOrDefault();

        if (nodes.Any(x => x.NoQuote))
        {
          log.Add($"NO QUOTE -- END OF CALCULATION");
          return (0, false, true);
        }
        if (nodes.Any(x => x.CallForQuote))
        {
          log.Add($"CALL FOR QUOTE -- END OF CALCULATION");
          return (0, true, false);
        }

        // TODO: ADD GRADE + CAT STATUS
        // _multipliers["grade"] = nodes.Where(x => x.LookupType == ValuationLookupTypeEnum.Body && x.LookupId == vehicle.Grade.ToString()).FirstOrDefault();
        // _multipliers["catStatus"] = nodes.Where(x => x.LookupType == ValuationLookupTypeEnum.Body && x.LookupId == vehicle.CatStatusId.ToString()).FirstOrDefault();

        foreach (var kvp in _multipliers)
        {
          if (kvp.Value == null)
          {
            log.Add($"{kvp.Key} NO ADJUSTMENT FOR " + kvp.Key);
            continue;
          }

          var v = kvp.Value;

          if (v.NoQuote)
          {
            // EXCEPTION ??
            log.Add($"{kvp.Key} NO QUOTE -- END OF CALCULATION");
            break;
          }

          if (v.CallForQuote)
          {
            // EXCEPTION ??
            log.Add($"{kvp.Key} CALL FOR QUOTE -- END OF CALCULATION");
            break;
          }

          Double adjustment = 0;

          if (v.Multiplier != 0 && v.Multiplier != 1)
          {
            adjustment = Math.Round(valuation * v.Multiplier) - valuation;
            ops.Add($"{kvp.Key.ToUpper()} Multiplier: {v.Multiplier} Adjustment: {CurrVal(adjustment)}");
          }
          else if (v.AddValue != null)
          {
            adjustment = v.AddValue.Value;
            ops.Add($"{kvp.Key.ToUpper()} AddValue: {CurrVal(v.AddValue.Value)} Adjustment: {CurrVal(adjustment)}");
          }

          newValuation += adjustment;

          log.Add($"{kvp.Key.ToUpper()} Current Value: {CurrVal(newValuation)}");
        }
      }

      return (newValuation, false, false);
    }

    private string CurrVal(double val)
    {
      return String.Format("{0:c0}", (decimal)val);
    }

    private static int GetMonthDifference(DateTime startDate, DateTime endDate)
    {
      int monthsApart = 12 * (startDate.Year - endDate.Year) + startDate.Month - endDate.Month;
      return Math.Abs(monthsApart);
    }

    public async Task<IEnumerable<ValuationAttributeDTO>> GetLookupAttributesAsync(CancellationToken cancellationToken)
    {
      // model and deriv are returned after selecting parent item
      var response = new List<ValuationAttributeDTO> {
        await GetValuationAttribute(ValuationLookupTypeEnum.Make, cancellationToken),
        await GetValuationAttribute(ValuationLookupTypeEnum.Model, cancellationToken),
        await GetValuationAttribute(ValuationLookupTypeEnum.Age, cancellationToken),
        await GetValuationAttribute(ValuationLookupTypeEnum.Deriv, cancellationToken),
        await GetValuationAttribute(ValuationLookupTypeEnum.Fuel, cancellationToken),
        await GetValuationAttribute(ValuationLookupTypeEnum.Colour, cancellationToken),
        await GetValuationAttribute(ValuationLookupTypeEnum.Transmission, cancellationToken),
        await GetValuationAttribute(ValuationLookupTypeEnum.Doors, cancellationToken),
        await GetValuationAttribute(ValuationLookupTypeEnum.Body, cancellationToken),
        await GetValuationAttribute(ValuationLookupTypeEnum.VehicleGrade, cancellationToken),
        await GetValuationAttribute(ValuationLookupTypeEnum.CatStatus, cancellationToken),
        await GetValuationAttribute(ValuationLookupTypeEnum.ServiceHistory, cancellationToken),
        await GetValuationAttribute(ValuationLookupTypeEnum.VATStatus, cancellationToken),
        await GetValuationAttribute(ValuationLookupTypeEnum.Keys, cancellationToken),
        await GetValuationAttribute(ValuationLookupTypeEnum.PreviousOwners, cancellationToken),
        await GetValuationAttribute(ValuationLookupTypeEnum.All, cancellationToken),
        await GetValuationAttribute(ValuationLookupTypeEnum.Margin, cancellationToken),
      };

      // add order values based on lookup type (easier to read/manage having it here)
      var order = new Dictionary<ValuationLookupTypeEnum, int>()
      {
        [ValuationLookupTypeEnum.All] = 1,
        [ValuationLookupTypeEnum.Body] = 2,
        [ValuationLookupTypeEnum.CatStatus] = 3,
        [ValuationLookupTypeEnum.Colour] = 4,
        [ValuationLookupTypeEnum.Doors] = 5,
        [ValuationLookupTypeEnum.Fuel] = 6,
        [ValuationLookupTypeEnum.Keys] = 7,
        [ValuationLookupTypeEnum.PreviousOwners] = 8,
        [ValuationLookupTypeEnum.ServiceHistory] = 9,
        [ValuationLookupTypeEnum.Transmission] = 10,
        [ValuationLookupTypeEnum.VATStatus] = 11,
        [ValuationLookupTypeEnum.VehicleGrade] = 12,
        [ValuationLookupTypeEnum.Margin] = 13,
      };

      foreach(var kvp in order)
      {
        var node = response.FirstOrDefault(x => x.LookupType == kvp.Key);
        if (node != null)
        {
          node.Order = kvp.Value;
        }
      }

      return response;
    }

    private async Task<ValuationAttributeDTO> GetValuationAttribute(ValuationLookupTypeEnum lookupType, CancellationToken cancellationToken)
    {
      ValuationAttributeDTO valuation = null;

      switch (lookupType)
      {
        case ValuationLookupTypeEnum.Make:
          var makes = await _context.Makes
            .AsNoTracking()
            .Where(x => x.StatusId == (uint)StatusEnum.Active)
            .Select(x => new SelectItemDTO { Label = x.MakeName, Value = x.Id.ToString() }).ToListAsync();

          valuation = new ValuationAttributeDTO
          {
            Name = "Make",
            LookupType = lookupType,
            IsPillar = true,
            LookupItems = makes
          };
          break;

        case ValuationLookupTypeEnum.Model:
          var models = await _context.Models
            .AsNoTracking()
            .Where(x => x.StatusId == (uint)StatusEnum.Active)
            .Select(x => new SelectItemDTO { Label = x.ModelName, Value = x.Id.ToString(), ParentId = x.MakeId.ToString() }).ToListAsync();

          valuation = new ValuationAttributeDTO
          {
            Name = "Model",
            LookupType = lookupType,
            IsPillar = true,
            LookupItems = models
          };
          break;

        case ValuationLookupTypeEnum.Age:
          valuation = new ValuationAttributeDTO
          {
            Name = "Age",
            LookupType = lookupType,
            IsPillar = true,
            LookupItems = new List<SelectItemDTO> {
              new SelectItemDTO { Label = "Up to 6 Months old", Value = "6" },
              new SelectItemDTO { Label = "6 to 12 Months old", Value = "12" },
              new SelectItemDTO { Label = "1 to 2 Years old", Value = "24" },
              new SelectItemDTO { Label = "2 to 5 Years old", Value = "60" },
              new SelectItemDTO { Label = "5 to 10 Years old", Value = "120" },
              new SelectItemDTO { Label = "Over 10 Years old", Value = "1200" },
            }
          };
          break;

        case ValuationLookupTypeEnum.Deriv:
          var derivs = await _context.Derivs
            .AsNoTracking()
            .Where(x => x.StatusId == (uint)StatusEnum.Active)
            .Select(x => new SelectItemDTO { Label = x.DerivName, Value = x.Id.ToString(), ParentId = x.ModelId.ToString() }).ToListAsync();

          valuation = new ValuationAttributeDTO
          {
            Name = "Deriv",
            LookupType = lookupType,
            IsPillar = true,
            LookupItems = derivs
          };
          break;

        case ValuationLookupTypeEnum.Fuel:
          var fuels = await _context.FuelTypes
            .AsNoTracking()
            .Where(x => x.StatusId == (uint)StatusEnum.Active)
            .Select(x => new SelectItemDTO { Label = x.FuelTypeName, Value = x.Id.ToString() }).ToListAsync();

          valuation = new ValuationAttributeDTO
          {
            Name = "Fuel",
            LookupType = lookupType,
            IsPillar = false,
            LookupItems = fuels
          };
          break;

        case ValuationLookupTypeEnum.Colour:
          var colours = await _context.VehicleColours
            .AsNoTracking()
            .Where(x => x.StatusId == (uint)StatusEnum.Active)
            .Select(x => new SelectItemDTO { Label = x.ColourName, Value = x.Id.ToString() }).ToListAsync();

          valuation = new ValuationAttributeDTO
          {
            Name = "Colour",
            LookupType = lookupType,
            IsPillar = false,
            LookupItems = colours
          };
          break;

        case ValuationLookupTypeEnum.Transmission:
          var trans = await _context.TransmissionTypes
            .AsNoTracking()
            .Where(x => x.StatusId == (uint)StatusEnum.Active)
            .Select(x => new SelectItemDTO { Label = x.TransmissionTypeName, Value = x.Id.ToString() }).ToListAsync();

          valuation = new ValuationAttributeDTO
          {
            Name = "Transmission",
            LookupType = lookupType,
            IsPillar = false,
            LookupItems = trans
          };
          break;

        case ValuationLookupTypeEnum.Body:
          var bodyTypes = await _context.BodyTypes
            .AsNoTracking()
            .Where(x => x.StatusId == (uint)StatusEnum.Active)
            .Select(x => new SelectItemDTO { Label = x.BodyTypeName, Value = x.Id.ToString() }).ToListAsync();

          valuation = new ValuationAttributeDTO
          {
            Name = "Body",
            LookupType = lookupType,
            IsPillar = false,
            LookupItems = bodyTypes
          };
          break;

        case ValuationLookupTypeEnum.Doors:
          valuation = new ValuationAttributeDTO
          {
            Name = "Doors",
            LookupType = lookupType,
            IsPillar = false,
            LookupItems = new List<SelectItemDTO> {
              new SelectItemDTO { Label = "2 Doors", Value = "2" },
              new SelectItemDTO { Label = "3 Doors", Value = "3" },
              new SelectItemDTO { Label = "4 Doors", Value = "4" },
              new SelectItemDTO { Label = "5 Doors", Value = "5" }
            }
          };
          break;

        case ValuationLookupTypeEnum.All:
          valuation = new ValuationAttributeDTO
          {
            Name = "All",
            LookupType = lookupType,
            IsPillar = false,
            LookupItems = new List<SelectItemDTO> {
              new SelectItemDTO { Label = "All", Value = "0" },
            }
          };
          break;

        case ValuationLookupTypeEnum.VehicleGrade:

          valuation = new ValuationAttributeDTO
          {
            Name = "Vehicle Grade",
            LookupType = lookupType,
            IsPillar = false,
            LookupItems = new List<SelectItemDTO> {
              new SelectItemDTO { Label = "Grade 1", Value = "1"},
              new SelectItemDTO { Label = "Grade 2", Value = "2"},
              new SelectItemDTO { Label = "Grade 3", Value = "3"},
              new SelectItemDTO { Label = "Grade 4", Value = "4"},
              new SelectItemDTO { Label = "Grade 5", Value = "5"},
            }
          };
          break;

        case ValuationLookupTypeEnum.CatStatus:

          valuation = new ValuationAttributeDTO
          {
            Name = "Cat Status",
            LookupType = lookupType,
            IsPillar = false,
            LookupItems = new List<SelectItemDTO> {
              new SelectItemDTO { Label = "Cat Clear", Value = "0"},
              new SelectItemDTO { Label = "Cat A", Value = "1"},
              new SelectItemDTO { Label = "Cat B", Value = "2"},
              new SelectItemDTO { Label = "Cat S", Value = "3"},
              new SelectItemDTO { Label = "Cat N", Value = "4"},
            }
          };
          break;

        case ValuationLookupTypeEnum.ServiceHistory:

          var serviceHistories = new List<SelectItemDTO>();

          foreach (string name in Enum.GetNames(typeof(ServiceHistoryTypeEnum)))
          {
            var val = (int)Enum.Parse(typeof(ServiceHistoryTypeEnum), name);

            serviceHistories.Add(new SelectItemDTO { Label = name, Value = val.ToString() });
          }

          valuation = new ValuationAttributeDTO
          {
            Name = "Service History",
            LookupType = lookupType,
            IsPillar = false,
            LookupItems = serviceHistories
          };
          break;

        case ValuationLookupTypeEnum.VATStatus:

          var vatStatuses = _context.Attribvals
            .Where(x => x.AttribId == (int)AttribEnum.VATStatus)
            .AsNoTracking()
            .Select(x => new SelectItemDTO() { Label = x.AttribvalName, Value = x.Id.ToString() });

          valuation = new ValuationAttributeDTO
          {
            Name = "VAT Status",
            LookupType = lookupType,
            IsPillar = false,
            LookupItems = vatStatuses
          };
          break;

        case ValuationLookupTypeEnum.Keys:

          valuation = new ValuationAttributeDTO
          {
            Name = "Keys Present",
            LookupType = lookupType,
            IsPillar = false,
            LookupItems = new List<SelectItemDTO> {
              new SelectItemDTO { Label = "3 keys", Value = "3"},
              new SelectItemDTO { Label = "2 keys", Value = "2"},
              new SelectItemDTO { Label = "1 key", Value = "1"},
              new SelectItemDTO { Label = "0 keys", Value = "0"},
            }
          };
          break;

        case ValuationLookupTypeEnum.PreviousOwners:

          valuation = new ValuationAttributeDTO
          {
            Name = "Previous Owners",
            LookupType = lookupType,
            IsPillar = false,
            LookupItems = new List<SelectItemDTO> {
              new SelectItemDTO { Label = "0 Previous Owners", Value = "0"},
              new SelectItemDTO { Label = "1 Previous Owners", Value = "1"},
              new SelectItemDTO { Label = "2 Previous Owners", Value = "2"},
              new SelectItemDTO { Label = "3 Previous Owners", Value = "3"},
              new SelectItemDTO { Label = "4 Previous Owners", Value = "4"},
              new SelectItemDTO { Label = "5+ Previous Owners", Value = "5"},
            }
          };
          break;
        case ValuationLookupTypeEnum.Margin:
          valuation = new ValuationAttributeDTO
          {
            Name = "Margin",
            LookupType = lookupType,
            IsPillar = false,
            LookupItems = new List<SelectItemDTO> {
              new SelectItemDTO { Label = "Margin", Value = "0" },
            }
          };
          break;
      }

      return valuation;
    }
    public async Task<ValidatedResultDTO<ValuationProfileDTO>> PatchProfile(Guid id, JsonPatchDocument<ValuationProfile> dto, CancellationToken cancellationToken)
    {
      var result = new ValidatedResultDTO<ValuationProfileDTO>() { IsValid = true, HTTPStatus = HTTPStatusEnum.OK };

      var entity = await _context.ValuationProfiles.Where(x => x.Id == id).FirstOrDefaultAsync(cancellationToken);

      dto.FilterPatch();
      dto.ApplyTo(entity);

      entity.Updated = DateTime.Now;

      await _context.SaveChangesAsync(cancellationToken);

      result.DTO = _mapper.Map<ValuationProfileDTO>(entity);
      return result;
    }

    public async Task<ValidatedResultDTO<ValuationPillarDTO>> PatchPillarNode(Guid id, JsonPatchDocument<ValuationPillar> dto, CancellationToken cancellationToken)
    {
      var result = new ValidatedResultDTO<ValuationPillarDTO>() { IsValid = true, HTTPStatus = HTTPStatusEnum.OK };

      var entity = await _context.ValuationPillars.Where(x => x.Id == id).FirstOrDefaultAsync();

      dto.FilterPatch();
      dto.ApplyTo(entity);

      entity.Updated = DateTime.Now;

      await _context.SaveChangesAsync();

      result.DTO = _mapper.Map<ValuationPillarDTO>(entity);
      return result;
    }

    public async Task<ValidatedResultDTO<ValuationNodeDTO>> PatchValuationNode(Guid id, JsonPatchDocument<ValuationNode> dto, CancellationToken cancellationToken)
    {
      var result = new ValidatedResultDTO<ValuationNodeDTO>() { IsValid = true, HTTPStatus = HTTPStatusEnum.OK };

      var entity = await _context.ValuationNodes.Where(x => x.Id == id).FirstOrDefaultAsync();

      dto.FilterPatch();
      dto.ApplyTo(entity);

      entity.Updated = DateTime.Now;

      await _context.SaveChangesAsync();

      result.DTO = _mapper.Map<ValuationNodeDTO>(entity);
      return result;
    }
    public async Task<ValidatedResultDTO<ValuationNodeDTO>> SetValuationNode(ValuationNodeDTO dto, CancellationToken cancellationToken)
    {
      var result = new ValidatedResultDTO<ValuationNodeDTO>() { IsValid = true, HTTPStatus = HTTPStatusEnum.OK };

      var entity = await _context.ValuationNodes.Where(x =>
        x.ValuationProfileId == dto.ValuationProfileId &&
        x.Pillar1 == dto.Pillar1 &&
        x.Pillar2 == dto.Pillar2 &&
        x.Pillar3 == dto.Pillar3 &&
        x.Pillar4 == dto.Pillar4 &&
        x.Pillar5 == dto.Pillar5 &&
        x.LookupType == dto.LookupType &&
        x.LookupId == dto.LookupId
        ).FirstOrDefaultAsync();

      // If it doesn't exist
      if (entity == null)
      {
        entity = new ValuationNode();
        entity.Added = DateTime.Now;
        entity = _mapper.Map<ValuationNodeDTO, ValuationNode>(dto);
        _context.ValuationNodes.Add(entity);
      }

      entity.Updated = DateTime.Now;

      // If we've been zeroed, no point storing it 
      if ((dto.AddValue == null || dto.AddValue.Value == 0) &&
          (dto.Multiplier == null || dto.Multiplier.Value == 0) &&
          (dto.NoQuote == false && dto.CallForQuote == false))
      {
        _context.ValuationNodes.Remove(entity);
      }
      else
      {
        entity.Multiplier = (dto.Multiplier == null) ? 1 : 1 + (dto.Multiplier.Value / 100);
        entity.AddValue = (dto.AddValue == null) ? 0 : dto.AddValue.Value;
        entity.CallForQuote = (dto.CallForQuote == null) ? false : dto.CallForQuote.Value;
        entity.NoQuote = (dto.NoQuote == null) ? false : dto.NoQuote.Value;
        entity.LookupName = dto.LookupName;
        entity.StatusId = (int)StatusEnum.Active;
      }


      await _context.SaveChangesAsync();

      result.DTO = _mapper.Map<ValuationNodeDTO>(entity);
      return result;
    }

    public async Task<IEnumerable<ValuationProfileDTO>> Search(ValuationSearchDTO searchDTO, CancellationToken cancellationToken)
    {
      var query = _context.ValuationProfiles.Where(x => x.StatusId == (uint)StatusEnum.Active);

      if (searchDTO.Component == "profile-edit")
      {

        query = query.Include(x => x.ValuationNodes.OrderBy(y => y.Pillar1).ThenBy(y => y.Pillar2).ThenBy(y => y.Pillar3).ThenBy(y => y.Pillar4).ThenBy(y => y.Pillar5).ThenBy(y => y.LookupType).ThenBy(y => y.Multiplier))
          .ThenInclude(x => x.Make)
        .Include(x => x.ValuationNodes.OrderBy(y => y.Pillar1).ThenBy(y => y.Pillar2).ThenBy(y => y.Pillar3).ThenBy(y => y.Pillar4).ThenBy(y => y.Pillar5).ThenBy(y => y.LookupType).ThenBy(y => y.Multiplier))
          .ThenInclude(x => x.Model)
        .Include(x => x.ValuationNodes.OrderBy(y => y.Pillar1).ThenBy(y => y.Pillar2).ThenBy(y => y.Pillar3).ThenBy(y => y.Pillar4).ThenBy(y => y.Pillar5).ThenBy(y => y.LookupType).ThenBy(y => y.Multiplier))
          .ThenInclude(x => x.Deriv);
      }

      query = query.AsNoTracking();

      if (searchDTO.Filters.ValuationProfileId.HasValue)
      {
        query = query.Where(x => x.Id == searchDTO.Filters.ValuationProfileId);
      }

      var result = await query.ToListAsync(cancellationToken);

      return _mapper.Map<IEnumerable<ValuationProfileDTO>>(result);
    }

    public async Task<SearchResultDTO<ValuationNodeDTO>> SearchValuationNodes(ValuationNodeSearchDTO dto, CancellationToken cancellationToken)
    {
      var nodes = _context.ValuationNodes.AsQueryable();

      if (dto.Filters.ValuationProfileId != null)
      {
        nodes = nodes.Where(x => x.ValuationProfileId == dto.Filters.ValuationProfileId.Value);
      }
      if (dto.Filters.Pillar1 != null)
      {
        nodes = nodes.Where(x => x.Pillar1 == dto.Filters.Pillar1.Value);
      }
      if (dto.Filters.Pillar2 != null)
      {
        nodes = nodes.Where(x => x.Pillar2 == dto.Filters.Pillar2.Value);
      }
      if (dto.Filters.Pillar3 != null)
      {
        nodes = nodes.Where(x => x.Pillar3 == dto.Filters.Pillar3.Value);
      }
      if (dto.Filters.Pillar4 != null)
      {
        nodes = nodes.Where(x => x.Pillar4 == dto.Filters.Pillar4.Value);
      }
      if (dto.Filters.Pillar5 != null)
      {
        nodes = nodes.Where(x => x.Pillar5 == dto.Filters.Pillar5.Value);
      }
      if (dto.Filters.LookupType != null)
      {
        nodes = nodes.Where(x => x.LookupType == dto.Filters.LookupType.Value);
      }
      if (!String.IsNullOrEmpty(dto.Filters.LookupId))
      {
        nodes = nodes.Where(x => x.LookupId == dto.Filters.LookupId);
      }

      var results = await nodes.AsNoTracking().ToListAsync(cancellationToken);

      return new SearchResultDTO<ValuationNodeDTO>()
      {
        TotalItems = results.Count,
        Results = _mapper.Map<IEnumerable<ValuationNode>, IEnumerable<ValuationNodeDTO>>(results)
      };
    }

    public async Task<bool> DeleteValuationNode(Guid id, CancellationToken cancellationToken)
    {
      var node = await _context.ValuationNodes.FirstOrDefaultAsync(x => x.Id == id);
      if (node != null)
      {
        _context.ValuationNodes.Remove(node);
        await _context.SaveChangesAsync();
        return true;
      }

      return false;
    }
  }
}
