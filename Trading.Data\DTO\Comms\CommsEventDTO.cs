﻿using DotLiquid;
using System;
using System.Collections.Generic;
using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO.Comms
{
  public class CommsEventDTO : BaseModelEntityIntDTO
  {
    public CommsMergeModelEnum? MergeModel { get; set; }
    public string CommsEventCode { get; set; }
    public string Title { get; set; }
    public string Description { get; set; }
    public ICollection<CommsTemplateDTO> CommsTemplates { get; set; }
  }

  public class CommsEventSearchDTO : BaseSearchDTO
  {
    public CommsEventFiltersDTO Filters { get; set; } = new CommsEventFiltersDTO() { };

  }

  public class CommsEventFiltersDTO
  {
    public bool? Active { get; set; }
    public CommsMergeModelEnum? MergeModel { get; set; }
    public string CommsEventCode { get; set; }
    public string CommsEventId { get; set; }
  }

  public class ProcessEventWithHashDTO
  {
    public string EventCode;
    public string ModelIdString;
    public string UniqueId;
    public Guid? CustomerId;
    public Hash Hash;
    public bool AllowDuplicate;
    public Hash Extra;
  }

  public class ProcessEventWithIdDTO
  {
    public string EventCode;
    public string ModelIdString;
    public Hash Extra;
    public ContactDTO User;
  }
}