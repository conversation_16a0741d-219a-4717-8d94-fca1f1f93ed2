﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System;
using System.Linq;

namespace Trading.API.Common.APIKeyAuth;

public class ApiKeyOperationFilter : IOperationFilter
{
  public void Apply(OpenApiOperation operation, OperationFilterContext context)
  {
    // Check for API key authorization via policy or RequiredScope attribute
    var requiresApiKey = context.MethodInfo.DeclaringType.GetCustomAttributes(true)
        .Union(context.MethodInfo.GetCustomAttributes(true))
        .Any(attr =>
            (attr is AuthorizeAttribute authAttr &&
             (authAttr.Policy == "ApiKeyPolicy" ||
              authAttr.Policy == "TradingPlatform" ||
              authAttr.Policy == "InspectCollect")) ||
            attr is RequiredScopeAttribute);

    // Check for API key attribute to show in Swagger
    var hasApiKeyAttribute = context.MethodInfo.DeclaringType.GetCustomAttributes(true)
        .Union(context.MethodInfo.GetCustomAttributes(true))
        .OfType<SwaggerCustomerAttribute>()
        .Any(attr => attr.Name == "ApiKeyCustomer");

    if (requiresApiKey || hasApiKeyAttribute)
    {
      operation.Security.Add(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "ApiKey"
                            }
                        },
                        Array.Empty<string>()
                    }
                });
    }
  }
}
