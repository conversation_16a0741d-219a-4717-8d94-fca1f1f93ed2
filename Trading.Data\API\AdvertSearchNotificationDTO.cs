﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO
{
  public class AdvertSearchNotificationDTO
  {
    public Guid Id { get; set; } // i.e. Volvo, Ford - or Volvo XC60, Ford Transit
    public string MakeName { get; set; }  // i.e. Volvo, Ford, or XC60, Transit etc.
    public string ModelName { get; set; }   // number of adverts in the DB according to Name group

    public string DerivName { get; set; } // the id of the corresponding group entity (i.e. makeId)
    public string AdvertURL { get; set; } 
  }
}