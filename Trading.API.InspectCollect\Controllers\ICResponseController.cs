using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Common.APIKeyAuth;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.DTO.InspectCollect.VehicleData;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.Extensions;
using Trading.Services.InspectCollect.Interfaces;
using Trading.API.Data.Enums;
using Trading.API.Data.Enums.InspectCollect;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/response")]
  [ApiController]
  [Authorize(Policy = "JwtOrApiKeyForInspectCollect")]  // Use the combined policy (either standard user jwt auth or API key auth)
  [SwaggerCustomer("ApiKeyCustomer")]  // make controller endpoints appear in the API docs
  public class ICResponseController : ControllerBase
  {
    private readonly ICResponseInterface _icResponseService;
    private readonly ICContainerInterface _icContainerService;
    private readonly ICVehicleInterface _icVehicleService;
    private readonly ICUserServiceInterface _icUserService;

    public ICResponseController(
      ICResponseInterface serviceInterface,
      ICContainerInterface containerService,
      ICVehicleInterface icVehicleService,
      ICUserServiceInterface icUserService
      )
    {
      _icResponseService = serviceInterface;
      _icContainerService = containerService;
      _icVehicleService = icVehicleService;
      _icUserService = icUserService;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(Guid id, [FromQuery] string? query, CancellationToken cancellationToken)
    {
      ICResponseSearchDTO dto;

      if (!String.IsNullOrEmpty(query))
      {
        dto = JsonConvert.DeserializeObject<ICResponseSearchDTO>(query);
      }
      else
      {
        dto = new ICResponseSearchDTO();
      }

      var res = await _icResponseService.Get(id, dto, cancellationToken);
      return Ok(res);
    }

    [HttpPost]
    [Route("{id}/trigger")]
    public async Task<ActionResult> Trigger(Guid id, [FromBody] ICTriggerRequestDTO triggerRequestDTO, CancellationToken cancellationToken)
    {
      var userICContainerGroupId = User.ICContainerGroupId();

      triggerRequestDTO.ICUserId = User.ICUserId();
      triggerRequestDTO.IsGod = User.IsGod();
      triggerRequestDTO.IsAdmin = User.IsAdmin();

      if (userICContainerGroupId != null)
      {
        triggerRequestDTO.ICContainerGroupId = userICContainerGroupId.Value;

        var response = await _icResponseService.Trigger(id, triggerRequestDTO, cancellationToken);
        return Ok(response);
      }

      return BadRequest("User does not have a valid container group ID associated with their account.");

    }

    [HttpGet]
    [Route("{id}/combined-data")]
    public async Task<ActionResult> GetCombined(Guid id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = new ICResponseSearchDTO();

      try
      {
        dto = JsonConvert.DeserializeObject<ICResponseSearchDTO>(query);
      }
      catch (Exception ex)
      {
        return BadRequest();
      }

      var combined = new ICCombinedResponseContainers();

      var getAppraisal = await _icResponseService.Get(id, dto, cancellationToken);

      combined.Response = getAppraisal;

      if (getAppraisal.IsValid)
      {
        var appraisal = getAppraisal.DTO;

        var containerSearch = new ICContainerSearchDTO()
        {
          Component = dto?.Component ?? "",
          Filters = new ICContainerSearchFilters()
          {
            ICContainerGroupId = appraisal.ICContainerGroupId
          }
        };

        var containers = await _icContainerService.Search(containerSearch, cancellationToken);

        combined.Containers = containers;

      }

      return Ok(combined);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icResponseService.Delete(id);
      return Ok(res);
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Create([FromBody] ICResponseCreateDTO dto, CancellationToken cancellationToken)
    {
      // GOING TO HAVE TO SPOOF A USER FOR NOW

      if (dto.ICUserId != User.ICUserId() && !User.IsAdmin())
      {
        //        return Forbid();
      }


      if (dto.ICContainerGroupId != User.ICContainerGroupId() && !User.IsAdmin())
      {
        //        return Forbid();
      }

      if (dto.ICUserId == null)
      {
        dto.ICUserId = User.ICUserId();
      }

      if (dto.ICContainerGroupId == null)
      {
        dto.ICContainerGroupId = User.ICContainerGroupId();
      }

      if (dto.ICResponseStatus == 0)
      {
        dto.ICResponseStatus = ICResponseStatusEnum.Issued;
      }

      if (dto.ICLocationId == null)
      {
        var user = await _icUserService.GetUser(User.ICUserId().Value);
        dto.ICLocationId = user.DefaultLocationId;
      }

      var res = await _icResponseService.Create(dto);

      return Ok(res);
    }

    [HttpGet]
    [Route("search")]
    [Route("/api/inspect-collect/responses")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICResponseSearchDTO>(query);

      // wildcard scope allows any container group
      if (!User.IsAdmin() && !dto.Filters.ICContainerGroupId.HasValue)
      {
        dto.Filters.ICContainerGroupId = User.ICContainerGroupIdFromScope();
      }

      // forbid requests without a container group id
      if (!User.IsAdmin() && dto.Filters.ICContainerGroupId == null)
      {
        return BadRequest("ContainerGroupId is required in the search query.");
      }

      // If this request comes from an API Key, validate container group ID access
      if (User.IsApiKeyAuthentication())
      {
        // Get the requested container group ID
        var requestedContainerGroupId = dto.Filters.ICContainerGroupId;

        if (requestedContainerGroupId.HasValue)
        {
          // Get all container group IDs from scopes
          var allowedContainerGroupIds = User.Claims
              .Where(c => c.Type == "scope" && c.Value.StartsWith("InspectCollect:ContainerGroup:"))
              .Select(c =>
              {
                string guidPart = c.Value.Substring("InspectCollect:ContainerGroup:".Length);
                if (Guid.TryParse(guidPart, out Guid containerGroupId))
                  return containerGroupId;
                return Guid.Empty;
              })
              .Where(id => id != Guid.Empty)
              .ToList();

          // Check if user has wildcard scope
          bool hasWildcardScope = User.HasClaim(c => c.Type == "scope" && c.Value == "*");

          // If not wildcard and not in allowed list, reject
          if (!hasWildcardScope && !allowedContainerGroupIds.Contains(requestedContainerGroupId.Value))
          {
            return StatusCode(StatusCodes.Status403Forbidden, "API key does not have access to the requested container group");
          }
        }
      }

      if (dto != null)
      {
        if (!User.IsManager() && !User.IsAdmin())
        {
          dto.Filters.ForUserId = User.ICUserId();
        }

        var res = await _icResponseService.Search(dto, cancellationToken);
        return Ok(res);
      }
      else
      {
        return BadRequest();
      }
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<ICResponse> dto)
    {
      var response = await _icResponseService.Patch(id, dto);
      return Ok(response);
    }

    [HttpGet]
    [Route("statistics")]
    public async Task<IActionResult> GetStatistics([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        ICResponseStatRequestDTO dto;
        if (!string.IsNullOrEmpty(query))
        {
          // Add deserialization settings to handle GUIDs properly
          var settings = new JsonSerializerSettings
          {
            DateTimeZoneHandling = DateTimeZoneHandling.Utc,
            NullValueHandling = NullValueHandling.Ignore
          };

          dto = JsonConvert.DeserializeObject<ICResponseStatRequestDTO>(query, settings);

        }
        else
        {
          dto = new ICResponseStatRequestDTO
          {
            Grouping = ICStatGroupingEnum.Day
          };
        }

        var result = await _icResponseService.GetResponseStatistics(dto, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        Console.WriteLine($"Deserialization error: {ex.Message}");
        return BadRequest(ex.Message);
      }
    }

  }
}