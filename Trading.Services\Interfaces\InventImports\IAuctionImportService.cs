﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Imports.Invent;

namespace Trading.Services.Interfaces.InventImports;

public interface IAuctionImportService
{
  Task<int> ImportAuctionsAsync(Guid inventUserId, CancellationToken cancellationToken = default);

  Task<int> ProcessAuctionLotsAsync(Guid inventUserId, IEnumerable<Guid> selectedLotIds = null, CancellationToken cancellationToken = default);


  Task<SearchResultDTO<InventAuctionLotDTO>> SearchAsync(InventSearchDTO searchDTO, CancellationToken cancellationToken = default);

  Task<bool> MarkVehicleAsActionedAsync(string id, CancellationToken cancellationToken = default);

  Task<bool> WithdrawVehiclesAsync(IEnumerable<Guid> ids, CancellationToken cancellationToken = default);

  Task<bool> RestoreVehicleAsync(Guid inventUserId, Guid lotId, CancellationToken cancellationToken = default);

  Task<SearchResultDTO<InventAuctionDTO>> SearchAuctionsAsync(InventAuctionSearchDTO searchDTO, CancellationToken cancellationToken = default);

  Task<bool> UpdateLotReservePrice(Guid id, decimal reservePrice, CancellationToken cancellationToken = default);

  Task<List<InventUserDTO>> GetInventUsersAsync(bool isAdmin, CancellationToken cancellationToken = default);
}