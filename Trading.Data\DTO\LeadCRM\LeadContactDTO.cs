﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.LeadCRM
{
  public class LeadContactDTO : LeadBaseModelDTO
  {
    public string Forename { get; set; }

    public string Surname { get; set; }

    public string Email { get; set; }

    public string Phone { get; set; }

    public string Mobile { get; set; }

    public List<LeadContactLinkDTO> LeadContactLinks { get; set; }

    public Guid? ContactId { get; set; } // internal contact (not CRM contact)
    public ContactDTO Contact { get; set; }

    public int MaxValuationsPerMonth { get; set; }
  }
}
