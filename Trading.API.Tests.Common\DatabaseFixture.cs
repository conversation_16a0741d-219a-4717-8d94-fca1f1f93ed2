﻿using Dapper;
using Microsoft.EntityFrameworkCore;
using MySqlConnector;
using Trading.API.Data;
using Trading.API.Tests.Common.Helpers;
using Xunit;

namespace Trading.API.Tests.Common
{
  public class DatabaseFixture : IDisposable
  {
    public DatabaseFixture()
    {
      try
      {
        DropDB();

        // create a dapper connection to the local server 
        using (var connection = new MySqlConnection(DBStrings.serverConn))
        {
          // create test db 
          connection.Execute($"CREATE DATABASE IF NOT EXISTS {DBStrings.db};");
        }

        // create trading context
        using (var context = GetContext())
        {
          // run migrations 
          context.Database.SetCommandTimeout(3000);

          context.Database.Migrate();
          context.Database.EnsureCreated();

          context.EnsureInitialData().Wait();
        }
      }
      catch (Exception ex)
      {
        DropDB();
      }
    }

    private TradingContext GetContext()
    {
      var optionsBuilder = new DbContextOptionsBuilder<TradingContext>();
      optionsBuilder.UseMySql(DBStrings.dbConn, 
        ServerVersion.AutoDetect(DBStrings.dbConn), 
        o => o.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery));

      return new TradingContext(optionsBuilder.Options);
    }

    public void Dispose()
    {
      // delete test db 
      //DropDB();
    }

    private void DropDB()
    {
      using (var connection = new MySqlConnection(DBStrings.serverConn))
      {
        // create test db 
        connection.Execute($"DROP DATABASE IF EXISTS {DBStrings.db};");
      }
    }
  }

}
