﻿using Microsoft.AspNetCore.Components.RenderTree;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;

namespace Trading.Services.Extensions
{
  struct CustomerClaimDTO
  {
    public string contactId { get; set; }
    public string customerId { get; set; }
    public int contactStatusId { get; set; }
    public int customerStatusId { get; set; }
    public string customerName { get; set; }
    public string contactTown { get; set; }
    public string userRoles { get; set; }
  }

  public static class UserExtensions
  {
    public static string Email(this ClaimsPrincipal user)
    {
      return user.Claims.FirstOrDefault(x => x.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress").Value;
    }
    public static string Username(this ClaimsPrincipal user)
    {
      return user.Claims.FirstOrDefault(x => x.Type == "cognito:username").Value;
    }
    public static string ContactName(this ClaimsPrincipal user)
    {
      return user.Claims.FirstOrDefault(x => x.Type == "name").Value;
    }

    /// <summary>
    /// Get the user name from the claims. This is a unique Guid Id from Cognito.
    /// </summary>
    /// <param name="user"></param>
    /// <returns></returns>
    public static string CognitoUserId(this ClaimsPrincipal user)
    {
      return user.Claims.FirstOrDefault(x => x.Type == "cognito:username").Value;
    }

    public static string CustomerName(this ClaimsPrincipal user)
    {
      var customersJSON = user.Claims.FirstOrDefault(x => x.Type == "customers").Value;

      if (customersJSON != null)
      {
        var z = JsonConvert.DeserializeObject<List<CustomerClaimDTO>>(customersJSON).FirstOrDefault();

        return z.customerName;
      }

      return null;
    }
    public static string ContactTown(this ClaimsPrincipal user)
    {
      var customersJSON = user.Claims.FirstOrDefault(x => x.Type == "customers").Value;

      if (customersJSON != null)
      {
        var z = JsonConvert.DeserializeObject<List<CustomerClaimDTO>>(customersJSON).FirstOrDefault();

        return z.contactTown;
      }

      return null;
    }

    public static IEnumerable<string> Roles(this ClaimsPrincipal user)
    {
      var roles = user.Claims.Where(x => x.Type == "userRoles").Select(x => x.Value).FirstOrDefault();

      return roles.Split(",");
    }

    public static bool IsAdmin(this ClaimsPrincipal user)
    {
      return user.IsInRole("ADMIN");
    }

    public static bool IsApiKeyAuthentication(this ClaimsPrincipal user)
    {
      if (user.Identity?.AuthenticationType == "ApiKey")
      {
        return true;
      }

      return false;
    }

    public static Guid? ICContainerGroupIdFromScope(this ClaimsPrincipal user)
    {
      var containerGroupClaim = user.Claims
        .FirstOrDefault(c => c.Type == "scope" && c.Value.StartsWith("InspectCollect:ContainerGroup:"));

      if (containerGroupClaim != null)
      {
        string guidPart = containerGroupClaim.Value.Substring("InspectCollect:ContainerGroup:".Length);
        if (Guid.TryParse(guidPart, out Guid containerGroupId))
        {
          return containerGroupId;
        }
      }

      return null;
    }

    public static bool IsManager(this ClaimsPrincipal user)
    {
      return user.IsInRole("MANAGER");
    }

    public static bool IsImportAdmin(this ClaimsPrincipal user)
    {
      return user.IsInRole("IMPORT_ADMIN");
    }

    public static bool IsPowerUser(this ClaimsPrincipal user)
    {
      return user.IsInRole("POWER_USER");
    }

    public static string InspectCollectUserId(this ClaimsPrincipal user)
    {
      // Look for the custom Cognito attribute
      var claim = user.FindFirst("custom:icUserId");

      if (claim != null)
      {
        return claim.Value;
      }

      // Fallback to standard sub claim if custom claim not found
      var subClaim = user.FindFirst(ClaimTypes.NameIdentifier);
      return subClaim?.Value;
    }

    public static bool HasRole(this ClaimsPrincipal user, params string[] roles)
    {
      foreach (var role in roles)
      {
        if (user.IsInRole(role)) return true;
      }
      return false;
    }

    public static bool IsAuctionAdmin(this ClaimsPrincipal user)
    {
      return user.IsInRole("AUCTION_ADMIN");
    }

    public static bool IsGod(this ClaimsPrincipal user)
    {
      return user.IsInRole("GOD");
    }

    public static string CustomerIdString(this ClaimsPrincipal user)
    {
      try
      {
        return GetCustomerIdString(user);
      }
      catch (Exception e)
      {
        return null;

      }
    }
    public static Guid? CustomerId(this ClaimsPrincipal user)
    {
      try
      {
        var CustomerIdString = GetCustomerIdString(user);

        if (!string.IsNullOrEmpty(CustomerIdString))
        {
          return Guid.Parse(CustomerIdString);
        }
        return null;
      }
      catch (Exception e)
      {
        return null;

      }
    }

    public static string GetCustomerIdString(ClaimsPrincipal user)
    {
      return user.Claims.FirstOrDefault(x => x.Type == "customerId")?.Value ?? null;
    }

    public static uint? CustomerStatusId(this ClaimsPrincipal user)
    {
      try
      {
        return Convert.ToUInt32(user.Claims.FirstOrDefault(x => x.Type == "customerStatusId").Value);
      }
      catch (Exception e)
      {
        return null;

      }
    }
    public static string ContactIdString(this ClaimsPrincipal user)
    {
      try
      {
        return GetCustomerIdString(user);
      }
      catch (Exception e)
      {
        return null;

      }
    }
    public static Guid? ContactId(this ClaimsPrincipal user)
    {
      try
      {
        return Guid.Parse(GetContactIdString(user));
      }
      catch (Exception e)
      {
        return null;

      }
    }
    public static Guid? Impersonator(this ClaimsPrincipal user)
    {
      try
      {
        var claim = user.Claims.FirstOrDefault(x => x.Type == "impersonator");

        if (claim != null)
        {
          return Guid.Parse(claim.Value);
        }
        return null;
      }
      catch (Exception e)
      {
        return null;

      }
    }

    public static string GetContactIdString(ClaimsPrincipal user)
    {
      try
      {
        if (! user.HasClaim(x => x.Type == "contactId"))
        {
          return null;
        }

        var x = user.Claims.FirstOrDefault(x => x.Type == "contactId");

        if (x.Value == "")
        {
          return null;
        }

        return x.Value;

      }
      catch (Exception e)
      {
        return null;

      }
    }
    public static Guid? ICUserId(this ClaimsPrincipal user)
    {
      try
      {
        var x = user.Claims.FirstOrDefault(x => x.Type == "custom:icUserId");

        return Guid.Parse(x.Value);
      }
      catch (Exception e)
      {
        return null;

      }
    }
    public static Guid? ICContainerGroupId(this ClaimsPrincipal user)
    {
      try
      {
        var x = user.Claims.FirstOrDefault(x => x.Type == "custom:icContainerGroupId");

        return Guid.Parse(x.Value);
      }
      catch (Exception e)
      {
        return null;

      }
    }
    public static uint? StatusId(this ClaimsPrincipal user)
    {
      try
      {
        var x = user.Claims.FirstOrDefault(x => x.Type == "StatusId" || x.Type == "custom:statusId");

        return uint.Parse(x.Value);
      }
      catch (Exception e)
      {
        return null;

      }
    }
  }
}
