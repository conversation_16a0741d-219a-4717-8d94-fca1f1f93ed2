﻿using System;

namespace Trading.API.Data.DTO.Components.MainDashboard
{
  public class RecentAdvertDTO : BaseModelEntityDTO
  {
    public Guid? DealId { get; set; }
    public uint? BuyItNowPrice { get; set; }
    public RecentVehicleDTO Vehicle { get; set; }
  }

  public class RecentVehicleDTO : BaseModelEntityDTO
  {
    public string PrimaryImageURL { get; set; }
    public ModelDTO Model { get; set; }
    public PlateDTO Plate { get; set; }
    public FuelTypeDTO FuelType { get; set; }
    public TransmissionTypeDTO TransmissionType { get; set; }
    public BodyTypeDTO BodyType { get; set; }
    public uint? Odometer { get; set; }
  }

  public class RecentAdvertSale : BaseModelEntityDTO
  {
    public SaleTypeDTO SaleType { get; set; }
    public DateTime? SaleStart { get; set; }
  }
}
