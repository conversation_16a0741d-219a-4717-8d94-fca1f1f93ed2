using System;
using System.Net;

namespace Trading.Services.Exceptions
{
  /// <summary>
  /// Base exception for all dotAdmin related errors
  /// </summary>
  public abstract class DotAdminException : Exception
  {
    protected DotAdminException(string message) : base(message) { }
    protected DotAdminException(string message, Exception innerException) : base(message, innerException) { }
  }

  /// <summary>
  /// Exception thrown when dotAdmin authentication fails
  /// </summary>
  public class DotAdminAuthenticationException : DotAdminException
  {
    public string Username { get; }

    public DotAdminAuthenticationException(string username, string message) 
      : base($"Authentication failed for user '{username}': {message}")
    {
      Username = username;
    }

    public DotAdminAuthenticationException(string username, string message, Exception innerException) 
      : base($"Authentication failed for user '{username}': {message}", innerException)
    {
      Username = username;
    }
  }

  /// <summary>
  /// Exception thrown when dotAdmin API returns an error response
  /// </summary>
  public class DotAdminApiException : DotAdminException
  {
    public HttpStatusCode? StatusCode { get; }
    public string Endpoint { get; }
    public string ResponseContent { get; }

    public DotAdminApiException(string endpoint, HttpStatusCode statusCode, string responseContent) 
      : base($"dotAdmin API error on {endpoint}: {statusCode} - {responseContent}")
    {
      Endpoint = endpoint;
      StatusCode = statusCode;
      ResponseContent = responseContent;
    }

    public DotAdminApiException(string endpoint, string message) 
      : base($"dotAdmin API error on {endpoint}: {message}")
    {
      Endpoint = endpoint;
    }

    public DotAdminApiException(string endpoint, string message, Exception innerException) 
      : base($"dotAdmin API error on {endpoint}: {message}", innerException)
    {
      Endpoint = endpoint;
    }
  }

  /// <summary>
  /// Exception thrown when vehicle creation fails in dotAdmin
  /// </summary>
  public class DotAdminVehicleCreationException : DotAdminException
  {
    public string Registration { get; }
    public string Vin { get; }
    public Guid? ICVehicleId { get; }

    public DotAdminVehicleCreationException(string registration, string message) 
      : base($"Failed to create vehicle '{registration}' in dotAdmin: {message}")
    {
      Registration = registration;
    }

    public DotAdminVehicleCreationException(string registration, string vin, string message) 
      : base($"Failed to create vehicle '{registration}' (VIN: {vin}) in dotAdmin: {message}")
    {
      Registration = registration;
      Vin = vin;
    }

    public DotAdminVehicleCreationException(Guid icVehicleId, string registration, string message) 
      : base($"Failed to create vehicle '{registration}' (ICVehicle: {icVehicleId}) in dotAdmin: {message}")
    {
      ICVehicleId = icVehicleId;
      Registration = registration;
    }

    public DotAdminVehicleCreationException(string registration, string message, Exception innerException) 
      : base($"Failed to create vehicle '{registration}' in dotAdmin: {message}", innerException)
    {
      Registration = registration;
    }
  }

  /// <summary>
  /// Exception thrown when ICVehicle data is invalid for dotAdmin submission
  /// </summary>
  public class DotAdminValidationException : DotAdminException
  {
    public Guid? ICVehicleId { get; }
    public string Field { get; }

    public DotAdminValidationException(string field, string message) 
      : base($"Validation failed for field '{field}': {message}")
    {
      Field = field;
    }

    public DotAdminValidationException(Guid icVehicleId, string field, string message) 
      : base($"Validation failed for ICVehicle {icVehicleId}, field '{field}': {message}")
    {
      ICVehicleId = icVehicleId;
      Field = field;
    }

    public DotAdminValidationException(string message) : base(message) { }
  }

  /// <summary>
  /// Exception thrown when dotAdmin configuration is invalid
  /// </summary>
  public class DotAdminConfigurationException : DotAdminException
  {
    public string ConfigurationKey { get; }

    public DotAdminConfigurationException(string configurationKey, string message) 
      : base($"Invalid dotAdmin configuration for '{configurationKey}': {message}")
    {
      ConfigurationKey = configurationKey;
    }

    public DotAdminConfigurationException(string message) : base(message) { }
  }

  /// <summary>
  /// Exception thrown when dotAdmin service is not available or times out
  /// </summary>
  public class DotAdminServiceUnavailableException : DotAdminException
  {
    public TimeSpan? Timeout { get; }

    public DotAdminServiceUnavailableException(string message) : base(message) { }

    public DotAdminServiceUnavailableException(TimeSpan timeout, string message) 
      : base($"dotAdmin service unavailable after {timeout}: {message}")
    {
      Timeout = timeout;
    }

    public DotAdminServiceUnavailableException(string message, Exception innerException) 
      : base(message, innerException) { }
  }

  /// <summary>
  /// Exception thrown when customer or location selection fails
  /// </summary>
  public class DotAdminCustomerLocationException : DotAdminException
  {
    public int? CustomerId { get; }
    public int? LocationId { get; }

    public DotAdminCustomerLocationException(int customerId, int locationId, string message) 
      : base($"Failed to select customer {customerId} and location {locationId}: {message}")
    {
      CustomerId = customerId;
      LocationId = locationId;
    }

    public DotAdminCustomerLocationException(string message) : base(message) { }
  }

  /// <summary>
  /// Exception thrown when token refresh fails
  /// </summary>
  public class DotAdminTokenException : DotAdminException
  {
    public DateTime? TokenExpiry { get; }

    public DotAdminTokenException(string message) : base(message) { }

    public DotAdminTokenException(DateTime tokenExpiry, string message) 
      : base($"Token expired at {tokenExpiry}: {message}")
    {
      TokenExpiry = tokenExpiry;
    }

    public DotAdminTokenException(string message, Exception innerException) 
      : base(message, innerException) { }
  }
}
