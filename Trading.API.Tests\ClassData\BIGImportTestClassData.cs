﻿using Newtonsoft.Json;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.Services.ExternalDTO.BIG;

namespace Trading.API.Tests.ClassData
{
  public class BIGImportTestClassData : IEnumerable<object[]>
  {
    public IEnumerator<object[]> GetEnumerator()
    {
      // read in the datafile and parse into BIG import dtos
      var dataText = File.ReadAllText("DataFiles/BigAuctionExport.json");

      var dtos = JsonConvert.DeserializeObject<IEnumerable<BIGImportDTO>>(dataText);

      foreach(var dto in dtos)
      {
        yield return new object[] { dto };
      }
    }

    IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();
  }
}
