﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO
{
  public class DVLAVehicleDTO : BaseModelEntityDTO
  {
    public string RegistrationNumber { get; set; } // i.e. "WA68VSG"
    public int Co2Emissions { get; set; } // i.e. 141
    public int EngineCapacity { get; set; } // i.e. 1991
    public bool MarkedForExport { get; set; }
    public string FuelType { get; set; } // i.e. "PETROL"
    public string MotStatus { get; set; } // i.e. "No details held by DVLA" or "Valid"
    public string MotExpiryDate { get; set; } 
    public int RevenueWeight { get; set; } // i.e. 1965
    public string Colour { get; set; } // i.e. "BLACK"
    public string Make { get; set; } // i.e. "MERCEDES-BENZ"
    public string TypeApproval { get; set; } // i.e. "M1"
    public int YearOfManufacture { get; set; } // i.e. 2018
    public DateTime? TaxDueDate { get; set; } // i.e. "2020-10-08"
    public string TaxStatus { get; set; } // i.e. "Untaxed"
    public DateTime? DateOfLastV5CIssued { get; set; } // i.e. "2020-10-06"
    public string Wheelplan { get; set; } // i.e. "2 AXLE RIGID BODY"
    public string MonthOfFirstRegistration { get; set; } // i.e. "2018-09"
    public string ErrorStatus { get; set; } // null or empty if no errors (this is for mapping from DVLAData model)

    public DVLADataErrorDTO[] Errors { get; set; }
}
}
