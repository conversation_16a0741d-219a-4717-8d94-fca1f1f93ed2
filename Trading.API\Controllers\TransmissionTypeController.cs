using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/transmissionType")]
  [ApiController]
  public class TransmissionTypeController : ControllerBase
  {
    private readonly ITransmissionTypeService _transmissionTypeService;

    public TransmissionTypeController(ITransmissionTypeService transmissionTypeService)
    {
      this._transmissionTypeService = transmissionTypeService;
    }

    [HttpGet]
    [Route("/api/vehicleType/{vehicleTypeId}/TransmissionTypes")]
    public async Task<IActionResult> GetAll(uint vehicleTypeId, [FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var searchDTO = new TransmissionTypeSearchDTO() { };

        if (!String.IsNullOrEmpty(query))
        {
          searchDTO = JsonSerializer.Deserialize<TransmissionTypeSearchDTO>(query);
        }

        searchDTO.Filters.VehicleTypeId = vehicleTypeId;

        var transTypes = await _transmissionTypeService.Search(searchDTO, cancellationToken);
        return Ok(transTypes);
      } 
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
