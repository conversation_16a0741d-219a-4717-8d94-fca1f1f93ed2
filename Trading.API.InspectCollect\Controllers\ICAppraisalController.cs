﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Text.Json;
using Trading.API.Data.DTO.InspectCollect.ResponseConditionItems;
using Trading.Services.InspectCollect.Interfaces;
using Trading.Services.Interfaces;

namespace Trading.API.InspectCollect.Controllers;

[Route("api/inspect-collect/appraisal")]
[ApiController]
[Authorize(Policy = "RequireInspectCollect")]
[Authorize("InspectCollect")]  // API key policy
[AllowAnonymous] // for testing
public class ICAppraisalController : ControllerBase
{
  private readonly IAppraisalService _serviceInterface;
  private readonly ICConditionItemInterface _conditionItemService;

  public ICAppraisalController(IAppraisalService serviceInterface, ICConditionItemInterface conditionItemService)
  {
    _serviceInterface = serviceInterface;
    _conditionItemService = conditionItemService;
  }
  // Body Part Groups and Damages endpoints
  [HttpGet]
  [Route("bodyPartGroups")]
  public async Task<IActionResult> GetBodyPartGroups([FromQuery] bool isInternal, CancellationToken cancellationToken)
  {
    try
    {
      var dtos = await _serviceInterface.GetBodyPartGroups(isInternal, cancellationToken);
      return Ok(dtos);
    }
    catch (Exception ex)
    {
      return BadRequest(ex);
    }
  }

  [HttpGet]
  [Route("damages")]
  public async Task<IActionResult> GetDamages(CancellationToken cancellationToken)
  {
    try
    {
      var dtos = await _serviceInterface.GetDamages(cancellationToken);
      return Ok(dtos);
    }
    catch (Exception ex)
    {
      return BadRequest(ex);
    }
  }

  // Condition Item Methods
  [HttpGet]
  [Route("condition/{id}")]
  public async Task<IActionResult> GetConditionItemById(Guid id)
  {
    var item = await _conditionItemService.GetConditionItemByIdAsync(id);
    if (item == null)
      return NotFound();

    return Ok(item);
  }

  [HttpGet]
  [Route("condition/response/{responseId}")]
  public async Task<IActionResult> GetConditionItemsByResponseId(Guid responseId)
  {
    var items = await _conditionItemService.GetConditionItemsByResponseIdAsync(responseId);
    return Ok(items);
  }

  [HttpPost]
  [Route("condition")]
  public async Task<IActionResult> CreateConditionItem([FromBody] CreateICResponseConditionItemDTO createDto)
  {
    var createdItem = await _conditionItemService.CreateConditionItemAsync(createDto);
    return CreatedAtAction(nameof(GetConditionItemById), new { id = createdItem.Id }, createdItem);
  }

  [HttpPut]
  [Route("condition/{id}")]
  public async Task<IActionResult> UpdateConditionItem(Guid id, [FromBody] UpdateICResponseConditionItemDTO updateDto)
  {
    var updatedItem = await _conditionItemService.UpdateConditionItemAsync(id, updateDto);
    if (updatedItem == null)
      return NotFound();

    return Ok(updatedItem);
  }

  [HttpDelete]
  [Route("condition/{id}")]
  public async Task<IActionResult> DeleteConditionItem(Guid id)
  {
    var result = await _conditionItemService.DeleteConditionItemAsync(id);
    if (!result)
      return NotFound();

    return Ok(result);
  }

  // Condition Item Media Methods
  [HttpGet]
  [Route("condition/{conditionItemId}/media")]
  public async Task<IActionResult> GetAllMediaForConditionItem(Guid conditionItemId)
  {
    var mediaItems = await _conditionItemService.GetAllMediaForConditionItemAsync(conditionItemId);
    return Ok(mediaItems);
  }

  [HttpGet]
  [Route("condition/media/{id}")]
  public async Task<IActionResult> GetMediaById(Guid id)
  {
    var media = await _conditionItemService.GetMediaByIdAsync(id);
    if (media == null)
      return NotFound();

    return Ok(media);
  }

  [HttpPost]
  [Route("condition/{conditionItemId}/media")]
  [Consumes("multipart/form-data")]
  public async Task<IActionResult> AddMediaToConditionItem(
      Guid conditionItemId,
      [FromForm] CreateICResponseConditionItemMediaDTO createDto,
      IFormFile file,
      CancellationToken cancellationToken)
  {
    try
    {
      var createdMedia = await _conditionItemService.AddMediaToConditionItemAsync(conditionItemId, createDto, file, cancellationToken);
      return CreatedAtAction(nameof(GetMediaById), new { id = createdMedia.Id }, createdMedia);
    }
    catch (Exception ex)
    {
      return BadRequest(ex.Message);
    }
  }

  [HttpPost]
  [Route("condition/{conditionItemId}/media/batch")]
  [Consumes("multipart/form-data")]
  public async Task<IActionResult> AddMultipleMediaToConditionItem(
      Guid conditionItemId,
      [FromForm] CreateICResponseConditionItemMediaDTO createDto,
      IFormFileCollection files,
      CancellationToken cancellationToken)
  {
    try
    {
      var createdMediaItems = await _conditionItemService.AddMultipleMediaToConditionItemAsync(conditionItemId, createDto, files, cancellationToken);
      return Ok(createdMediaItems);
    }
    catch (Exception ex)
    {
      return BadRequest(ex.Message);
    }
  }

  [HttpPut]
  [Route("condition/media/{id}")]
  public async Task<IActionResult> UpdateMedia(
      Guid id, [FromBody] UpdateICResponseConditionItemMediaDTO updateDto)
  {
    var updatedMedia = await _conditionItemService.UpdateMediaAsync(id, updateDto);
    if (updatedMedia == null)
      return NotFound();

    return Ok(updatedMedia);
  }

  [HttpDelete]
  [Route("condition/media/{id}")]
  public async Task<IActionResult> DeleteMedia(Guid id, CancellationToken cancellationToken)
  {
    var result = await _conditionItemService.DeleteMediaAsync(id, cancellationToken);
    if (!result)
      return NotFound();

    return Ok(result);
  }
}