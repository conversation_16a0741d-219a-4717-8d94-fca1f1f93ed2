using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.Extensions;
using Trading.Services.InspectCollect.Classes;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/response-input")]
  [ApiController]
  [AllowAnonymous]
  public class ICResponseInputController : ControllerBase
  {
    private readonly ICResponseInputInterface _icResponseInputService;

    public ICResponseInputController(ICResponseInputInterface serviceInterface)
    {
      _icResponseInputService = serviceInterface;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icResponseInputService.Get(id, null, cancellationToken);
      return Ok(res);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icResponseInputService.Delete(id);
      return Ok(res);
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Post([FromBody] ICResponseInputCreateDTO dto, CancellationToken cancellationToken)
    {


      var res = await _icResponseInputService.Create(dto);
      return Ok(res);
    }

    [HttpGet]
    [Route("search")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICResponseInputSearchDTO>(query);
      var res = await _icResponseInputService.Search(dto, cancellationToken);
      return Ok(res);
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<ICResponseInput> dto)
    {
      var response = await _icResponseInputService.Patch(id, dto);
      return Ok(response);
    }

    [HttpPut]
    [Route("/api/inspect-collect/response/{icResponseId}")]
    public async Task<ActionResult> Put(Guid icResponseId, CancellationToken cancellationToken)
    {
      string json;
      using (StreamReader reader = new StreamReader(Request.Body))
      {
        json = await reader.ReadToEndAsync();
      }

      var dto = JsonConvert.DeserializeObject<ICResponseUpdateDTO>(json);

      var ipAddress = Request.Headers["X-Forwarded-For"].FirstOrDefault();

      if (ipAddress == null)
      {
        var ip = HttpContext.Connection.RemoteIpAddress;

        if (ip != null)
        {
          if (ip.IsIPv4MappedToIPv6)
          {
            ipAddress = ip.MapToIPv4().ToString();
          }
          else
          {
            ipAddress = ip.ToString();
          }
        }
      }

      if (ipAddress != null && dto != null)
      {
        dto.ResponseGeoIP = ipAddress;
      }

      try
      {
        var res = await _icResponseInputService.UpdateInputs(icResponseId, dto);
        return Ok(res);
      }
      catch (Exception ex)
      {
        return Ok();

      }
    }
  }
}