﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO.Imports.Invent;
using Trading.API.Data.Models;
using Trading.API.Data.Models.InventData;

namespace Trading.Services.Interfaces.InventImports;

/// <summary>
/// Interface for mapping auction data to vehicle entities
/// </summary>
public interface IAuctionVehicleMappingService
{
  /// <summary>
  /// Maps an auction lot from the Auction API to a Vehicle entity
  /// </summary>
  /// <param name="lotData">data for a lot from the Auction API</param>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>A Vehicle object populated with data from the API</returns>
  Task<Vehicle> MapFromAuctionLotAsync(InventAuctionLot lot, CancellationToken cancellationToken = default);
}
