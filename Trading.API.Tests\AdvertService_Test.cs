using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.API.Tests.Common;
using Trading.API.Tests.Common.Helpers;
using Trading.API.Tests.Common.Params;
using Trading.Services.Classes;
using Trading.Services.ExternalDTO;
using Trading.Services.Interfaces;
using Xunit;

namespace Trading.API.Tests
{
  [Collection("DatabaseCollection")]
  public class AdvertService_Test : TestBase
  {
    DatabaseFixture _fixture;

    public AdvertService_Test(DatabaseFixture fixture)
    {
      _fixture = fixture;
    }

    private IAdvertService GetService()
    {
      IContactActionService contactActionService = new ContactActionService(_context, _mapper, _common.UserService, null, null, null);

      var stripeDTO = new Mock<IOptionsSnapshot<StripeDTO>>();
      stripeDTO.SetupAllProperties();
      stripeDTO.SetupGet(p => p.Value).Returns(new StripeDTO { SecretKey = "Fake Key" });

      var logger = new Mock<ILogger<StripeService>>().Object;
      IStripeService stripeService = new StripeService(logger, stripeDTO.Object, _context, _common.XeroService, contactActionService, null, null, null, null, null, null);

      ICustomerOrderService customerOrderService = new CustomerOrderService(_context, _common.XeroService, _mapper, _common.FileStorageService, null, stripeService);

      IDealService dealService = new DealService(_context, _mapper, null, null, customerOrderService, contactActionService, _common.StaffNotificationService);

      INegotiationService negotiationService = new NegotiationService(_context, _mapper, _common.UserService);
      IEmailService emailService = new Mock<IEmailService>().Object;

      var dpDTO = new Mock<IOptionsSnapshot<DefaultPlatformDTO>>();
      dpDTO.SetupAllProperties();
      dpDTO.SetupGet(p => p.Value).Returns(new DefaultPlatformDTO { PlatformId = 1 });

      IBidService bidService = new BidService(dealService, null, _context, _mapper, _common.MessageService, _common.InMailService,
        negotiationService, emailService, dpDTO.Object, null, null, _common.StaffNotificationService);

      return new AdvertService(_context, bidService, _mapper, _common.ContactService, _common.CustomerService, _common.LookupService, _common.UserService, _common.MessageService, null, _common.ContactActionService, null, _common.CustomerInternalInfoService, _common.ServiceQueueService, _common.VehicleCheckService);
    }

    private IBidService GetBidService()
    {
      var dpDTO = new Mock<IOptionsSnapshot<DefaultPlatformDTO>>();
      dpDTO.SetupAllProperties();
      dpDTO.SetupGet(p => p.Value).Returns(new DefaultPlatformDTO { PlatformId = 1 });
      IBidService service = new BidService(_common.DealService, _common.DbConnection, _context, _mapper, _common.MessageService,
        _common.InMailService, _common.NegotiationService, _common.EmailService, dpDTO.Object, null, null, _common.StaffNotificationService);

      return service;
    }

    [Fact]
    public async Task PublishAdvert()
    {
      // setup the in-memory db context 
      //var context = CreateDataContext();

      // test 1 - create an advert 
      AdvertDTO advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD21FPX");
      var advert = await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        buyItNowPrice = 25000,
        startPrice = 1000,
        acceptBids = true,
        bidIncrement = 100,
        saleType = SaleTypeEnum.BuyNow
      });

      // set required properties
      advert.Vehicle.VatStatusId = 1;
      advert.Vehicle.VehicleMedia = new List<VehicleMedia> { new VehicleMedia { MediaTypeId = (uint)Data.Enums.MediaTypeEnum.Image } };
      advert.BuyItNowPrice = 20000;

      await _context.SaveChangesAsync();

      // create advert service instance 
      var service = GetService();

      var exception = await Record.ExceptionAsync(async () => await service.PublishAdvert(_testBase.BaseContactIdGuid, advert.Id, CancellationToken.None));
      Assert.Null(exception); // no exception thrown

      // test published advert properties 
      Assert.True(advert.SoldStatus == Data.Enums.SoldStatusEnum.Active && advert.StatusId == (uint)Data.Enums.SoldStatusEnum.Active);
    }

    [Fact]
    public async Task CreateAdvert()
    {
      var service = GetService();

      // test 1 - create an advert 
      var vehicle = await VehicleHelper.CreateTestVehicle(_context, _lookupFactory, _testBase.BaseCustomerIdGuid, CreateVehicleParams.GetDefault("AD18FPX"));
      var advert = await service.CreateAdvert(new CreateAdvertDTO { VehicleId = vehicle.Id, ContactId = _testBase.BaseContactIdGuid, CustomerId = _testBase.BaseCustomerIdGuid }, CancellationToken.None);

      // assert advert was created
      Assert.True(advert != null && advert.Id.HasValue);

      // assert advert is present in db 
      Assert.True(await _context.Adverts.AnyAsync(x => x.Id == advert.Id));
    }

    [Fact]
    public async Task PublishNoVAT()
    {
      var advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "ANOTHER");
      var advert = await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        saleType = SaleTypeEnum.BuyNow
      });

      // create advert service instance 
      var service = GetService();

      // set price and images
      advert.BuyItNowPrice = 20000;
      advert.Vehicle.VehicleMedia = new List<VehicleMedia> { new VehicleMedia { MediaTypeId = (uint)Data.Enums.MediaTypeEnum.Image } };

      await _context.SaveChangesAsync();

      var id = advert.Id;
      advert = null;

      var ex = await Assert.ThrowsAsync<Exception>(async () => await service.PublishAdvert(_testBase.BaseContactIdGuid, id, CancellationToken.None));
      // can test exact message if required
      //Assert.True(ex.Message == "Actual exception message");
    }

    [Fact]
    public async Task PublishNoPrice()
    {
      var advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "TESTVRM");
      var advert = await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        saleType = SaleTypeEnum.BuyNow
      });

      // create advert service instance 
      var service = GetService();

      // set vat type and images
      advert.Vehicle.VatStatusId = 1;
      advert.Vehicle.VehicleMedia = new List<VehicleMedia> { new VehicleMedia { MediaTypeId = (uint)Data.Enums.MediaTypeEnum.Image } };

      await _context.SaveChangesAsync();

      var id = advert.Id;
      advert = null;

      var ex = await Assert.ThrowsAsync<Exception>(async () => await service.PublishAdvert(_testBase.BaseContactIdGuid, id, CancellationToken.None));
    }

    [Fact]
    public async Task PublishNoImages()
    {
      var advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD19FPX");
      var advert = await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        saleType = SaleTypeEnum.BuyNow
      });

      // create advert service instance 
      var service = GetService();

      // set price and vat
      advert.Vehicle.VatStatusId = 1;
      advert.ReservePrice = 2000;

      // save changes 
      await _context.SaveChangesAsync();

      var id = advert.Id;
      advert = null;

      await Assert.ThrowsAsync<Exception>(async () => await service.PublishAdvert(_testBase.BaseContactIdGuid, id, CancellationToken.None));
    }

    [Fact]
    public async Task RelistByVehicle()
    {
      var advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD10FPX");
      var advert = await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        saleType = SaleTypeEnum.BuyNow,
        vatStatusId = 1,
        reserve = 2000,
      });

      advert.Vehicle.VehicleMedia = new List<VehicleMedia> { new VehicleMedia { MediaTypeId = (uint)Data.Enums.MediaTypeEnum.Image } };

      // create advert service instance 
      var service = GetService();
      await service.PublishAdvert(_testBase.BaseContactIdGuid, advert.Id, CancellationToken.None);

      // advert must be ended in order to relist 
      var bidService = GetBidService();
      await bidService.EndListing(advert.Id, false, null, CancellationToken.None);

      // relist using vehicleId 
      var newId = await service.RelistByVehicle(advert.VehicleId.Value, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, false, CancellationToken.None);

      // old advert sold status should be unsold 
      Assert.True(advert.SoldStatus == SoldStatusEnum.Unsold);

      // we should have a new advert with the existing vehicleId from the old advert 
      var newAd = await _context.Adverts.FirstOrDefaultAsync(x => x.Id == newId && x.VehicleId == advert.VehicleId
        && x.AdvertStatus == AdvertStatusEnum.Inactive && x.SoldStatus == SoldStatusEnum.Draft);

      Assert.NotNull(newAd);
    }

    [Fact]
    public async Task RelistByAdvert()
    {
      var advertDTO = await AdvertHelper.CreateTestAdvert(_context, _mapper, _common, _lookupFactory, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, "AD11FPX");
      var advert = await AdvertHelper.SetAdvertProperties(_context, new AdvertPropertiesDTO()
      {
        adId = advertDTO.Id.Value,
        saleType = SaleTypeEnum.BuyNow,
        vatStatusId = 1,
        reserve = 2000,
      });

      advert.Vehicle.VehicleMedia = new List<VehicleMedia> { new VehicleMedia { MediaTypeId = (uint)Data.Enums.MediaTypeEnum.Image } };

      // create advert service instance 
      var service = GetService();
      await service.PublishAdvert(_testBase.BaseContactIdGuid, advert.Id, CancellationToken.None);

      // advert must be ended in order to relist 
      var bidService = GetBidService();
      await bidService.EndListing(advert.Id, false, null, CancellationToken.None);

      // relist using advertId
      var newId = await service.Relist(advert.Id, _testBase.BaseCustomerIdGuid, _testBase.BaseContactIdGuid, false, CancellationToken.None);

      // old advert sold status should be unsold 
      Assert.True(advert.SoldStatus == SoldStatusEnum.Unsold);

      // we should have a new advert with the existing vehicleId from the old advert 
      var newAd = await _context.Adverts.FirstOrDefaultAsync(x => x.Id == newId && x.VehicleId == advert.VehicleId
        && x.AdvertStatus == AdvertStatusEnum.Inactive && x.SoldStatus == SoldStatusEnum.Draft);

      Assert.NotNull(newAd);
    }

    [Fact]
    public async Task RelistByAdvertAndDealsDeleted()
    {

    }
  }
}
