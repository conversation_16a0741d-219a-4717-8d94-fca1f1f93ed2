﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Models.Prospects;
using Trading.API.Data.Models;

namespace Trading.API.Data.DTO.Prospects
{
  public class BrokerageDTO : BaseModelEntityDTO
  {
    public Guid? AdvertId { get; set; }

    // manager for the advert
    public Guid? ContactId { get; set; }
    public virtual ContactDTO Contact { get; set; }

    public uint? BrokerageStateId { get; set; }
  }
}
