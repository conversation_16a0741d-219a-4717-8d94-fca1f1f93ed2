﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums.InspectCollect;

namespace Trading.API.Data.Models.InspectCollect.VehicleData;

[Table("ICVehicleOutcome")]
public class ICOutcome : BaseModelEntityInt
{
  [ForeignKey("ICContainerGroup")]
  public Guid ICContainerGroupId { get; set; }
  public virtual ICContainerGroup ICContainerGroup { get; set; }

  [MaxLength(100)]
  public string Outcome { get; set; }

  [MaxLength(20)]
  public string Colour { get; set; }

  [MaxLength(80)]
  public string ExternalLocationRef { get; set; }

  public ICOutcomeTypeEnum OutcomeType { get; set; }

  // for disposal types we need to know the destination - i.e. CITY, or TRADE 

  [MaxLength(12)]
  public string DisposalCode { get; set; }
}
