﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.Totals
{
  public class CustomerWatchlistTotalsDTO
  {
    public Guid CustomerId { get; set; }
    public string CustomerName { get; set; }
    public string CustomerEmail { get; set; }
    public int WatchedCount { get; set; }
    public DateTime? LatestWatchedDate { get; set; }
    public DateTime? LatestLoginDate { get; set; }
  }
}
