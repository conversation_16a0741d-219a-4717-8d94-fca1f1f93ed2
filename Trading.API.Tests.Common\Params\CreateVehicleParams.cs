﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Tests.Common.Params
{
  public struct CreateVehicleParams
  {
    public static CreateVehicleParams GetDefault(string vrm)
    {
      return new CreateVehicleParams { 
        Vrm = vrm,
        Make = "Volvo",
        Model = "XC90",
        Deriv = "AWD R-Sport",
        Transmission = "Automatic",
        Fuel = "Diesel",
        Colour = "Grey",
        Plate = "2017(67)"
      };
    }

    public string Vrm { get; set; }
    public string Make { get; set; }
    public string Model { get; set; }
    public string Deriv { get; set; }
    public string Fuel { get; set; }
    public string Transmission { get; set; }
    public string Plate { get; set; }
    public string Colour { get; set; }
  }
}
