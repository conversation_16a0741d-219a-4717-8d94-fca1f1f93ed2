﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO.Components.AdvertSearch
{
  public class AdvertSearchSummaryAdvertDTO : BaseModelEntityDTO
  {
    public DateTime? EndDateTime { get; set; }
    public uint? BuyItNowPrice { get; set; }
    public uint? CurrentPrice { get; set; }
    public SoldStatusEnum SoldStatus { get; set; }
    public AdvertStatusEnum? AdvertStatus { get; set; }
    public AdvertSearchSummaryVehicleDTO Vehicle { get; set; }
    public AdvertSearchSummarySaleDTO Sale { get; set; }
    public uint? GGG { get; set; }

    public bool ReserveMet { get; set; }
    public uint? StartPrice { get; set; }
  }

  public class AdvertSearchSummaryVehicleDTO : BaseModelEntityDTO
  {
    public Guid CustomerId { get; set; }
    public uint? ImageCount { get; set; }
    public bool? Runner { get; set; }
    public string Vrm { get; set; }
    public string Colour { get; set; }
    public DateTime? MotExpires { get; set; }
    public DateTime? DateOfReg { get; set; }
    public bool? LogBook { get; set; }
    public ushort? EngineCc { get; set; }
    public uint? Co2 { get; set; }
    public ushort? Doors { get; set; }
    public uint? Odometer { get; set; }
    public uint? NoOfKeys { get; set; }
    public uint? Owners { get; set; }
    public ServiceHistoryTypeEnum? ServiceHistoryType { get; set; }
    public string PrimaryImageURL { get; set; }
    public MakeDTO Make { get; set; }
    public ModelDTO Model { get; set; }
    public DerivDTO Deriv { get; set; }
    public FuelTypeDTO FuelType { get; set; }
    public TransmissionTypeDTO TransmissionType { get; set; }
    public PlateDTO Plate { get; set; }
    public BodyTypeDTO BodyType { get; set; }
    public AttribvalDTO VATStatus { get; set; }
    public AdvertSearchSummaryVehicleAddressDTO Address { get; set; }
    public AdvertSearchSummaryVehicleCheckDTO LatestValuation { get; set; }
    public AdvertSearchSummaryProvenanceDTO LatestProvenance { get; set; }
  }

  public class AdvertSearchSummarySaleDTO : BaseModelEntityDTO
  {
    public uint? SaleTypeId { get; set; }
    public string SaleName { get; set; }
    public DateTime? SaleStart { get; set; }
  }

  public class AdvertSearchSummaryVehicleAddressDTO
  {
    public string AddressName { get; set; }
  }

  public class AdvertSearchSummaryVehicleCheckDTO
  {
    public int? PriceClean { get; set; }
  }
  public class AdvertSearchSummaryProvenanceDTO
  {
    public bool? Finance { get; set; }
    public bool? Stolen { get; set; }
    public bool? Scrapped { get; set; }
    public bool? VcaR_Damage { get; set; }
  }
}
