﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums.InspectCollect;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICResponseInputAssetDTO : BaseModelEntityDTO
  {
    public Guid? ICResponseInputId { get; set; }

    public ICResponseInputDTO ICResponseInput { get; set; }
    public string AssetString { get; set; }
    public string AssetURL { get; set; }

    public string Label { get; set; }
    public string Path { get; set; }
    public ICAssetTypeEnum? ICAssetType { get; set; }
  }

  public class ICResponseInputAssetSearchDTO : BaseSearchDTO
  {
    public ICResponseInputAssetSearchFilters Filters { get; set; } = new ICResponseInputAssetSearchFilters();
  }

  public class ICResponseInputAssetSearchFilters : BaseFilter
  {
    public Guid? ICResponseInputId { get; set; }
  }

  public class ICResponseInputAssetCreateDTO
  {
    public Guid? ICResponseInputId { get; set; }
    public Guid? ICResponseId { get; set; } /* We may not have an ICResponseInputId at the point we're uploading */
    public Guid? ICInputId { get; set; } /* We may not have an ICResponseInputId at the point we're uploading */
    public string AssetString { get; set; }
    public string AssetURL { get; set; }
    public ICAssetTypeEnum? ICAssetType { get; set; }
  }
}
