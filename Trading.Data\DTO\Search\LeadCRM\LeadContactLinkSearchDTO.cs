﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.Search.LeadCRM
{
  public class LeadContactLinkSearchDTO : BaseSearchDTO
  {
    public LeadContactLinkFilters Filters { get; set; } = new LeadContactLinkFilters() { };
  }

  public class LeadContactLinkFilters : BaseFilter
  {
    public Guid? Id { get; set; }
    public Guid? LeadContactLinkId { get; set; }
    public Guid? LeadId { get; set; }
    public Guid? LeadContactId { get; set; }

  }
}
