﻿using Microsoft.EntityFrameworkCore;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.API.Data.Models.AppraisalLinkModels;
using Trading.API.Data.Models.Imports;
using Trading.API.Data.Models.Comms;
using Trading.API.Data.Models.LeadCRM;
using Trading.API.Data.Models.Movements;
using Trading.API.Data.Models.UInspections;
using Trading.API.Data.Models.Valuation;
using Trading.API.Data.Models.MechanicalFaults;
using Trading.API.Data.Models.WhosWhoNS;
using Trading.API.Data.Models.Prospects;
using Trading.API.Data.Models.VOIP;
using Trading.API.Data.Models.ContactModels;
using Trading.API.Data.Models.ServiceQueueNS;
using System.Linq;
using Trading.API.Data.Models.InspectCollect;
using System;
using Trading.API.Data.Models.ExtLeads;
using Trading.API.Data.Models.InspectCollect.VehicleData;
using Trading.API.Data.Models.InspectCollect.ResponseConditionItems;
using DocumentFormat.OpenXml.Office2019.Drawing.Ink;
using Trading.API.Data.Models.InventData;
using Trading.API.Data.Models.AutoTrader;

namespace Trading.API.Data
{
  public partial class TradingContext : DbContext
  {

    public TradingContext(DbContextOptions<TradingContext> options)
        : base(options)
    {
    }


    /* SITE SCAN */
    public virtual DbSet<ScanBatch> ScanBatches { get; set; }
    public virtual DbSet<ScanConfig> ScanConfigs { get; set; }
    public virtual DbSet<ScanCustomer> ScanCustomers { get; set; }
    public virtual DbSet<ScanError> ScanErrors { get; set; }
    public virtual DbSet<ScanField> ScanFields { get; set; }
    public virtual DbSet<ScanImage> ScanImages { get; set; }
    public virtual DbSet<ScanQueue> ScanQueues { get; set; }
    public virtual DbSet<ScanSample> ScanSamples { get; set; }
    public virtual DbSet<ScanService> ScanServices { get; set; }
    public virtual DbSet<ScanStage> ScanStages { get; set; }
    public virtual DbSet<ScanStyle> ScanStyles { get; set; }
    public virtual DbSet<ScanVehicle> ScanVehicles { get; set; }

    /* END OF SITE SCAN */

    public virtual DbSet<Address> Addresses { get; set; }
    public virtual DbSet<Advert> Adverts { get; set; }
    public virtual DbSet<Adview> Adviews { get; set; }
    public virtual DbSet<Alt> Alts { get; set; }
    public virtual DbSet<Appraisal> Appraisals { get; set; }
    public virtual DbSet<AppraisalItem> AppraisalItems { get; set; }
    public virtual DbSet<AppraisalMedia> AppraisalMedia { get; set; }
    public virtual DbSet<Attrib> Attribs { get; set; }
    public virtual DbSet<Attribval> Attribvals { get; set; }
    public virtual DbSet<Bid> Bids { get; set; }
    public virtual DbSet<BodyType> BodyTypes { get; set; }
    public virtual DbSet<Contact> Contacts { get; set; }
    public virtual DbSet<ContactRole> ContactRoles { get; set; }
    public virtual DbSet<ContactAction> ContactActions { get; set; }
    public virtual DbSet<ContactComm> ContactComms { get; set; }
    public virtual DbSet<Country> Countries { get; set; }
    public virtual DbSet<Customer> Customers { get; set; }
    public virtual DbSet<CustomerMedia> CustomerMedias { get; set; }
    public virtual DbSet<CustomerAttrib> CustomerAttribs { get; set; }
    public virtual DbSet<CustomerNote> CustomerNotes { get; set; }
    public virtual DbSet<AdminTask> AdminTasks { get; set; }
    public virtual DbSet<CustomerGroup> CustomerGroups { get; set; }
    public virtual DbSet<Deal> Deals { get; set; }
    public virtual DbSet<Deriv> Derivs { get; set; }
    public virtual DbSet<Document> Documents { get; set; }
    public virtual DbSet<DocumentTemplate> DocumentTemplates { get; set; }
    public virtual DbSet<EventQueue> EventQueues { get; set; }
    public virtual DbSet<FuelType> FuelTypes { get; set; }
    public virtual DbSet<Invite> Invites { get; set; }
    public virtual DbSet<Language> Languages { get; set; }
    public virtual DbSet<Make> Makes { get; set; }
    public virtual DbSet<MediaType> MediaTypes { get; set; }
    public virtual DbSet<Media> Media { get; set; }
    public virtual DbSet<Model> Models { get; set; }
    public virtual DbSet<Offer> Offers { get; set; }
    public virtual DbSet<Plate> Plates { get; set; }
    public virtual DbSet<Platform> Platforms { get; set; }
    public virtual DbSet<Role> Roles { get; set; }
    public virtual DbSet<Sale> Sales { get; set; }
    public virtual DbSet<SaleType> SaleTypes { get; set; }
    public virtual DbSet<SavedSearch> SavedSearches { get; set; }
    public virtual DbSet<Search> Searches { get; set; }
    public virtual DbSet<Site> Sites { get; set; }
    public virtual DbSet<Status> Statuses { get; set; }
    public virtual DbSet<TransmissionType> TransmissionTypes { get; set; }
    public virtual DbSet<Vehicle> Vehicles { get; set; }
    public virtual DbSet<VehicleAttrib> VehicleAttribs { get; set; }
    public virtual DbSet<VehicleCheck> VehicleChecks { get; set; }
    public virtual DbSet<VehicleCheckProvider> VehicleCheckProviders { get; set; }
    public virtual DbSet<VehicleMedia> VehicleMedia { get; set; }
    public virtual DbSet<VehicleType> VehicleTypes { get; set; }
    public virtual DbSet<Watchlist> Watchlists { get; set; }
    public virtual DbSet<InMail> InMails { get; set; }
    public virtual DbSet<ContactAttrib> ContactAttribs { get; set; }
    public virtual DbSet<Product> Products { get; set; }
    public virtual DbSet<CountryProduct> CountryProducts { get; set; }
    public virtual DbSet<Tax> Taxes { get; set; }
    public virtual DbSet<CustomerOrder> CustomerOrders { get; set; }
    public virtual DbSet<Currency> Currencies { get; set; }
    public virtual DbSet<OrderLine> OrderLines { get; set; }
    public virtual DbSet<BodyPartGroup> BodyPartGroups { get; set; }
    public virtual DbSet<BodyPart> BodyParts { get; set; }
    public virtual DbSet<Damage> Damages { get; set; }
    public virtual DbSet<DamageSeverity> DamageSeverities { get; set; }
    public virtual DbSet<DamageDetail> DamageDetail { get; set; }
    public virtual DbSet<MOTItem> MOTItems { get; set; }
    public virtual DbSet<MOTHistory> MOTHistories { get; set; }
    public virtual DbSet<ServiceHistory> ServiceHistories { get; set; }
    public virtual DbSet<DVLAData> DVLADatas { get; set; }

    public virtual DbSet<RateCard> RateCards { get; set; }
    public virtual DbSet<CustomerDiscount> CustomerDiscounts { get; set; }
    public virtual DbSet<ProductDiscount> ProductDiscounts { get; set; }
    public virtual DbSet<VehicleColour> VehicleColours { get; set; }
    public virtual DbSet<MileageRange> MileageRanges { get; set; }
    public virtual DbSet<PriceRange> PriceRanges { get; set; }
    public virtual DbSet<CapacityRange> CapacityRanges { get; set; }

    public virtual DbSet<XeroTokenRecord> XeroTokenRecords { get; set; }
    public virtual DbSet<YoutubeTokenRecord> YoutubeTokenRecords { get; set; }

    public virtual DbSet<Bill> Bills { get; set; }
    public virtual DbSet<ListingEndReason> ListingEndReasons { get; set; }
    public virtual DbSet<Negotiation> Negotiations { get; set; }
    public virtual DbSet<NegotiationNote> NegotiationNotes { get; set; }

    public virtual DbSet<SaleAttendee> SaleAttendees { get; set; }
    public virtual DbSet<RostrumMessage> RostrumMessages { get; set; }

    public virtual DbSet<SaleProfile> SaleProfiles { get; set; }
    public virtual DbSet<SaleSearchProfile> SaleSearchProfiles { get; set; }

    public virtual DbSet<CommsTemplate> CommsTemplates { get; set; }
    public virtual DbSet<CommsTemplateExclude> CommsTemplateExcludes { get; set; }
    public virtual DbSet<CommsHistory> CommsHistories { get; set; }
    public virtual DbSet<CommsEvent> CommsEvents { get; set; }


    // Lead CRM
    public virtual DbSet<CampaignParticipant> CampaignParticipants { get; set; }
    public virtual DbSet<Lead> Leads { get; set; }
    public virtual DbSet<LeadCustomer> LeadCustomers { get; set; }
    public virtual DbSet<LeadContact> LeadContacts { get; set; }
    public virtual DbSet<LeadProduct> LeadProducts { get; set; }
    public virtual DbSet<LeadStatus> LeadStatuses { get; set; }
    public virtual DbSet<LeadNote> LeadNotes { get; set; }
    public virtual DbSet<LeadSource> LeadSources { get; set; }
    public virtual DbSet<LeadContactLink> LeadContactLinks { get; set; }
    public virtual DbSet<LeadDocument> LeadDocuments { get; set; }
    public virtual DbSet<LeadEventDocTemplate> LeadEventDocTemplates { get; set; }
    public virtual DbSet<LeadMedia> LeadMedias { get; set; }
    public virtual DbSet<LeadDocumentCategory> LeadDocumentCategories { get; set; }
    public virtual DbSet<Campaign> Campaigns { get; set; }
    public virtual DbSet<CRMAttrib> CRMAttribs { get; set; }
    public virtual DbSet<CampaignLead> CampaignLeads { get; set; }

    public virtual DbSet<CampaignOutcome> CampaignOutcomes { get; set; }

    public virtual DbSet<LeadAction> LeadActions { get; set; }

    public virtual DbSet<VehicleLookupInfo> VehicleLookupInfos { get; set; }

    public virtual DbSet<ValuationPillar> ValuationPillars { get; set; }
    public virtual DbSet<ValuationNode> ValuationNodes { get; set; }
    public virtual DbSet<ValuationProfile> ValuationProfiles { get; set; }

    public virtual DbSet<VehicleValue> VehicleValues { get; set; }

    public virtual DbSet<ValuationQuote> ValuationQuotes { get; set; }

    public virtual DbSet<LeadVehicle> LeadVehicles { get; set; }
    public virtual DbSet<LeadStatusChange> LeadStatusChanges { get; set; }

    /** EXTERNAL APPRAISALS **/

    public virtual DbSet<UInspect> UInspections { get; set; }
    public virtual DbSet<UInspectFormat> UInspectFormats { get; set; }
    public virtual DbSet<UInspectMedia> UInspectMedia { get; set; }
    public virtual DbSet<UInspectAnswer> UInspectAnswers { get; set; }
    public virtual DbSet<UInspectSection> UInspectSections { get; set; }
    public virtual DbSet<UInspectQuestion> UInspectQuestions { get; set; }
    public virtual DbSet<UInspectQuestionOption> UInspectQuestionOptions { get; set; }
    public virtual DbSet<UInspectSectionComplete> UInspectSectionsComplete { get; set; }

    public virtual DbSet<AppraisalLink> AppraisalLinks { get; set; }
    public virtual DbSet<AppraisalLinkData> AppraisalLinkDatas { get; set; }
    public virtual DbSet<LeadVehicleAppraisal> LeadVehicleAppraisals { get; set; }

    /** END EXTERNAL APPRAISALS **/

    public virtual DbSet<Stat> Stats { get; set; }
    public virtual DbSet<CRMUser> CRMUsers { get; set; }

    // MOVEMENTS
    public virtual DbSet<Movement> Movements { get; set; }
    public virtual DbSet<MovementAddress> MovementAddresses { get; set; }

    // SPHINX
    public virtual DbSet<SphAdvert> SphAdvert { get; set; }
    public virtual DbSet<SphAll> SphAll { get; set; }
    public virtual DbSet<SphLead> SphLeads { get; set; }
    public virtual DbSet<SphScanVehicle> SphScanVehicles { get; set; }
    public virtual DbSet<SphLink> SphLinks { get; set; }
    public virtual DbSet<SphUnlotted> SphUnlotted { get; set; }
    public virtual DbSet<SphContactLink> SphContactLink { get; set; }
    public virtual DbSet<SphContact> SphContact { get; set; }

    // IMPORT LOGGING
    public virtual DbSet<Import> Imports { get; set; }
    public virtual DbSet<ImportLog> ImportLogs { get; set; }
    public virtual DbSet<ImportData> ImportDatas { get; set; }

    public virtual DbSet<ApiKey> ApiKeys { get; set; }

    public virtual DbSet<FaultCategory> FaultCategories { get; set; }
    public virtual DbSet<FaultItem> FaultItems { get; set; }
    public virtual DbSet<FaultStatus> FaultStatuses { get; set; }

    public virtual DbSet<VehicleFaultCheck> VehicleFaultChecks { get; set; }
    public virtual DbSet<VehicleFaultCheckItem> VehicleFaultCheckItems { get; set; }
    public virtual DbSet<FaultCheckType> FaultCheckTypes { get; set; }
    public virtual DbSet<FaultCheckTypeCategory> FaultCheckTypeCategories { get; set; }
    public virtual DbSet<WhosWho> WhosWhos { get; set; }
    public virtual DbSet<CustomerInternalInfo> CustomerInternalInfos { get; set; }

    public virtual DbSet<Brokerage> Brokerages { get; set; }
    public virtual DbSet<BrokerageState> BrokerageStates { get; set; }
    public virtual DbSet<Prospect> Prospects { get; set; }
    public virtual DbSet<ProspectState> ProspectStates { get; set; }
    public virtual DbSet<Notification> Notifications { get; set; }
    public virtual DbSet<ProspectAction> ProspectActions { get; set; }
    public virtual DbSet<AdvertNote> AdvertNotes { get; set; }
    public virtual DbSet<AdvertNoteAction> AdvertNoteActions { get; set; }
    public virtual DbSet<CallRecord> CallRecords { get; set; }
    public virtual DbSet<ContactInternalInfo> ContactInternalInfos { get; set; }

    public virtual DbSet<TermsTemplate> TermsTemplates { get; set; }
    public virtual DbSet<ServiceQueue> ServiceQueues { get; set; }
    public virtual DbSet<ScanPrice> ScanPrices { get; set; }
    public virtual DbSet<AICache> AICache { get; set; }
    public virtual DbSet<ICContainer> ICContainers { get; set; }
    public virtual DbSet<ICContainerGroup> ICContainerGroups { get; set; }
    public virtual DbSet<ICContainerWidget> ICContainerWidgets { get; set; }
    public virtual DbSet<ICInput> ICInputs { get; set; }
    public virtual DbSet<ICInputCategory> ICInputCategories { get; set; }
    public virtual DbSet<ICInputOption> ICInputOptions { get; set; }
    public virtual DbSet<ICInputValidation> ICInputValidations { get; set; }
    public virtual DbSet<ICLayout> ICLayouts { get; set; }
    public virtual DbSet<ICTrigger> ICTriggers { get; set; }
    public virtual DbSet<ICResponse> ICResponses { get; set; }
    public virtual DbSet<ICResponseInput> ICResponseInputs { get; set; }
    public virtual DbSet<ICResponseInputAsset> ICResponseInputAssets { get; set; }
    public virtual DbSet<ICResponseInputValue> ICResponseInputValues { get; set; }
    public virtual DbSet<ICWidget> ICWidgets { get; set; }
    public virtual DbSet<ICContainerWidgetInput> ICContainerWidgetInputs { get; set; }
    public virtual DbSet<ICStyle> ICStyles { get; set; }
    public virtual DbSet<ICContainerWidgetLink> ICContainerWidgetLinks { get; set; }
    public virtual DbSet<ICContainerWidgetAsset> ICContainerWidgetAssets { get; set; }
    public virtual DbSet<ICContainerWidgetStyle> ICContainerWidgetStyles { get; set; }
    public virtual DbSet<ICContainerWidgetInputStyle> ICContainerWidgetInputStyles { get; set; }
    public virtual DbSet<ICContainerWidgetInputTrigger> ICContainerWidgetInputTriggers { get; set; }
    public virtual DbSet<ICContainerWidgetInputValidation> ICContainerWidgetInputValidations { get; set; }
    public virtual DbSet<ICAsset> ICAssets { get; set; }

    public virtual DbSet<ICUser> ICUsers { get; set; }

    public virtual DbSet<ICUserRole> ICUserRoles { get; set; }

    public virtual DbSet<ICVehicle> ICVehicles { get; set; }
    public virtual DbSet<ICVehicleCache> ICVehicleCaches { get; set; }

    public virtual DbSet<ICCapData> ICCapDatas { get; set; }
    public virtual DbSet<ICAutoTraderData> ICAutoTraderDatas { get; set; }

    public virtual DbSet<ICCapValuation> ICValuations { get; set; }
    public virtual DbSet<ICCapProvenance> ICProvenances { get; set; }

    public virtual DbSet<ICLocation> ICLocations { get; set; }
    public virtual DbSet<ICUserLocation> ICUserLocations { get; set; }

    public virtual DbSet<ICResponseConditionItem> ICResponseConditionItems { get; set; }
    public virtual DbSet<ICResponseConditionItemMedia> ICResponseConditionItemMedias { get; set; }

    public virtual DbSet<ICAutoTraderSettings> ICAutoTraderSettings { get; set; }

    /* External Leads */
    public virtual DbSet<ExtLead> ExtLeads { get; set; }
    public virtual DbSet<ExtLeadVehicle> ExtLeadVehicles { get; set; }
    public virtual DbSet<ExtLeadVehicleImage> ExtLeadVehicleImages { get; set; }

    /* INVENT Tables */
    public DbSet<InventAuction> InventAuctions { get; set; }
    public DbSet<InventAuctionLot> InventAuctionLots { get; set; }
    public DbSet<InventAuctionLotImage> InventAuctionLotImages { get; set; }

    public DbSet<InventUser> InventUsers { get; set; }

    // AutoTrader data tables
    public virtual DbSet<AutoTraderVehicle> AutoTraderVehicles { get; set; }
    public virtual DbSet<AutoTraderValuation> AutoTraderValuations { get; set; }

    public virtual DbSet<AutoTraderFeatureList> AutoTraderFeatureLists { get; set; }
    public virtual DbSet<AutoTraderFeature> AutoTraderFeatures { get; set; }
    public virtual DbSet<AutoTraderVehicleMetricData> AutoTraderVehicleMetrics { get; set; }
    //


    public virtual DbSet<ICValidation> ICValidations { get; set; }
    public virtual DbSet<ICOutcome> ICOutcomes { get; set; }

    public virtual DbSet<VehicleTyreInfo> VehicleTyreInfos { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {

    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
      modelBuilder.Entity<ScanBatch>(entity =>
      {
        entity.ToTable("ScanBatch");
        entity.HasIndex(e => new { e.Started, e.Priority }, "StartedPriority");
      });

      modelBuilder.Entity<TransmissionType>(entity =>
      {
        entity.ToTable("TransmissionType");
        entity.HasIndex(e => new { e.VehicleTypeId, e.TransmissionTypeName }, "StartedPriority");
      });

      modelBuilder.Entity<CustomerAttrib>(entity =>
      {
        entity.ToTable("CustomerAttrib");
        entity.HasIndex(e => new { e.CustomerId, e.AttribId }, "CustomerAttrib").IsUnique();
      });

      modelBuilder.Entity<LeadContact>(entity =>
      {
        entity.ToTable("LeadContact");
        entity.HasIndex(e => new { e.Email }, "LeadEmail");
      });

      modelBuilder.Entity<LeadCustomer>(entity =>
      {
        entity.ToTable("LeadCustomer");
        entity.HasIndex(e => new { e.Name }, "LeadCustomerName");
      });

      modelBuilder.Entity<LeadVehicle>(entity =>
      {
        entity.ToTable("LeadVehicle");
        entity.HasIndex(e => new { e.ExternalAppraisalSource, e.ExternalAppraisalCode }, "ExternalAppraisalCode");
      });

      modelBuilder.Entity<EventQueue>(entity =>
      {
        entity.ToTable("EventQueue");
        entity.HasIndex(e => new { e.EventSource, e.EventType, e.ExternalId }, "Unique").IsUnique();
      });

      modelBuilder.Entity<UInspect>(entity =>
      {
        entity.ToTable("UInspect");
        entity.HasIndex(e => new { e.UInspectFormatId, e.ExternalRef }, "ExternalRef");
        entity.HasIndex(e => new { e.UInspectFormatId, e.VRM }, "VRM");
        entity.HasIndex(e => new { e.UInspectFormatId, e.Email }, "Email");
        entity.HasIndex(e => new { e.UInspectFormatId, e.MobileNumber }, "Mobile");
      });

      modelBuilder.Entity<UInspectMedia>(entity =>
      {
        entity.ToTable("UInspectMedia");
        entity.HasIndex(e => new { e.UInspectId, e.UInspectSectionId, e.UInspectQuestionId }, "XYZ");
      });

      modelBuilder.Entity<UInspectAnswer>(entity =>
      {
        entity.ToTable("UInspectAnswer");
        entity.HasIndex(e => new { e.UInspectId, e.UInspectQuestionId }, "UniqueAnswer").IsUnique();
      });

      modelBuilder.Entity<UInspectSectionComplete>(entity =>
      {
        entity.ToTable("UInspectSectionComplete");
        entity.HasIndex(e => new { e.UInspectId, e.UInspectSectionId }, "CompleteSection").IsUnique();
      });

      modelBuilder.Entity<LeadContactLink>(entity =>
      {
        entity.ToTable("LeadContactLink");
        entity.HasIndex(e => new { e.LeadId, e.LeadContactId }, "LeadContact").IsUnique();
        entity.HasIndex(e => new { e.LeadContactId, e.LeadId }, "LeadContact2").IsUnique();
      });

      modelBuilder.Entity<LeadNote>(entity =>
      {
        entity.ToTable("LeadNote");
        entity.HasIndex(e => new { e.CreatedByContactId, e.StatusId, e.Reminder }, "Reminders");
        entity.HasIndex(e => new { e.CreatedByContactId, e.StatusId, e.ReminderSet, e.Reminder }, "RemindersSet");
      });

      modelBuilder.Entity<ValuationNode>(entity =>
      {
        entity.ToTable("ValuationNode");
        entity.HasIndex(e => new { e.ValuationProfileId, e.Pillar1, e.Pillar2, e.Pillar3, e.Pillar4, e.Pillar5, e.LookupType });
      });

      modelBuilder.Entity<ContactAttrib>(entity =>
      {
        entity.ToTable("ContactAttrib");
        entity.HasIndex(e => new { e.ContactId, e.AttribId }, "ContactAttrib").IsUnique();
      });

      modelBuilder.Entity<ScanConfig>(entity =>
      {
        entity.ToTable("ScanConfig");
        entity.HasIndex(e => new { e.ScanStyleId, e.ScanStageId, e.ScanFieldId }, "StyleStageField");
        entity.HasIndex(e => new { e.ScanStyleId, e.ScanFieldId }, "StyleField");

        entity.HasOne(d => d.ScanStage)
          .WithMany(p => p.ScanConfigs)
          .HasForeignKey(d => d.ScanStageId);

        entity.HasOne(d => d.ScanField)
          .WithMany(p => p.ScanConfigs)
          .HasForeignKey(d => d.ScanFieldId);
      });

      modelBuilder.Entity<ScanCustomer>(entity =>
      {
        entity.ToTable("ScanCustomer");
        entity.HasIndex(e => new { e.ScanStyleId }, "ScanStyleId");
        entity.HasIndex(e => new { e.StatusId, e.LastScan }, "StatusLastScan");

      });

      modelBuilder.Entity<ScanError>(entity =>
      {
        entity.ToTable("ScanError");
        entity.HasIndex(e => new { e.Cleared, e.ScanCustomerId, e.Added }, "A1");
        entity.HasIndex(e => new { e.Cleared, e.ScanStyleId, e.Added }, "B2");
        entity.HasIndex(e => new { e.Cleared, e.Id, e.Added }, "C1");
      });

      modelBuilder.Entity<ScanImage>(entity =>
      {
        entity.ToTable("ScanImage");
        entity.HasIndex(e => new { e.ScanVehicleId, e.OriginalUrl });
      });

      modelBuilder.Entity<ScanQueue>(entity =>
      {
        entity.ToTable("ScanQueue");
        entity.HasIndex(e => new { e.ScanBatchId }, "A1");
        entity.HasIndex(e => new { e.ScanCustomerId, e.ScanStageId, e.ScanBatchId }, "B2");
        entity.HasIndex(e => new { e.ScanCustomerId, e.ScanStageId, e.ScanUrl }, "C1");

        entity.HasOne(d => d.ScanCustomer)
          .WithMany(p => p.ScanQueues)
          .HasForeignKey(d => d.ScanCustomerId);


      });

      modelBuilder.Entity<ScanSample>(entity =>
      {
        entity.ToTable("ScanSample");
        entity.HasIndex(e => new { e.ScanQueueId, e.ScanStageId }, "SS").IsUnique();
        entity.HasIndex(e => new { e.ScanCustomerId, e.ScanUrl }, "S2");

        entity.HasOne(d => d.ScanQueue)
          .WithMany(d => d.ScanSamples)
          .HasForeignKey(d => d.ScanQueueId);
      });

      modelBuilder.Entity<ScanService>(entity =>
      {
        entity.ToTable("ScanService");

        entity.HasMany(d => d.ScanStyles)
          .WithOne(p => p.ScanService)
          .HasForeignKey(d => d.ScanServiceId);
      });


      modelBuilder.Entity<ScanStyle>(entity =>
      {
        entity.ToTable("ScanStyle");
        entity.HasMany(d => d.ScanStages)
          .WithOne(p => p.ScanStyle)
          .HasForeignKey(d => d.ScanStyleId);
      });

      modelBuilder.Entity<ScanStage>(entity =>
      {
        entity.ToTable("ScanStage");
        entity.HasIndex(e => e.ScanStyleId);
        entity.HasIndex(e => e.NextScanStageId);
      });

      modelBuilder.Entity<ScanVehicle>(entity =>
      {
        entity.ToTable("ScanVehicle");
        entity.HasIndex(e => new { e.ScanCustomerId, e.UniqueId }).IsUnique();
        entity.HasIndex(e => new { e.ScanCustomerId, e.StatusId });
        entity.HasIndex(e => new { e.ScanCustomerId, e.Vrm });
      });

      modelBuilder.Entity<Address>(entity =>
      {
        entity.ToTable("Address")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.StatusId, "IDX_D4E6F816BF700BD");

        entity.HasIndex(e => e.CustomerId, "IDX_D4E6F819395C3F3");

        entity.HasIndex(e => e.CountryId, "IDX_D4E6F81F92F3E70");

        entity.Property(e => e.Added)
            .HasColumnType("datetime")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.AddressCounty)
            .HasColumnType("varchar(80)");

        entity.Property(e => e.AddressLine1)
            .HasColumnType("varchar(80)")
            ;

        entity.Property(e => e.AddressLine2)
            .HasColumnType("varchar(80)")
            ;

        entity.Property(e => e.AddressLine3)
            .HasColumnType("varchar(80)")
            ;

        entity.Property(e => e.AddressPostcode)
            .HasColumnType("varchar(10)")
            ;

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")
            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime");

        entity.HasOne(d => d.Country)
            .WithMany(p => p.Addresses)
            .HasForeignKey(d => d.CountryId)
            .HasConstraintName("FK_D4E6F81F92F3E70");

        entity.HasOne(d => d.Customer)
            .WithMany(p => p.Addresses)
            .HasForeignKey(d => d.CustomerId)
            .HasConstraintName("FK_D4E6F819395C3F3");

        entity.HasOne(d => d.Status)
            .WithMany(p => p.Addresses)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("FK_D4E6F816BF700BD");
      });

      modelBuilder.Entity<Advert>(entity =>
      {
        entity.ToTable("Advert")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.Id, "advert_id_UNIQUE")
            .IsUnique();

        entity.HasIndex(e => e.ContactId, "fk_advert_contact1_idx");

        entity.HasIndex(e => e.SaleId, "fk_advert_sale1_idx");

        entity.HasIndex(e => e.StatusId, "fk_advert_status1_idx");

        entity.HasIndex(e => e.VehicleId, "fk_advert_vehicle1_idx");

        entity.HasIndex(e => new { e.CustomerId, e.AdvertStatus }, "customer_id");

        entity.HasIndex(e => new { e.CustomerId, e.SoldStatus }, "customer_sold");

        entity.HasIndex(e => new { e.StatusId, e.AdvertStatus, e.EndDateTime }, "ended");

        entity.Property(e => e.Added)
            .HasColumnType("datetime")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.AvailableDate)
            .HasColumnType("datetime");

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")
            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime");

        entity.Property(e => e.SoldStatus)
            .HasColumnType("int(10) unsigned")
            .HasDefaultValue(SoldStatusEnum.Draft); // draft

        entity.HasOne(d => d.Contact)
            .WithMany(p => p.Adverts)
            .HasForeignKey(d => d.ContactId)
            .HasConstraintName("fk_advert_contact1");

        entity.HasOne(d => d.Sale)
            .WithMany(p => p.Adverts)
            .HasForeignKey(d => d.SaleId)
            .HasConstraintName("fk_advert_sale1");

        entity.HasOne(d => d.Status)
            .WithMany(p => p.Adverts)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_advert_status1");

        entity.HasOne(d => d.Vehicle)
            .WithMany(p => p.Adverts)
            .HasForeignKey(d => d.VehicleId)
            .HasConstraintName("fk_advert_vehicle1");
      });

      modelBuilder.Entity<CampaignParticipant>(entity =>
      {
        entity.ToTable("CampaignParticipant");
        entity.HasIndex(e => new { e.CampaignId, e.ContactId }, "campaign_participant").IsUnique();
      });

      modelBuilder.Entity<Lead>(entity =>
      {
        entity.ToTable("Lead");

        entity.HasMany(d => d.LeadContactLinks)
          .WithOne(p => p.Lead)
          .HasForeignKey(d => d.LeadId);

        entity.HasIndex(x => new { x.OwnerId, x.StatusId, x.LeadStatusId });
      });

      modelBuilder.Entity<CampaignLead>(entity =>
      {
        entity.ToTable("CampaignLead");
        entity.HasIndex(e => new { e.CampaignId, e.LeadId }, "campaign_leads").IsUnique();
      });

      modelBuilder.Entity<Adview>(entity =>
      {
        entity.ToTable("Adview")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => new { e.ContactId, e.AdvertId }, "contact_advert_id");

        entity.HasIndex(e => e.Id, "adview_id_UNIQUE")
            .IsUnique();

        entity.Property(e => e.Added)
            .HasColumnType("datetime")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")
            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime");

        entity.HasOne(d => d.Advert)
            .WithMany(p => p.Adviews)
            .HasForeignKey(d => d.AdvertId)
            .HasConstraintName("fk_adview_advert1");

        entity.HasOne(d => d.Contact)
            .WithMany(p => p.Adviews)
            .HasForeignKey(d => d.ContactId)
            .HasConstraintName("fk_adview_contact1");

        entity.HasOne(d => d.Status)
            .WithMany(p => p.Adviews)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_adview_status1");
      });

      modelBuilder.Entity<Alt>(entity =>
      {
        entity.ToTable("Alt")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.VehicleTypeId, "IDX_2874E98BDA3FD1FC");

        entity.HasIndex(e => new { e.AltTable, e.AltParentId, e.VehicleTypeId, e.AltVal }, "aaa")
            .IsUnique();

        entity.HasIndex(e => new { e.AltTable, e.AltVal, e.StatusId }, "bbb");

        entity.HasIndex(e => new { e.StatusId }, "ggg");

        entity.HasIndex(e => e.Id, "alt_id")
            .IsUnique();

        entity.Property(e => e.Added)
            .HasColumnType("datetime")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.AltTable)
            .IsRequired()
            .HasColumnType("varchar(30)")
            ;

        entity.Property(e => e.AltVal)
            .IsRequired()
            .HasColumnType("varchar(255)")
            ;

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")
            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime");

        entity.HasOne(d => d.VehicleType)
            .WithMany(p => p.Alts)
            .HasForeignKey(d => d.VehicleTypeId)
            .HasConstraintName("FK_2874E98BDA3FD1FC");
      });

      modelBuilder.Entity<VehicleLookupInfo>(entity =>
      {
        entity.ToTable("VehicleLookupInfo")
          .HasIndex(e => new { e.VRM, e.ServiceProvider, e.StatusId });
      });

      modelBuilder.Entity<Appraisal>(entity =>
      {
        entity.ToTable("Appraisal")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.Id, "appraisal_id_UNIQUE")
            .IsUnique();

        entity.HasIndex(e => e.AppraisedBy, "fk_appraisal_contact1_idx");

        entity.HasIndex(e => e.StatusId, "fk_appraisal_status1_idx");

        entity.HasIndex(e => e.VehicleId, "fk_appraisal_vehicle1_idx");

        entity.Property(e => e.Added)
            .HasColumnType("datetime")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.AppraisalDate)
            .HasColumnType("datetime");

        entity.Property(e => e.AppraisalRef)
            .HasColumnType("varchar(80)")
            ;

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")
            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime");

        entity.HasOne(d => d.AppraisedByNavigation)
            .WithMany(p => p.Appraisals)
            .HasForeignKey(d => d.AppraisedBy)
            .HasConstraintName("fk_appraisal_contact1");

        entity.HasOne(d => d.Status)
            .WithMany(p => p.Appraisals)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_appraisal_status1");

        entity.HasOne(d => d.Vehicle)
            .WithMany(p => p.Appraisals)
            .HasForeignKey(d => d.VehicleId)
            .HasConstraintName("fk_appraisal_vehicle1");
      });

      modelBuilder.Entity<AppraisalItem>(entity =>
      {
        entity.ToTable("AppraisalItem")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.AppraisalId, "IDX_C248E570DD670628");

        entity.HasIndex(e => e.Id, "appraisal_item_id_UNIQUE")
            .IsUnique();

        entity.Property(e => e.ItemDesc)
            .HasColumnType("varchar(255)")
            ;

        entity.Property(e => e.ItemLocation)
            .HasColumnType("varchar(80)")
            ;

        entity.Property(e => e.RepairCost)
            .HasPrecision(5, 2);

        entity.Property(e => e.RepairDesc)
            .HasColumnType("varchar(255)")
            ;

        entity.Property(e => e.Score)
            .HasPrecision(10, 2);

        entity.HasOne(d => d.Appraisal)
            .WithMany(p => p.AppraisalItems)
            .HasForeignKey(d => d.AppraisalId)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("FK_C248E570DD670628");

        entity.HasOne(d => d.BodyPart)
          .WithMany(p => p.AppraisalItems)
          .HasForeignKey(d => d.BodyPartId)
          .OnDelete(DeleteBehavior.ClientSetNull)
          .HasConstraintName("FK_BodyPartId");

        entity.HasOne(d => d.DamageSeverity)
          .WithMany(p => p.AppraisalItems)
          .HasForeignKey(d => d.DamageSeverityId)
          .OnDelete(DeleteBehavior.ClientSetNull)
          .HasConstraintName("FK_DamageSeverityId");

        entity.HasOne(d => d.DamageDetail)
          .WithMany(p => p.AppraisalItems)
          .HasForeignKey(d => d.DamageDetailId)
          .OnDelete(DeleteBehavior.ClientSetNull)
          .HasConstraintName("FK_DamageDetailId");
      });

      modelBuilder.Entity<AppraisalMedia>(entity =>
      {
        entity.HasKey(e => e.Id)
            .HasName("PRIMARY");

        entity.ToTable("AppraisalMedia")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.Id, "appraisal_media_id_UNIQUE")
            .IsUnique();

        entity.HasIndex(e => e.AppraisalItemId, "fk_appraisal_media_appraisal_item1_idx");

        entity.Property(e => e.CustomerRef)
            .HasColumnType("varchar(4096)")
                        ;

        entity.HasOne(d => d.AppraisalItem)
            .WithMany(p => p.AppraisalMedia)
            .HasForeignKey(d => d.AppraisalItemId)
            .HasConstraintName("fk_appraisal_media_appraisal_item1");
      });

      modelBuilder.Entity<Attrib>(entity =>
      {
        entity.ToTable("Attrib")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.Id, "attrib_Id_UNIQUE")
            .IsUnique();

        entity.HasIndex(e => e.AttribName, "attrib_name_UNIQUE")
            .IsUnique();

        entity.HasIndex(e => e.StatusId, "fk_attrib_status1_idx");

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.AttribCode)
            .HasColumnType("varchar(100)")
            ;

        entity.Property(e => e.AttribName)
            .IsRequired()
            .HasColumnType("varchar(45)");

        entity.Property(e => e.AttribType)
            .HasColumnType("int");

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Status)
            .WithMany(p => p.Attribs)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_attrib_status1");

        entity.HasIndex(e => e.AttribCode, "ix_attrib_code");
      });

      modelBuilder.Entity<Attribval>(entity =>
      {
        entity.ToTable("Attribval")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.Id, "attribval_id_UNIQUE")
            .IsUnique();

        entity.HasIndex(e => e.AttribId, "fk_attribval_attrib1_idx");

        entity.HasIndex(e => e.StatusId, "fk_attribval_status1_idx");

        entity.HasIndex(e => e.VehicleTypeId, "fk_attribval_vehicle_type1_idx");

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.AttribvalCode)
            .IsRequired()
            .HasColumnType("varchar(45)");

        entity.Property(e => e.AttribvalName)
            .IsRequired()
            .HasColumnType("varchar(45)");

        entity.Property(e => e.SortOrder)
            .HasColumnType("smallint(6)")
            ;

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Attrib)
            .WithMany(p => p.Attribvals)
            .HasForeignKey(d => d.AttribId)
            .HasConstraintName("fk_attribval_attrib1");

        entity.HasOne(d => d.Status)
            .WithMany(p => p.Attribvals)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_attribval_status1");

        entity.HasOne(d => d.VehicleType)
            .WithMany(p => p.Attribvals)
            .HasForeignKey(d => d.VehicleTypeId)
            .HasConstraintName("fk_attribval_vehicle_type1");
      });

      modelBuilder.Entity<Bid>(entity =>
      {
        entity.ToTable("Bid")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.Id, "bid_id_UNIQUE")
            .IsUnique();

        entity.HasIndex(e => e.AdvertId, "fk_bid_advert1_idx");

        entity.HasIndex(e => e.ContactId, "fk_bid_contact1_idx");

        entity.HasIndex(e => e.StatusId, "fk_bid_status1_idx");

        entity.HasIndex(e => e.BidGuid);

        entity.HasIndex(e => new { e.VendorCustomerId, e.StatusId, e.IsOffer });

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.BidAmt)
            .HasPrecision(10, 30)
            ;

        entity.Property(e => e.Expires)
            .HasColumnType("datetime")
            ;

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Advert)
            .WithMany(p => p.Bids)
            .HasForeignKey(d => d.AdvertId)
            .HasConstraintName("fk_bid_advert1");

        entity.HasOne(d => d.Contact)
            .WithMany(p => p.Bids)
            .HasForeignKey(d => d.ContactId)
            .HasConstraintName("fk_bid_contact1");

        entity.HasOne(d => d.Status)
            .WithMany(p => p.Bids)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_bid_status1");

        entity.HasIndex(e => e.AdvertId, "ix_bid_advertId");
        entity.HasIndex(e => e.ContactId, "ix_bid_contactId");

        entity.HasIndex(e => new { e.IsOffer, e.BidStatus }, "ix_offers");
      });

      modelBuilder.Entity<BodyType>(entity =>
      {
        entity.ToTable("BodyType")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.Id, "body_type_Id_UNIQUE")
            .IsUnique();

        entity.HasIndex(e => e.StatusId, "fk_body_type_status1_idx");

        entity.HasIndex(e => e.VehicleTypeId, "fk_body_type_vehicle_type1_idx");

        entity.HasIndex(e => new { e.VehicleTypeId, e.BodyTypeName }, "vehicle_name")
            .IsUnique();

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.BodyTypeName)
            .IsRequired()
            .HasColumnType("varchar(45)");

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Status)
            .WithMany(p => p.BodyTypes)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_body_type_status1");
      });

      modelBuilder.Entity<Contact>(entity =>
      {
        entity.ToTable("Contact")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.PrimaryAddressId, "IDX_4C62E638CB134313");

        entity.HasIndex(e => new { e.Id, e.Email }, "contact_id_email")
            .IsUnique();

        entity.HasIndex(e => e.CustomerId, "fk_contact_customer1_idx");

        entity.HasIndex(e => e.SiteId, "fk_contact_site1_idx");

        entity.HasIndex(e => e.StatusId, "fk_contact_status1_idx");

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.Email)
            .IsRequired()
            .HasColumnType("varchar(100)");

        entity.Property(e => e.Password)
            .HasColumnType("varchar(100)");

        entity.Property(e => e.Phone1)
            .HasColumnType("varchar(45)");

        entity.Property(e => e.Phone2)
            .HasColumnType("varchar(45)");

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Customer)
            .WithMany(p => p.Contacts)
            .HasForeignKey(d => d.CustomerId)
            .HasConstraintName("fk_contact_customer1");

        entity.HasOne(d => d.PrimaryAddress)
            .WithMany(p => p.Contacts)
            .HasForeignKey(d => d.PrimaryAddressId)
            .HasConstraintName("FK_4C62E638CB134313");

        entity.HasOne(d => d.Site)
            .WithMany(p => p.Contacts)
            .HasForeignKey(d => d.SiteId)
            .HasConstraintName("fk_contact_site1");

        entity.HasOne(d => d.Status)
            .WithMany(p => p.Contacts)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_contact_status1");
      });

      modelBuilder.Entity<ContactAction>(entity =>
      {
        entity.ToTable("ContactAction")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.Id, "contact_action_id_UNIQUE")
            .IsUnique();

        entity.HasIndex(e => e.ContactActionType, "fk_contact_action_attribval1_idx");

        entity.HasIndex(e => e.ContactId, "fk_contact_action_contact1_idx");

        entity.HasIndex(e => e.StatusId, "fk_contact_action_status1_idx");

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Contact)
            .WithMany(p => p.ContactActions)
            .HasForeignKey(d => d.ContactId)
            .HasConstraintName("fk_contact_action_contact1");

        entity.HasOne(d => d.Status)
            .WithMany(p => p.ContactActions)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_contact_action_status1");
      });

      modelBuilder.Entity<ContactComm>(entity =>
      {
        entity.HasKey(e => e.Id)
            .HasName("PRIMARY");

        entity.ToTable("ContactComm")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.Id, "contact_comms_id_UNIQUE")
            .IsUnique();

        entity.HasIndex(e => e.ContactId, "fk_contact_comms_contact1_idx");

        entity.HasIndex(e => e.StatusId, "fk_contact_comms_status1_idx");

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.Read)
            .HasColumnType("tinyint(1)")
            ;

        entity.Property(e => e.Sent)
            .HasColumnType("tinyint(1)")
            ;

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Contact)
            .WithMany(p => p.ContactComms)
            .HasForeignKey(d => d.ContactId)
            .HasConstraintName("fk_contact_comms_contact1");

        entity.HasOne(d => d.Status)
            .WithMany(p => p.ContactComms)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_contact_comms_status1");
      });

      modelBuilder.Entity<Country>(entity =>
      {
        entity.ToTable("Country")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.StatusId, "IDX_5373C9666BF700BD");

        entity.HasIndex(e => e.Id, "country_id_UNIQUE")
            .IsUnique();

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.CountryName)
            .HasColumnType("varchar(45)");

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Status)
            .WithMany(p => p.Countries)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("FK_5373C9666BF700BD");
      });

      modelBuilder.Entity<Customer>(entity =>
      {
        entity.ToTable("Customer")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.Id, "customer_id_UNIQUE")
            .IsUnique();

        entity.HasIndex(e => e.CustomerGroupId, "fk_customer_company_group1_idx");

        entity.HasIndex(e => e.ParentCustomerId, "fk_customer_customer1_idx");

        entity.HasIndex(e => e.BillingCustomerId, "fk_customer_customer2_idx");

        entity.HasIndex(e => e.StatusId, "fk_customer_status1_idx");

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.CustomerName)
            .IsRequired()
            .HasColumnType("varchar(70)");

        entity.Property(e => e.Email)
            .HasColumnType("varchar(100)");

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.Property(e => e.WebsiteUrl)
            .HasColumnType("varchar(100)");

        entity.HasOne(d => d.BillingCustomer)
            .WithMany(p => p.InverseBillingCustomer)
            .HasForeignKey(d => d.BillingCustomerId)
            .HasConstraintName("fk_customer_customer2");

        entity.HasOne(d => d.CustomerGroup)
            .WithMany(p => p.Customers)
            .HasForeignKey(d => d.CustomerGroupId)
            .HasConstraintName("FK_81398E09D2919A68");

        entity.HasOne(d => d.ParentCustomer)
            .WithMany(p => p.InverseParentCustomer)
            .HasForeignKey(d => d.ParentCustomerId)
            .HasConstraintName("fk_customer_customer1");

        entity.HasOne(d => d.Status)
            .WithMany(p => p.Customers)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_customer_status1");

        entity.Property(e => e.BuyingLimit).HasDefaultValue(3);
      });

      modelBuilder.Entity<CustomerGroup>(entity =>
      {
        entity.ToTable("CustomerGroup")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.Id, "customer_group_id")
            .IsUnique();

        entity.HasIndex(e => e.StatusId, "fk_customer_status1_idx");

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.GroupName)
            .HasColumnType("varchar(45)");

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Status)
            .WithMany(p => p.CustomerGroups)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("FK_A3F531FE6BF700BD");
      });

      modelBuilder.Entity<Stat>(entity =>
      {
        entity.ToTable("Stat")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");
        entity.HasIndex(e => new { e.StatName, e.StatName2, e.StatName3, e.StatDate }, "StatName");
        entity.HasIndex(e => new { e.StatName, e.StatName2, e.StatName3, e.StatYear, e.StatMonth, e.StatMonthDay }, "StatName2");
        entity.HasIndex(e => new { e.StatName, e.StatName2, e.StatName3, e.StatYear, e.StatWeek }, "StatName3");
      });

      modelBuilder.Entity<Deal>(entity =>
      {
        entity.ToTable("Deal")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.Id, "deal_id_UNIQUE")
            .IsUnique();


        entity.HasIndex(e => e.AdvertId, "fk_deal_advert1_idx");

        entity.HasIndex(e => e.BidId, "fk_deal_bid1_idx");

        entity.HasIndex(e => e.BuyerContactId, "fk_deal_contact1_idx");

        entity.HasIndex(e => e.SellerContactId, "fk_deal_contact2_idx");

        entity.HasIndex(e => e.StatusId, "fk_deal_status1_idx");

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Advert)
            .WithMany(p => p.Deals)
            .HasForeignKey(d => d.AdvertId)
            .HasConstraintName("fk_deal_advert1");

        entity.HasOne(d => d.BuyerContact)
            .WithMany(p => p.DealBuyerContacts)
            .HasForeignKey(d => d.BuyerContactId)
            .HasConstraintName("fk_deal_contact1");

        entity.HasOne(d => d.SellerContact)
            .WithMany(p => p.DealSellerContacts)
            .HasForeignKey(d => d.SellerContactId)
            .HasConstraintName("fk_deal_contact2");

        entity.HasOne(d => d.Status)
            .WithMany(p => p.Deals)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_deal_status1");

        entity.HasOne(d => d.Bid)
            .WithOne(p => p.Deal)
            .HasForeignKey<Bid>(d => d.DealId)
            .HasConstraintName("fk_bid_deal");

      });

      modelBuilder.Entity<Deriv>(entity =>
      {
        entity.ToTable("Deriv")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => new { e.ModelId, e.DerivName }, "dyz")
            .IsUnique();

        entity.HasIndex(e => e.StatusId, "IDX_FC4499786BF700BD");

        entity.HasIndex(e => e.ModelId, "IDX_FC4499787975B7E7");

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.DerivName)
            .IsRequired()
            .HasColumnType("varchar(60)");

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Model)
            .WithMany(p => p.Derivs)
            .HasForeignKey(d => d.ModelId)
            .HasConstraintName("FK_FC4499787975B7E7");

        entity.HasOne(d => d.Status)
            .WithMany(p => p.Derivs)
            .HasForeignKey(d => d.StatusId)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("FK_FC4499786BF700BD");
      });

      modelBuilder.Entity<FuelType>(entity =>
      {
        entity.ToTable("FuelType")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.StatusId, "fk_fuel_type_status1_idx");

        entity.HasIndex(e => e.FuelTypeName, "fuel_type_type_name");

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.FuelTypeName)
            .IsRequired()
            .HasColumnType("varchar(45)");

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Status)
            .WithMany(p => p.FuelTypes)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_fuel_type_status1");
      });

      modelBuilder.Entity<Language>(entity =>
      {
        entity.ToTable("Language")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.StatusId, "fk_language_status1_idx");

        entity.HasIndex(e => e.Id, "language_id_UNIQUE")
            .IsUnique();

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.LanguageName)
            .IsRequired()
            .HasColumnType("varchar(45)");

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Status)
            .WithMany(p => p.Languages)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_language_status1");
      });

      modelBuilder.Entity<Location>(entity =>
      {
        entity.ToTable("Location")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.PlatformId, "fk_location_platform1_idx");

        entity.HasIndex(e => e.StatusId, "fk_location_status1_idx");

        entity.HasIndex(e => e.Id, "location_Id_UNIQUE")
            .IsUnique();

        entity.HasIndex(e => new { e.PlatformId, e.LocationName }, "platform_location")
            .IsUnique();

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.LocationName)
            .IsRequired()
            .HasColumnType("varchar(45)");

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Platform)
            .WithMany(p => p.Locations)
            .HasForeignKey(d => d.PlatformId)
            .HasConstraintName("fk_location_platform1");

        entity.HasOne(d => d.Status)
            .WithMany(p => p.Locations)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_location_status1");
      });

      modelBuilder.Entity<Make>(entity =>
      {
        entity.ToTable("Make")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.StatusId, "IDX_1ACC766E6BF700BD");

        entity.HasIndex(e => e.VehicleTypeId, "IDX_1ACC766EDA3FD1FC");

        entity.HasIndex(e => e.Id, "make_id")
            .IsUnique();

        entity.HasIndex(e => new { e.VehicleTypeId, e.MakeName }, "make_veh_name")
            .IsUnique();

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.MakeName)
            .IsRequired()
            .HasColumnType("varchar(60)");

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Status)
            .WithMany(p => p.Makes)
            .HasForeignKey(d => d.StatusId)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("FK_1ACC766E6BF700BD");
      });

      modelBuilder.Entity<MediaType>(entity =>
      {
        entity.ToTable("MediaType")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.StatusId, "fk_media_type_status1_idx");

        entity.HasIndex(e => e.Id, "media_type_id_UNIQUE")
            .IsUnique();

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.MediaTypeName)
            .IsRequired()
            .HasColumnType("varchar(45)");

        entity.Property(e => e.MIMEType)
            .HasColumnType("varchar(50)");

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Status)
            .WithMany(p => p.MediaTypes)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_media_type_status1");
      });

      modelBuilder.Entity<Media>(entity =>
      {
        entity.HasKey(e => e.Id)
            .HasName("PRIMARY");

        entity.ToTable("Media")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.StatusId, "fk_media_status1_idx");

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Status)
            .WithMany(p => p.Media)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_media_status1");
      });

      modelBuilder.Entity<Model>(entity =>
      {
        entity.ToTable("Model")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.StatusId, "IDX_D79572D96BF700BD");

        entity.HasIndex(e => e.MakeId, "fk_make_id");

        entity.HasIndex(e => new { e.MakeId, e.Id }, "make_model")
            .IsUnique();

        entity.HasIndex(e => new { e.MakeId, e.ModelName }, "myz")
            .IsUnique();

        entity.HasIndex(e => e.Id, "model_id")
            .IsUnique();

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.ModelName)
            .IsRequired()
            .HasColumnType("varchar(60)");

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Make)
            .WithMany(p => p.Models)
            .HasForeignKey(d => d.MakeId)
            .HasConstraintName("FK_D79572D9CFBF73EB");

        entity.HasOne(d => d.Status)
            .WithMany(p => p.Models)
            .HasForeignKey(d => d.StatusId)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("FK_D79572D96BF700BD");
      });

      modelBuilder.Entity<Offer>(entity =>
      {
        entity.ToTable("Offer")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.BidId, "fk_offer_bid1_idx");

        entity.HasIndex(e => e.ContactId, "fk_offer_contact1_idx");

        entity.HasIndex(e => e.StatusId, "fk_offer_status1_idx");

        entity.HasIndex(e => e.Id, "offer_id_UNIQUE")
            .IsUnique();

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.Expires)
            .HasColumnType("datetime")
            ;

        entity.Property(e => e.OfferAmt)
            .HasPrecision(10, 30)
            ;

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Contact)
            .WithMany(p => p.Offers)
            .HasForeignKey(d => d.ContactId)
            .HasConstraintName("fk_offer_contact1");

        entity.HasOne(d => d.Status)
            .WithMany(p => p.Offers)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_offer_status1");
      });

      modelBuilder.Entity<Plate>(entity =>
      {
        entity.ToTable("Plate")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.PlateName, "country_plate")
            .IsUnique();

        entity.HasIndex(e => e.CountryId, "fk_plate_country1_idx");

        entity.HasIndex(e => e.StatusId, "fk_plate_status1_idx");

        entity.HasIndex(e => e.Id, "plate_id_UNIQUE")
            .IsUnique();

        entity.HasIndex(e => new { e.CountryId, e.PlateName }, "plated_id_UNIQUE")
            .IsUnique();

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.PlateEnd)
            .HasColumnType("date")
            ;

        entity.Property(e => e.PlateName)
            .IsRequired()
            .HasColumnType("varchar(45)");

        entity.Property(e => e.PlateStart)
            .HasColumnType("date")
            ;

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Country)
            .WithMany(p => p.Plates)
            .HasForeignKey(d => d.CountryId)
            .HasConstraintName("fk_plate_country1");

        entity.HasOne(d => d.Status)
            .WithMany(p => p.Plates)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_plate_status1");
      });

      modelBuilder.Entity<CustomerInternalInfo>(entity =>
        entity.HasIndex(e => e.CustomerId, "CID").IsUnique()
      );

      modelBuilder.Entity<CustomerInternalInfo>(entity =>
        entity.HasIndex(e => e.IdPending, "IDP")
      );

      modelBuilder.Entity<CustomerInternalInfo>(entity =>
        entity.HasIndex(e => e.AssignedTo, "AssTo")
      );

      modelBuilder.Entity<AdminTask>(entity =>
      {
        entity.ToTable("AdminTask")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");
        entity.HasIndex(e => new { e.CustomerId, e.StatusId }, "customerId2");
        entity.HasIndex(e => new { e.StatusId, e.SleepUntil }, "activesleep");
      });

      modelBuilder.Entity<Platform>(entity =>
      {
        entity.ToTable("Platform")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.StatusId, "fk_platform_status1_idx");

        entity.HasIndex(e => e.Id, "platform_id_UNIQUE")
            .IsUnique();

        entity.HasIndex(e => e.PlatformName, "platform_name")
            .IsUnique();

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.PlatformName)
            .IsRequired()
            .HasColumnType("varchar(45)");

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.Property(e => e.OdometerUnits)
            .HasDefaultValue(1);

        entity.Property(e => e.DefaultUnit)
          .HasDefaultValue(1);

        entity.HasOne(d => d.Status)
            .WithMany(p => p.Platforms)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_platform_status1");
      });

      modelBuilder.Entity<Sale>(entity =>
      {
        entity.ToTable("Sale")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.AddressId, "fk_sale_location1_idx");

        entity.HasIndex(e => e.PlatformId, "fk_sale_platform1_idx");

        entity.HasIndex(e => e.SaleTypeId, "fk_sale_sale_type1_idx");

        entity.HasIndex(e => e.StatusId, "fk_sale_status1_idx");

        entity.HasIndex(e => new { e.SaleTypeId, e.LiveBiddingStatus }, "idxSaleTypeBiddingStatus");

        entity.HasIndex(e => e.Id, "sale_id_UNIQUE")
            .IsUnique();

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.SaleEnd)
            .HasColumnType("datetime")
            ;

        entity.Property(e => e.SaleName)
            .IsRequired()
            .HasColumnType("varchar(45)");

        entity.Property(e => e.SaleStart)
            .HasColumnType("datetime")
            ;

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Address)
            .WithMany(p => p.Sales)
            .HasForeignKey(d => d.AddressId)
            .HasConstraintName("fk_sale_location1");

        entity.HasOne(d => d.Platform)
            .WithMany(p => p.Sales)
            .HasForeignKey(d => d.PlatformId)
            .HasConstraintName("fk_sale_platform1");

        entity.HasOne(d => d.SaleType)
            .WithMany(p => p.Sales)
            .HasForeignKey(d => d.SaleTypeId)
            .HasConstraintName("fk_sale_sale_type1");

        entity.HasOne(d => d.Status)
            .WithMany(p => p.Sales)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_sale_status1");
      });

      modelBuilder.Entity<SaleType>(entity =>
      {
        entity.ToTable("SaleType")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.StatusId, "fk_sale_type_status2_idx");

        entity.HasIndex(e => e.Id, "sale_type_id_UNIQUE")
            .IsUnique();

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.SaleTypeName)
            .IsRequired()
            .HasColumnType("varchar(45)");

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Status)
            .WithMany(p => p.SaleTypes)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_sale_type_status2");
      });

      modelBuilder.Entity<SavedSearch>(entity =>
      {
        entity.ToTable("SavedSearch")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.ContactId, "fk_saved_search_contact1_idx");

        entity.HasIndex(e => e.SearchId, "fk_saved_search_search1_idx");

        entity.HasIndex(e => e.StatusId, "fk_saved_search_status1_idx");

        entity.HasIndex(e => e.Id, "saved_search_id_UNIQUE")
            .IsUnique();

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.PauseUntil)
            .HasColumnType("date")
            ;

        entity.Property(e => e.SendUpdates)
            .HasColumnType("tinyint(1)")
            ;

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Contact)
            .WithMany(p => p.SavedSearches)
            .HasForeignKey(d => d.ContactId)
            .HasConstraintName("fk_saved_search_contact1");

        entity.HasOne(d => d.Search)
            .WithMany(p => p.SavedSearches)
            .HasForeignKey(d => d.SearchId)
            .HasConstraintName("fk_saved_search_search1");

        entity.HasOne(d => d.Status)
            .WithMany(p => p.SavedSearches)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_saved_search_status1");
      });

      modelBuilder.Entity<Search>(entity =>
      {
        entity.ToTable("Search")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.ContactId, "fk_search_contact1_idx");

        entity.HasIndex(e => e.Id, "search_id_UNIQUE")
            .IsUnique();

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.Property(e => e.SearchJSONValue).HasMaxLength(16384);

        entity.HasOne(d => d.Contact)
            .WithMany(p => p.Searches)
            .HasForeignKey(d => d.ContactId)
            .HasConstraintName("fk_search_contact1");
      });

      modelBuilder.Entity<Site>(entity =>
      {
        entity.ToTable("Site")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.PlatformId, "fk_site_platform1_idx");

        entity.HasIndex(e => e.StatusId, "fk_site_status1_idx");

        entity.HasIndex(e => e.Id, "site_id_UNIQUE")
            .IsUnique();

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.SiteName)
            .IsRequired()
            .HasColumnType("varchar(45)");

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Platform)
            .WithMany(p => p.Sites)
            .HasForeignKey(d => d.PlatformId)
            .HasConstraintName("fk_site_platform1");

        entity.HasOne(d => d.Status)
            .WithMany(p => p.Sites)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_site_status1");
      });

      modelBuilder.Entity<SphContact>(entity =>
      {
        entity.ToTable("SphContact");
        entity.HasKey(e => e.id).HasName("PRIMARY");
        entity.HasIndex(e => e.phrase, "phrase");
      });

      modelBuilder.Entity<SphContactLink>(entity =>
      {
        entity.ToTable("SphContactLink");
        entity.HasIndex(e => e.ContactId).IsUnique();
      });

      modelBuilder.Entity<SphLink>(entity =>
      {
        entity.ToTable("SphLink");
        entity.HasIndex(e => e.AdvertId).IsUnique();
      });

      modelBuilder.Entity<SphAdvert>(entity =>
      {
        entity.ToTable("SphAdvert");

        entity.HasKey(e => e.id)
          .HasName("PRIMARY");

        entity.HasIndex(e => e.phrase, "phrase");

      });

      modelBuilder.Entity<SphAll>(entity =>
      {
        entity.ToTable("SphAll")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasKey(e => e.id)
          .HasName("PRIMARY");

        entity.HasIndex(e => e.phrase, "phrase");

      });

      modelBuilder.Entity<Status>(entity =>
      {
        entity.ToTable("Status")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.Id, "status_id_UNIQUE")
            .IsUnique();

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.StatusName)
            .HasColumnType("varchar(45)");

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;
      });

      modelBuilder.Entity<TransmissionType>(entity =>
      {
        entity.ToTable("TransmissionType")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.StatusId, "fk_transmission_type_status1_idx");

        entity.HasIndex(e => e.Id, "transmission_type_id_UNIQUE")
            .IsUnique();

        entity.HasIndex(e => e.TransmissionTypeName, "type_name")
            .IsUnique();

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.TransmissionTypeName)
            .IsRequired()
            .HasColumnType("varchar(45)");

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Status)
            .WithMany(p => p.TransmissionTypes)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_transmission_type_status1");
      });

      modelBuilder.Entity<Vehicle>(entity =>
      {
        entity.ToTable("Vehicle")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.ModelId, "IDX_1B80E4867975B7E7");

        entity.HasIndex(e => e.MakeId, "IDX_1B80E486CFBF73EB");

        entity.HasIndex(e => e.DerivId, "IDX_1B80E486ED9148FE");

        entity.HasIndex(e => e.BodyTypeId, "fk_vehicle_body_type1_idx");

        entity.HasIndex(e => e.ContactId, "fk_vehicle_contact1_idx");

        entity.HasIndex(e => e.CustomerId, "fk_vehicle_customer1_idx");

        entity.HasIndex(e => e.FuelTypeId, "fk_vehicle_fuel_type1_idx");

        entity.HasIndex(e => e.PlateId, "fk_vehicle_plate1_idx");

        entity.HasIndex(e => e.StatusId, "fk_vehicle_status1_idx");

        entity.HasIndex(e => e.Vrm, "vrm");

        entity.HasIndex(e => e.TransmissionTypeId, "fk_vehicle_transmission_type1_idx");

        //entity.HasIndex(e => new { e.VehicleMediaId, e.PrimaryImageId }, "fk_vehicle_vehicle_media1_idx");

        entity.HasIndex(e => e.VehicleTypeId, "fk_vehicle_vehicle_type1_idx");

        entity.HasIndex(e => e.Id, "vehicle_id_UNIQUE")
            .IsUnique();

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.Colour)
            .HasColumnType("varchar(45)");

        entity.Property(e => e.CustomerRef)
            .HasColumnType("varchar(45)");

        entity.Property(e => e.DateOfReg)
            .HasColumnType("date")
            ;

        entity.Property(e => e.MotExpires)
            .HasColumnType("date")
            ;

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.Property(e => e.Vin)
            .HasColumnType("varchar(30)");

        entity.Property(e => e.Vrm)
            .HasColumnType("char(10)");

        entity.Property(e => e.OdometerUnit)
          .HasDefaultValue(1);

        entity.HasOne(d => d.BodyType)
            .WithMany(p => p.Vehicles)
            .HasForeignKey(d => d.BodyTypeId)
            .HasConstraintName("fk_vehicle_body_type1");

        entity.HasOne(d => d.Contact)
            .WithMany(p => p.Vehicles)
            .HasForeignKey(d => d.ContactId)
            .HasConstraintName("FK_1B80E486E7A1254A");

        entity.HasOne(d => d.Customer)
            .WithMany(p => p.Vehicles)
            .HasForeignKey(d => d.CustomerId)
            .HasConstraintName("FK_1B80E4869395C3F3");

        entity.HasOne(d => d.Deriv)
            .WithMany(p => p.Vehicles)
            .HasForeignKey(d => d.DerivId)
            .HasConstraintName("FK_1B80E486ED9148FE");

        entity.HasOne(d => d.FuelType)
            .WithMany(p => p.Vehicles)
            .HasForeignKey(d => d.FuelTypeId)
            .HasConstraintName("fk_vehicle_fuel_type1");

        entity.HasOne(d => d.Make)
            .WithMany(p => p.Vehicles)
            .HasForeignKey(d => d.MakeId)
            .HasConstraintName("FK_1B80E486CFBF73EB");

        entity.HasOne(d => d.Model)
            .WithMany(p => p.Vehicles)
            .HasForeignKey(d => d.ModelId)
            .HasConstraintName("FK_1B80E4867975B7E7");

        entity.HasOne(d => d.Plate)
            .WithMany(p => p.Vehicles)
            .HasForeignKey(d => d.PlateId)
            .HasConstraintName("fk_vehicle_plate1");

        entity.HasOne(d => d.Status)
            .WithMany(p => p.Vehicles)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_vehicle_status1");

        entity.HasOne(d => d.TransmissionType)
            .WithMany(p => p.Vehicles)
            .HasForeignKey(d => d.TransmissionTypeId)
            .HasConstraintName("fk_vehicle_transmission_type1");

        entity.HasOne(d => d.VehicleType)
            .WithMany(p => p.Vehicles)
            .HasForeignKey(d => d.VehicleTypeId)
            .HasConstraintName("fk_vehicle_vehicle_type1");

        modelBuilder.Entity<Vehicle>()
          .HasOne(v => v.LatestValuation)
          .WithOne()
          .HasForeignKey<Vehicle>(v => v.LatestValuationId);

        modelBuilder.Entity<Vehicle>()
            .HasOne(v => v.LatestProvenance)
            .WithOne()
            .HasForeignKey<Vehicle>(v => v.LatestProvenanceId);

        modelBuilder.Entity<Vehicle>()
        .HasOne(v => v.LatestAIValuation)
        .WithOne()
        .HasForeignKey<Vehicle>(v => v.LatestAIValuationId);

        modelBuilder.Entity<Vehicle>()
            .HasOne(v => v.LatestVehicleFaultCheck)
            .WithOne()
            .HasForeignKey<Vehicle>(v => v.LatestVehicleFaultCheckId);

        modelBuilder.Entity<VehicleFaultCheckItem>()
          .HasIndex(x => new { x.VehicleFaultCheckId, x.FaultItemId }, "FaultItem")
          .IsUnique();

        //entity.HasOne(d => d.LatestValuation)
        //    .WithOne(p => p.Vehicle)
        //    .HasForeignKey<Vehicle>(d => d.LatestValuationId)
        //    .HasConstraintName("fk_latestvaluation_vehicle");

        //entity.HasOne(d => d.LatestProvenance)
        //    .WithOne(p => p.Vehicle)
        //    .HasForeignKey<Vehicle>(d => d.LatestProvenanceId)
        //    .HasConstraintName("fk_latestprovenance_vehicle");

        //entity.HasMany(x => x.VehicleChecks)
        //  .WithOne(p => p.Vehicle)
        //  .HasForeignKey(pv => pv.VehicleId);

      });

      modelBuilder.Entity<VehicleAttrib>(entity =>
      {
        entity.ToTable("VehicleAttrib")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.AttribId, "fk_vehicle_attrib_attrib1_idx");

        // removed to allow 1-many for vehicle options
        //entity.HasIndex(e => new { e.VehicleId, e.AttribId }, "vehicle_attrib").IsUnique();

        entity.HasIndex(e => e.AttribvalId, "fk_vehicle_attrib_attribval1_idx");

        entity.HasIndex(e => e.StatusId, "fk_vehicle_attrib_status1_idx");

        entity.HasIndex(e => e.VehicleId, "fk_vehicle_attrib_vehicle1_idx");

        entity.HasIndex(e => e.Id, "vehicle_attrib_id_UNIQUE")
            .IsUnique();

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.AttribChar)
            .HasColumnType("varchar(45)");

        entity.Property(e => e.AttribDatetime)
            .HasColumnType("datetime")
            ;

        entity.Property(e => e.AttribDecimal)
            .HasPrecision(7, 2)
            ;

        entity.Property(e => e.AttribInt)
            .HasColumnType("int(11)")
            ;

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Attrib)
            .WithMany(p => p.VehicleAttribs)
            .HasForeignKey(d => d.AttribId)
            .HasConstraintName("fk_vehicle_attrib_attrib1");

        entity.HasOne(d => d.Attribval)
            .WithMany(p => p.VehicleAttribs)
            .HasForeignKey(d => d.AttribvalId)
            .HasConstraintName("fk_vehicle_attrib_attribval1");

        entity.HasOne(d => d.Status)
            .WithMany(p => p.VehicleAttribs)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_vehicle_attrib_status1");

        entity.HasOne(d => d.Vehicle)
            .WithMany(p => p.VehicleAttribs)
            .HasForeignKey(d => d.VehicleId)
            .HasConstraintName("fk_vehicle_attrib_vehicle1");

      });

      modelBuilder.Entity<VehicleMedia>(entity =>
      {
        entity.HasKey(e => e.Id)
            .HasName("PRIMARY");

        entity.ToTable("VehicleMedia")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.MediaCategoryId, "fk_vehicle_media_attribval1_idx");

        entity.HasIndex(e => e.MediaId, "fk_vehicle_media_media1_idx");

        entity.HasIndex(e => e.MediaTypeId, "fk_vehicle_media_media_type1_idx");

        entity.HasIndex(e => e.StatusId, "fk_vehicle_media_status1_idx");

        entity.HasIndex(e => e.VehicleId, "fk_vehicle_media_vehicle1_idx");

        entity.HasIndex(e => new { e.VehicleId, e.MediaTypeId, e.StatusId }, "Vehicle_Type_Status");

        entity.HasIndex(e => new { e.VehicleId, e.MediaCategoryId, e.StatusId }, "Vehicle_Category_Status");

        entity.HasIndex(e => e.Id, "vehicle_media_id_UNIQUE")
            .IsUnique();

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.MediaCategory)
            .WithMany(p => p.VehicleMedia)
            .HasForeignKey(d => d.MediaCategoryId)
            .HasConstraintName("fk_vehicle_media_attribval1");

        entity.HasOne(d => d.Media)
            .WithMany(p => p.VehicleMedia)
            .HasForeignKey(d => d.MediaId)
            .HasConstraintName("fk_vehicle_media_media1");

        entity.HasOne(d => d.MediaType)
            .WithMany(p => p.VehicleMedia)
            .HasForeignKey(d => d.MediaTypeId)
            .HasConstraintName("fk_vehicle_media_media_type1");

        entity.HasOne(d => d.Status)
            .WithMany(p => p.VehicleMedia)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_vehicle_media_status1");

        entity.HasOne(d => d.Vehicle)
            .WithMany(p => p.VehicleMedia)
            .HasForeignKey(d => d.VehicleId)
            .HasConstraintName("fk_vehicle_media_vehicle1");
      });

      modelBuilder.Entity<VehicleType>(entity =>
      {
        entity.ToTable("VehicleType")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.StatusId, "fk_vehicle_type_status1_idx");

        entity.HasIndex(e => e.Id, "vehicle_type_id_UNIQUE")
            .IsUnique();

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.IsHidden);

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.Property(e => e.VehicleTypeName)
            .IsRequired()
            .HasColumnType("varchar(45)");

        entity.HasOne(d => d.Status)
            .WithMany(p => p.VehicleTypes)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_vehicle_type_status1");
      });

      modelBuilder.Entity<RostrumMessage>(entity =>
      {
        entity.HasIndex(e => new { e.SaleId, e.Replied });
      });

      modelBuilder.Entity<Watchlist>(entity =>
      {
        entity.ToTable("Watchlist")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => e.AdvertId, "fk_watchlist_advert1_idx");

        entity.HasIndex(e => e.ContactId, "fk_watchlist_contact1_idx");

        entity.HasIndex(e => e.StatusId, "fk_watchlist_status1_idx");

        entity.HasIndex(e => e.Id, "watchlist_id_UNIQUE")
            .IsUnique();

        entity.HasIndex(e => new { e.ContactId, e.AdvertId })
            .IsUnique();

        entity.Property(e => e.Added)
            .HasColumnType("datetime")

            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.StatusId)
            .HasColumnType("int(10) unsigned")

            .HasDefaultValue(1u);

        entity.Property(e => e.Updated)
            .HasColumnType("datetime")
            ;

        entity.HasOne(d => d.Advert)
            .WithMany(p => p.Watchlists)
            .HasForeignKey(d => d.AdvertId)
            .HasConstraintName("fk_watchlist_advert1");

        entity.HasOne(d => d.Contact)
            .WithMany(p => p.Watchlists)
            .HasForeignKey(d => d.ContactId)
            .HasConstraintName("fk_watchlist_contact1");

        entity.HasOne(d => d.Status)
            .WithMany(p => p.Watchlists)
            .HasForeignKey(d => d.StatusId)
            .HasConstraintName("fk_watchlist_status1");
      });

      modelBuilder.Entity<Search>(entity =>
      {
        entity.Property(e => e.StatusId)
          .HasDefaultValue(1u);

        entity.Property(e => e.Added)
          .HasDefaultValueSql("CURRENT_TIMESTAMP");
      });

      modelBuilder.Entity<CommsTemplate>(entity =>
      {
        entity.Property(e => e.StatusId)
          .HasDefaultValue(1u);

        entity.Property(e => e.Added)
          .HasDefaultValueSql("CURRENT_TIMESTAMP");
      });

      modelBuilder.Entity<AppraisalMedia>(entity =>
      {
        entity.Property(e => e.StatusId)
          .HasDefaultValue(1u);

        entity.Property(e => e.Added)
          .HasDefaultValueSql("CURRENT_TIMESTAMP");
      });

      modelBuilder.Entity<AppraisalItem>(entity =>
      {
        entity.Property(e => e.StatusId)
          .HasDefaultValue(1u);

        entity.Property(e => e.Added)
          .HasDefaultValueSql("CURRENT_TIMESTAMP");
      });

      modelBuilder.Entity<SphLead>(entity =>
      {
        entity.HasKey(e => e.id)
          .HasName("PRIMARY");

        entity.HasIndex(e => e.phrase, "phrase");
      });

      modelBuilder.Entity<SphScanVehicle>(entity =>
      {
        entity.HasKey(e => e.id)
          .HasName("PRIMARY");

        entity.HasIndex(e => e.phrase, "phrase");
      });


      modelBuilder.Entity<MOTHistory>(entity =>
      {
        entity.Property(e => e.Added)
          .HasDefaultValueSql("CURRENT_TIMESTAMP");
      });

      modelBuilder.Entity<MOTItem>(entity =>
      {
        entity.Property(e => e.Added)
          .HasDefaultValueSql("CURRENT_TIMESTAMP");
      });

      modelBuilder.Entity<Vehicle>(entity =>
      {
        entity.Property(e => e.NoOfKeys).HasDefaultValue(1);
      });

      modelBuilder.Entity<Invite>(entity =>
      {
        entity.HasIndex(e => new { e.CustomerId, e.Email }, "idx_Unique_Customer_Email")
          .IsUnique();
      });

      modelBuilder.Entity<CustomerNote>(entity =>
      {
        entity.HasIndex(e => new { e.StatusId, e.SleepUntil }, "opendate");
      });

      modelBuilder.Entity<Contact>().HasMany(x => x.Roles)
        .WithMany(x => x.Contacts)
        .UsingEntity<ContactRole>(
            x => x.HasOne(x => x.Role)
            .WithMany().HasForeignKey(x => x.RoleId),
            x => x.HasOne(x => x.Contact)
           .WithMany().HasForeignKey(x => x.ContactId));

      modelBuilder.Entity<ContactRole>().HasIndex(i => new { i.ContactId, i.RoleId }).IsUnique();
      modelBuilder.Entity<Role>().HasIndex(i => i.RoleName).IsUnique();

      modelBuilder.Entity<Customer>().HasIndex(i => new { i.CustomerName, i.PlatformId });

      modelBuilder.Entity<InMail>(entity =>
      {
        entity.ToTable("InMails")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasIndex(e => new { e.ToContactId, e.IsRead, e.ToFolder }, "idx_ToContactIsRead");
        entity.HasIndex(e => new { e.FromContactId, e.IsRead, e.FromFolder }, "idx_FromContactIsRead");
        entity.HasIndex(e => new { e.ToContactId, e.ToFolder }, "idx_ToContact");
        entity.HasIndex(e => new { e.FromContactId, e.FromFolder }, "idx_FromContact");
        entity.Property(e => e.Added).HasDefaultValueSql("CURRENT_TIMESTAMP");
      });

      modelBuilder.Entity<Tax>(entity =>
      {
        entity.HasIndex(e => new { e.CountryId, e.TaxCode }).IsUnique();
      });

      modelBuilder.Entity<Product>(entity =>
      {
        entity.HasIndex(e => new { e.ProductName, e.ProductCode }).IsUnique();
      });

      modelBuilder.Entity<CountryProduct>(entity =>
      {
        entity.HasIndex(e => new { e.PlatformId, e.CountryId, e.ProductId }).IsUnique();
      });

      modelBuilder.Entity<CustomerOrder>(entity =>
      {
        entity.HasIndex(e => e.CustomerId);
        entity.HasIndex(e => e.ContactId);
      });

      modelBuilder.Entity<Damage>(entity =>
      {
        entity.HasOne(d => d.BodyPartGroup)
          .WithMany(p => p.Damages)
          .HasForeignKey(d => d.BodyPartGroupId);
      });

      modelBuilder.Entity<CustomerOrder>(entity =>
      {
        entity.Property(e => e.IssuedDate).HasDefaultValueSql("CURRENT_TIMESTAMP");
      });

      modelBuilder.Entity<ListingEndReason>(entity =>
        {
          entity.Property(e => e.StatusId)
          .HasColumnType("int(10) unsigned")
          .HasDefaultValue(1u);
        }
      );

      modelBuilder.Entity<VehicleCheck>(entity =>
      {
        entity.HasIndex(e => new { e.VehicleId, e.Odometer, e.VehicleCheckProviderId, e.Added }).IsUnique();
        entity.HasIndex(e => e.Vrm);
        entity.HasIndex(e => e.Vin);
      });

      modelBuilder.Entity<FaultCheckTypeCategory>(entity =>
      {
        entity.HasIndex(e => new { e.FaultCheckTypeId, e.FaultCategoryId }).IsUnique();
      });

      modelBuilder.Entity<Negotiation>(entity =>
      {
        entity.HasIndex(e => new { e.SleepUntil }).IsUnique();
      });

      modelBuilder.Entity<SphUnlotted>(entity =>
      {
        entity.ToTable("SphUnlotted")
          .HasCharSet("utf8mb4")
          .UseCollation("utf8mb4_general_ci");

        entity.HasKey(e => e.id)
          .HasName("PRIMARY");

        entity.HasIndex(e => e.phrase, "phrase");
      });

      modelBuilder.Entity<VehicleValue>(entity =>
      {
        entity.HasIndex(e => new { e.MakeId, e.ModelId, e.DerivId, e.StatusId });
        entity.HasIndex(e => new { e.CapId, e.StatusId });
        entity.HasIndex(e => e.ExternalId);
      });

      modelBuilder.Entity<ValuationQuote>(entity =>
      {
        entity.HasIndex(e => e.VRM);
      });

      modelBuilder.Entity<ValuationQuoteData>(entity =>
      {
        entity.HasOne(e => e.ValuationQuote).WithOne(p => p.ValuationQuoteData);
      });

      modelBuilder.Entity<LeadContact>(entity =>
      {
        entity.HasIndex(e => e.Email);
        entity.HasIndex(e => e.Mobile);
        entity.Property(e => e.MaxValuationsPerMonth).HasDefaultValue(10);
      });

      modelBuilder.Entity<CRMUser>(entity =>
      {
        entity.HasIndex(e => e.ContactId).IsUnique();
      });

      modelBuilder.Entity<LeadCustomer>(entity =>
      {
        entity.HasIndex(e => e.Email);
        entity.HasIndex(e => e.Mobile);
      });

      modelBuilder.Entity<CRMAttrib>(entity =>
      {
        entity.HasIndex(e => e.AttribId).IsUnique();
      });

      modelBuilder.Entity<SphAdvert>().ToTable("SphAdvert", t => t.ExcludeFromMigrations());
      modelBuilder.Entity<SphAll>().ToTable("SphAll", t => t.ExcludeFromMigrations());
      modelBuilder.Entity<SphUnlotted>().ToTable("SphUnlotted", t => t.ExcludeFromMigrations());
      modelBuilder.Entity<SphLead>().ToTable("SphLead", t => t.ExcludeFromMigrations());
      modelBuilder.Entity<SphScanVehicle>().ToTable("SphScanVehicle", t => t.ExcludeFromMigrations());


      modelBuilder.Entity<AppraisalLink>()
        .HasOne(x => x.AppraisalLinkData)
        .WithOne(x => x.AppraisalLink)
        .HasForeignKey<AppraisalLink>(x => x.AppraisalLinkDataId);

      modelBuilder.Entity<Import>()
        .HasOne(d => d.ImportData)
        .WithOne(p => p.Import)
        .HasForeignKey<ImportData>(d => d.ImportId)
        .HasConstraintName("fk_import_importdata");

      modelBuilder.Entity<VehicleFaultCheck>(entity =>
      {
        entity.HasIndex(e => e.VehicleId);
      });

      modelBuilder.Entity<AdvertNote>()
          .HasOne(an => an.AdminTask)
          .WithOne(at => at.AdvertNote)
          .HasForeignKey<AdvertNote>(an => an.AdminTaskId)
          .OnDelete(DeleteBehavior.SetNull);

      modelBuilder.Entity<CustomerNote>()
          .HasOne(an => an.AdminTask)
          .WithOne(at => at.CustomerNote)
          .HasForeignKey<CustomerNote>(an => an.AdminTaskId)
          .OnDelete(DeleteBehavior.SetNull);

      modelBuilder.Entity<CallRecord>(entity =>
      {
        entity.HasIndex(e => e.Date);
        entity.HasIndex(e => new { e.ContactId, e.Date, e.CallDirection });
      });

      modelBuilder.Entity<ExtLeadVehicle>(entity =>
      {
        entity.HasIndex(e => new { e.ExtLeadId, e.UniqueId }).IsUnique();
      });

      modelBuilder.Entity<ExtLeadVehicle>(entity =>
      {
        entity.HasIndex(e => new { e.ExtLeadId, e.Updated });
      });

      modelBuilder.Entity<ExtLead>(entity =>
      {
        entity.HasIndex(e => new { e.Source, e.Company });
      });

      modelBuilder.Entity<ExtLead>(entity =>
      {
        entity.HasIndex(e => new { e.Source, e.Company }).IsUnique();
      });


      modelBuilder.Entity<Contact>()
            .HasOne(u => u.ContactInternalInfo)
            .WithOne(up => up.Contact)
            .HasForeignKey<ContactInternalInfo>(up => up.ContactId);

      modelBuilder.Entity<ServiceQueue>()
        .Property(e => e.Result)
        .HasColumnType("json");

      modelBuilder
        .Entity<AICache>()
        .Property(a => a.Question)
        .IsRequired()
        .HasMaxLength(1024)
        .UseCollation("utf8mb4_unicode_ci");

      modelBuilder
        .Entity<AICache>()
        .Property(a => a.Response)
        .IsRequired()
        .HasColumnType("text")
        .UseCollation("utf8mb4_unicode_ci");

      modelBuilder
        .Entity<AICache>()
        .HasIndex(a => a.Question)
        .HasDatabaseName("IX_AICache_Question");

      modelBuilder.Entity<Deal>()
        .HasIndex(a => new { a.BuyerContactId, a.Completed });

      modelBuilder.Entity<Customer>()
        .HasMany(c => c.BuyerDeals)
        .WithOne()
        .HasForeignKey(d => d.BuyerCustomerId)
        .OnDelete(DeleteBehavior.Restrict);

      modelBuilder.Entity<ICInputCategory>(entity =>
      {
        entity.HasIndex(e => new { e.ICContainerGroupId, e.FieldName }).IsUnique();
      });


      modelBuilder.Entity<ICInput>(entity =>
      {
        entity.HasIndex(e => new { e.ICContainerGroupId, e.FieldName }).IsUnique();
      });

      modelBuilder.Entity<ICLayout>(entity =>
      {
        entity.HasIndex(e => new { e.ICContainerGroupId, e.IsDefaultLayout });
      });

      modelBuilder.Entity<ICInputOption>(entity =>
      {
        entity.HasIndex(e => new { e.ICInputId, e.Value }).IsUnique();
      });

      modelBuilder.Entity<ICContainerWidgetLink>(entity =>
      {
        entity.HasIndex(e => new { e.ICContainerWidgetId, e.ICLinkedContainerId }).IsUnique();
      });

      // many-to-many relationship between user and location
      modelBuilder.Entity<ICUserLocation>()
          .HasKey(ul => new { ul.ICUserId, ul.ICLocationId });

      // relationship between ICUser and DefaultLocation
      modelBuilder.Entity<ICUser>()
          .HasOne(u => u.DefaultLocation)
          .WithMany()
          .HasForeignKey(u => u.DefaultLocationId)
          .OnDelete(DeleteBehavior.SetNull);

      modelBuilder.Entity<ApiKey>(entity =>
      {
        entity.Property(e => e.Id)
            .HasColumnType("binary(16)")
            .ValueGeneratedOnAdd()
            .HasDefaultValueSql("(UUID())");
      });

      modelBuilder.Entity<ICResponse>(entity =>
      {
        entity.HasIndex(x => x.ICContainerGroupId);
      });

      modelBuilder.Entity<ICResponse>(entity =>
      {
        entity.HasIndex(x => new { x.ICContainerGroupId, x.Updated });
      });

      modelBuilder.Entity<ApiKey>(entity =>
      {
        entity.HasIndex(x => x.Key);
      });

      modelBuilder.Entity<InventAuction>()
        .HasMany(a => a.Lots)
        .WithOne(l => l.Auction)
        .HasForeignKey(l => l.AuctionId);

      modelBuilder.Entity<InventAuctionLot>()
          .HasMany(l => l.Images)
          .WithOne(i => i.AuctionLot)
          .HasForeignKey(i => i.AuctionLotId)
          .OnDelete(DeleteBehavior.Cascade);

      // Configure query filter for interior images
      modelBuilder.Entity<InventAuctionLotImage>()
          .HasQueryFilter(i => !i.IsInterior);

      modelBuilder.Entity<AutoTraderFeature>()
        .HasIndex(f => f.FactoryFitted);


      // ** Vehicle tyre info configuration **
      modelBuilder.Entity<VehicleTyreInfo>(entity =>
      {
        entity.ToTable("VehicleTyreInfo");
        entity.HasKey(x => x.Id);
        entity.Property(x => x.Id)
          .ValueGeneratedOnAdd();
      });

      // Index configurations
      modelBuilder.Entity<VehicleTyreInfo>(entity =>
      {
        entity.HasIndex(x => new { x.VehicleId, x.Position })
          .IsUnique();
      });

      modelBuilder.Entity<VehicleTyreInfo>(entity =>
      {
        entity.HasIndex(x => x.Make);
      });

      modelBuilder.Entity<VehicleTyreInfo>(entity =>
      {
        entity.HasIndex(x => x.Condition);
      });

      modelBuilder.Entity<VehicleTyreInfo>(entity =>
      {
        entity.HasIndex(x => x.VehicleId);
      });

      // Property configurations
      modelBuilder.Entity<VehicleTyreInfo>(entity =>
      {
        entity.Property(x => x.Position)
          .IsRequired()
          .HasMaxLength(10);
      });

      modelBuilder.Entity<VehicleTyreInfo>(entity =>
      {
        entity.Property(x => x.Make)
          .HasMaxLength(50);
      });

      modelBuilder.Entity<VehicleTyreInfo>(entity =>
      {
        entity.Property(x => x.Condition)
          .HasMaxLength(20);
      });

      modelBuilder.Entity<VehicleTyreInfo>(entity =>
      {
        entity.Property(x => x.Depth)
          .HasColumnType("decimal(3,1)");
      });

      // Foreign key relationship 
      modelBuilder.Entity<VehicleTyreInfo>()
        .HasOne(x => x.Vehicle)
        .WithMany(x => x.Tyres)
        .HasForeignKey(x => x.VehicleId)
        .OnDelete(DeleteBehavior.Cascade);

      OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);

  }
}
