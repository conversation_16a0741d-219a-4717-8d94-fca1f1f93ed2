﻿using Microsoft.AspNetCore.JsonPatch;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.Services.ExternalDTO;

namespace Trading.Services.Interfaces
{
  public interface ISaleService
  {
    Task<IEnumerable<SaleDTO>> GetStaticSales(CancellationToken cancellationToken = default);
    Task<Guid?> GetBuyNowSaleId(CancellationToken cancellationToken = default);
    Task<Guid?> GetTimedAuctionSaleId(CancellationToken cancellationToken = default);
    Task<Guid?> GetStaticSaleIdByType(SaleTypeEnum saleType, CancellationToken cancellationToken = default);
    Task<SaleDTO> GetStaticSale(SaleTypeEnum saleType, CancellationToken cancellationToken = default);
    void InvalidateStaticSalesCache();

    Task<IEnumerable<SaleDTO>> GetSalesByVehicleVRM(string vrm, CancellationToken cancellationToken);
    Task<SaleSearchResultDTO> Search(SaleSearchDTO dto, CancellationToken cancellationToken);
    Task<SaleDTO> Create(SaleDTO dto, CancellationToken cancellationToken);
    Task<SaleDTO> Patch(Guid saleId, JsonPatchDocument<Sale> patch, CancellationToken cancellationToken);
    Task<Sale> Get(Guid saleId, BaseSearchDTO dto, CancellationToken cancellationToken);
    Task<bool> SetSaleLots(IEnumerable<SetSaleLotDTO> dto, CancellationToken cancellationToken);
    Task<uint> SetSaleLotCount(Guid saleId, CancellationToken cancellationToken);
    Task<bool> SaleStart(Guid saleId, CancellationToken cancellationToken);
    Task<bool> SalePause(Guid saleId, CancellationToken cancellationToken);
    Task<bool> SaleEnd(Guid saleId, CancellationToken cancellationToken);
    Task<bool> ResetLotEndDates(Guid saleId, CancellationToken cancellationToken);
    Task<bool> GoToNextLot(Guid saleId, Guid currentAdvertId, bool skip, CancellationToken cancellationToken);
    Task<bool> SetCurrentLot(Guid saleId, Guid advertId, CancellationToken cancellationToken);
    Task<uint?> CurrentLotSeq(Guid saleId, CancellationToken cancellationToken);

    Task<int> GetSaleLotCount(Guid saleId, CancellationToken cancellationToken);

    Task<IEnumerable<SaleAttendeeDTO>> GetSaleAttendees(Guid saleId, CancellationToken cancellationToken);
    Task<SaleAttendeeDTO> AddSaleAttendee(SaleAttendeeDTO dto, CancellationToken cancellationToken);
    Task RemoveAttendee(uint attendeeId, CancellationToken cancellationToken);
    Task<SaleSearchResultDTO> SalesLive(bool forceUpdate, CancellationToken cancellationToken);
  }
}
