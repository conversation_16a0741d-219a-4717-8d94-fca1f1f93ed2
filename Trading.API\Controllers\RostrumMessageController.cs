using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/[controller]")]
  [ApiController]
  public class RostrumMessageController : ControllerBase
  {
    private readonly IRostrumMessageService _rostrumMessageService;

    public RostrumMessageController(IRostrumMessageService RostrumMessageService)
    {
      this._rostrumMessageService = RostrumMessageService;
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> RostrumMessage([FromBody] RostrumMessageDTO dto,
      CancellationToken cancellationToken)
    {
      try
      {
        dto.ContactId = this.User.ContactId();
        dto.ContactName = this.User.ContactName();
        dto.CustomerName = this.User.CustomerName();

        var response = await _rostrumMessageService.CreateMessageToRostrum(dto, cancellationToken);
        return Ok(response);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}