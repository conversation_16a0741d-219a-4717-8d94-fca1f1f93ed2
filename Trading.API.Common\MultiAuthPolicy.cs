﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;

namespace Trading.API.Common.Authorization
{
  public class JwtOrApiKeyRequirement : IAuthorizationRequirement
  {
    public string JwtScheme { get; }
    public string ApiKeyScheme { get; }
    public string ApiKeyScope { get; }

    public JwtOrApiKeyRequirement(string jwtScheme, string apiKeyScheme, string apiKeyScope)
    {
      JwtScheme = jwtScheme;
      ApiKeyScheme = apiKeyScheme;
      ApiKeyScope = apiKeyScope;
    }
  }

  public class JwtOrApiKeyHandler : AuthorizationHandler<JwtOrApiKeyRequirement>
  {
    private readonly IHttpContextAccessor _httpContextAccessor;

    public JwtOrApiKeyHandler(IHttpContextAccessor httpContextAccessor)
    {
      _httpContextAccessor = httpContextAccessor;
    }

    protected override Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        JwtOrApiKeyRequirement requirement)
    {
      // Check if user is already authenticated
      if (context.User?.Identity?.IsAuthenticated == true)
      {
        // For JWT authentication, check for cognito:groups claim or a role claim
        bool hasJwtAuth = context.User.HasClaim(c => c.Type == "cognito:groups");

        // For API Key authentication, check for the scope claim
        bool hasApiKeyAuth = context.User.HasClaim(c =>
            c.Type == "scope" && (c.Value == requirement.ApiKeyScope || c.Value == "*"));

        if (hasJwtAuth || hasApiKeyAuth)
        {
          context.Succeed(requirement);
        }
      }
      // If not authenticated through the normal pipeline, 
      // we don't attempt further authentication here as that would 
      // duplicate the authentication middleware's functionality.

      return Task.CompletedTask;
    }
  }
}