﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Threading;
using Trading.Services.ExternalDTO.BIG;
using Trading.Services.Interfaces.Imports;
using System;
using Trading.API.Remarq.Controllers.Extensions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using Trading.API.Data.DTO.Imports;

namespace Trading.API.Remarq.Controllers
{
  [Route("api/import")]
  [ApiController]
  //[Authorize]
  public class ImportController : ControllerBase
  {
    private readonly IImportService _importService;
    private readonly IWebHostEnvironment _webHostEnvironment;

    public ImportController(IImportService importService, IWebHostEnvironment webHostEnvironment)
    {
      _importService = importService;
      this._webHostEnvironment = webHostEnvironment;
    }

    [HttpGet]
    [Route("/api/imports/process")]
    public async Task<IActionResult> ProcessImports(CancellationToken cancellationToken)
    {
      try
      {
        await Task.Run(() => this.HttpContext.Response.KeepConnectionAlive(cancellationToken), cancellationToken);

        await _importService.ProcessImports();

        await HttpContext.Response.CompleteProcess();
        return Ok(new { Text = "Processed Imports" });
      }
      catch (Exception ex)
      {
        await HttpContext.Response.CompleteProcessWithError(ex);
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{importId}/process")]
    public async Task<IActionResult> ProcessImport(Guid importId, CancellationToken cancellationToken)
    {
      try
      {
        await Task.Run(() => this.HttpContext.Response.KeepConnectionAlive(cancellationToken), cancellationToken);

        await _importService.ProcessImports(importId);

        await HttpContext.Response.CompleteProcess();
        return Ok(new { Text = "Processed Import: " + importId.ToString() });
      }
      catch (Exception ex)
      {
        await HttpContext.Response.CompleteProcessWithError(ex);
        return BadRequest(ex);
      }
    }
  }
}
