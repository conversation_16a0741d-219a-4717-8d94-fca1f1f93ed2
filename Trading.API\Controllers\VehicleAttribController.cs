using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.API.Helpers;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/vehicleattribs")]
  [ApiController]
  public class VehicleAttribController : ControllerBase
  {
    private readonly IVehicleAttribService _vehicleAttribService;

    public VehicleAttribController(IVehicleAttribService vehicleAttribService)
    {
      this._vehicleAttribService = vehicleAttribService;
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create(VehicleAttribDTO dto, CancellationToken cancellationToken)
    {
      var response = await _vehicleAttribService.Create(dto, cancellationToken);
      return Ok(response);
    }

    [HttpDelete]
    [Route("{attribId}")]
    public async Task<IActionResult> Delete(uint attribId, CancellationToken cancellationToken)
    {
      var response = await _vehicleAttribService.Delete(attribId, cancellationToken);
      return Ok(response);
    }
  }
}
