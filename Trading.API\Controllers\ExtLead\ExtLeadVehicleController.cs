using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Threading.Tasks;
using System.Threading;
using System;
using Trading.Services.Extensions;
using Trading.Services.Interfaces.ExtLeads;
using Trading.API.Data.DTO.ExtLeads;
using Trading.Services.Classes.ExtLeads;

namespace Trading.API.Controllers.ExtLead
{
  [Route("api/extlead-vehicle")]
  [ApiController]
  [Authorize]
  public class ExtLeadVehicleController : ControllerBase
  {
    public IExtLeadVehicleService _extLeadVehicleService;
    public IMapper _mapper;

    public ExtLeadVehicleController(IExtLeadVehicleService extLeadVehicleService, IMapper mapper)
    {
      _extLeadVehicleService = extLeadVehicleService;
      _mapper = mapper;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<IActionResult> Get(Guid id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = new ExtLeadVehicleSearchDTO();

        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<ExtLeadVehicleSearchDTO>(query);
        }

        var result = await _extLeadVehicleService.Get(id, dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("")]
    [Route("/api/ext-lead/vehicles")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = String.IsNullOrEmpty(query) ? null : JsonConvert.DeserializeObject<ExtLeadVehicleSearchDTO>(query);
        var pageres = await _extLeadVehicleService.Search(dto, cancellationToken);
        return Ok(pageres);
      }
      catch (Exception e)
      {
        return BadRequest(e);
      }
    }


    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create([FromBody] ExtLeadVehicleDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        var result = await _extLeadVehicleService.Create(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<IActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        var response = await _extLeadVehicleService.Delete(id, cancellationToken);

        return Ok(response);
      }

      return Forbid();
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, [FromBody] JsonPatchDocument<Trading.API.Data.Models.ExtLeads.ExtLeadVehicle> patch, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        try
        {
          return Ok(await _extLeadVehicleService.Patch(id, patch, cancellationToken));
        }
        catch (Exception ex)
        {
          return BadRequest(ex);
        }
      }

      return Forbid();
    }

    [HttpGet("{vehicleId}/import")]
    public async Task<IActionResult> ImportVehicle(Guid vehicleId, CancellationToken ct)
    {
      try
      {
        var result = await _extLeadVehicleService.ImportVehicleAsync(vehicleId, ct);
        return Ok(new { success = true, result });
      }
      catch (Exception ex)
      {
        //_logger.LogError(ex, "Error importing vehicle {VehicleId}", vehicleId);
        return StatusCode(500, new { success = false, message = "Import failed" });
      }
    }
  }
}