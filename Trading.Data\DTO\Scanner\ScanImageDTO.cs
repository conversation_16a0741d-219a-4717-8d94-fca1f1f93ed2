using System;
using System.Linq;
using Trading.API.Data.DTO;

namespace Trading.API.Data.Models.DTO
{
  public class ScanImageDTO : BaseModelEntityIntDTO
  {
    public string Url { get; set; }

    public uint Sequence { get; set; }

    public bool Hosted { get; set; }

    public string OriginalUrl { get; set; }
  }

  public class ScanImageSearchDTO : BaseSearchDTO
  {
    public ScanImageFilters Filters { get; set; } = new ScanImageFilters() { };

  }

  public class ScanImageFilters : BaseFilterInt
  {
    public Guid ScanVehicleGuid { get; set; }

  }
}