﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO.Search
{
  public class WhosWhoAdminSearchDTO : BaseSearchDTO
  {
    public WhosWhoAdminSearchFiltersDTO Filters { get; set; } = new WhosWhoAdminSearchFiltersDTO() { };
  }

  public class WhosWhoAdminSearchFiltersDTO : BaseFilterInt
  {
    public Guid? CustomerId { get; set; }
    public int? DaysAgo { get; set; } // context-dependent
    public ContactActionEnum? ContactAction { get; set; }
    
    // if has a value then search for customers who have not logged in for at least this many days
    public int? NotLoggedInDaysAgo { get; set; }

    // if has a value then search for customers who have not listed an advert for at least this many days
    public int? NotAdvertisedDaysAgo { get; set; }

    public Guid? AssignedTo { get; set; }
  }
}
