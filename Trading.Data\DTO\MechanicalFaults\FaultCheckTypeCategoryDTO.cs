﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Models.MechanicalFaults;
using Trading.API.Data.Models;

namespace Trading.API.Data.DTO.MechanicalFaults
{
  public class FaultCheckTypeCategoryDTO : BaseModelEntityInt
  {
    public uint? FaultCheckTypeId { get; set; }
    public FaultCheckTypeDTO FaultCheckType { get; set; }
    public uint? FaultCategoryId { get; set; }
    public FaultCategoryDTO FaultCategory { get; set; }

    public uint Sequence { get; set; }
  }
}
