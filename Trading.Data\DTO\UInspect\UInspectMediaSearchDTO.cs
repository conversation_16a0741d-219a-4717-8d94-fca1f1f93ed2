﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.UInspections
{
  public class UInspectMediaSearchDTO : BaseSearchDTO
  {
    public UInspectMediaSearchFilters Filters { get; set; } = new UInspectMediaSearchFilters() { };

    public class UInspectMediaSearchFilters : BaseFilter
    {
      public Guid? UInspectId { get; set; }
    }
  }
}
