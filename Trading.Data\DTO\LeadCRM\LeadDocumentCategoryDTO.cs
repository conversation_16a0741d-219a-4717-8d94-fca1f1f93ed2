﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums.LeadCRM;
using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO.LeadCRM
{
  public class LeadDocumentCategoryDTO: BaseModelEntityIntDTO
  {
    [MaxLength(60)]
    public string DocumentCategoryName { get; set; }
    public MediaTypeEnum MediaType { get; set; }
    public CRMTypeEnum CRMType { get; set; }
  }

  public class LeadDocumentCategorySearchDTO : BaseSearchDTO
  {
    public LeadDocumentCategoryFiltersDTO Filters { get; set; } = new LeadDocumentCategoryFiltersDTO();
  }
  public class LeadDocumentCategoryFiltersDTO : BaseFilterInt
  {
  }
}
