using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using AutoMapper;
using Trading.API.Data.DTO.DotAdmin;
using Trading.Services.ExternalDTO.Configs;
using Trading.Services.InspectCollect.Interfaces;
using Trading.Services.Exceptions;
using Trading.API.Data;

namespace Trading.Services.InspectCollect.Classes.DotAdmin
{
  /// <summary>
  /// Service for dotAdmin business logic operations
  /// </summary>
  public class DotAdminService : IDotAdminService
  {
    private readonly IDotAdminClient _client;
    private readonly DotAdminDTO _config;
    private readonly ILogger<DotAdminService> _logger;
    private readonly TradingContext _tradingContext;
    private readonly IMapper _mapper;

    public DotAdminService(
      IDotAdminClient client,
      IOptionsSnapshot<DotAdminDTO> config,
      ILogger<DotAdminService> logger,
      TradingContext tradingContext,
      IMapper mapper)
    {
      _client = client ?? throw new ArgumentNullException(nameof(client));
      _config = config.Value ?? throw new ArgumentNullException(nameof(config));
      _logger = logger ?? throw new ArgumentNullException(nameof(logger));
      _tradingContext = tradingContext ?? throw new ArgumentNullException(nameof(tradingContext));
      _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
    }

    public async Task<DotAdminVehicle> CreateVehicleFromICVehicleAsync(
      Guid icResponseId,
      Guid icVehicleId,
      int locationId,
      int customerId,
      CancellationToken cancellationToken = default)
    {
      _logger.LogInformation("Creating dotAdmin vehicle from ICVehicle {ICVehicleId} for response {ICResponseId}",
        icVehicleId, icResponseId);

      // Get ICVehicle data
      var icVehicle = await _tradingContext.ICVehicles
        .Include(v => v.CapData)
        .Include(v => v.AutoTraderData)
        .FirstOrDefaultAsync(v => v.Id == icVehicleId, cancellationToken);

      if (icVehicle == null)
      {
        throw new ArgumentException($"ICVehicle with ID {icVehicleId} not found", nameof(icVehicleId));
      }

      // Validate required fields
      if (string.IsNullOrWhiteSpace(icVehicle.VRM))
      {
        throw new InvalidOperationException($"ICVehicle {icVehicleId} does not have a valid VRM");
      }

      // Create the dotAdmin vehicle request using AutoMapper
      var request = _mapper.Map<DotAdminCreateVehicleRequest>(icVehicle);
      request.VendorId = customerId;
      request.LogisticsLocationId = locationId;
      request.Lookup = true; // Enable lookup for additional vehicle data

      // Apply vehicle type and classification mappings
      //ApplyVehicleTypeAndClassification(request, icVehicle);

      try
      {
        var response = await _client.CreateVehicleAsync(request, cancellationToken);

        _logger.LogInformation("Successfully created dotAdmin vehicle {VehicleId} from ICVehicle {ICVehicleId}",
          response.Vehicle.Id, icVehicleId);

        return response.Vehicle;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error creating dotAdmin vehicle from ICVehicle {ICVehicleId}", icVehicleId);
        throw;
      }
    }

    public async Task<DotAdminVehicle> CreateVehicleAsync(
      string registration,
      string? vin,
      int customerId,
      int locationId,
      bool useLookup = true,
      CancellationToken cancellationToken = default)
    {
      _logger.LogInformation("Creating dotAdmin vehicle with registration {Registration}", registration);

      if (string.IsNullOrWhiteSpace(registration))
      {
        throw new ArgumentException("Registration cannot be null or empty", nameof(registration));
      }

      var request = new DotAdminCreateVehicleRequest
      {
        MotorVehicleRegistration = registration,
        MotorVehicleVin = vin,
        VendorId = customerId,
        LogisticsLocationId = locationId,
        Lookup = useLookup
      };

      var response = await _client.CreateVehicleAsync(request, cancellationToken);

      _logger.LogInformation("Successfully created dotAdmin vehicle {VehicleId}", response.Vehicle.Id);
      return response.Vehicle;
    }

    public async Task<DotAdminVehicle> CreateVehicleWithDetailsAsync(
      DotAdminCreateVehicleRequest request,
      CancellationToken cancellationToken = default)
    {
      _logger.LogInformation("Creating dotAdmin vehicle with detailed request");

      if (request == null)
      {
        throw new ArgumentNullException(nameof(request));
      }

      var response = await _client.CreateVehicleAsync(request, cancellationToken);

      _logger.LogInformation("Successfully created dotAdmin vehicle {VehicleId}", response.Vehicle.Id);
      return response.Vehicle;
    }

    public async Task<List<DotAdminCustomer>> GetAvailableCustomersAsync(CancellationToken cancellationToken = default)
    {
      _logger.LogInformation("Getting available customers from dotAdmin");

      // Try to get customers from current auth first
      if (_client.CurrentAuth?.Customers?.Count > 0)
      {
        _logger.LogInformation("Found {CustomerCount} customers in current auth response", _client.CurrentAuth.Customers.Count);
        return _client.CurrentAuth.Customers;
      }

      // If no customers available, re-authenticate to get fresh list
      if (!string.IsNullOrEmpty(_config.Username) && !string.IsNullOrEmpty(_config.Password))
      {
        _logger.LogInformation("Re-authenticating to get fresh customer list");

        var authResponse = await _client.AuthenticateAsync(
          _config.Username,
          _config.Password,
          cancellationToken: cancellationToken);

        return authResponse.Customers ?? new List<DotAdminCustomer>();
      }

      _logger.LogWarning("No customers available and no credentials configured for re-authentication");
      return new List<DotAdminCustomer>();
    }

    public async Task<Dictionary<string, string>> GetAvailableLocationsAsync(CancellationToken cancellationToken = default)
    {
      _logger.LogInformation("Getting available locations from dotAdmin");

      // Try to get locations from current auth first
      if (_client.CurrentAuth?.Locations?.Count > 0)
      {
        _logger.LogInformation("Found {LocationCount} locations in current auth response", _client.CurrentAuth.Locations.Count);
        return _client.CurrentAuth.Locations;
      }

      // If no locations available, re-authenticate to get fresh list
      if (!string.IsNullOrEmpty(_config.Username) && !string.IsNullOrEmpty(_config.Password))
      {
        _logger.LogInformation("Re-authenticating to get fresh location list");

        var authResponse = await _client.AuthenticateAsync(
          _config.Username,
          _config.Password,
          cancellationToken: cancellationToken);

        return authResponse.Locations ?? new Dictionary<string, string>();
      }

      _logger.LogWarning("No locations available and no credentials configured for re-authentication");
      return new Dictionary<string, string>();
    }

    public async Task<bool> SelectCustomerLocationAsync(
      int customerId,
      int locationId,
      CancellationToken cancellationToken = default)
    {
      try
      {
        _logger.LogInformation("Selecting customer {CustomerId} and location {LocationId}", customerId, locationId);

        var response = await _client.SelectCustomerLocationAsync(customerId, locationId, cancellationToken);

        if (response.Success)
        {
          _logger.LogInformation("Successfully selected customer and location");
          return true;
        }

        _logger.LogWarning("Failed to select customer and location");
        return false;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error selecting customer and location");
        return false;
      }
    }

    // New helper methods for vehicle type and classification support

    public DotAdminVehicleType GetVehicleType(string vehicleTypeName)
    {
      var mappedType = DotAdminEnumHelper.MapVehicleType(vehicleTypeName);
      _logger.LogDebug("Mapped vehicle type '{VehicleTypeName}' to {MappedType}", vehicleTypeName, mappedType);
      return mappedType ?? DotAdminVehicleType.MotorVehicleMotorcycle;
    }

    public DotAdminVehicleClassification GetVehicleClassification(string bodyType, string vehicleType = null)
    {
      var mappedClassification = DotAdminEnumHelper.MapVehicleClassification(bodyType, vehicleType);
      _logger.LogDebug("Mapped body type '{BodyType}' with vehicle type '{VehicleType}' to {MappedClassification}",
        bodyType, vehicleType, mappedClassification);
      return mappedClassification ?? DotAdminVehicleClassification.Car;
    }

    public async Task<bool> ValidateCustomerLocationAsync(int customerId, int locationId, CancellationToken cancellationToken = default)
    {
      try
      {
        var customers = await GetAvailableCustomersAsync(cancellationToken);
        var locations = await GetAvailableLocationsAsync(cancellationToken);

        var customerExists = customers.Any(c => c.IdAsInt == customerId);
        var locationExists = locations.ContainsKey(locationId.ToString());

        if (!customerExists)
        {
          _logger.LogWarning("Customer {CustomerId} not found in available customers", customerId);
        }

        if (!locationExists)
        {
          _logger.LogWarning("Location {LocationId} not found in available locations", locationId);
        }

        return customerExists && locationExists;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error validating customer {CustomerId} and location {LocationId}", customerId, locationId);
        return false;
      }
    }

    private void ApplyVehicleTypeAndClassification(DotAdminCreateVehicleRequest request, dynamic icVehicle)
    {
      try
      {
        // Extract vehicle type and body style from ICVehicle (adjust properties as needed)
        var vehicleTypeName = icVehicle?.VehicleType?.ToString();
        var bodyStyleName = icVehicle?.BodyStyle?.ToString();

        if (!string.IsNullOrEmpty(vehicleTypeName) || !string.IsNullOrEmpty(bodyStyleName))
        {
          var vehicleType = GetVehicleType(vehicleTypeName);
          var classification = GetVehicleClassification(bodyStyleName, vehicleTypeName);

          request.MotorVehicleTypeId = (int)vehicleType;
          request.MotorVehicleClassificationId = (int)classification;
        }
      }
      catch (Exception ex)
      {
        _logger.LogWarning(ex, "Error applying vehicle type and classification mappings - continuing without them");
        // Don't fail the entire operation if this fails
      }
    }

    private async Task EnsureAuthenticatedAsync(CancellationToken cancellationToken = default)
    {
      if (!_client.IsAuthenticated)
      {
        if (string.IsNullOrEmpty(_config.Username) || string.IsNullOrEmpty(_config.Password))
        {
          throw new InvalidOperationException("No valid authentication and no credentials configured for automatic authentication");
        }

        await _client.AuthenticateAsync(_config.Username, _config.Password, _config.DefaultCustomerId, _config.DefaultLocationId, cancellationToken);
      }
    }
  }
}