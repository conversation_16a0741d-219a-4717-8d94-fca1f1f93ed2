﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.LeadCRM
{
  public class LeadCustomerDTO : LeadBaseModelDTO
  {
    public string Name { get; set; }

    public string Address { get; set; }
    public string Postcode { get; set; }
    public string Email { get; set; }

    public string Phone { get; set; }

    public string Mobile { get; set; }

    public List<LeadDTO> Leads { get; set; }

    public Guid? CustomerId { get; set; } // internal customer (not CRM customer)
    public CustomerDTO Customer { get; set; }

    public Guid? ParentLeadCustomerId { get; set; }
    public LeadCustomerDTO ParentLeadCustomer { get; set; }

    public string Description { get; set; }
  }
}
