﻿
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Movements;
using Trading.API.Data.DTO.Search.Movements;
using Trading.API.Data.Models.Movements;
using Trading.Services.Extensions;
using Trading.Services.Interfaces.Movements;

namespace Trading.API.Controllers.Movements
{
  [Route("api/movement-address")]
  [ApiController]
  [Authorize]
  public class MovementAddressController : ControllerBase
  {
    private readonly IMovementAddressService _movementAddressService;

    public MovementAddressController(IMovementAddressService movementAddressService)
    {
      this._movementAddressService = movementAddressService;
    }


    [HttpGet]
    [Route("{id}")]
    public async Task<IActionResult> Get(uint id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = new MovementAddressSearchDTO();

        if (! String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<MovementAddressSearchDTO>(query);
        }

        var result = await _movementAddressService.Get(id, dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/movement-addresses")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = JsonConvert.DeserializeObject<MovementAddressSearchDTO>(query);

        var result = await _movementAddressService.Search(dto, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create([FromBody] MovementAddressDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        var result = await _movementAddressService.Create(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpDelete]
    [Route("{movementAddressId}")]
    public async Task<IActionResult> Delete(uint movementAddressId, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        var response = await _movementAddressService.Delete(movementAddressId, cancellationToken);

        return Ok(response);
      }

      return Forbid();
    }

    [HttpPatch]
    [Route("{movementAddressId}")]
    public async Task<IActionResult> Patch(uint movementAddressId, [FromBody] JsonPatchDocument<MovementAddress> patch, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        try
        {
          return Ok(await _movementAddressService.Patch(movementAddressId, patch, cancellationToken));
        }
        catch (Exception ex)
        {
          return BadRequest(ex);
        }
      }

      return Forbid();
    }
  }
}
