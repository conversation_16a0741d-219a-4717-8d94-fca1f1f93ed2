﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO
{
  public class ContactRoleSearchDTO : BaseSearchDTO
  {
    public ContactRoleFilters Filters { get; set; } = new ContactRoleFilters() { };
  }
  public class ContactRoleFilters : BaseFilterGuid
  {
    public Guid RoleId { get; set; }
    public string RoleName { get; set; }
    public Guid? ContactCustomerId { get; set; }
    public StatusEnum? ContactStatusId { get; set; }
  }
}
