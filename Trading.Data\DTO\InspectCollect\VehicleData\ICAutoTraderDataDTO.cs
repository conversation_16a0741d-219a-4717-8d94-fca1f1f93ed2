﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.DTO.AutoTrader;

namespace Trading.API.Data.DTO.InspectCollect.VehicleData;

public class ICAutoTraderDataDTO
{
  public Guid? ICVehicleId { get; set; }
  public uint? LatestVehicleId { get; set; }
  public virtual AutoTraderVehicleDTO LatestVehicle { get; set; }

  public uint? LatestVehicleMetricsId { get; set; }
  public virtual AutoTraderVehicleMetricDataDTO LatestVehicleMetrics { get; set; }

  public uint? LatestVehicleValuationsId { get; set; }
  public virtual AutoTraderValuationDTO LatestValuation { get; set; }

  public uint? LatestFeatureListId { get; set; }

  public virtual AutoTraderFeatureListDTO LatestFeatureList { get; set; }
}
