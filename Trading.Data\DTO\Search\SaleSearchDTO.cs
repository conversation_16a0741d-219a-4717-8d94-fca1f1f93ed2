﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;


namespace Trading.API.Data.DTO
{
  public class Filters : BaseFilterGuid
  {
    public Guid? SaleId { get; set; }
    public string SaleName { get; set; }
    public uint? SaleTypeId { get; set; }
    public uint? StatusId { get; set; }
    public Guid? AddressId { get; set; }
    public DateTime? SaleDateFrom { get; set; }
    public DateTime? SaleDateTo { get; set; }

    public List<uint> IncludeSaleTypeIds { get; set; } // if specified only include these sale types

    public List<uint> IncludeStatusIds { get; set; } 
  }

  public class SaleSearchDTO: BaseSearchDTO
  {
    public Filters Filters { get; set; } = new Filters() { };
  }
}
