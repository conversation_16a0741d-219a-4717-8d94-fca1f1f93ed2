﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Models.Prospects;
using Trading.API.Data.Models;

namespace Trading.API.Data.DTO.Prospects
{
  public class ProspectNoteDTO : BaseModelEntityDTO
  {
    public Guid ProspectId { get; set; }

    public uint ProspectActionId { get; set; }
    public ProspectActionDTO ProspectAction { get; set; }

    public string Note { get; set; }


    // the contact of the user who created the history record
    public Guid ContactId { get; set; }

    public virtual ContactDTO Contact { get; set; }

    public DateTime? ReminderDateTime { get; set; }
  }
}
