﻿using DocumentFormat.OpenXml.Office2016.Drawing.ChartDrawing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO.Components.AdvertView
{
  public class AdvertViewBidBoxDTO
  {
    public uint StatusId { get; set; }
    public Guid? CustomerId { get; set; }
    public DateTime? EndDateTime { get; set; }
    public Guid? SaleId { get; set; }
    public int? GGG { get; set; }
    public bool AcceptBids { get; set; }
    public uint CurrentPrice { get; set; }
    public uint BidIncrement { get; set; }
    public bool ReserveMet {  get; set; }
    public int BidCount { get; set; }
    public Guid? TopBidGuid { get; set; }
    public SoldStatusEnum SoldStatus { get; set; }
    public uint? StartPrice { get; set; }
    public AdvertStatusEnum AdvertStatus { get; set; }
    public uint? BuyItNowPrice { get; set; }
    public uint SaleTypeId { get; set; }
    public Guid? CurrentAdvertId { get; set; }
    public uint? UnderwriteReservePrice { get; set; }
    public string MakeName { get; set; }
    public string ModelName { get; set; }
    public string DerivName { get; set; }
    public string PlateName { get; set; }
    public string TransmissionTypeName { get; set; }
    public string FuelTypeName { get; set; }
    public AdvertBidsSummaryDTO BidsSummary { get; set; }
  }

}
