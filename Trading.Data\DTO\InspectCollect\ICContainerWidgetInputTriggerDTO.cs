﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICContainerWidgetInputTriggerDTO : BaseModelEntityDTO
  {
    public Guid ICContainerWidgetStyleId { get; set; }
    public uint? Position { get; set; }
    public ICTriggerDTO ICTrigger { get; set; }
    public Guid? ICTriggerId { get; set; }
  }


  public class ICContainerWidgetInputTriggerSearchDTO : BaseSearchDTO
  {
    public ICContainerWidgetInputTriggerSearchFilters Filters { get; set; } = new ICContainerWidgetInputTriggerSearchFilters();
  }

  public class ICContainerWidgetInputTriggerSearchFilters : BaseFilter
  {
    public Guid? ICContainerWidgetInputId { get; set; }
  }

  public class ICContainerWidgetInputTriggerCreateDTO
  {
    public Guid ICContainerWidgetInputId { get; set; }
    public uint? Position { get; set; }
  }
}
