﻿using Microsoft.AspNetCore.SignalR;
using Moq;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Tests.Common;
using Trading.Services.HubConfig;
using Xunit;

namespace Trading.API.Tests
{
  [Collection("DatabaseCollection")]
  public class SignalR_Test : TestBase
  {
    DatabaseFixture _fixture;

    public SignalR_Test(DatabaseFixture fixture)
    {
      _fixture = fixture;
    }

    [Fact]
    public async Task HubsAreMockableViaDynamic()
    {
      // arrange
      Mock<IHubCallerClients> mockClients = new Mock<IHubCallerClients>();
      Mock<IClientProxy> mockClientProxy = new Mock<IClientProxy>();

      mockClients.Setup(clients => clients.All).Returns(mockClientProxy.Object);

      MessageHub simpleHub = new MessageHub(null, null, null, null, null, _common.MessageService)
      {
        Clients = mockClients.Object
      };

      // act
      await simpleHub.Welcome();

      // assert
      mockClients.Verify(clients => clients.All, Times.Once);

      mockClientProxy.Verify(
          clientProxy => clientProxy.SendCoreAsync(
              "welcome",
              It.Is<object[]>(o => o != null && o.Length == 1 && ((object[])o[0]).Length == 3),
              default(CancellationToken)),
          Times.Once);
    }
  }
}
