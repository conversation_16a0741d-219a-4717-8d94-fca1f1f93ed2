﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.LeadCRM;
using Trading.API.Data.DTO.Search.LeadCRM;
using Trading.API.Data.Models.LeadCRM;
using Trading.Services.Extensions;
using Trading.Services.LeadCRM.Interfaces;

namespace Trading.API.LeadCRM.Controllers
{
  [Route("api/campaign-lead")]
  [ApiController]
  [Authorize]
  public class CampaignLeadController : ControllerBase
  {
    private readonly ICampaignLeadService _campaignLeadService;

    public CampaignLeadController(ICampaignLeadService campaignLeadService)
    {
      this._campaignLeadService = campaignLeadService;
    }

    /* CAMPAIGN LEAD STARTS HERE */

    [HttpGet]
    [Route("/api/campaign-leads")]
    public async Task<IActionResult> SearchCampaignLeads([FromQuery] string? query, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = JsonConvert.DeserializeObject<CampaignLeadSearchDTO>(query);
        var result = await _campaignLeadService.SearchCampaignLeads(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<IActionResult> GetCampaignLead(
      Guid id, 
      [FromQuery] string? query, 
      CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = JsonConvert.DeserializeObject<CampaignLeadSearchDTO>(query);
        var result = await _campaignLeadService.GetCampaignLead(id, cancellationToken, dto);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPut]
    [Route("/api/campaign/{campaignId}/campaign-lead/{leadId}")]
    public async Task<IActionResult> PutCampaignLead(Guid campaignId, Guid leadId, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = new CampaignLeadDTO()
        {
          CampaignId = campaignId,
          LeadId = leadId,
          Added = DateTime.Now,
          Updated = DateTime.Now,
        };
        var result = await _campaignLeadService.CreateCampaignLead(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> CreateCampaignLead([FromBody] CampaignLeadDTO dto, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _campaignLeadService.CreateCampaignLead(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> PatchCampaign([FromBody] JsonPatchDocument<CampaignLead> patch, Guid id, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _campaignLeadService.PatchCampaignLead(id, patch, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    /* END CAMPAIGN */
  }
}
