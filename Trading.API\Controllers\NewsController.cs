﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Net;
using System.Threading.Tasks;

namespace Trading.API.Controllers
{
  [Route("api/news")]
  [ApiController]
  public class NewsController : Controller
  {
    private IMemoryCache _cache;

    public NewsController(IMemoryCache memoryCache)
    {
      _cache = memoryCache;
    }

    [HttpGet]
    [Route("")]
    [ResponseCache(Duration = 600)]
    public async Task<IActionResult> News()
    {
      string cacheKey = "SiteNews";

      string data;

      if (!_cache.TryGetValue(cacheKey, out data))
      {
        var url = "https://www.fleetnews.co.uk/news/latest-fleet-news/rss.xml";
        var webClient = new WebClient();
        data = await webClient.DownloadStringTaskAsync(url);

        _cache.Set(cacheKey, data, TimeSpan.FromHours(1));
      }

      return Ok(data);
    }
  }
}
