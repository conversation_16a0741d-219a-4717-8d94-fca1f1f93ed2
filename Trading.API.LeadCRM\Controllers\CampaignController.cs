﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.LeadCRM;
using Trading.API.Data.DTO.Search.LeadCRM;
using Trading.API.Data.Models.LeadCRM;
using Trading.Services.Extensions;
using Trading.Services.LeadCRM.Interfaces;

namespace Trading.API.LeadCRM.Controllers
{
  [Route("api/campaign")]
  [ApiController]
  [Authorize]
  public class CampaignController : ControllerBase
  {
    private readonly ICampaignService _campaignService;

    public CampaignController(ICampaignService campaignService)
    {
      this._campaignService = campaignService;
    }

    /* CAMPAIGN STARTS HERE */

    [HttpGet]
    [Route("/api/campaigns")]
    public async Task<IActionResult> SearchCampaigns([FromQuery] string? query, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = new CampaignSearchDTO() { };

        if (query != null)
        {
          dto = JsonConvert.DeserializeObject<CampaignSearchDTO>(query);
        }
        var result = await _campaignService.SearchCampaigns(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<IActionResult> GetCampaign([FromQuery] string? query, Guid id, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = JsonConvert.DeserializeObject<CampaignSearchDTO>(query);
        var result = await _campaignService.GetCampaign(id, cancellationToken, dto);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> CreateCampaign([FromBody] CampaignDTO dto, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        dto.CreatedByContactId = User.ContactId();
        dto.UpdatedByContactId = User.ContactId();
        var result = await _campaignService.CreateCampaign(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> PatchCampaign([FromBody] JsonPatchDocument<Campaign> patch, Guid id, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _campaignService.PatchCampaign(id, patch, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPut]
    [Route("{id}/participants")]
    public async Task<IActionResult> SetCampaignParticipants(Guid id, [FromBody] List<Guid> participants, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _campaignService.SetCampaignParticipants(id, participants, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    /* END CAMPAIGN */
  }
}
