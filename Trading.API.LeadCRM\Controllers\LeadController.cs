﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.LeadCRM;
using Trading.API.Data.DTO.Search.LeadCRM;
using Trading.API.Data.DTO.Search.Valuation;
using Trading.API.Data.Enums;
using Trading.API.Data.Models.LeadCRM;
using Trading.Services.Extensions;
using Trading.Services.LeadCRM.Interfaces;
using Trading.Services.Valuations.Interfaces;

namespace Trading.API.LeadCRM.Controllers
{
  [Route("api/lead")]
  [ApiController]
  [Authorize]
  public class LeadController : ControllerBase
  {
    private readonly ILeadCRMService _leadCRMService;
    private readonly IValuationQuoteService _valuationQuoteService;
    private readonly IMarketingProviderService _klaviyo;

    public LeadController(ILeadCRMService leadCRMService, IValuationQuoteService valuationQuoteService, IMarketingProviderService klaviyo)
    {
      this._leadCRMService = leadCRMService;
      this._valuationQuoteService = valuationQuoteService;
      this._klaviyo = klaviyo;
    }

    [HttpGet]
    [Route("/api/leads")]
    public async Task<IActionResult> SearchLeads(CancellationToken cancellationToken, [FromQuery] string? query)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = JsonConvert.DeserializeObject<LeadSearchDTO>(query);
        var result = await _leadCRMService.SearchLeads(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("sphSearch")]
    public async Task<IActionResult> SphSearch(CancellationToken cancellationToken, [FromQuery] string? query)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = JsonConvert.DeserializeObject<LeadSearchDTO>(query);
        var result = await _leadCRMService.SphSearch(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpGet]
    [Route("/api/lead-customers")]
    public async Task<IActionResult> SearchLeadCustomers(CancellationToken cancellationToken, [FromQuery] string? query)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = new LeadCustomerSearchDTO() { };
        if (query != null)
        {
          dto = JsonConvert.DeserializeObject<LeadCustomerSearchDTO>(query);
        }
        var result = await _leadCRMService.SearchLeadCustomers(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [AllowAnonymous]
    [Route("/api/leadVehicle/{leadVehicleId}/request-callback")]
    public async Task<IActionResult> RequestCallBack(Guid leadVehicleId, CancellationToken cancellationToken)
    {
      var dto = new LeadCallbackRequestDTO() { LeadVehicleId = leadVehicleId };

      var result = await _leadCRMService.RequestCallback(dto, cancellationToken);

      if (result.IsValid)
      {
        return Ok(result.DTO.Id);
      }
      else
      {
        return BadRequest();
      }
    }

    [HttpGet]
    [Route("/api/lead-contacts")]
    public async Task<IActionResult> SearchLeadContacts(CancellationToken cancellationToken, [FromQuery] string? query)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = JsonConvert.DeserializeObject<LeadContactSearchDTO>(query);
        var result = await _leadCRMService.SearchLeadContacts(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [AllowAnonymous]
    [Route("/api/klaviyo/create")]
    public async Task<IActionResult> KlaviyoCreate(CancellationToken cancellationToken)
    {
      await _klaviyo.CreateMarketingContact(new LeadContactDTO()
      {
        Email = "<EMAIL>",
        Forename = "David",
        Surname = "Brown",
        Phone = "07876033334",
        Id = Guid.NewGuid()
      }, cancellationToken);
      return Ok();
    }


    [HttpPost]
    [Route("/api/lead/assign")]
    public async Task<IActionResult> LeadAssign([FromBody] LeadAssignDTO dto, CancellationToken cancellationToken)
    {
      var result = await _leadCRMService.LeadAssign(dto, cancellationToken);
      return Ok();
    }

    [HttpPost]
    [Route("/api/lead/{leadId}/contact-link")]
    public async Task<IActionResult> CreateLeadContactLink(Guid leadId, [FromBody] LeadContactLinkDTO dto, CancellationToken cancellationToken)
    {
      dto.LeadId = leadId;

      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadCRMService.GetOrCreateLeadContactLink(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("/api/lead-contact-link/{leadContactLink}")]
    public async Task<IActionResult> PatchLeadContactLink(Guid leadContactLink, [FromBody] JsonPatchDocument<LeadContactLink> patch, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadCRMService.PatchLeadContactLink(leadContactLink, patch, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/lead/{leadId}/contact-links")]
    public async Task<IActionResult> SearchLeadContactLinks(Guid leadId, CancellationToken cancellationToken, [FromQuery] string? query)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = JsonConvert.DeserializeObject<LeadContactLinkSearchDTO>(query);
        dto.Filters.LeadId = leadId;
        var result = await _leadCRMService.SearchLeadContactLinks(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/lead-contact/{leadContactId}/contact-links")]
    public async Task<IActionResult> SearchLeadContactLinks2(Guid leadContactId, CancellationToken cancellationToken, [FromQuery] string? query)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = JsonConvert.DeserializeObject<LeadContactLinkSearchDTO>(query);
        dto.Filters.LeadContactId = leadContactId;
        var result = await _leadCRMService.SearchLeadContactLinks(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/lead-product/{leadProductId}")]
    public async Task<IActionResult> FindLeadProduct(uint leadProductId, CancellationToken ct, [FromQuery] string? query)
    {
      var dto = new LeadProductSearchDTO();

      if (!String.IsNullOrEmpty(query))
      {
        dto = JsonConvert.DeserializeObject<LeadProductSearchDTO>(query);
      }

      if (!User.IsAdmin() && !User.IsAuctionAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadCRMService.GetLeadProduct(leadProductId, ct, dto);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }



    [HttpGet]
    [ResponseCache(Duration = 60)]
    [Route("/api/lead-products")]
    public async Task<IActionResult> SearchLeadProducts(CancellationToken cancellationToken, [FromQuery] string? query)
    {
      var dto = new LeadProductSearchDTO();

      if (!String.IsNullOrEmpty(query))
      {
        //        dto = JsonConvert.DeserializeObject<LeadProductSearchDTO>(query);
      }

      if (!User.HasRole(UserRoleEnum.AuctionAdmin, UserRoleEnum.Admin))
      {
        return Forbid();
      }

      try
      {
        var result = await _leadCRMService.SearchLeadProducts(dto, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [ResponseCache(Duration = 60)]
    [Route("/api/lead-statuses")]
    public async Task<IActionResult> GetLeadStatuses(CancellationToken cancellationToken, [FromQuery] string? query)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = new LeadStatusSearchDTO() { };

        if (!string.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<LeadStatusSearchDTO>(query);
        }
        var result = await _leadCRMService.SearchLeadStatuses(dto, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("/api/lead-status")]
    public async Task<IActionResult> CreatedLeadStatus([FromBody] LeadStatusDTO dto, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadCRMService.AddLeadStatus(dto, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("/api/lead-product")]
    public async Task<IActionResult> CreatedLeadProduct([FromBody] LeadProductDTO dto, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadCRMService.AddLeadProduct(dto, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("/api/lead-product/{id}")]
    public async Task<IActionResult> PatchLeadProduct(uint id, JsonPatchDocument<LeadProduct> patch, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadCRMService.PatchLeadProduct(id, patch, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("/api/lead-note/{id}")]
    public async Task<IActionResult> PatchLeadNote(Guid id, JsonPatchDocument<LeadNote> patch, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadCRMService.PatchLeadNote(id, patch, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("/api/lead/{id}")]
    public async Task<IActionResult> PatchLead(Guid id, JsonPatchDocument<Lead> patch, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadCRMService.PatchLead(id, patch, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpPatch]
    [Route("/api/lead-status/{id}")]
    public async Task<IActionResult> PatchLeadStatus(uint id, JsonPatchDocument<LeadStatus> patch, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadCRMService.PatchLeadStatus(id, patch, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("/api/lead-customer/{id}")]
    public async Task<IActionResult> PatchLeadCustomer(Guid id, JsonPatchDocument<LeadCustomer> patch, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadCRMService.PatchLeadCustomer(id, patch, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("/api/lead-contact/{id}")]
    public async Task<IActionResult> PatchLeadContact(Guid id, JsonPatchDocument<LeadContact> patch, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadCRMService.PatchLeadContact(id, patch, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpPost]
    [Route("/api/lead-customer")]
    public async Task<IActionResult> AddCustomer([FromBody] LeadCustomerDTO dto, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin() && !User.IsAuctionAdmin())
      {
        return Forbid();
      }

      try
      {
        dto.CreatedByContactId = User.ContactId().Value;
        var result = await _leadCRMService.GetOrCreateLeadCustomer(dto, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("/api/lead-contact")]
    public async Task<IActionResult> AddContact([FromBody] LeadContactDTO dto, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin() && !User.IsAuctionAdmin())
      {
        return Forbid();
      }

      try
      {
        dto.CreatedByContactId = User.ContactId().Value;
        var result = await _leadCRMService.GetOrCreateLeadContact(dto, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> AddLead([FromBody] LeadDTO dto, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin() && !User.IsAuctionAdmin())
      {
        return Forbid();
      }

      try
      {
        dto.CreatedByContactId = User.ContactId().Value;
        var result = await _leadCRMService.GetOrCreateLead(dto, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{leadId}")]
    public async Task<IActionResult> GetLead(Guid leadId, CancellationToken cancellationToken, [FromQuery] string? query)
    {
      var dto = JsonConvert.DeserializeObject<LeadSearchDTO>(query);

      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadCRMService.GetLead(leadId, cancellationToken, dto);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/contact/{contactId}/leadQueue")]
    [Route("/api/customer/{customerId}/leadQueue")]
    public async Task<IActionResult> GetLeadQueue(Guid? customerId, Guid? contactId, CancellationToken cancellationToken, [FromQuery] string? query)
    {

      if (!User.HasRole(UserRoleEnum.CRMAdmin, UserRoleEnum.Admin))
      {
        return Forbid();
      }

      var dto = JsonConvert.DeserializeObject<LeadNoteSearchDTO>(query);

      try
      {
        dto.Filters.CreatedForContactId = contactId;
        dto.Filters.CreatedForCustomerId = customerId;
        dto.Filters.RemindersOnly = true;

        var result = await _leadCRMService.SearchLeadNotes(dto, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("{leadId}/lead-note")]
    public async Task<IActionResult> AddLeadNote(Guid leadId, [FromBody] LeadNoteDTO dto, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin() && !User.IsAuctionAdmin())
      {
        return Forbid();
      }

      try
      {
        dto.CreatedByContactId = User.ContactId().Value;
        dto.LeadId = leadId;

        if (dto.CreatedByContactId == Guid.Empty)
        {
          dto.CreatedByContactId = User.ContactId().Value;
        }

        var result = await _leadCRMService.AddLeadNote(dto, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{leadId}/lead-notes")]
    public async Task<IActionResult> GetLeadNotes(Guid leadId, CancellationToken cancellationToken, [FromQuery] string? query)
    {
      var dto = JsonConvert.DeserializeObject<LeadNoteSearchDTO>(query);

      if (!User.IsAdmin() && !User.IsAuctionAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadCRMService.GetLeadNotes(leadId, cancellationToken, dto);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpGet]
    [Route("/api/lead-contact/{leadContactId}")]
    public async Task<IActionResult> GetLeadContact(Guid leadContactId, CancellationToken cancellationToken, [FromQuery] LeadContactSearchDTO dto)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadCRMService.GetLeadContact(leadContactId, cancellationToken, dto);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/lead-customer/{customerId}")]
    public async Task<IActionResult> GetLeadCustomer(Guid customerId, CancellationToken cancellationToken, [FromQuery] string? query)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      var dto = JsonConvert.DeserializeObject<LeadCustomerSearchDTO>(query);

      try
      {
        var result = await _leadCRMService.GetLeadCustomer(customerId, cancellationToken, dto);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpGet]
    [Route("{leadId}/valuation-quotes")]
    public async Task<IActionResult> GetLeadQuotes(Guid leadId, CancellationToken cancellationToken, [FromQuery] string? query)
    {
      if (!User.IsAdmin() && !User.IsAuctionAdmin())
      {
        return Forbid();
      }

      var dto = new ValuationQuoteSearchDTO();

      try
      {
        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<ValuationQuoteSearchDTO>(query);
        }

        dto.Filters.LeadId = leadId;

        var result = await _valuationQuoteService.SearchValuationQuotes(dto, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/leadsByOwner")]
    public async Task<IActionResult> GetLeadsByOwner(CancellationToken cancellationToken, [FromQuery] string? query)
    {
      if (!User.IsAdmin() && !User.IsAuctionAdmin())
      {
        return Forbid();
      }

      var dto = new LeadSearchDTO();

      try
      {
        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<LeadSearchDTO>(query);
        }

        var result = await _leadCRMService.SearchLeadsByOwner(dto, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/lead-vehicle-appraisal/{leadVehicleAppraisalId}")]
    public async Task<IActionResult> GetLeadVehicleAppraisal(Guid leadVehicleAppraisalId, CancellationToken cancellationToken)
    {
      var dto = new LeadAppraisalSearchDTO()
      {
        Component = "lead-vehicle-appraisal",
        Filters = new LeadAppraisalFilters() { Id = leadVehicleAppraisalId }
      };

      var result = await _leadCRMService.SearchLeadAppraisals(dto, cancellationToken);
      return Ok(result);
    }

    [HttpGet]
    [Route("{leadId}/appraisals")]
    public async Task<IActionResult> GetLeadAppraisals(Guid leadId, CancellationToken cancellationToken, [FromQuery] LeadAppraisalSearchDTO dto)
    {
      /*
      if (!User.IsAdmin() && !User.IsAuctionAdmin())
      {
        return Forbid();
      }
      */

      try
      {
        if (dto == null)
        {
          dto = new LeadAppraisalSearchDTO();
        }

        if (dto.Filters == null)
        {
          dto.Filters = new LeadAppraisalFilters();
        }

        dto.Filters.LeadId = leadId;

        var result = await _leadCRMService.SearchLeadAppraisals(dto, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/lead-crm/chart-data")]
    public async Task<IActionResult> GetCRMChartData(CancellationToken cancellationToken)
    {
      var result = await _leadCRMService.GetLeadChartData(cancellationToken);
      return Ok(result);
    }
  }
}
