﻿using Microsoft.Extensions.Options;
using Moq;
using System.Threading.Tasks;
using Trading.API.Tests.Common;
using Trading.Services;
using Trading.Services.Classes;
using Trading.Services.ExternalDTO;
using Trading.Services.Interfaces;
using Xunit;

namespace Trading.API.Tests
{
  [Collection("DatabaseCollection")]
  public class VehicleLookup_Test : TestBase
  {
    DatabaseFixture _fixture;

    // Vehicle lookup data is not free so any tests here must only be for exception handling etc. and not for actual lookups

    public VehicleLookup_Test(DatabaseFixture fixture)
    {
      _fixture = fixture;
    }

    private IVRMLookupService GetService()
    {
      // this can be used with real API key but remember real lookups cost money
      var ukVehicleDTO = new Mock<IOptionsSnapshot<UKVehicleDTO>>();
      ukVehicleDTO.SetupAllProperties();
      ukVehicleDTO.SetupGet(p => p.Value).Returns(new UKVehicleDTO { APIKey = "FakeKey" });

      IVRMLookupService vrmLookupService = new UKVehicleDataService(_context, ukVehicleDTO.Object, _mapper, _common.LookupService);
      return vrmLookupService;
    }

    [Fact]
    // expect a specific exception when no valid API key present (todo: change this when service uses ResultDTO pattern)
    public async Task VehicleLookupData_ErrorResult()
    {
      var service = GetService();

      //var vehicleData = await service.GetVehicleData("DG67CTO", CancellationToken.None);

      // expect a specific exception when no valid API key present (todo: change this when service uses ResultDTO pattern)
      Assert.True(true);
    }
  }
}
