﻿using System;
using System.Collections.Generic;

namespace Trading.API.Data.DTO.MechanicalFaults
{
  public class VehicleFaultCheckDTO : BaseModelEntityDTO
  {
    public Guid? VehicleId { get; set; }
    public virtual VehicleDTO Vehicle { get; set; }

    public DateTime? InspectDate { get; set; }
    public int? Odometer { get; set; }

    public string FreeText { get; set; }

    public IEnumerable<VehicleFaultCheckItemDTO> FaultCheckItems { get; set; }

    public uint? FaultCheckTypeId { get; set; }
    public FaultCheckTypeDTO FaultCheckType { get; set; }
  }
}
