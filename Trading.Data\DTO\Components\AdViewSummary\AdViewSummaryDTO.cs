﻿using DocumentFormat.OpenXml.Office.CoverPageProps;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.Components.AdViewSummary
{
  public class AdViewSummaryDTO
  {
    public string SellerEmail { get; set; }
    public long TotalViews { get; set; }
    public List<AdViewSummaryCustomerDTO> CustomerDetails { get; set; }
  }

  public class AdViewSummaryCustomerDTO
  {
    public string Name { get; set; }
    public string Email { get; set; }
    public string Phone { get; set; }

    public long NumViews { get; set; }
    public DateTime? LatestViewDate { get; set; }
  }
}
