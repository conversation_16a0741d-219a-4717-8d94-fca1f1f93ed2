﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Threading;
using Trading.Services.ExternalDTO.BIG;
using Trading.Services.Interfaces.Imports;
using System;
using Trading.API.Remarq.Controllers.Extensions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using Trading.API.Data.DTO.Imports;
using Trading.Services.Interfaces.ExtLeads;

namespace Trading.API.Remarq.Controllers
{
  [Route("api/simple-import")]
  [ApiController]
  [Authorize]
  public class SimpleImportController : ControllerBase
  {
    private readonly ISimpleImportService _simpleImportService;
    private readonly IWebHostEnvironment _webHostEnvironment;
    private readonly IExtLeadService _extLeadService;

    public SimpleImportController(ISimpleImportService simpleImportService, IWebHostEnvironment webHostEnvironment, IExtLeadService extLeadService)
    {
      _simpleImportService = simpleImportService;
      _extLeadService = extLeadService;
      _webHostEnvironment = webHostEnvironment;
    }

    [HttpPost]
    [Route("europcar/process")]
    public async Task<IActionResult> ProcessEuropCarImports(IFormFile file, CancellationToken cancellationToken)
    {
      if (file == null || file.Length == 0)
      {
        return BadRequest("No file provided.");
      }

      var res = _simpleImportService.GetImportCreateAdvertDTOs(file); //, !_webHostEnvironment.IsDevelopment()

      return Ok(res);
    }



    [HttpPost]
    [Route("{vendor}/import")]
    public async Task<IActionResult> ImportProcessedEntry(string vendor, [FromBody] ImportCreateAdvertDTO dto, CancellationToken cancellationToken)
    {
      var res = await _simpleImportService.CreateAdvertsFromImportData(vendor, dto);
      return Ok(res);
    }

  }
}
