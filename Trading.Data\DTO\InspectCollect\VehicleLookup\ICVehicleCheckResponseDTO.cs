﻿using System;
using Trading.API.Data.DTO.InspectCollect.VehicleData;

namespace Trading.API.Data.DTO.InspectCollect.VehicleLookup;

public class ICVehicleCheckResponseDTO
{
  public Guid CapDataId { get; set; }
  public Guid AutoTraderDataId { get; set; }

  public string VRM { get; set; }
  public string VIN { get; set; }
  public int Odometer { get; set; }

  public ICVehicleLookupDataDTO LookupData { get; set; } = new ICVehicleLookupDataDTO();

  public ICCapDataDTO CapData { get; set; } = new ICCapDataDTO(); // Contains CAP specific data

  public ICAutoTraderDataDTO AutoTraderData { get; set; } = new ICAutoTraderDataDTO(); // Contains AutoTrader specific data

}