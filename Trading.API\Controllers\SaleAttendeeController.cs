using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.API.Helpers;
using Trading.Services.Extensions;
using Trading.Services.ExternalDTO;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/saleAttendees")]
  [ApiController]
  public class SaleAttendeeController : ControllerBase
  {
    private readonly ISaleService _saleService;

    public SaleAttendeeController(ISaleService sService)
    {
      _saleService = sService;
    }


    [HttpGet]
    [Route("/api/sale/{saleId}/saleAttendees")]
    [Authorize]
    public async Task<IActionResult> GetSaleAttendees(Guid saleId, CancellationToken cancellationToken)
    {
      try
      {
        var response = await _saleService.GetSaleAttendees(saleId, cancellationToken);
        return Ok(response);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpPost]
    [Route("/api/sale/{saleId}/saleAttendee")]
    [Authorize]
    public async Task<IActionResult> AddAttendee(Guid saleId, [FromBody]SaleAttendeeDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        dto.SaleId = saleId;

        var response = await _saleService.AddSaleAttendee(dto, cancellationToken);
        return Ok(response);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpDelete]
    [Route("{id}")]
    [Authorize]
    public async Task<IActionResult> DeleteAttendee(uint id, CancellationToken cancellationToken)
    {
      try
      {
        await _saleService.RemoveAttendee(id, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
