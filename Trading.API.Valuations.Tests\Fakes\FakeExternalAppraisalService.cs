﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.UInspect.StatData;
using Trading.API.Data.DTO.UInspections;
using Trading.API.Data.Models.UInspections;
using Trading.Services.UInspections.Interfaces;

namespace Trading.API.Valuations.Tests.Fakes
{
  public class FakeExternalAppraisalService : IUInspectService
  {
    public Task<ValidatedResultDTO<UInspectDTO>> Create(UInspectDTO dto, CancellationToken cancellationToken)
    {
      throw new NotImplementedException();
    }

    public Task<bool> Delete(Guid id, CancellationToken cancellationToken)
    {
      throw new NotImplementedException();
    }

    public Task<(bool, string)> FetchAppraisalData(string assistCode, CancellationToken cancellationToken)
    {
      throw new NotImplementedException();
    }

    public Task<ValidatedResultDTO<UInspectDTO>> Get(Guid externalAppraisalId, UInspectSearchDTO dto, CancellationToken cancellationToken)
    {
      throw new NotImplementedException();
    }

    public Task<ValidatedResultDTO<UInspectDTO>> Get(Guid id, CancellationToken cancellationToken, UInspectSearchDTO searchDTO)
    {
      throw new NotImplementedException();
    }

    public Task<List<UInspectSectionDTO>> GetAppraisalSections(int seats, CancellationToken cancellationToken)
    {
      throw new NotImplementedException();
    }

    public Task<ValidatedResultDTO<UInspectWithAnswerDTO>> GetWithAnswers(Guid id, CancellationToken cancellationToken, UInspectSearchDTO searchDTO)
    {
      throw new NotImplementedException();
    }

    public Task<ValidatedResultDTO<UInspectDTO>> Patch(Guid id, JsonPatchDocument<UInspect> dto, CancellationToken cancellationToken)
    {
      throw new NotImplementedException();
    }

    public Task<SearchResultDTO<UInspectDTO>> Search(UInspectSearchDTO dto, CancellationToken cancellationToken)
    {
      throw new NotImplementedException();
    }

    public Task<string> NewInspection(UInspectRequestDTO requestDTO)
    {
      throw new NotImplementedException();
    }

    public Task<ValidatedResultDTO<UInspectDTO>> StoreAppraisalData(Guid leadVehicleId, string assistData, CancellationToken cancellationToken)
    {
      throw new NotImplementedException();
    }

    public Task<List<ValidatedResultDTO<UInspectMediaDTO>>> UploadAppraisalImages(UInspectMediaDTO dto, IFormFileCollection files, CancellationToken cancellationToken)
    {
      throw new NotImplementedException();
    }

    public Task SetOpened(Guid id, CancellationToken cancellationToken)
    {
      throw new NotImplementedException();
    }

    public Task SetCompleted(Guid id, CancellationToken cancellationToken)
    {
      throw new NotImplementedException();
    }

    public Task<ValidatedResultDTO<UInspectDTO>> Create(UInspectRequestDTO dto, CancellationToken cancellationToken)
    {
      throw new NotImplementedException();
    }

    public Task SetOpened(Guid id, string ipAddress, CancellationToken cancellationToken)
    {
      throw new NotImplementedException();
    }

    public Task SetCompleted(Guid id, string ipAddress, CancellationToken cancellationToken)
    {
      throw new NotImplementedException();
    }

    public Task<List<UInspectStatDataDTO>> GetStatData(UInspectStatRequestDTO requestDTO, CancellationToken cancellationToken)
    {
      throw new NotImplementedException();
    }

    public Task<List<UInspectStatDataDTO>> GetFunnelData(UInspectStatRequestDTO requestDTO, CancellationToken cancellationToken)
    {
      throw new NotImplementedException();
    }
  }
}
