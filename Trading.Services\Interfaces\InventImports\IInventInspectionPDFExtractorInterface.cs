﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.DTO.Imports.Invent;

namespace Trading.Services.Interfaces.InventImports;
public interface IInventInspectionPDFExtractorInterface
{
  Task<CityVehicleInspectionResult> ExtractInspectionDataFromURLAsync(string pdfUrl);
  Task<CityVehicleInspectionResult> ExtractInspectionDataAsync(string pdfFilePath);
}
