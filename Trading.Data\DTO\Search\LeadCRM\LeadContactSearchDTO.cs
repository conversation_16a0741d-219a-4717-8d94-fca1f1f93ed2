﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.Search.LeadCRM
{
  public class LeadContactSearchDTO : BaseSearchDTO
  {
    public LeadContactFilters Filters { get; set; } = new LeadContactFilters() { };
  }
  public class LeadContactFilters : BaseFilterGuid
  {
    public string Forename { get; set; }
    public string Surname { get; set; }
    public string Email { get; set; }
    public string Phone { get; set; }
    public string Mobile { get; set; }
    public string Postcode { get; set; }
    public Guid? CustomerId { get; set; }

  }
}
