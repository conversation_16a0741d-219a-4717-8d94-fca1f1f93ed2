﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.Models.InventData;

[Table("InventAuctionLot")]
public class InventAuctionLot : BaseModelEntity
{
  public Guid AuctionId { get; set; }

  public long LotId { get; set; }

  public int? LotNumber { get; set; }

  /// <summary>
  /// Lot status: 1 = Selling, 2 = Processed, 3 = ?, 4 = Sold, 5 = Withdrawn, 6 = Error
  /// </summary>
  public int Status { get; set; }

  public string Type { get; set; }

  public string Description { get; set; }

  // Vehicle details
  public string Vrm { get; set; }

  public string Vin { get; set; }

  public string Manufacturer { get; set; }

  public string Model { get; set; }

  public string Variant { get; set; }

  public int? Year { get; set; }

  public int? Mileage { get; set; }

  public string MileageDenominator { get; set; }

  public bool MileageWarranted { get; set; }

  public DateTime? FirstRegistered { get; set; }

  public string Colour { get; set; }

  public string FuelType { get; set; }

  public int? EngineSize { get; set; }

  public string BodyType { get; set; }

  public int? DoorCount { get; set; }

  public string Transmission { get; set; }

  public int? FormerKeeper { get; set; }

  public string NamaGrade { get; set; }

  [Column(TypeName = "decimal(18,4)")]
  public decimal? CapRetail { get; set; }

  [Column(TypeName = "decimal(18,4)")]
  public decimal? CapClean { get; set; }

  [Column(TypeName = "decimal(18,4)")]
  public decimal? CapAverage { get; set; }

  [Column(TypeName = "decimal(18,4)")]
  public decimal? CapBelow { get; set; }

  [Column(TypeName = "decimal(18,4)")]
  public decimal? AutotraderRetail { get; set; }

  [Column(TypeName = "decimal(18,4)")]
  public decimal? AutotraderTrade { get; set; }

  public string ClassificationId { get; set; }

  public string ClassificationTitle { get; set; }

  public string VatStatus { get; set; }

  public bool HasV5 { get; set; }

  public string V5OrderStatus { get; set; }

  public bool ServiceHistory { get; set; }

  public bool NonRunner { get; set; }

  public string InspectionReportUrl { get; set; }

  public string Co2 { get; set; }

  public string StandardEuroEmissions { get; set; }

  public int? NumKeys { get; set; }

  [Column(TypeName = "decimal(18,4)")]
  public decimal? ReservePrice { get; set; }

  [Column(TypeName = "decimal(18,4)")]
  public decimal? CurrentBid { get; set; }

  [Column(TypeName = "decimal(18,4)")]
  public decimal? SalePrice { get; set; }

  public int? SoldToCustomerId { get; set; }

  
  public virtual InventAuction Auction { get; set; }

  public virtual ICollection<InventAuctionLotImage> Images { get; set; }

  [MaxLength(2048)]
  public string ImportErrorText { get; set; }

  public long? InventVehicleId { get; set; } 

  // platform specific fields
  public Guid? VehicleId { get; set; } // for vehicles on the platform to allow withdrawal etc.

  public virtual Vehicle Vehicle { get; set; } 
}
