using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Common;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Documents;
using Trading.API.Data.DTO.Search;
using Trading.API.Data.DTO.WhosWhoNS;
using Trading.API.Data.Models;
using Trading.API.Remarq.Controllers.Extensions;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/customer")]
  [ApiController]
  [Authorize]
  public class CustomerController : ControllerBase
  {
    private readonly ICustomerService _customerService;
    private readonly ICustomerInternalInfoService _customerInternalInfoService;
    private readonly IMapper _mapper;

    public CustomerController(
      ICustomerService customerService, 
      ICustomerInternalInfoService customerInternalInfoService, 
      IMapper mapper)
    {
      _customerService = customerService;
      _customerInternalInfoService = customerInternalInfoService;
      _mapper = mapper;
    }

    [HttpGet("/api/customers/bid-limit-reached")]
    [Authorize(Roles = "ADMIN, POWER_USER")]

    public async Task<IActionResult> GetBidLimitedCustomers(CancellationToken cancellationToken)
    {
      if (!User.IsAdmin() && !User.IsPowerUser())
      {
        return Forbid();
      }

      var res = await _customerService.GetBidLimitedCustomerInfo(cancellationToken);
      return Ok(res);
    }


    // GET: api/Customers
    [HttpGet("/api/customers")]
    [AllowAnonymous]
    public async Task<IActionResult> GetCustomers([FromQuery] string? query, CancellationToken cancellationToken)
    {
      CustomerSearchDTO dto = new CustomerSearchDTO() { };

      if (query != null)
      {
        dto = JsonConvert.DeserializeObject<CustomerSearchDTO>(query);
      }

      var isAPIKey = HttpContext.IsAPIKeyRequest();
      if (isAPIKey && string.IsNullOrEmpty(dto.Component))
      {
        dto.Component = "isAPIKey";
      }

      var all = await _customerService.Search(dto, cancellationToken);

      return Ok(new CustomerSearchResultDTO() { Customers = all.Customers });
    }

    [HttpGet("{customerId}")]
    public async Task<IActionResult> GetDetails(Guid customerId, CancellationToken cancellationToken)
    {
      try
      {
        if (User.CustomerId() != customerId && !User.IsAdmin())
        {
          return Forbid();
        }

        var customer = await _customerService.GetDetails(customerId, cancellationToken);
        return Ok(customer);
      } 
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    // get admin details for specified customer (cars sold, bought, contacts, actions etc.)
    [HttpGet("/api/customer/{customerId}/adminDetails")]
    public async Task<IActionResult> GetAdminDetails(Guid customerId, CancellationToken cancellationToken)
    {
      try
      {
        var customer = await _customerService.GetAdminDetails(customerId, cancellationToken);
        return Ok(customer);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    // SET: api/Customers
    [HttpPut("{customerId}/primaryContact/{contactId}")]
    public async Task<IActionResult> SetPrimaryContact(Guid customerId, Guid contactId, CancellationToken cancellationToken)
    {
      var all = await _customerService.SetPrimaryContact(customerId, contactId, cancellationToken);

      return Ok(all);
    }

    // GET
    [HttpGet("{customerId}/primary-contact-id")]
    public async Task<IActionResult> GetPrimaryContactId(Guid customerId, CancellationToken cancellationToken)
    {
      var res = await _customerService.GetPrimaryContactId(customerId, cancellationToken);
      return Ok(res);
    }


    // PATCH: api/Customers
    [HttpPatch("{customerId}")]
    public async Task<IActionResult> PatchCustomer(Guid customerId, JsonPatchDocument<Customer> patch, CancellationToken cancellationToken)
    {
      if (User.CustomerId() != customerId && !User.IsAdmin())
      {
        return Forbid();
      }

      var all = await _customerService.Patch(customerId, patch, cancellationToken);

      return Ok(all);
    }


    [HttpGet("status")]
    [HttpGet("{customerId}/status")]
    public async Task<IActionResult> GetStatus(Guid customerId, CancellationToken cancellationToken)
    {
      if (customerId == Guid.Empty)
      {
        customerId = (Guid) User.CustomerId();
      }

      var customer = await _customerService.Get(customerId, new CustomerSearchDTO(), cancellationToken);

      if (customer == null)
      {
        return NotFound();
      }

      return Ok(new { statusId = customer.StatusId });
    }

    [HttpGet]
    [Route("advertising-totals")]
    [Authorize(Roles = "GOD")]
    public async Task<IActionResult> GetAdvertisingTotals([FromQuery] string? query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = new WhosWhoAdminSearchDTO();

        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<WhosWhoAdminSearchDTO>(query);
        }

        var result = await _customerService.GetAdvertisingTotals(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpGet]
    [Route("{customerId}/has-address")]
    public async Task<IActionResult> CustomerHasAddress(Guid customerId, CancellationToken cancellationToken)
    {
      var res = await _customerService.CustomerHasAddress(customerId, cancellationToken);
      return Ok(res);
    }

  }
}
