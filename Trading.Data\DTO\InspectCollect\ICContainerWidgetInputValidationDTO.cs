﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICContainerWidgetInputValidationDTO : BaseModelEntityDTO
  {
    public ICValidationDTO ICValidation { get; set; }
    public Guid? ICValidationId { get; set; }
    public uint? Position { get; set; }
  }


  public class ICContainerWidgetInputValidationSearchDTO : BaseSearchDTO
  {
    public ICContainerWidgetInputValidationSearchFilters Filters { get; set; } = new ICContainerWidgetInputValidationSearchFilters();
  }

  public class ICContainerWidgetInputValidationSearchFilters : BaseFilter
  {
    public Guid? ICContainerWidgetInputId { get; set; }
  }

  public class ICContainerWidgetInputValidationCreateDTO
  {
    public Guid ICContainerWidgetInputId { get; set; }
    public uint? Position { get; set; }
  }
}
