using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using Trading.API.Data.DTO;
using Trading.API.Data.Enums;

namespace Trading.API.Data.Models.DTO
{
  public class SphScanVehicleDTO : BaseModelEntityIntDTO
  {
    public uint id { get; set; }
    public uint weight { get; set; }
    [Required]
    public string phrase { get; set; }
    public uint CO2 { get; set; }
    public uint Doors { get; set; }
    public uint Odometer { get; set; }
    public uint ImageCount { get; set; }
    public uint EngineCC { get; set; }
    public uint PlateId { get; set; }
    public uint BodyTypeId { get; set; }
    public uint ScanCustomerId { get; set; }
    public uint DerivId { get; set; }
    public uint ModelId { get; set; }
    public uint MakeId { get; set; }
    public uint FuelTypeId { get; set; }
    public uint TransmissionTypeId { get; set; }
    public uint Price { get; set; }
    public uint CustomerIdCRC { get; set; }
    public uint HaveVRM { get; set; }
  }
}