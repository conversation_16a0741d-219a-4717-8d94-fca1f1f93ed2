using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect")]
  [ApiController]
  [AllowAnonymous]
  public class ICTestController : ControllerBase
  {
    private readonly ICITestInterface _icTestInterface;

    public ICTestController(ICITestInterface icTestInterface)
    {
      _icTestInterface = icTestInterface;
    }

    [HttpGet]
    [Route("test")]
    public async Task<ActionResult> Test(CancellationToken cancellationToken)
    {
      var ok = await _icTestInterface.GetTestData(cancellationToken);
      return Ok(ok);
    }
  }
}