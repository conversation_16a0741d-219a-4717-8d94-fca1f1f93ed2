<div mdbModal #addTriggerModal="mdbModal" class="modal" tabindex="-1"
     [config]="{ignoreBackdropClick: true}"
     aria-labelledby="editTriggerModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="addTriggerModalLabel">Add Trigger</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="addTriggerModal.hide()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form [formGroup]="addTriggerForm">

          <div class="form-group">
            <label for="addTriggerName">Trigger Name</label>
            <input type="text" class="form-control" id="addTriggerName" formControlName="name"
                   placeholder="Enter Trigger name">
          </div>

          <input type="hidden" formControlName="icContainerGroupId">


        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="hideAddTrigger()">Close</button>
        <button type="button" [disabled]="!addTriggerForm.valid" class="btn btn-primary" (click)="saveAddTrigger()">Save
        </button>
      </div>
    </div>
  </div>
</div>

<div mdbModal #editTriggerModal="mdbModal" class="modal" tabindex="-1"
     [config]="{ignoreBackdropClick: true}"
     aria-labelledby="editTriggerModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="EditTriggerModalLabel">Edit Trigger</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="addTriggerModal.hide()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form [formGroup]="editTriggerForm">

          <input type="hidden" formControlName="id">

          <div class="form-group">
            <label for="addTriggerName">Trigger Name</label>
            <input type="text" class="form-control" id="editTriggerName" formControlName="name"
                   placeholder="Enter Trigger name">
          </div>
          Trigger Type
          <mdb-select-2 formControlName="triggerType"
                        [outline]="true">
            <mdb-select-option *ngFor="let triggerTypeOption of triggerTypeOptions"
                               [value]="triggerTypeOption.value">{{ triggerTypeOption.label }}
            </mdb-select-option>
          </mdb-select-2>

          <div class="input-group mt-3 d-block">
            <div class="ui-switch switch blue-white-switch">
              <label>
                <input type="checkbox" formControlName="remoteCall">
                <span class="lever"></span>
              </label>
              <span class="switch-label" style="line-height: 29px;">Remote Call&nbsp;</span>
            </div>
          </div>

          <div class="input-group mt-3 d-block">
            <div class="ui-switch switch blue-white-switch">
              <label>
                <input type="checkbox" formControlName="wait">
                <span class="lever"></span>
              </label>
              <span class="switch-label" style="line-height: 29px;">Wait&nbsp;</span>
            </div>
          </div>

          <div class="input-group mt-3 d-block">
            <div>Remote Trigger Name</div>
            <input formControlName="remoteTriggerName" class="form-control">
          </div>

          <div class="input-group mt-3 d-block">
            <div>Payload Template (JSON)</div>
            <textarea class="form-control" formControlName="payloadTemplate"></textarea>
          </div>

          <div class="input-group mt-3 d-block">
            <mdb-select-2 formControlName="statusId"
                          [outline]="true">
              <mdb-select-option *ngFor="let statusOption of statusOptions"
                                 [value]="statusOption.value">{{ statusOption.label }}
              </mdb-select-option>
            </mdb-select-2>
          </div>

        </form>
      </div>
      <div class="modal-footer">
        <div class="d-flex w-100">
          <div class="flex-grow-1 text-left">
            <button type="button" class="btn btn-danger-outline" (click)="deleteTrigger()">Delete</button>
          </div>
          <div class="flex-shrink-1">
            <button type="button" class="btn btn-secondary mr-2" (click)="hideEditTrigger()">Close</button>
            <button type="button" [disabled]="!editTriggerForm.valid" class="btn btn-primary"
                    (click)="saveEditTrigger()">
              Save
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
