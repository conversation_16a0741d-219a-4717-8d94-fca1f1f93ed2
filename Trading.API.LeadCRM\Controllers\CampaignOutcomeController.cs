﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.LeadCRM;
using Trading.API.Data.DTO.Search.LeadCRM;
using Trading.API.Data.Models.LeadCRM;
using Trading.Services.Extensions;
using Trading.Services.LeadCRM.Interfaces;

namespace Trading.API.LeadCRM.Controllers
{
  [Route("api/campaign-outcome")]
  [ApiController]
  [Authorize]
  public class CampaignOutcomeController : ControllerBase
  {
    private readonly ICampaignOutcomeService _campaignOutcomeService;

    public CampaignOutcomeController(ICampaignOutcomeService campaignOutcomeService)
    {
      this._campaignOutcomeService = campaignOutcomeService;
    }

    /* CAMPAIGN LEAD STARTS HERE */

    [HttpGet]
    [Route("/api/campaign-outcomes")]
    public async Task<IActionResult> SearchCampaignOutcomes(
      [FromQuery] string? query, 
      CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = new CampaignOutcomeSearchDTO() { };
        
        if (query != null)
        {
          dto = JsonConvert.DeserializeObject<CampaignOutcomeSearchDTO>(query);
        }
        var result = await _campaignOutcomeService.SearchCampaignOutcomes(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<IActionResult> GetCampaignOutcome(
      uint id, 
      [FromQuery] string? query, 
      CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = JsonConvert.DeserializeObject<CampaignOutcomeSearchDTO>(query);
        var result = await _campaignOutcomeService.GetCampaignOutcome(id, cancellationToken, dto);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> CreateCampaignOutcome([FromBody] CampaignOutcomeDTO dto, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _campaignOutcomeService.CreateCampaignOutcome(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> PatchCampaign([FromBody] JsonPatchDocument<CampaignOutcome> patch, uint id, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _campaignOutcomeService.PatchCampaignOutcome(id, patch, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
