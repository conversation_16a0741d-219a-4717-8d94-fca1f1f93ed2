using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/input-validation")]
  [ApiController]
  [AllowAnonymous]
  public class ICInputValidationController : ControllerBase
  {
    private readonly ICInputValidationInterface _icInputValidationService;

    public ICInputValidationController(ICInputValidationInterface serviceInterface)
    {
      _icInputValidationService = serviceInterface;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icInputValidationService.Get(id, null, cancellationToken);
      return Ok(res);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icInputValidationService.Delete(id);
      return Ok(res);
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Post([FromBody]ICInputValidationCreateDTO dto, CancellationToken cancellationToken)
    {
      var res = await _icInputValidationService.Create(dto);
      return Ok(res);
    }

    [HttpGet]
    [Route("search")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICInputValidationSearchDTO>(query);
      var res = await _icInputValidationService.Search(dto, cancellationToken);
      return Ok(res);
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<ICInputValidation> dto)
    {
      var response = await _icInputValidationService.Patch(id, dto);
      return Ok(response);
    }
  }
}