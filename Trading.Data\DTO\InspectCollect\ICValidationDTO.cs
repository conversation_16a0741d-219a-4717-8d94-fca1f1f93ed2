﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums.InspectCollect;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICValidationDTO : BaseModelEntityDTO
  {
    public Guid ICContainerGroupId { get; set; }

    public ICContainerGroupDTO ICContainerGroup { get; set; }
    public string Name { get; set; }
    public string ValidationCode { get; set; }
  }

  public class ICValidationSearchDTO : BaseSearchDTO
  {
    public ICValidationSearchFilters Filters { get; set; } = new ICValidationSearchFilters();
  }

  public class ICValidationSearchFilters
  {
    public Guid? Id { get; set; }
    public Guid? ICContainerGroupId { get; set; }
    public string Name { get; set; }
  }

  public class ICValidationCreateDTO
  {
    public Guid ICContainerGroupId { get; set; }
    public string Name { get; set; }
    public string ValidationCode { get; set; }
  }

  public class ICValidationRequestDTO
  {
  }
}