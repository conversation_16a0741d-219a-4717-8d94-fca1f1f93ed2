﻿using System;

namespace Trading.API.Common.APIKeyAuth;

/// <summary>
/// Use this attribute to mark controllers and actions for specific Swagger documentation groups
/// </summary>
[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class, AllowMultiple = true)]
public class SwaggerCustomerAttribute : Attribute
{
  /// <summary>
  /// Name of the Swagger documentation group
  /// </summary>
  public string Name { get; }

  /// <summary>
  /// Create a new SwaggerCustomer attribute
  /// </summary>
  /// <param name="name">Name of the documentation group (e.g., "ApiKeyCustomer", "Remarq")</param>
  public SwaggerCustomerAttribute(string name)
  {
    Name = name;
  }
}
