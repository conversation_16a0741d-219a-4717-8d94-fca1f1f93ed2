﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO.UInspections;
using Trading.API.Data.Models.UInspections;
using Trading.Services.UInspections.Interfaces;

namespace Trading.API.UInspection.Controllers
{
  [Route("api/uinspect/format")]
  [ApiController]
  public class UInspectFormatController : ControllerBase
  {
    private readonly IUInspectFormatService _uInspectFormatService;

    public UInspectFormatController(IUInspectFormatService uInspectFormatService)
    {
      this._uInspectFormatService = uInspectFormatService;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<IActionResult> Get(uint id, CancellationToken cancellationToken, [FromQuery] string query = "")
    {
      try
      {
        var dto = new UInspectFormatSearchDTO();
        
        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<UInspectFormatSearchDTO>(query);
        }

        var result = await _uInspectFormatService.Get(id, cancellationToken, dto);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/uinspect/formats")]
    public async Task<IActionResult> Search(CancellationToken cancellationToken, [FromQuery] string query = "")
    {
      try
      {
        var dto = new UInspectFormatSearchDTO();
        
        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<UInspectFormatSearchDTO>(query);
        }

        var result = await _uInspectFormatService.Search(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create([FromBody] UInspectFormatDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        var result = await _uInspectFormatService.Create(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(uint id, JsonPatchDocument<UInspectFormat> patch, CancellationToken cancellationToken)
    {
      try
      {
        var result = await _uInspectFormatService.Patch(id, patch, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
