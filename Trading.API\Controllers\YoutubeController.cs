﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [ApiController]
  [FormatFilter]
  [Route("api/[controller]")]
  [EnableCors("AllowOrigin")]
  public class YoutubeController : ControllerBase
  {
    private readonly ILogger<YoutubeController> _logger;
    private readonly IYTService _youtubeService;

    public YoutubeController(
        ILogger<YoutubeController> logger,
        IYTService youtubeService)
    {
      _logger = logger;
      _youtubeService = youtubeService;
    }

    /// <summary>
    /// Initiates the OAuth 2.0 authorization flow by redirecting the user to Google's consent screen.
    /// </summary>
    [HttpGet]
    [Route("auth")]
    [EnableCors("AllowOrigin")]
    [ApiExplorerSettings(IgnoreApi = true)] // Won't show in Swagger documentation
    public IActionResult Auth()
    {
      try
      {
        // Generate the authorization URL
        var authorizationUrl = _youtubeService.GenerateAuthorizationUrl();

        // Redirect the user to Google's OAuth 2.0 consent screen
        return Redirect(authorizationUrl);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error generating authorization URL.");
        return BadRequest(new { message = "Error generating authorization URL.", error = ex.Message });
      }
    }

    /// <summary>
    /// Handles the OAuth 2.0 callback from Google, exchanges the authorization code for tokens.
    /// </summary>
    [HttpGet]
    [Route("authorised")]
    [ApiExplorerSettings(IgnoreApi = true)] // Won't show in Swagger documentation
    public async Task<IActionResult> Authorised([FromQuery] string code, CancellationToken cancellationToken)
    {
      if (string.IsNullOrEmpty(code))
      {
        return BadRequest(new { message = "Authorization code not found in the query parameters." });
      }

      try
      {
        // Exchange the authorization code for tokens
        await _youtubeService.ExchangeCodeForTokensAsync(code, cancellationToken);

        // Redirect or inform the user that authorization was successful
        return Ok(new { message = "YouTube authorization successful." });
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error exchanging authorization code for tokens.");
        return BadRequest(new { message = "Error exchanging authorization code for tokens.", error = ex.Message });
      }
    }
  }
}
