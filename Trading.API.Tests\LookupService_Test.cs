﻿using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.API.Tests.Common;
using Xunit;

namespace Trading.API.Tests
{
  [Collection("DatabaseCollection")]
  public class LookupService_Test : TestBase
  {
    private readonly DatabaseFixture _databaseFixture;

    public LookupService_Test(DatabaseFixture databaseFixture)
    {
      _databaseFixture = databaseFixture;
    }

    [Fact]
    public async Task GetMake()
    {
      // use a make we know is in the test database, the lookup should return the existing make and it should have status 1
      var makeName = "Volvo";
      var make = await _lookupFactory.GetLookup<Make>(new LookupDTO { TableName = "make", LookupValue = makeName, VehicleType = VehicleTypeEnum.Car });

      Assert.NotNull(make);
      Assert.True(make.MakeName == makeName);

      var entity = await _context.Makes.FirstOrDefaultAsync(x => x.Id == make.Id);
      Assert.NotNull(entity);
      Assert.True(entity.StatusId == 1);
    }

    [Fact]
    public async Task GetMake_Alt()
    {
      // use a make name we know is not in the test database, the lookup service should create an Alt value with a pointer to the real value 
      // the alt and the real value should be statusId 2 (pending)
      var name = "Made up make";
      var result = await _lookupFactory.GetLookup<Make>(new LookupDTO { TableName = "make", LookupValue = name, VehicleType = VehicleTypeEnum.Car });

      var entity = await _context.Makes.FirstOrDefaultAsync(x => x.Id == result.Id);
      Assert.True(entity.StatusId == (uint)StatusEnum.Pending);

      var alt = await _context.Alts.FirstOrDefaultAsync(x => x.AltRealId == entity.Id && x.AltTable == "make");
      Assert.NotNull(alt);
      //Assert.True(alt.StatusId == (uint)StatusEnum.Pending); // is this correct?
    }

    [Fact]
    public async Task GetModel()
    {
      // use a make we know is in the test database, the lookup should return the existing make and it should have status 1
      var makeName = "Kia";
      var make = await _lookupFactory.GetLookup<Make>(new LookupDTO { TableName = "make", LookupValue = makeName, VehicleType = VehicleTypeEnum.Car });

      var name = "Picanto";
      var result = await _lookupFactory.GetLookup<Model>(new LookupDTO { TableName = "model", LookupValue = name, ParentId = make.Id, VehicleType = VehicleTypeEnum.Car });

      Assert.NotNull(result);
      Assert.True(result.ModelName == name);

      var entity = await _context.Models.FirstOrDefaultAsync(x => x.Id == result.Id);
      Assert.NotNull(entity);
      Assert.True(entity.StatusId == 1);
    }

    [Fact]
    public async Task GetModel_Alt()
    {
      // use a name we know is not in the test database, the lookup service should create an Alt value with a pointer to the real value 
      // the alt and the real value should be statusId 2 (pending)
      var make = await _lookupFactory.GetLookup<Make>(new LookupDTO { TableName = "make", LookupValue = "Anything", VehicleType = VehicleTypeEnum.Car });

      var name = "Made up model";
      var result = await _lookupFactory.GetLookup<Model>(new LookupDTO { TableName = "model", LookupValue = name, ParentId = make.Id, VehicleType = VehicleTypeEnum.Car });

      var entity = await _context.Models.FirstOrDefaultAsync(x => x.Id == result.Id);
      Assert.True(entity.StatusId == (uint)StatusEnum.Pending);

      var alt = await _context.Alts.FirstOrDefaultAsync(x => x.AltRealId == entity.Id && x.AltTable == "model");
      Assert.NotNull(alt);
      //Assert.True(alt.StatusId == (uint)StatusEnum.Pending); // is this correct?
    }

    [Fact]
    public async Task GetDeriv()
    {
      // use a make we know is in the test database, the lookup should return the existing make and it should have status 1
      var makeName = "Kia";
      var make = await _lookupFactory.GetLookup<Make>(new LookupDTO { TableName = "make", LookupValue = makeName, VehicleType = VehicleTypeEnum.Car });

      var modelName = "Picanto";
      var model = await _lookupFactory.GetLookup<Model>(new LookupDTO { TableName = "model", LookupValue = modelName, ParentId = make.Id, VehicleType = VehicleTypeEnum.Car });

      var name = "1.0 66 1 5Dr Manual";
      var result = await _lookupFactory.GetLookup<Deriv>(new LookupDTO { TableName = "deriv", LookupValue = name, ParentId = model.Id, VehicleType = VehicleTypeEnum.Car });

      Assert.NotNull(result);
      Assert.True(result.DerivName == name);

      var entity = await _context.Derivs.FirstOrDefaultAsync(x => x.Id == result.Id);
      Assert.NotNull(entity);
      Assert.True(entity.StatusId == 1);
    }

    [Fact]
    public async Task GetDeriv_Alt()
    {
      // use a name we know is not in the test database, the lookup service should create an Alt value with a pointer to the real value 
      // the alt and the real value should be statusId 2 (pending)
      var make = await _lookupFactory.GetLookup<Make>(new LookupDTO { TableName = "make", LookupValue = "Anything", VehicleType = VehicleTypeEnum.Car });
      var model = await _lookupFactory.GetLookup<Model>(new LookupDTO { TableName = "model", LookupValue = "AnyModel", ParentId = make.Id, VehicleType = VehicleTypeEnum.Car });

      var name = "Made up deriv";
      var result = await _lookupFactory.GetLookup<Deriv>(new LookupDTO { TableName = "deriv", LookupValue = name, ParentId = model.Id, VehicleType = VehicleTypeEnum.Car });

      var entity = await _context.Derivs.FirstOrDefaultAsync(x => x.Id == result.Id);
      Assert.True(entity.StatusId == (uint)StatusEnum.Pending);

      var alt = await _context.Alts.FirstOrDefaultAsync(x => x.AltRealId == entity.Id && x.AltTable == "deriv");
      Assert.NotNull(alt);
      Assert.True(alt.StatusId == (uint)StatusEnum.Pending);
    }


    [Fact]
    public async Task GetFuel()
    {
      // lookup should return the existing entity and it should have status 1
      var name = "DIESEL";
      var result = await _lookupFactory.GetLookup<FuelType>(new LookupDTO { TableName = "fuel_type", LookupValue = name, VehicleType = VehicleTypeEnum.Car });

      Assert.NotNull(result);
      Assert.True(result.FuelTypeName == "Diesel"); // we should return the correctly cased existing record

      var entity = await _context.FuelTypes.FirstOrDefaultAsync(x => x.Id == result.Id);
      Assert.NotNull(entity);
      Assert.True(entity.StatusId == 1);
    }

    [Fact]
    public async Task GetFuel_Alt()
    {
      // the lookup service should create an Alt value with a pointer to the real value 
      // the alt and the real value should be statusId 2 (pending)
      var name = "Petro-electric-2";
      var result = await _lookupFactory.GetLookup<FuelType>(new LookupDTO { TableName = "fuel_type", LookupValue = name, VehicleType = VehicleTypeEnum.Car });

      var entity = await _context.FuelTypes.FirstOrDefaultAsync(x => x.Id == result.Id);
      Assert.True(entity.StatusId == (uint)StatusEnum.Pending);
       
      var alt = await _context.Alts.FirstOrDefaultAsync(x => x.AltRealId == result.Id && x.AltTable == "fuel_type");
      Assert.NotNull(alt);
      Assert.True(alt.StatusId == (uint)StatusEnum.Pending);
    }


    [Fact]
    public async Task GetColour()
    {
      // lookup should return the existing entity and it should have status 1
      var name = "GREY";
      var result = await _lookupFactory.GetLookup<VehicleColour>(new LookupDTO { TableName = "colour", LookupValue = name, VehicleType = VehicleTypeEnum.Car });

      Assert.NotNull(result);
      Assert.True(result.ColourName == "Grey"); // we should return the correctly cased existing record

      var entity = await _context.VehicleColours.FirstOrDefaultAsync(x => x.Id == result.Id);
      Assert.NotNull(entity);
      Assert.True(entity.StatusId == 1);
    }

    [Fact]
    public async Task GetColour_Alt()
    {
      // the lookup service should create an Alt value with a pointer to the real value 
      // the alt and the real value should be statusId 2 (pending)
      var name = "Greige";
      var result = await _lookupFactory.GetLookup<VehicleColour>(new LookupDTO { TableName = "colour", LookupValue = name, VehicleType = VehicleTypeEnum.Car });

      var entity = await _context.VehicleColours.FirstOrDefaultAsync(x => x.Id == result.Id);
      Assert.True(entity.StatusId == (uint)StatusEnum.Pending);

      var alt = await _context.Alts.FirstOrDefaultAsync(x => x.AltRealId == entity.Id && x.AltTable == "colour");
      Assert.NotNull(alt);
      //Assert.True(alt.StatusId == (uint)StatusEnum.Pending); // is this correct?
    }


    [Fact]
    public async Task GetBody()
    {
      // lookup should return the existing entity and it should have status 1
      var name = "Saloon";
      var result = await _lookupFactory.GetLookup<BodyType>(new LookupDTO { TableName = "body_type", LookupValue = name, VehicleType = VehicleTypeEnum.Car });

      Assert.NotNull(result);
      Assert.True(result.BodyTypeName == name); 

      var entity = await _context.BodyTypes.FirstOrDefaultAsync(x => x.Id == result.Id);
      Assert.NotNull(entity);
      Assert.True(entity.StatusId == 1);
    }

    [Fact]
    public async Task GetBody_Alt()
    {
      // the lookup service should create an Alt value with a pointer to the real value 
      // the alt and the real value should be statusId 2 (pending)
      var name = "Sedan";
      var result = await _lookupFactory.GetLookup<BodyType>(new LookupDTO { TableName = "body_type", LookupValue = name, VehicleType = VehicleTypeEnum.Car });

      var entity = await _context.BodyTypes.FirstOrDefaultAsync(x => x.Id == result.Id);
      Assert.True(entity.StatusId == (uint)StatusEnum.Pending);

      var alt = await _context.Alts.FirstOrDefaultAsync(x => x.AltRealId == entity.Id && x.AltTable == "body_type");
      Assert.NotNull(alt);
      Assert.True(alt.StatusId == (uint)StatusEnum.Pending);
    }


    [Fact]
    public async Task GetTransmission()
    {
      // lookup should return the existing entity and it should have status 1
      var name = "Automatic";
      var result = await _lookupFactory.GetLookup<TransmissionType>(new LookupDTO { TableName = "transmission_type", LookupValue = name, VehicleType = VehicleTypeEnum.Car });

      Assert.NotNull(result);
      Assert.True(result.TransmissionTypeName == name); 

      var entity = await _context.FuelTypes.FirstOrDefaultAsync(x => x.Id == result.Id);
      Assert.NotNull(entity);
      Assert.True(entity.StatusId == 1);
    }

    [Fact]
    public async Task GetTransmission_Alt()
    {
      // the lookup service should create an Alt value with a pointer to the real value 
      // the alt and the real value should be statusId 2 (pending)
      var name = "Automagic";
      var result = await _lookupFactory.GetLookup<TransmissionType>(new LookupDTO { TableName = "transmission_type", LookupValue = name, VehicleType = VehicleTypeEnum.Car });

      var entity = await _context.TransmissionTypes.FirstOrDefaultAsync(x => x.Id == result.Id);
      Assert.True(entity.StatusId == (uint)StatusEnum.Pending);

      var alt = await _context.Alts.FirstOrDefaultAsync(x => x.AltRealId == entity.Id && x.AltTable == "transmission_type");
      Assert.NotNull(alt);
      //Assert.True(alt.StatusId == (uint)StatusEnum.Pending); // is this correct?
    }

  }
}
