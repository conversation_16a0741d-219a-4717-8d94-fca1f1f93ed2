﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Threading.Tasks;
using System.Threading;
using Trading.API.Data.DTO.Search;
using Trading.Services.Interfaces;
using Trading.Services.Extensions;
using System;
using Trading.API.Data.DTO.LeadCRM;
using Trading.API.Data.DTO;
using Microsoft.AspNetCore.JsonPatch;
using Trading.API.Data.Models;
using Trading.Services.Classes;

namespace Trading.API.Remarq.Controllers
{
  [Route("api/terms")]
  [ApiController]
  [Authorize]
  public class TermsTemplateController : ControllerBase 
  {
    private readonly ITermsTemplateService _termsTemplateService;

    public TermsTemplateController(ITermsTemplateService termsTemplateService) {
      _termsTemplateService = termsTemplateService;
    }

    [HttpGet]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var searchDTO = JsonConvert.DeserializeObject<TermsTemplateSearchDTO>(query);

      if (!User.IsAdmin() && searchDTO.Filters.CustomerId != User.CustomerId())
      {
        return Forbid();
      }

      var res = await _termsTemplateService.Search(searchDTO, cancellationToken);

      return Ok(res);
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create([FromBody] TermsTemplateDTO dto, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin() && dto.CustomerId != User.CustomerId())
      {
        return Forbid();
      }

      try
      {
        var result = await _termsTemplateService.CreateTemplate(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("{templateId}")]
    public async Task<IActionResult> PatchAdminTask(Guid templateId, JsonPatchDocument<TermsTemplate> patch, CancellationToken cancellationToken)
    {
      var res = await _termsTemplateService.Patch(templateId, patch);
      return Ok(res);
    }
  }
}
