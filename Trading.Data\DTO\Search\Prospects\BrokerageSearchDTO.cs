﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums.Prospects;

namespace Trading.API.Data.DTO.Search.Prospects
{
  public class BrokerageSearchDTO : BaseSearchDTO
  {
    public BrokerageFilters Filters { get; set; } = new BrokerageFilters() { };
  }
  
  public class BrokerageFilters : BaseFilterGuid
  {
    public Guid? AdvertId { get; set; }
    public Guid? ContactId { get; set; }
    public BrokerageStateEnum BrokerageState { get; set; }
    public List<uint> BrokerageStates { get; set; }
    public Guid? SaleId { get; set; }
    public string TextSearch { get; set; }
  }
}
