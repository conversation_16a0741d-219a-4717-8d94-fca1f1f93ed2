﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.Services.ExternalDTO.Accounts;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/[controller]")]
  [ApiController]
  [Authorize]
  public class PaymentController : ControllerBase
  {
    private readonly IStripeService _stripeService;
    private readonly ICustomerInternalInfoService _customerInternalInfoService;

    public PaymentController(IStripeService stripeService, ICustomerInternalInfoService customerInternalInfoService)
    {
      this._stripeService = stripeService;
      this._customerInternalInfoService = customerInternalInfoService;
    }

    [HttpGet]
    [Route("/api/invoice/{invoiceId}/makePaymentIntent")]
    public async Task<IActionResult> MakePaymentIntent(Guid invoiceId, CancellationToken cancellationToken)
    {
      try
      {
        var intent = await _stripeService.CreatePaymentIntentFromInvoice(invoiceId, cancellationToken);
        return Ok(intent);
      }
      catch (Exception ex) {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/paymentIntent/{paymentIntentId}/paymentMade")]
    public async Task<IActionResult> PaymentMade(string paymentIntentId, CancellationToken cancellationToken)
    {
      try
      {
        var paidDate = await _stripeService.PaymentMade(paymentIntentId, cancellationToken);
        return Ok(paidDate);
      }
      catch (Exception ex) {
        return BadRequest(ex);
      }
    }

    /// <summary>
    /// Creates a SetupIntent to securely store a customer's payment method for future use.
    /// </summary>
    /// <param name="request">The request containing customer ID and optional metadata.</param>
    /// <returns>The client secret and SetupIntent ID for client-side confirmation.</returns>
    [HttpPost("create-setup-intent")]
    [Authorize] 
    public async Task<IActionResult> CreateSetupIntent([FromBody] SetupIntentCreateRequest request)
    {
      if (!ModelState.IsValid)
        return BadRequest(ModelState);

      var response = await _stripeService.CreateSetupIntentAsync(request);

      if (response.Success)
        return Ok(new { clientSecret = response.ClientSecret, setupIntentId = response.SetupIntentId });
      else
        return BadRequest(new { error = response.Error });
    }


    [HttpGet("/api/customer/{customerId}/has-payment-method")]
    public async Task<IActionResult> CustomerHasPaymentMethod(Guid customerId, CancellationToken cancellationToken)
    {
      var res = await _customerInternalInfoService.CustomerHasPaymentMethod(customerId);
      return Ok(res);
    }

    [HttpPut("/api/customer/{customerId}/update-payment-method/{paymentMethodId}")]
    public async Task<IActionResult> UpdateCustomerPaymentMethod(Guid customerId, string paymentMethodId, CancellationToken cancellationToken)
    {
      var res = await _customerInternalInfoService.UpdateCustomerPaymentMethod(customerId, paymentMethodId);
      return Ok(res);
    }



    [HttpGet("/api/customer/{customerId}/payment-customer-id")]
    public async Task<IActionResult> GetPaymentCustomerId(Guid customerId)
    {
      var res = await _customerInternalInfoService.GetStripeCustomerId(customerId);
      return Ok(res);
    }

    [HttpGet]
    [Route("/api/customer/{customerId}/payment-method-details")]
    public async Task<IActionResult> GetPaymentMethod(Guid customerId, CancellationToken cancellationToken)
    {
      try
      {
        var res = await _stripeService.GetPaymentMethodDetailsAsync(customerId);
        return Ok(res);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
