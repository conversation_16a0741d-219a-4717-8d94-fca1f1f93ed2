using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.API.Helpers;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/savedSearch")]
  [ApiController]
  [Authorize]
  public class SavedSearchController : ControllerBase
  {
    private readonly ISavedSearchService _savedSearchService;
    private readonly IMapper _mapper;

    public SavedSearchController(
      ISavedSearchService savedSearchService,
      IMapper mapper)
    {
      this._savedSearchService = savedSearchService;
      this._mapper = mapper;
    }

    [HttpDelete]
    [Route("{searchId}")]
    public async Task<ActionResult> deleteSavedSearch(Guid searchId, CancellationToken cancellationToken)
    {
      var contactId = User.ContactId();
      var customerId = User.CustomerId();

      try
      {
        var response = await _savedSearchService.DeleteSearchByContact(customerId.Value, (Guid) contactId, searchId, cancellationToken);
        return Ok(response);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/contact/{contactId}/savedSearches")]
    public async Task<IActionResult> GetSearchesByContact(Guid contactId, CancellationToken cancellationToken)
    {
      if (User.ContactId() == contactId || User.IsAdmin())
      {
        try
        {
          var searchDTOs = await _savedSearchService.GetSearchesByContact(contactId, cancellationToken);
          return Ok(searchDTOs);
        }
        catch (Exception ex)
        {
          return BadRequest(ex);
        }
      }

      return Forbid();
    }


    [HttpGet]
    [Route("")]
    public async Task<IActionResult> GetProfileSearches(CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        try
        {
          var searchDTOs = await _savedSearchService.GetProfileSearches(cancellationToken);
          return Ok(searchDTOs);
        }
        catch (Exception ex)
        {
          return BadRequest(ex);
        }
      }

      return Forbid();
    }

    [HttpPatch]
    [Route("{savedSearchId}")]
    public async Task<IActionResult> UpdateSavedSearch(Guid savedSearchId, [FromBody] JsonPatchDocument<SavedSearch> patch, CancellationToken cancellationToken)
    {
      var contactId = Guid.Parse(patch.FetchValue("contactId").ToString());

      if (User.ContactId() != contactId && !User.IsAdmin())
      {
        return Forbid();
      }

      patch.Add(x => x.ContactId, contactId);

      var response = await _savedSearchService.Patch(savedSearchId, patch, cancellationToken);

      return Ok(response);
    }

    [HttpGet]
    [Route("{savedSearchId}/unsubscribe-info")]
    [AllowAnonymous]
    public async Task<IActionResult> GetSearchInfoForUnsubscribe(Guid savedSearchId, CancellationToken cancellationToken)
    {
      if (savedSearchId == Guid.Empty)
      {
        return BadRequest();
      }

      var response = await _savedSearchService.GetSavedSearchForUnsubscribe(savedSearchId, cancellationToken);

      return Ok(response);
    }

    [HttpGet]
    [Route("{savedSearchId}/set-alert-option/{option}")]
    [AllowAnonymous]
    public async Task<IActionResult> SetAlertOptions(Guid savedSearchId, string option, CancellationToken cancellationToken)
    {
      if(savedSearchId == Guid.Empty || string.IsNullOrEmpty(option))
      {
        return BadRequest(); 
      }
    
      await _savedSearchService.SetAlertOption(savedSearchId, option, cancellationToken);
      return Ok("Alert option set");
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> CreateSavedSearch([FromBody] SavedSearchDTO dto, CancellationToken cancellationToken)
    {
      if (User.ContactId() != dto.ContactId && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var response = await _savedSearchService.Create(dto, cancellationToken);

        return Ok(response);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }

      return Forbid();
    }
  }
}
