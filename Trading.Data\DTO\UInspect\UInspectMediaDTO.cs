﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.UInspections
{
  public class UInspectMediaDTO : BaseModelEntityDTO
  {
    public Guid UInspectId { get; set; }
    public string Path { get; set; }
    public string Description { get; set; }
    public Guid? UInspectQuestionId { get; set; }
    public int? UInspectSectionId { get; set; }
    public string MediaURL { get; set; }

  }
}
