﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.LeadCRM;
using Trading.API.Data.DTO.Search.LeadCRM;
using Trading.API.Data.DTO.Search.Valuation;
using Trading.API.Data.Enums;
using Trading.API.Data.Models.LeadCRM;
using Trading.Services.Extensions;
using Trading.Services.LeadCRM.Interfaces;
using Trading.Services.Valuations.Interfaces;

namespace Trading.API.LeadCRM.Controllers
{
  [Route("api/lead-vehicle")]
  [ApiController]
  [Authorize]
  public class LeadVehicleController : ControllerBase
  {
    private readonly ILeadCRMService _leadCRMService;
    private readonly IValuationQuoteService _valuationQuoteService;

    public LeadVehicleController(ILeadCRMService leadCRMService, IValuationQuoteService valuationQuoteService)
    {
      this._leadCRMService = leadCRMService;
      this._valuationQuoteService = valuationQuoteService;
    }

    [HttpPatch]
    [Route("{leadVehicleId}")]
    public async Task<IActionResult> Patch(Guid leadVehicleId, JsonPatchDocument<LeadVehicle> patch, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadCRMService.PatchLeadVehicle(leadVehicleId, patch, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
