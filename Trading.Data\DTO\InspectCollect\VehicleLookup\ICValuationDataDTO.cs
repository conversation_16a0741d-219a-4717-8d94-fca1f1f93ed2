﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.InspectCollect.VehicleLookup;

public class ICValuationDataDTO
{
  public int? PriceClean { get; set; }
  public int? PriceAvg { get; set; }
  public int? PriceBelow { get; set; }
  public int? PriceRetail { get; set; }
  public int? CostWhenNew { get; set; }
}
