using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/[controller]")]
  [ApiController]
  public class FuelTypeController : ControllerBase
  {
    private readonly IFuelTypeService _fuelTypeService;

    public FuelTypeController(IFuelTypeService FuelTypeService)
    {
      this._fuelTypeService = FuelTypeService;
    }

    [HttpGet]
    [Route("/api/VehicleType/{vehicleTypeId}/FuelTypes")]
    public async Task<IActionResult> Search(uint vehicleTypeId, [FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var searchDTO = new FuelTypeSearchDTO() { };

        if (!String.IsNullOrEmpty(query))
        {
          searchDTO = JsonSerializer.Deserialize<FuelTypeSearchDTO>(query);
        }

        searchDTO.Filters.VehicleTypeId = vehicleTypeId;

        var fuelTypes = await _fuelTypeService.Search(searchDTO, cancellationToken);
        return Ok(fuelTypes);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
