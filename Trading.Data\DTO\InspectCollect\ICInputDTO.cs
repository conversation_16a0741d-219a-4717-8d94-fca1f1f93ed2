﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums.InspectCollect;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICInputDTO : BaseModelEntityDTO
  {
    public string Label { get; set; }
    public string FieldName { get; set; }
    public ICInputTypeEnum ICInputType { get; set; }
    public Guid? ICInputCategoryId { get; set; }
    public ICInputCategoryDTO ICInputCategory { get; set; }
    public Guid? ICContainerGroupId { get; set; }
    public ICContainerGroupDTO ICContainerGroup { get; set; }
    public string Explanation { get; set; }
    public uint? MaxItems { get; set; }
    public uint? MinItems { get; set; }
    public string ConstantValue { get; set; }
    public Guid? ICNextContainerId { get; set; }
    public bool? RequiresValidContainer { get; set; }
    public List<ICInputValidationDTO> ICInputValidations { get; set; }
    public List<ICInputOptionDTO> ICInputOptions { get; set; }
    public string ICNextPath { get; set; }
    public ICInputMappingEnum? ICInputMapping { get; set; }
  }

  public class ICInputSearchDTO : BaseSearchDTO
  {
    public ICInputSearchFilters Filters { get; set; } = new ICInputSearchFilters();
  }

  public class ICInputSearchFilters : BaseFilter
  {
    public string? Label { get; set; }
    public string? FieldName { get; set; }
    public Guid? ICContainerGroupId { get; set; }
  }

  public class ICInputCreateDTO
  {
    public string Label { get; set; }
    public string? FieldName { get; set; }
    public ICInputTypeEnum? ICInputType { get; set; }
    public Guid ICContainerGroupId { get; set; }
    public Guid? ICInputCategoryId { get; set; }
    public string Explanation { get; set; }
    public uint? MaxItems { get; set; }
    public uint? MinItems { get; set; }
    public uint? ICNextPath { get; set; }
  }
}
