using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Threading.Tasks;
using System.Threading;
using System;
using Trading.Services.Extensions;
using Trading.Services.Interfaces.ExtLeads;
using Trading.API.Data.DTO.ExtLeads;
using Trading.API.Data.DTO.Comms;
using Microsoft.AspNetCore.Http;
using Trading.Services.Interfaces;
using System.Net;

namespace Trading.API.Controllers.ZohoCampaign
{
  [Route("api/zoho-campaign")]
  [ApiController]
  public class ZohoCampaignController : ControllerBase
  {
    public IMapper _mapper;
    public IZohoCampaignService _zohoCampaignService;

    // ClientID: 1000.6MK3DZO0YPII0CRH595DYQZ4KNUQXX
    // ClientSecret: 385fad6ce6ce2e15423e1c71178b2e485fc4b4c431

    public ZohoCampaignController(IMapper mapper, IZohoCampaignService zohoCampaignService)
    {
      _mapper = mapper;
      _zohoCampaignService = zohoCampaignService;
    }

    [HttpGet]
    [Route("sync")]
    [AllowAnonymous]
    public async Task<IActionResult> Sync(CancellationToken cancellationToken)
    {
      await _zohoCampaignService.Sync(cancellationToken);
      return Ok();
    }

    [HttpGet]
    [Route("callback")]
    public async Task<ContentResult> Callback([FromQuery] string code, [FromQuery] string location, CancellationToken cancellationToken)
    {
      var response = await _zohoCampaignService.Callback(code, cancellationToken);

      return new ContentResult
      {
        ContentType = "text/html",
        StatusCode = (int)HttpStatusCode.OK,
        Content = "<html><body>" + response + "</body></html>"
      };
    }

    [HttpGet]
    [Route("get-access-token")]
    public async Task<IActionResult> getAccessToken(CancellationToken cancellationToken)
    {
      // NOTE: This is a test endpoint, to test the stored refresh token works

      var access_token = await _zohoCampaignService.GetAccessToken(cancellationToken);

      return Ok(access_token);
    }

    [HttpGet]
    [Route("get-list-contacts")]
    public async Task<IActionResult> getListContacts(CancellationToken cancellationToken)
    {
      // NOTE: This is a test endpoint, to fetch contact list

      var access_token = await _zohoCampaignService.GetZohoContactList(cancellationToken);

      return Ok(access_token);
    }

    [HttpGet]
    [Route("get-authorization-url")]
    public async Task<ContentResult> getAuthorizationURL(CancellationToken cancellationToken)
    {
      var authorizationURL = await _zohoCampaignService.GetAuthorizationURL(cancellationToken);

      return new ContentResult
      {
        ContentType = "text/html",
        StatusCode = (int)HttpStatusCode.OK,
        Content = "<html><body><a href='" + authorizationURL + "'>" + authorizationURL + "</a></body></html>"
      };

    }
  }
}