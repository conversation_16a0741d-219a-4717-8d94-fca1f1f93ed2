using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.API.Helpers;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/site")]
  [ApiController]
  public class SiteController : ControllerBase
  {
    private readonly ISiteService _siteService;

    public SiteController(ISiteService siteService)
    {
      this._siteService = siteService;
    }

    [HttpGet]
    [Route("/api/sites")]
    public async Task<IActionResult> Search([FromQuery] int? unique, [FromQuery] string? search, CancellationToken cancellationToken)
    {
      var searchDTO = new SiteSearchDTO() { };

      if (!String.IsNullOrEmpty(search))
      {
        searchDTO = JsonSerializer.Deserialize<SiteSearchDTO>(search);
      }

      try
      {
        var sites = await _siteService.Search(searchDTO, cancellationToken);
        return Ok(sites);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{siteId}")]
    public async Task<IActionResult> Get(uint siteId, [FromQuery] int? unique, [FromQuery] string? search, CancellationToken cancellationToken)
    {
      var searchDTO = new SiteSearchDTO() { LockRecord = false };

      if (!String.IsNullOrEmpty(search))
      {
        searchDTO = JsonSerializer.Deserialize<SiteSearchDTO>(search);
      }

      try
      {
        var site = await _siteService.Get(siteId, searchDTO, cancellationToken);

        return Ok(site);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create(SiteDTO dto, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        var response = await _siteService.Create(dto, cancellationToken);

        return Ok(response);
      }

      return Forbid();
    }

    [HttpDelete]
    [Route("{siteId}")]
    public async Task<IActionResult> Delete(uint siteId, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        var response = await _siteService.Delete(siteId, cancellationToken);

        return Ok(response);
      }

      return Forbid();
    }

    [HttpPatch]
    [Route("{siteId}")]
    public async Task<IActionResult> Patch(uint siteId, [FromBody] JsonPatchDocument<Site> patch, CancellationToken cancellationToken)
    {
      // Only edit addresses that are ours (or if we're admin)
      if (User.IsAdmin())
      {
        try
        {
          return Ok(await _siteService.Patch(siteId, patch, cancellationToken));
        }
        catch (Exception ex)
        {
          return ex.ParseError();
        }
      }

      return Forbid();
    }
  }
}
