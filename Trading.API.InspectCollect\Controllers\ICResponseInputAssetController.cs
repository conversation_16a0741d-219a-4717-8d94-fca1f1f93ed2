using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/response-input-asset")]
  [ApiController]
  [AllowAnonymous]
  public class ICResponseInputAssetController : ControllerBase
  {
    private readonly ICResponseInputAssetInterface _icResponseInputAssetService;

    public ICResponseInputAssetController(ICResponseInputAssetInterface serviceInterface)
    {
      _icResponseInputAssetService = serviceInterface;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(Guid id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      ICResponseInputAssetSearchDTO dto;

      if (!String.IsNullOrEmpty(query))
      {
        dto = JsonConvert.DeserializeObject<ICResponseInputAssetSearchDTO>(query);
      }
      else
      {
        dto = new ICResponseInputAssetSearchDTO();
      }

      var res = await _icResponseInputAssetService.Get(id, dto, cancellationToken);
      return Ok(res);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icResponseInputAssetService.Delete(id);
      return Ok(res);
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Post([FromBody] ICResponseInputAssetCreateDTO dto, CancellationToken cancellationToken)
    {
      var res = await _icResponseInputAssetService.Create(dto);
      return Ok(res);
    }

    [HttpGet]
    [Route("search")]
    [Route("/api/inspect-collect/input-assets")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICResponseInputAssetSearchDTO>(query);
      var res = await _icResponseInputAssetService.Search(dto, cancellationToken);
      return Ok(res);
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<ICResponseInputAsset> dto)
    {
      var response = await _icResponseInputAssetService.Patch(id, dto);
      return Ok(response);
    }


  }
}