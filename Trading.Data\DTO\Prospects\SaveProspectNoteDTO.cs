﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums.Prospects;

namespace Trading.API.Data.DTO.Prospects
{
  // used from UI when adding or editing a prospect history as we need context (i.e. advertId etc)
  // in case the prospect/brokerage record doesn't exist
  public class SaveProspectNoteDTO
  {
    // use this to determine if there is a brokerage record 
    public Guid? ProspectId { get; set; }
    public Guid BrokerageId { get; set; }

    public uint ProspectActionId { get; set; }

    public Guid ProspectContactId { get; set; }

    public string Note { get; set; }


    // the contact of the user who created the history record
    public Guid ContactId { get; set; }

    public DateTime? ReminderDateTime { get; set; }

    public bool ManuallyAdded { get; set; }

    public ProspectStateEnum? ProspectState { get; set; }
  }
}
