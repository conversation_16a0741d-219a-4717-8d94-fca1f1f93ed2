﻿using Microsoft.AspNetCore.JsonPatch;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;

namespace Trading.Services.InspectCollect.Interfaces
{
  public interface ICContainerWidgetInputValidationInterface
  {
    Task<ICContainerWidgetInputValidationDTO> Create(ICContainerWidgetInputValidationCreateDTO dto);

    Task<ValidatedResultDTO<ICContainerWidgetInputValidationDTO>> Get(Guid id, ICContainerWidgetInputValidationSearchDTO search, CancellationToken cancellationToken);
    Task<bool> Delete(Guid id);

    Task<ICContainerWidgetInputValidationDTO> Patch(Guid id, JsonPatchDocument<ICContainerWidgetInputValidation> patch);

    Task<SearchResultDTO<ICContainerWidgetInputValidationDTO>> Search(ICContainerWidgetInputValidationSearchDTO search, CancellationToken cancellationToken);
  }
}
