﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System.Threading;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;
using Trading.Services.Interfaces.Custom;
using Trading.Services.ExternalDTO.BIG;
using Trading.API.Common;
using System.Collections.Generic;
using Trading.Services.Interfaces.Imports;
using Trading.API.Data.DTO.Imports;
using Trading.API.Data.Enums.Imports;
using Newtonsoft.Json;

namespace Trading.API.Remarq.Controllers.Custom
{
  //[Route("api/[controller]")]
  //[ApiController]
  //[Authorize]
  //public class BigController : ControllerBase
  //{
  //  private readonly IImportService _importService;

  //  public BigController(IImportService importService)
  //  {
  //    _importService = importService;
  //  }

  //  [HttpPost]
  //  [Route("import")]
  //  //[ApiKey] // not reading config for some reason
  //  [AllowAnonymous]
  //  [RequestFormLimits(ValueLengthLimit = int.MaxValue, MultipartBodyLengthLimit = int.MaxValue)]
  //  [DisableRequestSizeLimit]
  //  public async Task<IActionResult> ImportVehicles([FromBody] List<BIGImportDTO> dtos, CancellationToken cancellationToken)
  //  {
  //    // add a record to the import table for later processing and return the import Id
  //    var id = await _importService.CreateImportRecord(new CreateImportRecordDTO
  //    {
  //      ImportProvider = ImportProviderEnum.BIG,
  //      JsonBlob = JsonConvert.SerializeObject(dtos)
  //    });

  //    return Ok(new { Text = $"Import has been registered and will be processed soon, your import id is {id}" });
  //  }
  //}
}
