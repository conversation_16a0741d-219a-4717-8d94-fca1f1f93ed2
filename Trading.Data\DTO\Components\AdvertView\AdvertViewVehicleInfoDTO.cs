﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO.Components.AdvertView
{
  public class AdvertViewVehicleInfoDTO
  {
    public string Vrm {  get; set; }
    public uint? Odometer { get; set; }
    public string Colour { get; set; }
    public ushort? Doors { get; set; }
    public ushort? Owners { get; set; }
    public bool LogBook { get; set; }
    public bool ServiceHistory { get; set; }
    public ServiceHistoryTypeEnum? ServiceHistoryType { get; set; }
    public DateTime? MotExpires { get; set; }
    public ushort? EngineCc { get; set; }
    public byte? NoOfKeys { get; set; }
    public DateTime? DateOfReg { get; set; }
    public uint? Co2 { get; set; }

    public FuelTypeDTO FuelType { get; set; }
    public TransmissionTypeDTO TransmissionType { get; set; }
    public BodyTypeDTO BodyType { get; set; }

    public AdvertViewVehicleInfoAddressDTO Address { get; set; }
    public AttribvalDTO VATStatus { get; set; }

    public bool hasServiceHistory { get; set; }
    public bool hasMOTHistory { get; set; }
  }

  public class AdvertViewVehicleInfoAddressDTO
  {
    public string AddressName { get; set; }
  }
}
