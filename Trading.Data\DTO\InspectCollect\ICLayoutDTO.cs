﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICLayoutDTO : BaseModelEntityDTO
  {
    public string Name { get; set; }
    public Guid? FirstContainerId { get; set; }
    public Guid? LastContainerId { get; set; }
    public Guid? ICContainerGroupId { get; set; }
    public ICStyleDTO? ICStyle { get; set; }
    public Guid? ICStyleId { get; set; }
    public bool? IsDefaultLayout { get; set; }
    public bool? AuthRequired { get; set; }
    public Guid? DemoResponseId { get; set; }

    public Guid? PrimaryImageInputId { get; set; }
  }

  public class ICLayoutSearchDTO : BaseSearchDTO
  {
    public ICLayoutFilters Filters { get; set; } = new ICLayoutFilters();
  }

  public class ICLayoutFilters: BaseFilter
  {
    public string? Name { get; set; }
    public Guid? FirstContainerId { get; set; }
    public Guid? LastContainerId { get; set; }
    public Guid? ICContainerGroupId { get; set; }
  }

  public class ICLayoutCreateDTO 
  {
    public string Name { get; set; }
    public Guid? FirstContainerId { get; set; }
    public Guid? LastContainerId { get; set; }
    public Guid? ICContainerGroupId { get; set; }
  }
}
