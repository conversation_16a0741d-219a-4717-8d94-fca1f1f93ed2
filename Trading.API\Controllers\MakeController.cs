using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.API.Helpers;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/make")]
  [ApiController]
  public class MakeController : ControllerBase
  {
    private readonly IMakeService _makeService;
    private readonly IMergeService _mergeService;

    public MakeController(IMakeService makeService, IMergeService mergeService)
    {
      this._makeService = makeService;
      this._mergeService = mergeService;
    }

    [HttpPatch]
    [Route("{makeId}")]
    public async Task<IActionResult> Patch(uint makeId, [FromBody] JsonPatchDocument<Make> patch, CancellationToken cancellationToken)
    {
      // Only edit addresses that are ours (or if we're admin)
      if (User.IsAdmin())
      {
        try
        {
          return Ok(await _makeService.Patch(makeId, patch, cancellationToken));
        }
        catch (Exception ex)
        {
          return ex.ParseError();
        }
      }

      return Forbid();
    }

    [HttpGet]
    [Route("/api/vehicleType/{vehicleTypeId}/makes")]
    public async Task<IActionResult> GetMakes(uint vehicleTypeId, [FromQuery] string query, CancellationToken cancellationToken)
    {
      {
        var searchDTO = new MakeSearchDTO() { };

        if (!String.IsNullOrEmpty(query))
        {
          searchDTO = JsonSerializer.Deserialize<MakeSearchDTO>(query);
        }

        searchDTO.Filters.VehicleTypeId = vehicleTypeId;

        try
        {
          var makes = await _makeService.Search(searchDTO, cancellationToken);
          return Ok(makes);
        }
        catch (Exception ex)
        {
          return BadRequest(ex);
        }
      }
    }

   
  }
}
