﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums.InspectCollect;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICContainerDTO : BaseModelEntityDTO
  {
    public string Name { get; set; }
    public Guid? HelpContainerId { get; set; }
    public Guid? NextContainerId { get; set; }
    public bool? IsHelpContainer { get; set; }
    public bool? AutoShowHelp { get; set; }
    public ContainerOrientationEnum? Orientation { get; set; }
    public ContainerFlexDirectionEnum? FlexDirection { get; set; }
    public ICAlignmentEnum? Alignment { get; set; }
    public string Background { get; set; }
    public Guid? ICContainerGroupId { get; set; }
    public List<ICContainerWidgetDTO> ICContainerWidgets { get; set; }

    public ICStyleDTO? ICStyle { get; set; }
    public Guid? ICStyleId { get; set; }
    public uint? Position { get; set; }

    public ICResponseStatusEnum SetsResponseStatus { get; set; }
  }

  public class ICContainerSearchDTO : BaseSearchDTO
  {
    public ICContainerSearchFilters Filters { get; set; } = new ICContainerSearchFilters();
  }

  public class ICContainerSearchFilters : BaseFilter
  {
    public string Name { get; set; }
    public Guid? ICContainerGroupId { get; set; }
    public bool? IsHelpContainer { get; set; }
  }

  public class ICContainerCreateDTO
  {

    public Guid? ICContainerGroupId { get; set; }
    public ContainerOrientationEnum? Orientation { get; set; }
    public uint? Position { get; set; }
    public string Name { get; set; }
  }
}
