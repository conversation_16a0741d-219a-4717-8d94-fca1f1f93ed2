using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.API.Helpers;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/tax")]
  [ApiController]
  public class TaxController : ControllerBase
  {
    public ITaxService _taxService;
    public IMapper _mapper;

    public TaxController(
      ITaxService taxService,
      IMapper mapper)
    {
      _taxService = taxService;
      _mapper = mapper;
    }

    [HttpGet]
    [Route("/api/taxes")]
    public async Task<IActionResult> Search([FromQuery] int? unique, [FromQuery] string? search, CancellationToken cancellationToken)
    {
      var searchDTO = new TaxSearchDTO() { };

      if (!String.IsNullOrEmpty(search))
      {
        searchDTO = JsonSerializer.Deserialize<TaxSearchDTO>(search);
      }

      var response = await _taxService.Search(searchDTO, cancellationToken);

      return Ok(response);
    }

    [HttpDelete]
    [Route("{taxId}")]
    public async Task<IActionResult> Delete(uint taxId, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        var response = await _taxService.Delete(taxId, cancellationToken);

        return Ok(response);
      }

      return Forbid();
    }

    [HttpGet]
    [Route("{taxId}")]
    public async Task<IActionResult> GetTax(uint taxId, [FromQuery] string search, CancellationToken cancellationToken)
    {
      var searchDTO = new TaxSearchDTO() { };

      if (!String.IsNullOrEmpty(search))
      {
        searchDTO = JsonSerializer.Deserialize<TaxSearchDTO>(search);
      }

      var response = await _taxService.Get(taxId, searchDTO, cancellationToken);

      if (response != null)
      {
        return Ok(response);
      }

      return NotFound();
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> Create(TaxDTO dto, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        var response = await _taxService.Create(dto, cancellationToken);

        return Ok(response);
      }

      return Forbid();
    }

    [HttpPatch]
    [Route("{taxId}")]
    public async Task<IActionResult> Patch(uint taxId, [FromBody] JsonPatchDocument<Tax> patch, CancellationToken cancellationToken)
    {
      // Only edit addresses that are ours (or if we're admin)
      if (User.IsAdmin())
      {
        try
        {
          return Ok(await _taxService.Patch(taxId, patch, cancellationToken));
        }
        catch (Exception ex)
        {
          return ex.ParseError();
        }
      }

      return Forbid();
    }
  }
}
