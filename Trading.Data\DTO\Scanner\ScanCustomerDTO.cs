using System;
using System.Collections.Generic;
using System.Linq;
using Trading.API.Data.DTO;
using Trading.API.Data.Enums;

namespace Trading.API.Data.Models.DTO
{
  public class ScanCustomerDTO : BaseModelEntityIntDTO
  {
    public Guid? CustomerId { get; set; }

    public Customer Customer { get; set; }

    public uint? ScanStyleId { get; set; }
    public uint? EntryPoint { get; set; }
    public uint? MaxPages { get; set; }
    public string ScanUrl { get; set; }
    public string DomainName { get; set; }
    public string CompanyName { get; set; }
    public string Homepage { get; set; }
    public bool? ScanEnabled { get; set; }
    public uint? PreviewQueueId { get; set; }
    public uint? VehiclesIndexed { get; set; }
    public DateTime? LastScan { get; set; }
    public ScanStyleDTO ScanStyle { get; set; }
    public string RequestVarValue1 { get; set; }
    public string RequestVarValue2 { get; set; }
    public string RequestVarValue3 { get; set; }
    public string CustomHeaders { get; set; }
    public ICollection<ScanQueueDTO> ScanQueues { get; set; }
  }
}