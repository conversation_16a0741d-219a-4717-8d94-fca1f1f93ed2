﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.LeadCRM
{
  public class CampaignDTO : LeadBaseModelDTO
  {
    public string Description { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }

    public ICollection<CampaignParticipantDTO> Participants { get; set; }
    public ICollection<CampaignLeadDTO> Leads { get; set; }
    public ContactDTO Owner { get; set; }
    public Guid OwnerId { get; set; }

    public uint ParticipantCount { get; set; }
    public uint LeadCount { get; set; }
  }
}
