﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.UInspections
{
  public class UInspectSearchDTO : BaseSearchDTO
  {
    public UInspectSearchFilters Filters { get; set; } = new UInspectSearchFilters() { };

    public class UInspectSearchFilters : BaseFilterGuid
    {
      public Guid? LeadVehicleId { get; set; }

      public DateTime? FromDate { get; set; }
      public DateTime? ToDate { get; set; }
    }
  }
}
