﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/negotiation")] 
  [ApiController]
  [Authorize]

  public class NegotiationController : ControllerBase
  {
    private readonly INegotiationService _negotiationService;
    private readonly IBidService _bidService;

    public NegotiationController(INegotiationService negotiationService, IBidService bidService)
    {
      this._negotiationService = negotiationService;
      this._bidService = bidService;
    }

    [HttpPost, Route("")]
    public async Task<IActionResult> CreateNegotiation([FromBody] CreateNegotiationDTO dto, CancellationToken cancellationToken)
    {
      // only an admin can perform this function 
      if (User.IsAd<PERSON>())
      {
        try
        {
          var result = await _negotiationService.CreateNegotiation(dto.AdvertId, dto.ContactId, cancellationToken);
          return Ok(result);
        }
        catch (Exception ex)
        {
          return BadRequest(ex);
        }
      } 
      else
      {
        return Forbid();
      }
    }

    [HttpPatch]
    [Route("{negotiationId}")]
    public async Task<IActionResult> PatchAdminTask(Guid negotiationId, JsonPatchDocument<Negotiation> patch, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin())
      {
        return Forbid();
      }

      await _negotiationService.Patch(negotiationId, patch, cancellationToken);
      return Ok();
    }

    [HttpPost, Route("{negotiationId}/note")]
    public async Task<IActionResult> CreateNegotiationNote([FromBody] NegotiationNoteDTO noteDTO, CancellationToken cancellationToken)
    {
      // only an admin can perform this function 
      if (User.IsAdmin())
      {
        try
        {
          var result = await _negotiationService.CreateNegotiationNote(noteDTO, cancellationToken);
          return Ok(result);
        }
        catch (Exception ex)
        {
          return BadRequest(ex);
        }
      }
      else
      {
        return Forbid();
      }
    }


    [HttpPut]
    [Route("{negotiationId}/acceptBid/{bidGuid}")] // used when accepting a negotiating bid on behalf of a seller
    public async Task<IActionResult> AcceptBid(Guid negotiationId, Guid bidGuid, CancellationToken cancellationToken)
    {
      // only an admin can perform this function 
      if (!User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        await _bidService.AcceptNegotiationBid(negotiationId, bidGuid, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/negotiations/{advertId}")]
    public async Task<IActionResult> GetAdvertNegotiations(Guid advertId, CancellationToken cancellationToken)
    {
      // only an admin can perform this function 
      if (!User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        await _negotiationService.GetAdvertNegotiations(advertId, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

  }
}
