﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Search;
using Trading.API.Helpers;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/recordView")]
  [ApiController]
  [Authorize]
  public class AdViewController : ControllerBase
  {
    private readonly IAdviewService _adviewService;

    public AdViewController(IAdviewService adviewService)
    {
      _adviewService = adviewService;
    }


    [HttpGet]
    [Route("/api/contact/{contactId}/recentlyViewed")]
    public async Task<IActionResult> RecentlyViewed(Guid contactId, CancellationToken cancellationToken)
    {
      var userContactId = User.ContactId();

      if (userContactId == contactId || User.IsAdmin()) {
        return Ok(await _adviewService.RecentlyViewed((Guid) User.ContactId(), cancellationToken));
      }

      return BadRequest();
    }

    [HttpGet]
    [Route("{advertId}")]
    public async Task RecordView(Guid advertId, CancellationToken cancellationToken)
    {
      if (User.ContactId() != null) {
        await _adviewService.RecordView(advertId, User.CustomerId().Value, User.ContactId().Value, cancellationToken);
      }
    }

    [HttpGet]
    [Route("search")]
    public async Task<IActionResult> SearchAdviews([FromQuery] string query, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin())
      {
        return BadRequest();
      }

      var dto = JsonConvert.DeserializeObject<WhosWhoAdminSearchDTO>(query);
      var res = await _adviewService.SearchAdviews(dto, cancellationToken);
      return Ok(res);
    }

    [HttpGet]
    [Route("top-viewers")]
    public async Task<IActionResult> GetTopViewers([FromQuery] string query, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin() && !User.IsPowerUser())
      {
        return BadRequest();
      }

      var dto = JsonConvert.DeserializeObject<WhosWhoAdminSearchDTO>(query);
      var res = await _adviewService.GetTopViewers(dto, cancellationToken);
      return Ok(res);
    }

    [HttpGet]
    [Route("{advertId}/summary")]
    public async Task<IActionResult> GetAdViewSummary(Guid advertId, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin())
      {
        return BadRequest();
      }

      var res = await _adviewService.GetAdViewSummaryAsync(advertId, cancellationToken);
      return Ok(res);
    }

  }
}
