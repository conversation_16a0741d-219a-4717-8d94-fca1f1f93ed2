﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums.InspectCollect;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICAssetDTO : BaseModelEntityDTO
  {
    public Guid? ICContainerGroupId { get; set; }
    public ICContainerGroupDTO? ICContainerGroup { get; set; }
    public string? Label { get; set; }
    public string? Path { get; set; }
    public string? ClassIcon { get; set; }
    public ContainerOrientationEnum orientation { get; set; }

    public ICAssetTypeEnum? ICAssetType { get; set; }
  }

  public class ICAssetSearchDTO : BaseSearchDTO
  {
    public ICAssetSearchFilters Filters { get; set; } = new ICAssetSearchFilters();
  }

  public class ICAssetSearchFilters : BaseFilter
  {
    public string? Label { get; set; }
    public Guid? ICContainerGroupId { get; set; }
  }

  public class ICAssetCreateDTO
  {
    public string Label { get; set; }
    public Guid ICContainerGroupId { get; set; }
  }
}
