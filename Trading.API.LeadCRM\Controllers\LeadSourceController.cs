﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.LeadCRM;
using Trading.API.Data.DTO.Search.LeadCRM;
using Trading.API.Data.Models.LeadCRM;
using Trading.Services.Extensions;
using Trading.Services.LeadCRM.Interfaces;

namespace Trading.API.LeadCRM.Controllers
{
  [Route("api/lead-source")]
  [ApiController]
  [Authorize]
  public class LeadSourceController : ControllerBase
  {
    private readonly ILeadCRMService _leadCRMService;

    public LeadSourceController(ILeadCRMService leadCRMService)
    {
      this._leadCRMService = leadCRMService;
    }

    [HttpGet]
    [ResponseCache(Duration = 60)]
    [Route("/api/lead-sources")]
    public async Task<IActionResult> SearchLeadSources(CancellationToken cancellationToken, [FromQuery] string? query)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = new LeadSourceSearchDTO();
        
        if (!String.IsNullOrEmpty(query))
        {
          dto = JsonConvert.DeserializeObject<LeadSourceSearchDTO>(query);
        }

        var result = await _leadCRMService.SearchLeadSources(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("")]
    public async Task<IActionResult> CreateLeadSource([FromBody] LeadSourceDTO dto, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadCRMService.AddLeadSource(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<IActionResult> GetLeadSource(Guid leadSourceId, CancellationToken cancellationToken, [FromQuery] string? query)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var dto = JsonConvert.DeserializeObject<LeadSourceSearchDTO>(query);
        dto.Filters.Id = leadSourceId;
        var result = await _leadCRMService.SearchLeadSources(dto, cancellationToken);

        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> PatchLeadSource(uint id, JsonPatchDocument<LeadSource> patch, CancellationToken cancellationToken)
    {
      if (!User.IsAuctionAdmin() && !User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _leadCRMService.PatchLeadSource(id, patch, cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
