using System;
using System.Collections.Generic;
using System.Linq;
using Trading.API.Data.DTO;
using Trading.API.Data.Enums;

namespace Trading.API.Data.Models.DTO
{
  public class ScanStageDTO : BaseModelEntityIntDTO
  {
    public string StageName { get; set; }

    public uint? NextScanStageId { get; set; }

    public ScanStageDTO NextScanStage { get; set; }

    public string CreateRecords { get; set; }

    public ScanStyleDTO ScanStyle { get; set; }
    public uint? ScanStyleId { get; set; }

    public uint? OutputFieldId { get; set; }

    public uint? PreviewEntryPoint { get; set; }

    public string PreviewUrl { get; set; }
    public ScanContentTypeEnum ContentType { get; set; }
    public bool EntryPoint { get; set; }

    public ICollection<ScanConfigDTO> ScanConfigs { get; set; }
  }
}