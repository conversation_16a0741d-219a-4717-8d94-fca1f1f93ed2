﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Trading.Services.Interfaces;
using Xero.NetStandard.OAuth2.Client;
using Xero.NetStandard.OAuth2.Token;

namespace Trading.API.Remarq.Controllers.Accounting
{
  [ApiController]
  [FormatFilter]
  [Route("api/xero")]
  [EnableCors("AllowOrigin")]
  public class XeroAuthController : Controller
  {
    private readonly ILogger<XeroAuthController> _logger;
    private readonly IXeroService _accountingService;

    public object TokenUtilities { get; private set; }

    public XeroAuthController(
        ILogger<XeroAuthController> logger,
        IXeroService accountingService
        )
    {
      _logger = logger;
      _accountingService = accountingService;
    }

    [HttpGet]
    [Route("/xeroAuth", Name = "xeroAuth")]
    [EnableCors("AllowOrigin")]
    [AllowAnonymous]
    public async Task<IActionResult> xeroAuth(int forceRefresh)
    {
      var xeroClient = _accountingService.FetchXeroClient();

      XeroOAuth2Token xeroToken = null;

      bool expiredToken = false;

      try
      {
        xeroToken = await _accountingService.FetchToken();
      }
      catch (Exception e)
      {
        _logger.LogError("Error fetching Token in xeroAuth: " + e.Message);
      }

      if (xeroToken != null && xeroToken.ExpiresAtUtc.Ticks < DateTime.UtcNow.Ticks)
      {
        expiredToken = true;
      }

      if (xeroToken == null || forceRefresh == 1 || expiredToken)
      {
        var uri = xeroClient.BuildLoginUri();
        return Redirect(uri);
      }
      else
      {
        xeroToken = await _accountingService.RefreshToken(xeroToken);
        return View("/Views/Xero/XeroTokenFound/XeroTokenFound.cshtml", xeroToken);
      }
    }

    [HttpGet]
    [Route("/xeroResponse")]
    [EnableCors("AllowOrigin")]
    // GET /Authorization/Callback
    public async Task<ActionResult> Callback(string code, string state)
    {
      XeroClient xeroClient = _accountingService.FetchXeroClient();

      var xeroToken = (XeroOAuth2Token)await xeroClient.RequestAccessTokenAsync(code);

      await _accountingService.StoreToken(xeroToken);

      return RedirectToRoute("xeroAuth");
    }
  }
}
