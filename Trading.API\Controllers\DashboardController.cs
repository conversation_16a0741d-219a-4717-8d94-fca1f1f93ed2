﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Common;
using Trading.API.Data;
using Trading.API.Data.Enums;
using Trading.API.Helpers;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/dashboard")]
  [ApiController]
  public class DashboardController : ControllerBase
  {
    private readonly IDashboardService _dashboardService;

    public DashboardController(IDashboardService dashboardService)
    {
      this._dashboardService = dashboardService;
    }


    [HttpGet]
    [Authorize]
    [Route("buyerStats")]
    public async Task<IActionResult> GetBuyerStats(CancellationToken cancellationToken)
    {
      try
      {
        var contactId = User.ContactId().Value;
        var customerId = User.CustomerId().Value;
        var stats = await _dashboardService.GetContactStats(User.HasRole(UserRoleEnum.Seller), customerId, contactId, cancellationToken);
        return Ok(stats);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);

      }
    }

    //[ApiKey]
    [HttpGet]
    [AllowAnonymous]
    [Route("updateAdminStats")]
    public async Task<IActionResult> UpdateAdminDashboardStats(CancellationToken cancellationToken)
    {
      try
      {
        await _dashboardService.UpdateAdminDashboardStats(cancellationToken);
        
        return Ok("Admin dashboard stats updated");
      } 
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpGet]
    [Authorize]
    [Route("getAdminStats")]
    public async Task<IActionResult> GetAdminStats([FromQuery] Guid? assigneeId, CancellationToken cancellationToken)
    {
      try
      {
        var stats = await _dashboardService.GetAdminDashboardStats(assigneeId, cancellationToken);
        return Ok(stats);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
