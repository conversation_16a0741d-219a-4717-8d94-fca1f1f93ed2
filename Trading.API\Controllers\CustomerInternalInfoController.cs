using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Admin;
using Trading.API.Data.Models;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/customer")]
  [ApiController]
  [Authorize]
  public class CustomerInternalInfoController : ControllerBase
  {
    private readonly ICustomerInternalInfoService _customerInternalInfoService;
    private readonly IMapper _mapper;

    public CustomerInternalInfoController(
      ICustomerInternalInfoService customerInternalInfoService,
      IMapper mapper)
    {
      _customerInternalInfoService = customerInternalInfoService;
      _mapper = mapper;
    }



    // GET: api/Customers
    [HttpPut("{customerId}/setPending")]
    [AllowAnonymous]
    public async Task<IActionResult> SetIdPending(Guid customerId, CancellationToken cancellationToken)
    {
      if (User.CustomerId() != customerId && !User.IsAdmin())
      {
        return Forbid();
      }

      await _customerInternalInfoService.SetIdPending(customerId, cancellationToken);

      return Ok();
    }

    // GET: api/Customers
    [HttpPut("{customerId}/removePending")]
    [AllowAnonymous]
    public async Task<IActionResult> removeIdPending(Guid customerId, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin())
      {
        return Forbid();
      }

      await _customerInternalInfoService.RemoveIdPending(customerId, cancellationToken);

      return Ok();
    }

    // GET: api/Customers
    [HttpPut("{customerId}/assignedTo")]
    [Authorize(Roles = "ADMIN")]
    public async Task<IActionResult> SetAssignedTo(Guid customerId, [FromBody] CustomerAssignedToDTO dto, CancellationToken cancellationToken)
    {
      if (User.CustomerId() != customerId && !User.IsAdmin())
      {
        return Forbid();
      }

      dto.AssignedBy = User.ContactId();

      await _customerInternalInfoService.SetAssignedTo(customerId, dto, cancellationToken);

      return Ok();
    }

    // GET: api/Customers
    [HttpPatch("{customerId}/internalInfo")]
    [Authorize(Roles = "ADMIN")]
    public async Task<IActionResult> PatchCustomer(Guid customerId, [FromBody] JsonPatchDocument<CustomerInternalInfo> patch, CancellationToken cancellationToken)
    {
      if (User.CustomerId() != customerId && !User.IsAdmin())
      {
        return Forbid();
      }

      await _customerInternalInfoService.PatchUsingCustomerId(customerId, patch, cancellationToken);

      return Ok();
    }


    [HttpGet]
    [Route("id-pending-approval")]
    public async Task<IActionResult> GetIdPendingApproval(CancellationToken cancellationToken)
    {
      var res = await _customerInternalInfoService.GetIdPendingApproval(cancellationToken);
      return Ok(res);
    }
  }
}