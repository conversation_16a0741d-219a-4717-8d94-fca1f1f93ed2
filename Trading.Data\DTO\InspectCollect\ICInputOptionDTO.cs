﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICInputOptionDTO : BaseModelEntityDTO
  {
    public string Label { get; set; }
    public string Value { get; set; }
    public Guid? NextContainerId { get; set; }
    public ICContainerDTO NextContainer { get; set; }
    public uint? Position { get; set; }
  }

  public class ICInputOptionSearchDTO : BaseSearchDTO
  {
    public ICInputOptionSearchFilters Filters { get; set; } = new ICInputOptionSearchFilters();
  }

  public class ICInputOptionSearchFilters : BaseFilter
  {
    public string Label { get; set; }
    public Guid? ICInputId { get; set; }
  }

  public class ICInputOptionCreateDTO
  {
    public Guid ICInputId { get; set; }
    public string? Label { get; set; }
    public string? Value { get; set; }
  }
}
