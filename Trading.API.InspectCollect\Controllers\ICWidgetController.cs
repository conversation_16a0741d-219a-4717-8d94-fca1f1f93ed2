using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.Extensions;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/widget")]
  [ApiController]
  [AllowAnonymous]
  [Authorize(Policy = "RequireInspectCollect")]
  public class ICWidgetController : ControllerBase
  {
    private readonly ICWidgetInterface _icWidgetService;

    public ICWidgetController(ICWidgetInterface serviceInterface)
    {
      _icWidgetService = serviceInterface;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icWidgetService.Get(id, null, cancellationToken);
      return Ok(res);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icWidgetService.Delete(id);
      return Ok(res);
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Post([FromBody]ICWidgetCreateDTO dto, CancellationToken cancellationToken)
    {
      var res = await _icWidgetService.Create(dto);
      return Ok(res);
    }

    [HttpGet]
    [Route("/api/inspect-collect/widgets")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICWidgetSearchDTO>(query);

      var icContainerGroupId = User.ICContainerGroupId();

      if (icContainerGroupId != dto.Filters.ICContainerGroupId && !User.IsAdmin())
      {
        return Forbid();
      }

      var res = await _icWidgetService.Search(dto, cancellationToken);
      return Ok(res);
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<ICWidget> dto)
    {
      var response = await _icWidgetService.Patch(id, dto);
      return Ok(response);
    }
  }
}