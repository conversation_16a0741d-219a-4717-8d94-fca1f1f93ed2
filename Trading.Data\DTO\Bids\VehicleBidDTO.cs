﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO
{
  public class VehicleBidDTO 
  {
    public Guid AdvertId { get; set; }
    public string VRM { get; set; }
    public string PrimaryImageURL { get; set; }
    public string Make { get; set; }
    public string Model { get; set; }
    public string Deriv { get; set; }
    public uint? Odometer { get; set; }
    public List<BidDTO> Bids { get; set; }
    public List<OfferDTO> Offers { get; set; }
  }
  public class VehicleBidDTO_Public
  {
    public Guid AdvertId { get; set; }
    public string VRM { get; set; }
    public string PrimaryImageURL { get; set; }
    public string Make { get; set; }
    public string Model { get; set; }
    public string Deriv { get; set; }
    public uint? Odometer { get; set; }

    // QUESTION: SHOULD THIS BE BID_DTO_PUBLIC TO HIDE ADVERT RESERVE ?
    public List<BidDTO_Public> Bids { get; set; }
    public List<OfferDTO> Offers { get; set; }
  }
}
