﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.LeadCRM;
using Trading.API.Data.DTO.Prospects;
using Trading.API.Data.DTO.Search.Prospects;
using Trading.API.Data.Models.Prospects;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Remarq.Controllers.Prospects
{
  [Route("api/brokerage")]
  [ApiController]
  [Authorize]
  public class BrokerageController : ControllerBase
  {
    private readonly IBrokerageService _brokerageService;

    public BrokerageController(IBrokerageService brokerageService)
    {
      _brokerageService = brokerageService;
    }

    [HttpGet]
    [Authorize(Roles = "ADMIN")]
    public async Task<IActionResult> GetBrokerages([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<BrokerageSearchDTO>(query);
      var res = await _brokerageService.Search(dto, cancellationToken);
      return Ok(res);
    }

    [HttpGet("/api/listing/{advertId}/brokerage")]
    [Authorize(Roles = "ADMIN")]
    public async Task<IActionResult> GetAdvertBrokerage(Guid advertId, [FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = new BrokerageSearchDTO();

      if (!String.IsNullOrEmpty(query))
      {
        dto = JsonConvert.DeserializeObject<BrokerageSearchDTO>(query);
      }

      var res = await _brokerageService.GetBrokerageForAdvert(advertId, dto, cancellationToken);

      return Ok(res);
    }

    [HttpPatch]
    [Route("{id}")]
    [Authorize(Roles = "ADMIN")]
    public async Task<IActionResult> PatchBrokerage([FromBody] JsonPatchDocument<Brokerage> patch, Guid id, CancellationToken ct)
    {
      var result = await _brokerageService.Patch(id, patch, ct);
      return Ok(result);
    }

    [HttpGet]
    [Route("{id}")]
    [Authorize(Roles = "ADMIN")]
    public async Task<IActionResult> Get(Guid id, [FromQuery] string query, CancellationToken ct)
    {
      var dto = new BrokerageSearchDTO() { };

      if (!string.IsNullOrEmpty(query))
      {
        dto = JsonConvert.DeserializeObject<BrokerageSearchDTO>(query);
      }

      dto.Filters.Id = id;

      var result = await _brokerageService.Get(id, dto, ct);
      return Ok(result);
    }

    [HttpPost]
    [Route("")]
    [Authorize(Roles = "ADMIN")]
    public async Task<IActionResult> CreateBrokerage([FromBody] BrokerageDTO dto, CancellationToken ct)
    {
      var result = await _brokerageService.Create(dto, ct);
      return Ok(result);
    }
  }
}
