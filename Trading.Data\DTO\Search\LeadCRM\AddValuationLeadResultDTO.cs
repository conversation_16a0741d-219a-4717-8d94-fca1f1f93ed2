﻿using Trading.API.Data.DTO.LeadCRM;

namespace Trading.API.Data.DTO.Search.LeadCRM
{
  public class AddValuationLeadResult {
    public ValidatedResultDTO<LeadContactDTO> LeadContactResult { get; set; }
    public ValidatedResultDTO<LeadVehicleDTO> LeadVehicleResult { get; set; }
    public ValidatedResultDTO<LeadDTO> LeadResult { get; set; }
    public ValidatedResultDTO<LeadCustomerDTO> LeadCustomerResult { get; set; }
    public ValidatedResultDTO<LeadContactLinkDTO> LeadContactLinkResult { get; set; }
  }
}
