﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.Search.LeadCRM
{
  public class LeadNoteSearchDTO : BaseSearchDTO
  {
    public LeadNoteFilters Filters { get; set; } = new LeadNoteFilters() { };
  }

  public class LeadNoteFilters : BaseFilterGuid
  {
    public Guid? LeadId { get; set; }
    public Guid? CreatedByContactId { get; set; }
    public Guid? CreatedForContactId { get; set; }
    public Guid? CreatedForCustomerId { get; set; }
    public Guid? ContactId { get; set; }
    public bool? RemindersOnly { get; set; }
    public string QueueType { get; set; }

  }
}
