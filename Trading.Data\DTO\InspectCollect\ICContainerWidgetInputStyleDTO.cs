﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICContainerWidgetInputStyleDTO : BaseModelEntityDTO
  {
    public Guid ICContainerWidgetStyleId { get; set; }
    public uint? Position { get; set; }
    public ICStyleDTO ICStyle { get; set; }
    public Guid? ICStyleId { get; set; }
  }


  public class ICContainerWidgetInputStyleSearchDTO : BaseSearchDTO
  {
    public ICContainerWidgetInputStyleSearchFilters Filters { get; set; } = new ICContainerWidgetInputStyleSearchFilters();
  }

  public class ICContainerWidgetInputStyleSearchFilters : BaseFilter
  {
    public Guid? ICContainerWidgetInputId { get; set; }
  }

  public class ICContainerWidgetInputStyleCreateDTO
  {
    public Guid ICContainerWidgetInputId { get; set; }
    public uint? Position { get; set; }
  }
}
