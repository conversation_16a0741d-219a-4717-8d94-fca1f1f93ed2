using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.API.Helpers;
using Trading.Services.Extensions;
using Trading.Services.ExternalDTO;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/sale")]
  [ApiController]
  public class SaleController : ControllerBase
  {
    private readonly ISaleService _saleService;
    private readonly IMapper _mapper;

    public SaleController(ISaleService sService, IMapper mapper)
    {
      _saleService = sService;
      _mapper = mapper;
    }

    [HttpGet]
    [Route("salesByVRM")]
    public async Task<IActionResult> GetSalesByVRM([FromQuery] string vrm, CancellationToken cancellationToken)
    {

      try
      {
        var salesDTOs = await _saleService.GetSalesByVehicleVRM(vrm, cancellationToken);
        return Ok(salesDTOs);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{saleId}/start")]
    public async Task<IActionResult> SaleStart(Guid saleId, CancellationToken cancellationToken)
    {
      if (! User.IsInRole("AUCTION_ADMIN"))
      {
        return NotFound();
      }

      try
      {
        var salesDTOs = await _saleService.SaleStart(saleId, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{saleId}/skipLot/{advertId}")]
    public async Task<IActionResult> SkipLot(Guid saleId, Guid advertId, CancellationToken cancellationToken)
    {
      if (! User.IsInRole("AUCTION_ADMIN"))
      {
        return NotFound();
      }

      try
      {
        var salesDTOs = await _saleService.GoToNextLot(saleId, advertId, true, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpGet]
    [Route("{saleId}/end")]
    public async Task<IActionResult> SaleEnd(Guid saleId, CancellationToken cancellationToken)
    {
      if (! User.IsInRole("AUCTION_ADMIN"))
      {
        return NotFound();
      }

      try
      {
        var salesDTOs = await _saleService.SaleEnd(saleId, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
    [HttpGet]
    [Route("{saleId}/resetLotEndDates")]
    public async Task<IActionResult> resetLotEndDates(Guid saleId, CancellationToken cancellationToken)
    {
      if (! User.IsInRole("AUCTION_ADMIN"))
      {
        return NotFound();
      }

      try
      {
        var salesDTOs = await _saleService.ResetLotEndDates(saleId, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{saleId}/pause")]
    public async Task<IActionResult> SalePause(Guid saleId, CancellationToken cancellationToken)
    {
      if (! User.IsInRole("AUCTION_ADMIN"))
      {
        return NotFound();
      }

      try
      {
        var salesDTOs = await _saleService.SalePause(saleId, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{saleId}/currentLot/{advertId}")]
    public async Task<IActionResult> SetCurrentLot(Guid saleId, Guid advertId, CancellationToken cancellationToken)
    {
      if (! User.IsInRole("AUCTION_ADMIN"))
      {
        return NotFound();
      }

      try
      {
        var salesDTOs = await _saleService.SetCurrentLot(saleId, advertId, cancellationToken);

        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/sales")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      SaleSearchDTO searchDTO = new SaleSearchDTO();

      if (query != null)
      {
        searchDTO = JsonConvert.DeserializeObject<SaleSearchDTO>(query);
      }

      try
      {
        var response = await _saleService.Search(searchDTO, cancellationToken);
        return Ok(response);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/sale/{id}/lot-count")]
    [Authorize]
    public async Task<IActionResult> GetSaleLotCount(Guid id, CancellationToken cancellationToken)
    {
      try
      {
        var response = await _saleService.GetSaleLotCount(id, cancellationToken);
        return Ok(response);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }



    [HttpGet]
    [Route("{id}")]
    public async Task<IActionResult> GetSale(Guid id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      var searchDTO = new SaleSearchDTO() { LockRecord = false };

      if (query != null)
      {
        searchDTO = JsonConvert.DeserializeObject<SaleSearchDTO>(query);
      }

      searchDTO.Filters.Id = id;

      try
      {

        var saleSearchResult = await _saleService.Search(searchDTO, cancellationToken);

        return Ok(saleSearchResult);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpPost]
    [Route("setLots")]
    [Authorize]
    public async Task<IActionResult> SetSaleLots([FromBody] IEnumerable<SetSaleLotDTO> dto, CancellationToken cancellationToken)
    {

      try
      {
        await _saleService.SetSaleLots(dto, cancellationToken);

        return Ok(dto);

      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("")]
    [Authorize]
    public async Task<IActionResult> CreateSale([FromBody] SaleDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        var newSaleDTO = await _saleService.Create(dto, cancellationToken);

        return Ok(newSaleDTO);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> PatchSale(Guid id, [FromBody] JsonPatchDocument<Sale> patch, CancellationToken cancellationToken)
    {
      if (User.IsAdmin())
      {
        try
        {
          var saleDTO = await _saleService.Patch(id, patch, cancellationToken);

          return Ok(saleDTO);
        }
        catch (Exception ex)
        {
          return BadRequest(ex);
        }
      }
      else
        return Unauthorized("Only administrators can perform this function");
    }


    [HttpGet]
    [Route("{id}/attendees")]
    [Authorize]
    public async Task<IActionResult> GetSaleAttendees(Guid id, CancellationToken cancellationToken)
    {
      try
      {
        var response = await _saleService.GetSaleAttendees(id, cancellationToken);
        return Ok(response);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpPost]
    [Route("{id}/addAttendee")]
    [Authorize]
    public async Task<IActionResult> AddAttendee(Guid id, [FromBody]SaleAttendeeDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        dto.SaleId = id;

        var response = await _saleService.AddSaleAttendee(dto, cancellationToken);
        return Ok(response);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpDelete]
    [Route("attendees/{id}")]
    [Authorize]
    public async Task<IActionResult> DeleteAttendee(uint id, CancellationToken cancellationToken)
    {
      try
      {
        await _saleService.RemoveAttendee(id, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
