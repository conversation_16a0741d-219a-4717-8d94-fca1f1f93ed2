﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO
{
  public class DerivSearchDTO : BaseSearchDTO
  {
    public DerivFilters Filters { get; set; } = new DerivFilters() { };
  }

  public class DerivFilters : BaseFilterInt
  {
    public uint? modelId { get; set; }
    public string derivName { get; set; }
  }
}