using AutoMapper;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.API.Data.Models.ContactModels;
using Trading.API.Filters;
using Trading.API.Helpers;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/contact")]
  [ApiController]
  [Authorize]
  public class ContactController : ControllerBase
  {
    private readonly IContactService _contactService;
    private readonly IUserService _userService;
    private readonly ICognitoManagementService _cognitoService;
    private readonly IContactAttribService _contactAttribService;

    public ContactController(
      IContactService cService,
      IUserService userService,
      ICognitoManagementService cognitoService,
      IContactAttribService contactAttribService,
      IMapper mapper)
    {
      _contactService = cService;
      _cognitoService = cognitoService;
      _userService = userService;
      _contactAttribService = contactAttribService;
    }

    public class RolePutDTO
    {
      public string[] roles { get; set; }
    }

    [HttpGet("contactEmailExists/{email}")]
    public async Task<IActionResult> GetContactByEmail(string email, Guid customerId, CancellationToken cancellationToken)
    {
      var response = await _contactService.CustomerContactEmailExists(email, customerId, cancellationToken);
      return Ok(response);
    }

    [HttpGet]
    [Route("fromCognitoEmail")]
    public async Task<IActionResult> fromCognitoEmail(CancellationToken cancellationToken)
    {
      var user = await _cognitoService.FindUserByEmail(User.Email());

      return Ok(user);
    }

    

    [HttpPost]
    [Route("createContactUsingCognito")]
    [AllowAnonymous]
    [ApiExplorerSettings(IgnoreApi = true)] // won't show in swagger doc 
    public async Task<IActionResult> createContactUsingCognito(CancellationToken cancellationToken)
    {
      var contactDTO = new ContactDTO()
      {
        Email = User.Email(),
        ContactName = User.ContactName(),
        Customer = null
      };

      if (!String.IsNullOrEmpty(contactDTO.Email) && !String.IsNullOrEmpty(contactDTO.ContactName))
      {
        ContactDTO contact = await _contactService.Create(contactDTO, cancellationToken);

        return Ok(contact);
      }

      return Forbid();
    }


    [HttpGet]
    [Route("/api/clear-impersonation")]
    [AllowAnonymous]
    [ApiExplorerSettings(IgnoreApi = true)] // won't show in swagger doc 
    public async Task<IActionResult> ClearImpersonationRecords(CancellationToken cancellationToken)
    {
      
      var res = await _contactAttribService.ClearImpersonationRecords(cancellationToken);

      if (res)
      {
        return Ok("Cleared");
      } 
      else
      {
        return BadRequest("Unable to clear");
      }
    }


    [HttpPost]
    [Route("invite")]
    public async Task<IActionResult> inviteContact([FromBody] InviteContactDTO dto, CancellationToken cancellationToken)
    {
      dto.Comment = dto.Comment ?? "";

      var email = User.Email();
      var contactId = User.ContactId().Value;
      var customerId = User.CustomerId().Value;
      var inviter = User.ContactName();

      dto.Inviter = inviter;

      var dtoRoles = dto.Roles;
      var userRoles = User.Roles();

      var intersect = dtoRoles.Intersect(userRoles);

      var contact = await _contactService.InviteContact(
        dto,
        email,
        customerId,
        contactId,
        cancellationToken);

      return Ok(contact);
    }

    [HttpGet]
    [Route("invites/{id}/resend")]
    public async Task<IActionResult> ResendInvite(Guid id, CancellationToken cancellationToken)
    {
      if (id == Guid.Empty) { return BadRequest(); }

      await _contactService.ResendInvite(id, cancellationToken);

      return Ok();
    }

    [HttpGet]
    [Route("{id}/update-invitation")]
    public async Task<IActionResult> UpdateContactInvitation(Guid id, CancellationToken cancellationToken)
    {
      var res = await _contactService.UpdateContactInvitation(id);

      return Ok(res);
    }

    [HttpPatch]
    [Route("initContactCustomer")]
    public async Task<IActionResult> InitContactCustomer([FromBody] JsonPatchDocument<Contact> patch, CancellationToken cancellationToken)
    {
      try
      {
        await _contactService.InitContactCustomer(User.Email(), patch, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("initContactCustomerAdmin")]
    public async Task<IActionResult> InitContactCustomerAdmin([FromBody] CreateCognitoUserDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        var patch = new JsonPatchDocument<Contact>();

        patch.Add(x => x.StatusId, (uint) StatusEnum.Active);
        patch.Add(x => x.ContactName, dto.ContactName);
        patch.Add(x => x.Phone1, dto.Phone);
        patch.Add(x => x.Email, dto.Email);
        patch.Add(x => x.IsPrimary, true);
        patch.Add(x => x.Customer, new Customer()
        {
          CustomerName = dto.Company,
          IsBuyer = true,
          IsSeller = true,
          StatusId = (int) StatusEnum.Pending
        });

        var response = await _contactService.InitContactCustomer(dto.Email, patch, cancellationToken);
        return Ok(response);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("{contactId}/notifyLogin")]
    public async Task<IActionResult> notifyLogin(Guid contactId, CancellationToken cancellationToken)
    {
      if (contactId == User.ContactId() || User.IsAdmin())
      {
        await _contactService.NotifyLogin(contactId, cancellationToken);
      }

      return Ok();
    }

    [HttpGet]
    [Route("{contactId}/notifyLogout")]
    public async Task<IActionResult> notifyLogout(Guid contactId, CancellationToken cancellationToken)
    {
      if (contactId == User.ContactId() || User.IsAdmin())
      {
        await _contactService.NotifyLogout(contactId, cancellationToken);
      }

      return Ok();
    }

    [HttpGet]
    [Route("impersonate/{email}")]
    public async Task<IActionResult> impersonate(string email, CancellationToken cancellationToken)
    {
      //if (User.IsAdmin())
      {
        await _userService.ImpersonateUser((Guid)User.ContactId(), email, cancellationToken);
      }

      return Ok();
    }

    [HttpGet]
    [Route("endImpersonate")]
    public async Task<IActionResult> endImpersonate(CancellationToken cancellationToken)
    {
      await _userService.EndImpersonate((Guid)User.Impersonator(), cancellationToken);

      return Ok();
    }



    [HttpPut]
    [Route("{contactId}/roles")]
    public async Task<IActionResult> updateContactRoles(Guid contactId, [FromBody] RolePutDTO putDTO, CancellationToken cancellationToken)
    {
      if (contactId == User.ContactId() || User.IsAdmin())
      {
        await _contactService.UpdateContactRolesByName(User.Roles().ToList(), contactId, putDTO.roles, cancellationToken);
      }

      return Ok();
    }

    [HttpPatch]
    [Route("{contactId}")]
    public async Task<IActionResult> patchContact(Guid? contactId, [FromBody] JsonPatchDocument<Contact> patch, CancellationToken cancellationToken)
    {
      // TODO: If the contact is in the list of our contacts

      if (!contactId.HasValue || contactId == Guid.Empty)
      {
        contactId = User.ContactId();
      }

      if (contactId == User.ContactId() || User.IsAdmin())
      {
        try
        {
          return Ok(await _contactService.Patch((Guid)contactId, patch, cancellationToken));
        }
        catch (Exception ex)
        {
          return ex.ParseError();
        }
      }

      return Forbid();
    }

    [HttpDelete]
    [Route("/api/customer/{customerId}/contact/{contactId}")]
    public async Task<IActionResult> deleteContact(Guid customerId, Guid contactId, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin())
      {
        return Ok();

      }
      // We must be admin or the manager of the customer, and wanting to delete someone who isn't us
      if (User.IsAdmin() || (User.IsInRole("MANAGER") && customerId == User.CustomerId()) || contactId == User.ContactId())
      {
        try
        {
          return Ok(await _contactService.Delete(contactId, customerId, cancellationToken));
        }
        catch (Exception ex)
        {
          return ex.ParseError();
        }
      }

      return Forbid();
    }

    [HttpDelete]
    [Route("/api/contact/{contactId}/unverified")]
    public async Task<IActionResult> deleteUnverifiedContact(Guid contactId, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin())
      {
        return Ok();

      }
      // We must be admin or the manager of the customer, and wanting to delete someone who isn't us
      if (User.IsAdmin())
      {
        try
        {
          return Ok(await _contactService.Delete(contactId, null, cancellationToken));
        }
        catch (Exception ex)
        {
          return ex.ParseError();
        }
      }

      return Forbid();
    }

    [HttpGet]
    [ApiExplorerSettings(IgnoreApi = true)] // Don't make this endpoint visible to Swagger
    [Authorize(Roles = "ADMIN, POWER_USER")]
    [OutputCache(Duration = 60, PolicyName = "ConditionalCacheWithAuthPolicy")] // incicate policy name
    [Route("/api/contacts/internal")]
    public async Task<IActionResult> GetInternalContacts(CancellationToken cancellationToken, [FromQuery] string query)
    {
      var contactSearchDTO = JsonConvert.DeserializeObject<ContactSearchDTO>(query);

      contactSearchDTO.Filters.HasRole = "ADMIN";

      return Ok(await _contactService.GetContactsByRole(contactSearchDTO, cancellationToken));
    }

    [HttpGet]
    [ApiExplorerSettings(IgnoreApi = true)] // Don't make this endpoint visible to Swagger
    [Authorize(Roles = "ADMIN, POWER_USER")]
    [Route("/api/contacts/search")]
    public async Task<IActionResult> SearchContacts(CancellationToken cancellationToken, [FromQuery] string query)
    {
      var contactSearchDTO = JsonConvert.DeserializeObject<ContactSearchDTO>(query);
      return Ok(await _contactService.Search(contactSearchDTO, cancellationToken));
    }

    [HttpPatch]
    [ApiExplorerSettings(IgnoreApi = true)] // Don't make this endpoint visible to Swagger
    [Authorize(Roles = "GOD")]
    [Route("/api/contact/{id}/internalInfo")]
    public async Task<IActionResult> PatchContactInternal(Guid id, JsonPatchDocument<ContactInternalInfo> patch, CancellationToken cancellationToken, [FromQuery] string query)
    {
      return Ok(await _contactService.PatchInternalInfo(id, patch, cancellationToken));
    }

    [HttpGet]
    [Route("/api/customer/{id}/contacts")]
    public async Task<IActionResult> GetCustomerContacts(Guid id, CancellationToken cancellationToken, [FromQuery] string query)
    {
      if (id == User.CustomerId() || User.IsAdmin())
      {
        try
        {
          ContactSearchDTO dto = new ContactSearchDTO() { };

          if (!String.IsNullOrEmpty(query)) {
            dto = JsonConvert.DeserializeObject<ContactSearchDTO>(query);
          }

          dto.Filters.CustomerId = id;

          var response = await _contactService.Search(dto, cancellationToken);
          return Ok(response);
        }
        catch (Exception ex)
        {
          return BadRequest(ex);
        }
      }
      return Forbid();
    }

    [HttpGet]
    [Route("{contactId}/notificationPrefs")]
    public async Task<IActionResult> GetNotificationPrefs(Guid contactId, CancellationToken cancellationToken)
    {
      try
      {
        if (contactId == User.ContactId() || User.IsAdmin())
        {
          var response = await _contactService.GetNotificationPreferences(contactId, cancellationToken);
          return Ok(response);
        }

        return Forbid();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("{contactId}/notificationPref")]
    public async Task<IActionResult> GetNotificationPrefs(Guid contactId, [FromBody] ContactAttribDTO contactAttribDTO, CancellationToken cancellationToken)
    {
      try
      {
        if (contactId == User.ContactId() || User.IsAdmin())
        {
          var response = await _contactService.SaveNotificationPref(contactId, contactAttribDTO, cancellationToken);
          return Ok(response);
        }

        return Forbid();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpGet]
    [Route("/api/contact/{contactId}/attribs")] // NOTE: This will fetch ContactID from the JWT/Cookie
    public async Task<IActionResult> GetContactAttribs(Guid contactId, [FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        if (contactId == User.ContactId() || User.IsAdmin())
        {
          var dto = JsonConvert.DeserializeObject<ContactAttribSearchDTO>(query);
          dto.Filters.ContactId = contactId;
          var response = await _contactAttribService.Search(dto, cancellationToken);
          return Ok(response);
        }
        return Forbid();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPut]
    [Route("/api/contact/{contactId}/attrib/{attribId}")] // NOTE: This will fetch ContactID from the JWT/Cookie
    public async Task<IActionResult> PutContactAttrib(Guid contactId, AttribEnum attribId, [FromBody] ContactAttribDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        if (contactId == User.ContactId() || User.IsAdmin())
        {
          await _contactAttribService.SetContactAttrib(contactId, attribId, dto, cancellationToken);
          // PUT Contact Attrib
        }
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpPut]
    [Route("/api/customer/{customerId}/contact/{contactId}/suspend")]
    public async Task<IActionResult> suspendContact(Guid customerId, Guid contactId, CancellationToken cancellationToken)
    {
      if (!User.IsAdmin())
      {
        return Ok();

      }
      // We must be admin or the manager of the customer, and wanting to suspend someone who isn't us
      if (User.IsAdmin() || (User.IsInRole("MANAGER") && customerId == User.CustomerId()) || contactId == User.ContactId())
      {
        try
        {
          return Ok(await _contactService.SuspendContact(customerId, contactId, cancellationToken));
        }
        catch (Exception ex)
        {
          return ex.ParseError();
        }
      }

      return Forbid();
    }

    [HttpGet]
    [Route("invite/{id}")]
    [AllowAnonymous]
    public async Task<IActionResult> GetInvite(Guid id, CancellationToken cancellationToken)
    {
      var res = await _contactService.GetInviteDetails(id, cancellationToken);
      return Ok(res);
    }

    [HttpPost]
    [ApiExplorerSettings(IgnoreApi = true)] // Don't make this endpoint visible to Swagger
    [Authorize(Roles = "ADMIN")]
    [Route("/api/contacts/createCognitoUser")]
    public async Task<IActionResult> AdminContactCreator(CancellationToken cancellationToken, [FromBody] CreateCognitoUserDTO dto)
    {
      return Ok(await _cognitoService.CreateUserAsync(dto));
    }

    [HttpPost]
    [ApiExplorerSettings(IgnoreApi = true)] // Don't make this endpoint visible to Swagger
    [Authorize(Roles = "ADMIN")]
    [Route("")]
    public async Task<IActionResult> Create(CancellationToken cancellationToken, CreateCognitoUserDTO dto)
    {
      var createDTO = new ContactDTO() { 
        Email = dto.Email,
        ContactName = dto.ContactName,
        // Optionally do others
      };

      var ok = await _contactService.Create(createDTO, CancellationToken.None);

      dto.ContactId = ok.Id;

      return Ok(dto);
    }

    /* DANGEROUS
    [HttpGet]
    [Route("getAll")]
    public async Task<IActionResult> GetAllContacts(CancellationToken cancellationToken)
    {
      if (!User.IsAdmin())
      {
        return Forbid();
      }

      try
      {
        var result = await _contactService.GetContacts(cancellationToken);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
    */




  }
}

