﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO
{
  public class ChangeBidIncrementDTO
  {
    public Guid customerId;
    public uint currentBidIncrement;
    public bool increase;
  }
  public class AdvertSearchDTO : BaseSearchDTO
  {
    public AdvertSearchFiltersDTO Filters { get; set; } = new AdvertSearchFiltersDTO() { };
    public bool IsAdmin { get; set; }
  }

  public class AdvertSearchFiltersDTO : BaseFilterGuid
  {
    public string VRM { get; set; } // use to check for existing adverts for a vehicle 
    public string Keywords { get; set; }
    public Guid? CustomerId { get; set; }
    public Guid? ContactId { get; set; }
    public Guid? AddressId { get; set; }
    public IEnumerable<uint> AgeRange { get; set; }

    public IEnumerable<uint> VehicleTypeIds { get; set; }
    public IEnumerable<uint> MakeIds { get; set; }
    public IEnumerable<Guid> SaleIds { get; set; }
    public Guid? SaleId { get; set; }
    public IEnumerable<uint> ModelIds { get; set; }
    public IEnumerable<uint> DerivativeIds { get; set; }
    public IEnumerable<uint> FuelTypeIds { get; set; }
    public IEnumerable<uint> TransmissionTypeIds { get; set; }
    public IEnumerable<uint> BodyTypeIds { get; set; }
    //public IEnumerable<Guid> CustomerIds { get; set; }
    public IEnumerable<uint> CustomerIds { get; set; }
    public IEnumerable<uint> AdvertStatuses { get; set; }
    public AdvertStatusEnum AdvertStatus { get; set; }
    public IEnumerable<uint> SoldStatuses { get; set; }
    public SoldStatusEnum SoldStatus { get; set; }
    public IEnumerable<uint> PlateIds { get; set; }
    public IEnumerable<uint> VatStatusIds { get; set; }
    public IEnumerable<uint> MileageIds { get; set; }
    public IEnumerable<uint> CapacityIds { get; set; }
    public IEnumerable<uint> Doors { get; set; }
    public IEnumerable<uint> ColourIds { get; set; }
    public IEnumerable<uint> PriceIds { get; set; }
    public uint? LotSeq { get; set; }
    public uint? SaleTypeId { get; set; }

    public IEnumerable<uint> VendorIds { get; set; }
    public int? Runner { get; set; } // -1 = All, 0 = non-runner, 1 = runner
    public int? CatStatus { get; set; } // -1 = All, 0 = no cat status, 1 = cat status

    public AdvertSearchSortingEnum? SearchSortingEnum { get; set; }
    public AdvertSortingFilterEnum? SortingFilterEnum { get; set; }
    public AdvertSearchField CurrentSearchField { get; set; }
    public bool ExcludeBidSummary { get; set; }

    public bool? ExpiringSoon { get; set; }
    public bool? JustExpired { get; set; }
    public bool? Expired { get; set; }
    public Guid? AssignedTo { get; set; }
  }
}
