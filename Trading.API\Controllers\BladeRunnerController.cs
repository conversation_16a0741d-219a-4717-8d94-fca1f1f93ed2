using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Hosting;
using Trading.API.Data.DTO.Twitter;
using Tweetinvi;
using Tweetinvi.Models;

namespace Trading.API.Controllers
{


  [Route("api/[controller]")]
  public class BladeRunnerController : Controller
  {
    private IHostApplicationLifetime ApplicationLifetime { get; set; }

    public BladeRunnerController(IHostApplicationLifetime appLifetime)
    {
      ApplicationLifetime = appLifetime;
    }

    [HttpGet]
    [Route("restart")]
    public async Task<IActionResult> RestartSite()
    {
      ApplicationLifetime.StopApplication();
      return Ok("Done");
    }

    [HttpGet]
    [Route("tweet")]
    public async Task<IActionResult> Tweet()
    {
      var newTweet = new PostTweetRequestDTO() { text = "Hello World" };

      var client = new TwitterClient(
        "7PvbNPqiKehYl4Zaxi7Urdmr1",  // API KEY
        "64nACeuW3HXxYjiMjaAeRC2EBXDmkv0kAeWIFiPTChccdSjDOs", // API KEY SECRET
        "1797331347284860928-OWsJ9KjFdSN9P8iDZ3yIGq7gsFvP1C", // ACCESS TOKEN KEY
        "9g8iIuYQ0KEwksZPhgoHoo7Y1doSLG8wvLOkMAigif5J8" // ACCESS TOKEN SECRET
        );

      var result = await client.Execute.AdvanceRequestAsync(BuildTwitterRequest(newTweet, client));

      return Ok(result.Content);
    }

    private static Action<ITwitterRequest> BuildTwitterRequest(PostTweetRequestDTO newTweet, TwitterClient client)
    {
      return (ITwitterRequest request) => {
        var jsonBody = client.Json.Serialize(newTweet);
        var content = new StringContent(jsonBody, Encoding.UTF8, "application/json");

        request.Query.Url = "https://api.twitter.com/2/tweets";
        request.Query.HttpMethod = Tweetinvi.Models.HttpMethod.POST;
        request.Query.HttpContent = content;
      };
    }
  }
}