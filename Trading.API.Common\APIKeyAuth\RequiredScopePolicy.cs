﻿using Microsoft.AspNetCore.Authorization;
using System;
using System.Threading.Tasks;

namespace Trading.API.Common.APIKeyAuth;

public class RequireScopePolicy : IAuthorizationRequirement
{
  public string Scope { get; }

  public RequireScopePolicy(string scope)
  {
    Scope = scope ?? throw new ArgumentNullException(nameof(scope));
  }
}

public class RequireScopeHandler : AuthorizationHandler<RequireScopePolicy>
{
  protected override Task HandleRequirementAsync(
      AuthorizationHandlerContext context,
      RequireScopePolicy requirement)
  {
    if (context.User.HasClaim(c => c.Type == "scope" && c.Value == requirement.Scope) ||
        context.User.HasClaim(c => c.Type == "scope" && c.Value == "*"))
    {
      context.Succeed(requirement);
    }

    return Task.CompletedTask;
  }
}
