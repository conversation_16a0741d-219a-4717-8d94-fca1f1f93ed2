﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;


namespace Trading.API.Data.DTO
{
  public class VehicleFilters : BaseFilterGuid
  {
    public Guid? VehicleId { get; set; }
    public string vrm { get; set; }
    public string vrmMatches { get; set; }
  }

  public class VehicleSearchDTO: BaseSearchDTO
  {
    public VehicleFilters Filters { get; set; } = new VehicleFilters() { };
  }
}
