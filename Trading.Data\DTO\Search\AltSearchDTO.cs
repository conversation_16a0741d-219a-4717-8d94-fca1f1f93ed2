﻿namespace Trading.API.Data.DTO
{
  public class AltSearchDTO: BaseSearchDTO
  {
    public AltFilters Filters { get; set; } = new AltFilters() { };
  }

  public class AltFilters : BaseFilterInt
  {
    public uint? VehicleTypeId { get; set; } 
    public uint? AltRealId { get; set; } 
    public uint? AltParentId { get; set; } 
    public string AltTable { get; set; } 
    public string AltVal { get; set; } 
  }
}
