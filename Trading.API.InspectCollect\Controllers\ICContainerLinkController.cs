using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/container-widget-link")]
  [ApiController]
  [AllowAnonymous]
  public class ICContainerWidgetLinkController : ControllerBase
  {
    private readonly ICContainerWidgetLinkInterface _icContainerWidgetLinkService;

    public ICContainerWidgetLinkController(ICContainerWidgetLinkInterface serviceInterface)
    {
      _icContainerWidgetLinkService = serviceInterface;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icContainerWidgetLinkService.Get(id, null, cancellationToken);
      return Ok(res);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icContainerWidgetLinkService.Delete(id);
      return Ok(res);
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Post([FromBody] ICContainerWidgetLinkCreateDTO dto, CancellationToken cancellationToken)
    {
      var res = await _icContainerWidgetLinkService.Create(dto);
      return Ok(res);
    }

    [HttpGet]
    [Route("/api/inspect-collect/container-widget-links")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICContainerWidgetLinkSearchDTO>(query);
      var res = await _icContainerWidgetLinkService.Search(dto, cancellationToken);
      return Ok(res);
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<ICContainerWidgetLink> dto)
    {
      var response = await _icContainerWidgetLinkService.Patch(id, dto);
      return Ok(response);
    }
  }
}