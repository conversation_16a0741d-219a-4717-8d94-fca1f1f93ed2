﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums.Valuation;

namespace Trading.API.Data.DTO.Search.Valuation
{
  public class ValuationQuoteSearchDTO : BaseSearchDTO
  {
    public ValuationQuoteFilters Filters { get; set; } = new ValuationQuoteFilters();
  }

  public class ValuationQuoteFilters : BaseFilterGuid
  {
    public Guid? LeadId { get; set; }
    public ValuationQuoteTypeEnum? QuoteType { get; set; }
    public string VRM { get; set; }
  }
}
