﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO
{
  public class ScanStageSearchDTO : BaseSearchDTO
  {
    public bool? NoTracking { get; set; }
    public ScanStageFilters Filters { get; set; } = new ScanStageFilters() { };
  }

  public class ScanStageFilters : BaseFilterInt
  {
    public uint? ScanServiceId { get; set; }
    public uint? ScanStyleId { get; set; }
    public uint? NextScanStageId { get; set; }
  }
}