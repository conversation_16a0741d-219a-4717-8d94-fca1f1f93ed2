﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Helpers;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/lookup")]
  [ApiController]
  public class LookupController : ControllerBase
  {
    private readonly ILookupService _lookupService;
    private readonly IMapper _mapper;

    public LookupController(ILookupService lookupService, IMapper mapper)
    {
      this._lookupService = lookupService;
      this._mapper = mapper;
    }

    [HttpGet]
    [Route("lookupId")]
    public async Task<IActionResult> GetLookupId([FromQuery] LookupDTO lookupDTO, CancellationToken cancellationToken)
    {
      try
      {
        var res = await _lookupService.GetLookupId(lookupDTO, cancellationToken);
        return Ok(res);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

  }
}
