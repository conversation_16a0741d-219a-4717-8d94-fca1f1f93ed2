using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/container-widget-asset")]
  [ApiController]
  [AllowAnonymous]
  public class ICContainerWidgetAssetController : ControllerBase
  {
    private readonly ICContainerWidgetAssetInterface _icContainerWidgetAssetService;

    public ICContainerWidgetAssetController(ICContainerWidgetAssetInterface serviceInterface)
    {
      _icContainerWidgetAssetService = serviceInterface;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(Guid id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      ICContainerWidgetAssetSearchDTO dto = new ICContainerWidgetAssetSearchDTO();

      if (!String.IsNullOrEmpty(query))
      {
        dto = JsonConvert.DeserializeObject<ICContainerWidgetAssetSearchDTO>(query);
      }

      var res = await _icContainerWidgetAssetService.Get(id, dto, cancellationToken);
      return Ok(res);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icContainerWidgetAssetService.Delete(id);
      return Ok(res);
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Post([FromBody] ICContainerWidgetAssetCreateDTO dto, CancellationToken cancellationToken)
    {
      var res = await _icContainerWidgetAssetService.Create(dto);
      return Ok(res);
    }

    [HttpGet]
    [Route("/api/inspect-collect/container-widget-assets")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICContainerWidgetAssetSearchDTO>(query);
      var res = await _icContainerWidgetAssetService.Search(dto, cancellationToken);
      return Ok(res);
    }

    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<ICContainerWidgetAsset> dto)
    {
      var response = await _icContainerWidgetAssetService.Patch(id, dto);
      return Ok(response);
    }
  }
}