using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO;
using Trading.API.Data.Models.DTO;
using Trading.Services.SiteScan.Interfaces;

namespace Trading.API.SiteScan.Controllers
{
  [Route("api/scanVehicle")]
  [ApiController]
  public class ScanVehicleController : ControllerBase
  {
    public IMapper _mapper;
    public IScanVehicleService _scanVehicleService;
    public IScanImageService _scanImageService;

    public ScanVehicleController(
      IScanVehicleService scanVehicleService,
      IScanImageService scanImageService,
      IMapper mapper)
    {
      _mapper = mapper;
      _scanVehicleService = scanVehicleService;
      _scanImageService = scanImageService;
    }


    [HttpGet]
    [Route("")]
    public async Task<IActionResult> ScanVehicleSearch([FromQuery] string query, CancellationToken cancellationToken)
    {
      ScanVehicleSearchDTO xyz = new ScanVehicleSearchDTO();

      if (query != null)
      {
        xyz = JsonConvert.DeserializeObject<ScanVehicleSearchDTO>(query);
      }

      var searchResult = await _scanVehicleService.Search(xyz, cancellationToken);

      return Ok(searchResult);
    }

    [HttpGet]
    [Route("filters/{groupBy}")]
    public async Task<IActionResult> ScanVehicleFilterCount(string groupBy, [FromQuery] string query, CancellationToken cancellationToken)
    {
      ScanVehicleFiltersDTO xyz = new ScanVehicleFiltersDTO();

      if (query != null)
      {
        xyz = JsonConvert.DeserializeObject<ScanVehicleFiltersDTO>(query);
      }

      var searchResult = await _scanVehicleService.UKAutoFilter(groupBy, xyz, cancellationToken);

      return Ok(searchResult);
    }


    [ApiExplorerSettings(IgnoreApi = true)]
    [HttpGet("scanCheck/{vrm}")]
    [Authorize]
    public async Task<IActionResult> scanCheck(string vrm, CancellationToken cancellationToken)
    {
      var response = await _scanVehicleService.GetVehicleDataWithScanCheck(vrm, cancellationToken);

      return Ok(response);
    }

    [ApiExplorerSettings(IgnoreApi = true)]
    [HttpGet("scanImages/{scanVehicleGuid}")]
    [Authorize]
    public async Task<IActionResult> scanImages(Guid scanVehicleGuid, CancellationToken cancellationToken)
    {
      var response = await _scanImageService.Search(new ScanImageSearchDTO() { Filters = { ScanVehicleGuid = scanVehicleGuid } }, cancellationToken);

      return Ok(response);
    }

    [HttpGet]
    [Route("/api/scan/vehicles")]
    [AllowAnonymous]
    public async Task<ActionResult> Vehicles([FromQuery] string query, CancellationToken ct)
    {
      var dto = JsonConvert.DeserializeObject<ScanVehicleSearchDTO>(query);

      var response = await _scanVehicleService.Search(dto, ct);

      return Ok(response);
    }
  }
}