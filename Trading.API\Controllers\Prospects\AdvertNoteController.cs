﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System.Threading;
using Trading.API.Data.DTO.Prospects;
using Trading.Services.Classes;
using Trading.Services.Interfaces;
using System;
using Newtonsoft.Json;
using Microsoft.AspNetCore.JsonPatch;
using Trading.API.Data.Models.Prospects;

namespace Trading.API.Remarq.Controllers.Prospects
{
  [Route("api/listing")]
  [ApiController]
  [Authorize]
  public class AdvertNoteController : ControllerBase
  {
    private readonly IAdvertNoteService _advertNoteService;

    public AdvertNoteController(IAdvertNoteService advertNoteService)
    {
      _advertNoteService = advertNoteService;
    }

    [HttpGet("{id}/notes")]
    [Authorize(Roles = "ADMIN")]
    public async Task<IActionResult> GetNotes(Guid id, string query, CancellationToken cancellationToken)
    {
      var searchDTO = new AdvertNoteSearchDTO();

      if (query.Length > 0)
      {
        searchDTO = JsonConvert.DeserializeObject<AdvertNoteSearchDTO>(query);
      }

      searchDTO.Filters.AdvertId = id;

      var res = await _advertNoteService.Search(searchDTO, cancellationToken);
      return Ok(res);
    }

    [HttpPatch]
    [Route("/api/listing-note/{noteId}")]
    public async Task<IActionResult> Patch([FromBody] JsonPatchDocument<AdvertNote> patch, Guid noteId, CancellationToken cancellationToken)
    {
      try
      {
        var result = await _advertNoteService.Patch(noteId, patch);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost("{id}/note")]
    [Authorize(Roles = "ADMIN")]
    public async Task<IActionResult> AddNote(Guid id, [FromBody] SaveAdvertNoteDTO dto, CancellationToken cancellationToken)
    {
      var res = await _advertNoteService.Create(id, dto);
      return Ok(res);
    }
  }
}
