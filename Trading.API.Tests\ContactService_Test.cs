﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Moq;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Tests.Common;
using Trading.Services;
using Trading.Services.Classes;
using Trading.Services.Interfaces;
using Xunit;

namespace Trading.API.Tests
{
  [Collection("DatabaseCollection")]
  public class ContactService_Test : TestBase
  {
    public DatabaseFixture _fixture;

    public ContactService_Test(DatabaseFixture fixture) { 
      _fixture = fixture;
    }

    private IContactService GetService()
    {
      IEmailService emailService = new Mock<IEmailService>().Object;

      IContactActionService contactActionService = new ContactActionService(_context, base._mapper, _common.UserService, null, null, null);

      IRoleService roleService = new RoleService(_context, _mapper);
      IContactRoleService contactRoleService = new ContactRoleService(roleService, _context, _mapper);

      ICustomerService customerService = new CustomerService(_context, _mapper, emailService, null, contactActionService, _common.UserService);

      ICommsTemplateService templateService = new Mock<ICommsTemplateService>().Object;
      ICustomerNoteService noteService = new CustomerNoteService(_context, _mapper, null, null);
      ICustomerInternalInfoService customerInternalInfoService = new CustomerInternalInfoService(_context, _mapper, noteService, null);

      IContactService contactService = new ContactService(null, _context, contactRoleService, roleService, _common.Configuration, customerService, _mapper, contactActionService, templateService, customerInternalInfoService, _common.WhosWhoService);
            
      return contactService;
    }

    [Fact]
    public async Task RegisterCreateContact()
    {
      var contactService = GetService();

      var email = "<EMAIL>";
      var name = "test-user-name";

      var contactDTO = new ContactDTO()
      {
        Email = email,
        ContactName = name,
        Customer = null
      };
        
      ContactDTO contact = await contactService.Create(contactDTO, CancellationToken.None);

      Assert.True(contact != null && contact.Email == email && contact.ContactName == name);

      // ensure contact is in the db
      var entity = await _context.Contacts.FirstOrDefaultAsync(x => x.Id == contact.Id);
      Assert.True(entity != null && entity.Email == email && entity.ContactName == name);
    }
  }
}
