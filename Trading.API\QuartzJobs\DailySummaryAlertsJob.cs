﻿using Quartz;
using System.Threading;
using System.Threading.Tasks;
using Trading.Services.Interfaces;

namespace Trading.API.Remarq.QuartzJobs
{
  public class DailySummaryAlertsJob : IJob
  {
    private readonly IAdvertSearchService _searchService;
    public DailySummaryAlertsJob(IAdvertSearchService searchService)
    {
      _searchService = searchService;
    }

    public async Task Execute(IJobExecutionContext context)
    {
      await _searchService.ProcessDailyAlerts(new CancellationToken());
    }
  }
}
