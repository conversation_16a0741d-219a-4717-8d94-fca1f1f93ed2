﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.LeadCRM;
using Trading.API.Data.DTO.Prospects;
using Trading.API.Data.DTO.Search.Prospects;
using Trading.API.Data.Models.Prospects;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.API.Remarq.Controllers.Prospects
{
  [Route("api/prospect")]
  [ApiController]
  [Authorize]
  public class ProspectController : ControllerBase
  {
    private readonly IProspectService _prospectService;

    public ProspectController(IProspectService ProspectService)
    {
      _prospectService = ProspectService;
    }

    [HttpPost("/api/brokerage/{brokerageId}/prospect-note")]
    [Authorize(Roles = "ADMIN")]
    public async Task<IActionResult> AddProspectNote(Guid brokerageId, [FromBody] SaveProspectNoteDTO dto, CancellationToken cancellationToken)
    {
      dto.BrokerageId = brokerageId;

      var res = await _prospectService.CreateProspectNote(dto, cancellationToken);

      return Ok(res);
    }

    [HttpPost("/api/listing/{advertId}/prospect-bid")]
    [Authorize(Roles = "ADMIN")]
    public async Task<IActionResult> AddProspectBid(Guid advertId, [FromBody] BidDTO dto, CancellationToken cancellationToken)
    {
      dto.AdvertId = advertId;

      var res = await _prospectService.CreateProspectBid(dto, cancellationToken);

      return Ok(res);
    }

    [HttpGet]
    [Route("/api/listing/{advertId}/prospects")]
    [Authorize(Roles = "ADMIN")]
    public async Task<IActionResult> AdvertProspects(Guid advertId, [FromQuery] string query, CancellationToken cancellationToken)
    {
      var searchDTO = new ProspectSearchDTO();

      if (!string.IsNullOrEmpty(query))
      {
        searchDTO = JsonConvert.DeserializeObject<ProspectSearchDTO>(query);
      }

      searchDTO.Filters.AdvertId = advertId;

      var res = await _prospectService.Search(searchDTO, cancellationToken);
      return Ok(res);
    }

    [HttpPatch]
    [Route("{id}")]
    [Authorize(Roles = "ADMIN")]
    public async Task<IActionResult> PatchProspect([FromBody] JsonPatchDocument<Prospect> patch, Guid id, CancellationToken ct)
    {
      var result = await _prospectService.Patch(id, patch, ct);
      return Ok(result);
    }

    [HttpPost]
    [Route("")]
    [Authorize(Roles = "ADMIN")]
    public async Task<IActionResult> CreateProspect([FromBody] SaveProspectDTO dto, CancellationToken ct)
    {
      var result = await _prospectService.Create(dto, ct);
      return Ok(result);
    }

    [HttpPost]
    [Route("/api/listing/{advertId}/prospect")]
    [Authorize(Roles = "ADMIN")]
    public async Task<IActionResult> CreateForAdvertId(Guid advertId, [FromBody] SaveProspectDTO dto, CancellationToken ct)
    {
      dto.AdvertId = advertId;
      var result = await _prospectService.CreateProspectForAdvert(dto, ct);
      return Ok(result);
    }

    [HttpGet]
    [Route("/api/listing/{advertId}/prospects/suggest")]
    [Authorize(Roles = "ADMIN")]
    public async Task<IActionResult> AssembleProspects(Guid advertId, [FromQuery] string query, CancellationToken ct)
    {
      var dto = new ProspectSearchDTO();

      if (!String.IsNullOrEmpty(query))
      {
        dto = JsonConvert.DeserializeObject<ProspectSearchDTO>(query);
      }

      var result = await _prospectService.AssembleProspects(advertId, dto, ct);
      return Ok(result);
    }

  }
}
