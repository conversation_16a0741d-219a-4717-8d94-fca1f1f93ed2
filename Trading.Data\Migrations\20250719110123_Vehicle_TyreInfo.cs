﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Trading.API.Data.Migrations
{
  /// <inheritdoc />
  public partial class Vehicle_TyreInfo : Migration
  {
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
      migrationBuilder.CreateTable(
          name: "VehicleTyreInfo",
          columns: table => new
          {
            Id = table.Column<uint>(type: "int unsigned", nullable: false)
                  .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
            VehicleId = table.Column<Guid>(type: "binary(16)", nullable: false),
            Position = table.Column<string>(type: "varchar(10)", maxLength: 10, nullable: false)
                  .Annotation("MySql:CharSet", "utf8mb4"),
            Make = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true)
                  .Annotation("MySql:CharSet", "utf8mb4"),
            Condition = table.Column<string>(type: "varchar(20)", maxLength: 20, nullable: true)
                  .Annotation("MySql:CharSet", "utf8mb4"),
            Depth = table.Column<decimal>(type: "decimal(3,1)", nullable: true),
            StatusId = table.Column<uint>(type: "int unsigned", nullable: false),
            Added = table.Column<DateTime>(type: "datetime(6)", nullable: true),
            Updated = table.Column<DateTime>(type: "datetime(6)", nullable: true)
          },
          constraints: table =>
          {
            table.PrimaryKey("PK_VehicleTyreInfo", x => x.Id);
            table.ForeignKey(
                      name: "FK_VehicleTyreInfo_Status_StatusId",
                      column: x => x.StatusId,
                      principalTable: "Status",
                      principalColumn: "Id",
                      onDelete: ReferentialAction.Cascade);
            table.ForeignKey(
                      name: "FK_VehicleTyreInfo_Vehicle_VehicleId",
                      column: x => x.VehicleId,
                      principalTable: "Vehicle",
                      principalColumn: "Id",
                      onDelete: ReferentialAction.Cascade);
          })
          .Annotation("MySql:CharSet", "utf8mb4");

      migrationBuilder.CreateIndex(
          name: "IX_VehicleTyreInfo_Condition",
          table: "VehicleTyreInfo",
          column: "Condition");

      migrationBuilder.CreateIndex(
          name: "IX_VehicleTyreInfo_Make",
          table: "VehicleTyreInfo",
          column: "Make");

      migrationBuilder.CreateIndex(
          name: "IX_VehicleTyreInfo_StatusId",
          table: "VehicleTyreInfo",
          column: "StatusId");

      migrationBuilder.CreateIndex(
          name: "IX_VehicleTyreInfo_VehicleId",
          table: "VehicleTyreInfo",
          column: "VehicleId");

      migrationBuilder.CreateIndex(
          name: "IX_VehicleTyreInfo_VehicleId_Position",
          table: "VehicleTyreInfo",
          columns: new[] { "VehicleId", "Position" },
          unique: true);

      // Migrate existing tyre depth data
      migrationBuilder.Sql(@"
            INSERT INTO VehicleTyreInfo (VehicleId, Position, Depth, Added, Updated, StatusId)
            SELECT 
                Id,
                'NSF',
                TyreDepth_NSF,
                NOW(), NOW(), 1
            FROM Vehicle 
            WHERE TyreDepth_NSF IS NOT NULL
            
            UNION ALL
            
            SELECT 
                Id,
                'OSF',
                TyreDepth_OSF,
                NOW(), NOW(), 1
            FROM Vehicle 
            WHERE TyreDepth_OSF IS NOT NULL
            
            UNION ALL
            
            SELECT 
                Id,
                'NSR',
                TyreDepth_NSR,
                NOW(), NOW(), 1
            FROM Vehicle
            WHERE TyreDepth_NSR IS NOT NULL
            
            UNION ALL
            
            SELECT 
                Id,
                'OSR',
                TyreDepth_OSR,
                NOW(), NOW(), 1
            FROM Vehicle 
            WHERE TyreDepth_OSR IS NOT NULL

            UNION ALL
            
            SELECT 
                Id,
                'SPARE',
                TyreDepth_Spare,
                NOW(), NOW(), 1
            FROM Vehicle 
            WHERE TyreDepth_Spare IS NOT NULL
        ");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
      migrationBuilder.DropTable(
          name: "VehicleTyreInfo");
    }
  }
}
