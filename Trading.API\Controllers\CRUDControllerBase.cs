﻿//using AutoMapper;
//using Microsoft.AspNetCore.Mvc;
//using Newtonsoft.Json;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Threading;
//using System.Threading.Tasks;
//using Trading.API.Data;
//using Trading.API.Data.DTO;
//using Trading.API.Helpers;
//using Trading.Services.Interfaces;

//namespace Trading.API.Controllers
//{
//  public class CRUDControllerBase<T, TEntityDTO> : ControllerBase
//  {
//    protected readonly ICrudService<T, TEntityDTO> _crudService;
//    private readonly IMapper _mapper;

//    public CRUDControllerBase(ICrudService<T, TEntityDTO> crudService, IMapper mapper)
//    {
//      _crudService = crudService;
//      _mapper = mapper;
//    }


//    // GET: api/T
//    [HttpGet]
//    [HttpGet("getAll")]
//    public virtual async Task<IActionResult> GetAll(CancellationToken cancellationToken)
//    {
//      var all = await _crudService.GetAll(cancellationToken);
//      return Ok(all);
//    }

//    [HttpGet("getAllWithIncludes")]
//    public virtual async Task<IActionResult> GetAll([FromQuery] string searchDTO, CancellationToken cancellationToken)
//    {
//      var dto = JsonConvert.DeserializeObject<BaseSearchDTO>(searchDTO);

//      var all = await _crudService.GetAll(dto, cancellationToken);
//      return Ok(all);
//    }

//    // GET: api/T/5
//    [HttpGet("{id}")]
//    public async Task<ActionResult<TEntityDTO>> Get(Guid id, CancellationToken cancellationToken)
//    {
//      var entity = await _crudService.Get(id, cancellationToken);
//      if (entity == null)
//      {
//        return NotFound();
//      }

//      return Ok(entity);
//    }

//    // PUT: api/T
//    // NOTE: PUT SHOULD BE USED FOR UPDATING (for creates use POST)
//    // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
//    [HttpPut]
//    public async Task<IActionResult> Put([FromBody]TEntityDTO dto, CancellationToken cancellationToken)
//    {
//      try
//      {
//        await _crudService.Put(dto, cancellationToken);
//        return Ok();
//      }
//      catch { }

//      return NoContent();
//    }

//    // POST: api/T
//    // NOTE: POST SHOULD BE USED FOR CREATING (for updates use PUT)
//    // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
//    [HttpPost]
//    public async Task<ActionResult<TEntityDTO>> Post([FromBody]TEntityDTO dto, CancellationToken cancellationToken)
//    {
//      try
//      {
//        var entity = await _crudService.Post(dto, cancellationToken);
//        return Ok(entity);
//      }
//      catch (Exception ex)
//      {
//        return BadRequest(ex);
//      }
//    }

//    // DELETE: api/T/5
//    [HttpDelete("{id}")]
//    public async Task<IActionResult> Delete(Guid id, CancellationToken cancellationToken)
//    {
//      try
//      {
//        await _crudService.Delete(id, cancellationToken);
//      }
//      catch (Exception ex)
//      {
//        return BadRequest(ex);
//      }

//      return NoContent();
//    }
//  }
//}
