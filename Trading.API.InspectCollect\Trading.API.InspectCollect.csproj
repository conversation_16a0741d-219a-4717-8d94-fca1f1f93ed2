﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Configurations>RemarqDevelopment;RemarqProd;RemarqLocal-DevDB;RemarqLocal-ProdDB;ICLocal-Dev;;ICLocal-Prod;IC-Dev;IC-Prod</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.2.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Trading.Services.InspectCollect\Trading.Services.InspectCollect.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="local\motorvehicleinspection-143577.pdf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
