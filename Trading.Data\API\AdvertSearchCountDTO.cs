﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO
{
  public class AdvertSearchCountDTO
  {
    public string GroupName { get; set; } // i.e. Volvo, Ford - or Volvo XC60, Ford Transit
    
    public string Name { get; set; }  // i.e. Volvo, Ford, or XC60, Transit etc.
    public uint Count { get; set; }   // number of adverts in the DB according to Name group
    
    public long? EntityId { get; set; } // the id of the corresponding group entity (i.e. makeId)
  }
}