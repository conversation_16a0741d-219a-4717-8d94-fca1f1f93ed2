﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Trading.API.Data.Migrations
{
    /// <inheritdoc />
    public partial class ICOutcome : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<uint>(
                name: "ICOutcomeId",
                table: "ICVehicle",
                type: "int unsigned",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "OutcomeSetById",
                table: "ICVehicle",
                type: "binary(16)",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "OutcomeSetByUserId",
                table: "ICVehicle",
                type: "binary(16)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "OutcomeSetDate",
                table: "ICVehicle",
                type: "datetime(6)",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "ICVehicleOutcome",
                columns: table => new
                {
                    Id = table.Column<uint>(type: "int unsigned", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    Outcome = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Colour = table.Column<string>(type: "varchar(20)", maxLength: 20, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    OutcomeType = table.Column<int>(type: "int", nullable: false),
                    StatusId = table.Column<uint>(type: "int unsigned", nullable: false),
                    Added = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    Updated = table.Column<DateTime>(type: "datetime(6)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ICVehicleOutcome", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ICVehicleOutcome_Status_StatusId",
                        column: x => x.StatusId,
                        principalTable: "Status",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_ICVehicle_ICOutcomeId",
                table: "ICVehicle",
                column: "ICOutcomeId");

            migrationBuilder.CreateIndex(
                name: "IX_ICVehicle_OutcomeSetById",
                table: "ICVehicle",
                column: "OutcomeSetById");

            migrationBuilder.CreateIndex(
                name: "IX_ICVehicleOutcome_StatusId",
                table: "ICVehicleOutcome",
                column: "StatusId");

            migrationBuilder.AddForeignKey(
                name: "FK_ICVehicle_ICUsers_OutcomeSetById",
                table: "ICVehicle",
                column: "OutcomeSetById",
                principalTable: "ICUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_ICVehicle_ICVehicleOutcome_ICOutcomeId",
                table: "ICVehicle",
                column: "ICOutcomeId",
                principalTable: "ICVehicleOutcome",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ICVehicle_ICUsers_OutcomeSetById",
                table: "ICVehicle");

            migrationBuilder.DropForeignKey(
                name: "FK_ICVehicle_ICVehicleOutcome_ICOutcomeId",
                table: "ICVehicle");

            migrationBuilder.DropTable(
                name: "ICVehicleOutcome");

            migrationBuilder.DropIndex(
                name: "IX_ICVehicle_ICOutcomeId",
                table: "ICVehicle");

            migrationBuilder.DropIndex(
                name: "IX_ICVehicle_OutcomeSetById",
                table: "ICVehicle");

            migrationBuilder.DropColumn(
                name: "ICOutcomeId",
                table: "ICVehicle");

            migrationBuilder.DropColumn(
                name: "OutcomeSetById",
                table: "ICVehicle");

            migrationBuilder.DropColumn(
                name: "OutcomeSetByUserId",
                table: "ICVehicle");

            migrationBuilder.DropColumn(
                name: "OutcomeSetDate",
                table: "ICVehicle");
        }
    }
}
