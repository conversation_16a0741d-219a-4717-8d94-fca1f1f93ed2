﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System;
using Trading.Services.Interfaces.AI;

namespace Trading.API.Remarq.Controllers
{
  [Route("api/ai")]
  [ApiController]
  [Authorize]
  public class AIController : ControllerBase
  {
    private readonly IAIService _aiService;

    public AIController(IAIService aiService)
    {
      _aiService = aiService;
    }

    public class QuestionRequest
    {
      public string Question { get; set; }
    }

    [HttpPost]
    [AllowAnonymous]  // Remove this after testing!
    public async Task<IActionResult> AskQuestion([FromBody] QuestionRequest request)
    {
      if (request == null || string.IsNullOrEmpty(request.Question))
      {
        return BadRequest("Question is required.");
      }

      try
      {
        var answer = await _aiService.AskQuestionAsync(request.Question);
        return Ok(new { question = request.Question, answer });
      }
      catch (Exception ex)
      {
        return StatusCode(500, $"Internal server error: {ex.Message}");
      }
    }
  }
}
