﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums.InspectCollect;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICTriggerBase
  {
    public Guid? ICUserId { get; set; }
    public bool? IsAdmin { get; set; }
    public bool? IsGod { get; set; }
    public Guid? ICContainerGroupId { get; set; }
  }
}