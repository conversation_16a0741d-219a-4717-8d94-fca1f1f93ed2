﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.DTO.LeadCRM;
using Trading.API.Data.Enums.Valuation;

namespace Trading.API.Data.DTO.Valuation
{
  public class ValuationQuoteDTO : BaseModelEntityDTO
  {
    public string VRM { get; set; }
    public uint Odometer { get; set; }

    public uint? MakeId { get; set; }
    public MakeDTO Make { get; set; }

    public uint? ModelId { get; set; }
    public ModelDTO Model { get; set; }

    public uint? DerivId { get; set; }
    public DerivDTO Deriv { get; set; }

    public uint? FuelTypeId { get; set; }
    public FuelTypeDTO FuelType { get; set; }

    public uint? TransmissionTypeId { get; set; }
    public TransmissionTypeDTO TransmissionType { get; set; }

    public uint? VehicleColourId { get; set; }
    public VehicleColourDTO VehicleColour { get; set; }

    public uint? BodyTypeId { get; set; }
    public BodyTypeDTO BodyType { get; set; }

    public ValuationQuoteDataDTO ValuationQuoteData { get; set; } // json

    public int QuotedValue { get; set; }

    public ValuationQuoteTypeEnum QuoteType { get; set; }

    public Guid? LeadVehicleId { get; set; }

    public ContactDTO Contact { get; set; }
    public Guid? ContactId { get; set; }
    public DateTime? Expires { get; set; }
    public string ContactNote { get; set; }
    public LeadVehicleDTO LeadVehicle { get; set; }
  }
}
