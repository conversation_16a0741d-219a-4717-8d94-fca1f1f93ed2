using System;
using System.Collections.Generic;

namespace Trading.API.Data.DTO
{
  public class SavedSearchDTO : BaseModelEntityDTO
  {
    public Guid? SearchId { get; set; }
    public Guid? ContactId { get; set; }
    public bool? SendUpdates { get; set; }
    public bool? SendEmail { get; set; }
    public bool? SendSMS { get; set; }
    public uint? UpdateFrequency { get; set; }

    public DateTime? PauseUntil { get; set; }

    public string SearchName { get; set; }

    // public ContactDTO Contact { get; set; }
    public SearchDTO Search { get; set; }

    public bool IsProfile { get; set; }
    public uint? MaxVehicles { get; set; } // max that can be added to the Sale instance

    public List<SaleSearchProfileDTO> SaleSearchProfiles { get; set; } = new List<SaleSearchProfileDTO>();
  }
}