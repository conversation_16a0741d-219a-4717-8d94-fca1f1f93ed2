﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO
{
  public class CustomerNoteSearchDTO : BaseSearchDTO
  {
    public CustomerNoteFilters Filters { get; set; } = new CustomerNoteFilters() { };
  }
  public class CustomerNoteFilters : BaseFilterGuid
  {
    public Guid? CustomerId { get; set; }
  }
}
