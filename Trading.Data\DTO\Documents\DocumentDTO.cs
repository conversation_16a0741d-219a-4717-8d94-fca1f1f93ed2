using System;
using System.Collections.Generic;
using Trading.API.Data.Models;

namespace Trading.API.Data.DTO.Documents
{
    public class DocumentDTO : BaseModelEntityDTO
    {
        public bool Inbound { get; set; }
        public bool Signed { get; set; }
        public DateTime? DateSent { get; set; }
        public DateTime? DateReceived { get; set; }
        public DateTime? DueDate { get; set; }
        public DocumentTemplateDTO DocumentTemplate { get; set; }
        public Guid DocumentTemplateId { get; set; }

    }
    public class DocumentSearchDTO : BaseSearchDTO
    {
        public DocumentFilters Filters { get; set; } = new DocumentFilters();
    }

    public class DocumentFilters : BaseFilter
    {
    }
}