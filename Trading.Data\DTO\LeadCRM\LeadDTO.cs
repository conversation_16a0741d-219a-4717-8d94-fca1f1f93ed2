﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Models.LeadCRM;

namespace Trading.API.Data.DTO.LeadCRM
{
  public class LeadDTO : LeadBaseModelDTO
  {
    public uint LeadProductId { get; set; }
    public LeadProductDTO LeadProduct { get; set; }

    public Guid LeadCustomerId { get; set; }
    public LeadCustomerDTO LeadCustomer { get; set; }

    public uint LeadStatusId { get; set; }
    public LeadStatusDTO LeadStatus { get; set; }

    public Guid? OwnerId { get; set; }

    public ContactDTO Owner { get; set; }

    public Guid? PrimaryLeadContactLinkId { get; set; }

    public virtual LeadContactLinkDTO PrimaryLeadContactLink { get; set; }


    public uint LeadSourceId { get; set; }
    public LeadSourceDTO LeadSource { get; set; }
    public LeadVehicleDTO LeadVehicle { get; set; }
    public string LeadSourceComment { get; set; }

    public virtual List<LeadNoteDTO> LeadNotes { get; set; } = new List<LeadNoteDTO>();
    public virtual List<LeadContactLinkDTO> LeadContactLinks { get; set; } = new List<LeadContactLinkDTO>();

  }

}
